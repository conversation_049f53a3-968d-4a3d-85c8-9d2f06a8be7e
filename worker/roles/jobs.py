import logging

from aiohttp import web

from app.auth.db import select_role_by_id, update_role
from app.lib import types as core_types
from app.models import count
from app.reviews.tables import review_table
from app.services import services
from app.signatures.tables import signature_table

TARGET_FEW_SIGNATURES_COUNT = 2
TARGET_FEW_REVIEWS_COUNT = 2


async def recalculate_signatures_count_for_useful_meta(
    _: web.Application,
    data: core_types.DataDict,
    logger: logging.Logger,
) -> None:
    """
    Check if role has enough signatures to apply condition in db
        `hasFewSignatures = False`
    """

    role_id = data['role_id']

    logger.info(
        'Try to update has_few_signatures for role',
        extra={'role_id': role_id},
    )

    async with services.db_readonly.acquire() as conn:
        role = await select_role_by_id(conn=conn, role_id=role_id)

        if not role:
            logger.info(
                'Role not found for recalculating signatures useful meta.',
                extra={'role_id': role_id},
            )
            return

        if role.has_few_signatures is False:
            logger.info(
                'Skipping recalculating signatures for useful meta.',
                extra={'role_id': role.id_},
            )
            return

        signatures_count = await count(
            conn=conn,
            select_from=signature_table,
            clause=signature_table.c.role_id == role.id_,
            limit=2,
        )

        if signatures_count < TARGET_FEW_SIGNATURES_COUNT:
            logger.info(
                'Not enough signatures to update useful meta.',
                extra={'role_id': role.id_, 'signatures_count': signatures_count},
            )
            return

    async with services.db.acquire() as conn:
        await update_role(
            conn=conn,
            role_id=role.id_,
            data={'has_few_signatures': False},
        )
        logger.info(
            'Role has been updated with new useful meta property.',
            extra={'role_id': role.id_, 'has_few_signatures': False},
        )


async def recalculate_reviews_count_for_useful_meta(
    _: web.Application,
    data: core_types.DataDict,
    logger: logging.Logger,
) -> None:
    """
    Check if role has enough reviews to apply condition in db
        `hasFewReviews = False`
    """

    role_id = data['role_id']

    logger.info(
        'Try to update has_few_reviews for role',
        extra={'role_id': role_id},
    )

    async with services.db_readonly.acquire() as conn:
        role = await select_role_by_id(conn=conn, role_id=role_id)

        if not role:
            logger.info(
                'Role not found for recalculating reviews useful meta.',
                extra={'role_id': role_id},
            )
            return

        if role.has_few_reviews is False:
            logger.info(
                'Skipping recalculating reviews for useful meta.',
                extra={'role_id': role.id_},
            )
            return

        reviews_count = await count(
            conn=conn,
            select_from=review_table,
            clause=review_table.c.role_id == role.id_,
            limit=2,
        )

        if reviews_count < TARGET_FEW_REVIEWS_COUNT:
            logger.info(
                'Not enough reviews to update useful meta.',
                extra={'role_id': role.id_, 'reviews_count': reviews_count},
            )
            return

    async with services.db.acquire() as conn:
        await update_role(
            conn=conn,
            role_id=role.id_,
            data={'has_few_reviews': False},
        )
        logger.info(
            'Role has been updated with new useful meta property.',
            extra={'role_id': role.id_, 'has_few_reviews': False},
        )
