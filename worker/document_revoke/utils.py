from collections import defaultdict

from app.auth.constants import COMPANY_ID_REQUEST_KEY
from app.auth.schemas import CompanyConfig
from app.auth.types import User
from app.auth.utils import get_default_company_config
from app.documents.emailing import build_delete_request_sender_line
from app.documents.types import Document
from app.i18n import _
from app.lib import urls
from app.lib.emailing import send_email
from worker.documents.types import DeleteRequestRecipient


async def send_emails_about_new_revoke_request(
    initiator: User,
    message: str,
    companies_configs: dict[str, CompanyConfig],
    emails_mapping: defaultdict[str, defaultdict[str, list[Document]]],
    recipients_mapping: dict[tuple[str, str], DeleteRequestRecipient],
) -> None:
    """
    Parse emails_mapping and send emails
    """

    for email, documents_mapping in emails_mapping.items():
        recipient_doc_data = []
        for company_id, documents in documents_mapping.items():
            recipient = recipients_mapping.get((email, company_id))
            if not recipient:
                continue

            for document in documents:
                # TODO[revoke]: Add some parameter to open revoke request signing popup?
                recipient_doc_data.append(
                    {
                        'document': document,
                        'url': urls.build_url(
                            route='app',
                            tail=f'/documents/{document.id}',
                            get={COMPANY_ID_REQUEST_KEY: company_id},
                        ),
                    }
                )

            if len(documents_mapping) == 1:
                subject = _('Ви отримали запит на анулювання документа')
            else:
                subject = _('Ви отримали запит на анулювання документів')

            company_config = companies_configs.get(company_id) or get_default_company_config()

            initiator_line = build_delete_request_sender_line(
                company_name=initiator.company_name,
                company_edrpou=initiator.company_edrpou,
                user_email=initiator.email,
                company_config=company_config,
            )
            context = {
                'recipient_company_edrpou': recipient.company_edrpou,
                'recipient_company_name': recipient.company_name,
                'initiator_line': initiator_line,
                'documents_data': recipient_doc_data,
                'initiator_message': message,
                'header': subject,
            }
            await send_email(
                recipient_mixed=email,
                subject=subject,
                template_name='revoke_request_new',
                context=context,
                language=recipient.language,
            )
