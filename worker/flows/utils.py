import logging

from app.auth import utils as auth
from app.document_automation import utils as document_automation_templates
from app.documents import utils as documents
from app.documents.types import Document
from app.flow import utils as flow_utils
from app.flow.types import FlowItem
from app.lib.database import DBConnection
from app.lib.enums import Source
from app.reviews import utils as reviews
from app.services import services
from app.signatures import utils as signatures
from worker import topics
from worker.flows.types import FlowRecipient

logger = logging.getLogger(__name__)


async def _create_tags_for_multilateral_document(document: Document, flows: list[FlowItem]) -> None:
    """
    Create tags in recipient company for multilateral document by recipients
    contacts settings.
    """
    if not flows:
        return

    companies_edrpou = {flow.edrpou for flow in flows}

    for company_edrpou in companies_edrpou:
        await documents.connect_document_with_tags_by_contact(
            edrpou=company_edrpou,
            contact_edrpou=document.edrpou_owner,
            documents_ids=[document.id],
        )


async def _filter_flow_recipients_by_review(
    conn: DBConnection,
    recipients: list[FlowRecipient],
    document_id: str,
) -> list[FlowRecipient]:
    """
    Remote flows from document that have required review, but it is
    not approved yet.
    """
    statuses = await reviews.get_review_statuses(conn, document_id=document_id)
    required_reviews = {
        status.edrpou for status in statuses if status.is_required and not status.is_approved
    }

    return [recipient for recipient in recipients if recipient.edrpou not in required_reviews]


async def _filter_flows_by_document_signers(
    conn: DBConnection,
    recipients: list[FlowRecipient],
    document_id: str,
) -> list[FlowRecipient]:
    """
    Remote flows that have document signers
    """

    companies_edrpou = [recipient.edrpou for recipient in recipients]
    companies_edrpous_with_signers = await signatures.exists_document_signers(
        conn=conn,
        document_id=document_id,
        companies_edrpou=companies_edrpou,
    )

    return [
        recipient
        for recipient in recipients
        if recipient.edrpou not in companies_edrpous_with_signers
    ]


async def _start_document_automation_by_recipients(
    document_id: str,
    flows: list[FlowItem],
    source: Source,
) -> None:
    """Start document automation for each unique company from flows"""

    companies_edrpous = {flow.edrpou for flow in flows}
    for company_edrpou in companies_edrpous:
        await document_automation_templates.start_document_automation(
            document_id=document_id,
            company_edrpou=company_edrpou,
            source=source,
        )


async def _filter_flow_recipients_by_owner(
    conn: DBConnection,
    recipients: list[FlowRecipient],
    document: Document,
) -> list[FlowRecipient]:
    """Remove document owner from flows recipients"""

    document_owner = await auth.get_user(conn, role_id=document.role_id)
    if not document_owner:
        return []

    result = []
    for recipient in recipients:
        # Remove uploader from recipients of notification
        if (
            recipient.edrpou == document_owner.company_edrpou
            and recipient.email == document_owner.email
            and recipient.order in (0, None)
        ):
            continue

        result.append(recipient)

    return result


async def _get_flows_recipients(
    conn: DBConnection,
    flows: list[FlowItem],
    document: Document,
    company_edrpou: str | None = None,
) -> list[FlowRecipient]:
    """
    Get list of recipients for flows, convert it to unflattened list of FlowRecipient
    instances and remove flows which do not comply with our requirements.
    """

    # Filter flows only for given company
    if company_edrpou:
        flows = [flow for flow in flows if flow.edrpou == company_edrpou]

    # This can be when the last signer was removed
    if not flows:
        return []

    # Get flows with recipients
    flows_ids = [flow.id for flow in flows]
    _flows = await flow_utils.get_flows_with_recipients(conn, flows_ids=flows_ids)

    # Unflatten flows by creating instance of FlowRecipient for each recipient email.
    recipients: list[FlowRecipient] = [
        FlowRecipient(
            id=flow.id,
            edrpou=flow.edrpou,
            email=email,
            order=flow.order,
        )
        for flow in _flows
        for email in flow.emails_list
    ]

    # TODO: should we use some orchestrator here to decouple reviews and document
    #  signers from flows? By current logic, we doesn't send notification about
    #  document to the company if it has not finished required reviews or document
    #  signers. At the same time we expect that reviews and documents signers modules
    #  will trigger "send_multilateral_document_notification" job when their state
    #  will be changed.

    # Remove flows with not finished required reviews
    recipients = await _filter_flow_recipients_by_review(
        conn=conn,
        recipients=recipients,
        document_id=document.id,
    )

    # Remove flows with document signers
    recipients = await _filter_flows_by_document_signers(
        conn=conn,
        recipients=recipients,
        document_id=document.id,
    )

    # Remove document owner from flow
    recipients = await _filter_flow_recipients_by_owner(
        conn=conn,
        recipients=recipients,
        document=document,
    )

    return recipients


async def send_multilateral_document_to_recipients(
    conn: DBConnection,
    *,
    document_id: str,
    source: Source,
) -> None:
    """
    Send multilateral document to recipients:
     - send notification to recipients
     - create tags by recipients contacts
     - start document automation
    """

    state = await flow_utils.get_flows_state(conn, document_id=document_id)
    flows = state.flows_for_execute_send_jobs
    if not flows:
        logger.info('No flows for sending', extra={'document_id': document_id})
        return

    document = await documents.get_document(conn, document_id=document_id)
    if not document:
        # Probably document was deleted
        logger.info('Document not found', extra={'document_id': document_id})
        return

    logger.info(
        msg='Sending flows for document',
        extra={
            'document_id': document_id,
            'flows': [flow.id for flow in flows],
        },
    )

    # Schedule next async tasks to create tags, start document automation and send
    # notification
    await _create_tags_for_multilateral_document(
        document=document,
        flows=flows,
    )
    await _start_document_automation_by_recipients(
        document_id=document_id,
        flows=flows,
        source=source,
    )
    await flow_utils.send_multilateral_document_notification_job(document_id=document_id)

    await flow_utils.mark_send_flows_jobs_as_executed(
        conn=conn,
        flows_ids=[flow.id for flow in flows],
    )


async def send_notification_about_multilateral_document(
    conn: DBConnection,
    document_id: str,
    company_edrpou: str | None,
) -> None:
    """
    Send notification about multilateral document
    """

    state = await flow_utils.get_flows_state(conn, document_id=document_id)
    flows = state.flows_for_send_notifications
    if not flows:
        logger.info('No flows for sending', extra={'document_id': document_id})
        return

    document = await documents.get_document(conn, document_id=document_id)
    if not document:
        # Probably document was deleted
        logger.info('Document not found', extra={'document_id': document_id})
        return

    logger.info(
        msg='Sending notification about document',
        extra={
            'document_id': document_id,
            'flows': [flow.id for flow in flows],
        },
    )

    recipients = await _get_flows_recipients(
        conn=conn,
        flows=flows,
        document=document,
        company_edrpou=company_edrpou,
    )
    for recipient in recipients:
        await services.kafka.send_record(
            topic=topics.SEND_DOCUMENT_TO_RECIPIENT,
            value={
                'recipient_edrpou': recipient.edrpou,
                'recipient_email': recipient.email,
                'document_id': document.id,
                'current_role_id': document.role_id,
            },
        )

    flows_ids = list({recipient.id for recipient in recipients})
    await flow_utils.mark_send_flows_notifications_as_executed(
        conn=conn,
        flows_ids=flows_ids,
    )
