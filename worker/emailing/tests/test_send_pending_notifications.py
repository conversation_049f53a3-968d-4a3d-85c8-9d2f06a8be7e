from aiohttp.test_utils import make_mocked_request
from yarl import URL

from app.lib.datetime_utils import utc_now
from app.lib.enums import DocumentStatus
from app.notifications.db import (
    insert_pending_notification,
)
from app.notifications.enums import PendingNotificationGroup
from app.services import services
from app.tests.common import (
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    prepare_client,
    prepare_document_data,
)
from app.tokens.db import select_short_registration_token
from app.trigger_notifications.db import select_pending_notification_by_id
from cron.jobs import send_pending_notifications
from worker.emailing.utils import (
    prepare_data,
    prepare_pending_notification_context,
    select_pending_notifications,
    update_pending_as_sent,
)


async def test_prepare_context(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    request = make_mocked_request('GET', '/', app=app)
    document = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.signed_and_sent.value,
    )

    async with services.db.acquire() as conn:
        await insert_pending_notification(
            conn, user.role_id, document.id, PendingNotificationGroup.inbox
        )

        date_select = utc_now()
        raw_data = await select_pending_notifications(conn, date_select)
    data = prepare_data(raw_data)
    recipients = await prepare_pending_notification_context(request.app, data, utc_now())

    recipient = recipients[0]
    assert recipient.email == TEST_DOCUMENT_EMAIL_RECIPIENT
    assert recipient.subject == 'У Вас оновлення у документах'

    context = recipient.context
    assert context['first_name'] is None
    assert context['header'] == 'У Вас оновлення у документах'

    companies = context['companies']
    assert len(companies) == 1

    company = companies[0]
    assert company['is_confirmed'] is False
    assert company['button_label'] == 'Зареєструватись'
    assert company['company_label'] == (f'Компанія з ЄДРПОУ/ІПН "{TEST_DOCUMENT_EDRPOU_RECIPIENT}"')
    assert company['groups']['sign'] == 1

    button_url = URL(company['button_url'])
    assert button_url.path.startswith('/invite/')

    token = button_url.path[8:]
    async with app['db'].acquire() as conn:
        short_reg_token = await select_short_registration_token(conn, token)
    assert short_reg_token.invited_by_company == user.company_edrpou
    assert short_reg_token.invited_by_user == user.id
    assert short_reg_token.email == TEST_DOCUMENT_EMAIL_RECIPIENT
    assert short_reg_token.edrpou == TEST_DOCUMENT_EDRPOU_RECIPIENT
    assert short_reg_token.is_verified_inviter is None
    assert short_reg_token.registered_user_id is None
    assert short_reg_token.date_registered is None


async def test_prepare_data(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.signed_and_sent.value,
    )

    async with services.db.acquire() as conn:
        await insert_pending_notification(
            conn, user.role_id, document.id, PendingNotificationGroup.inbox
        )

        date_select = utc_now()
        raw_data = await select_pending_notifications(conn, date_select)
    data = prepare_data(raw_data)

    recipient = data[TEST_DOCUMENT_EMAIL_RECIPIENT]
    assert recipient['is_registered'] is False
    assert recipient['first_name'] is None
    companies = recipient['companies']
    company = companies[TEST_DOCUMENT_EDRPOU_RECIPIENT]
    assert company['is_confirmed'] is False
    assert company['company_name'] is None
    assert company['company_id'] is None
    assert str(company['created_by']) == user.role_id
    assert company['groups']['sign'] == 1


async def test_select_pending_notifications(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.signed_and_sent.value,
    )

    async with services.db.acquire() as conn:
        await insert_pending_notification(
            conn, user.role_id, document.id, PendingNotificationGroup.inbox
        )

        date_select = utc_now()
        data = await select_pending_notifications(conn, date_select)
        assert len(data) == 1

    pending = data[0]
    # User meta
    assert pending.email == TEST_DOCUMENT_EMAIL_RECIPIENT
    assert pending.is_registered is False
    assert str(pending.created_by) == user.role_id
    assert pending.first_name is None
    # Company meta
    assert pending.is_confirmed is False
    assert pending.company_id is None
    assert pending.company_edrpou == TEST_DOCUMENT_EDRPOU_RECIPIENT
    assert pending.count_documents == 1
    assert pending.group == 'sign'


async def test_send_pending_notifications(mailbox, aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    document_one = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.signed_and_sent.value,
    )
    document_two = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': ['<EMAIL>'],
            }
        ],
        status_id=DocumentStatus.signed_and_sent.value,
    )
    document_three = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': ['this_is_wrong_email_address@'],
            }
        ],
        status_id=DocumentStatus.signed_and_sent.value,
    )

    async with services.db.acquire() as conn:
        await insert_pending_notification(
            conn, user.role_id, document_one.id, PendingNotificationGroup.inbox
        )
        await insert_pending_notification(
            conn, user.role_id, document_two.id, PendingNotificationGroup.inbox
        )
        await insert_pending_notification(
            conn, user.role_id, document_three.id, PendingNotificationGroup.inbox
        )

        date_select = utc_now()
        data = await select_pending_notifications(conn, date_select)
        assert len(data) == 3

        assert len(mailbox) == 0
        await send_pending_notifications(app)
        assert len(mailbox) == 3

        assert {m['to'] for m in mailbox} == {
            TEST_DOCUMENT_EMAIL_RECIPIENT,
            '<EMAIL>',
            # currently, we don't validate email address, we rely on SMTP provider to validate it
            # TODO: fix that
            'this_is_wrong_email_address@',
        }

        data = await select_pending_notifications(conn, date_select)
        assert len(data) == 0


async def test_update_pending_as_sent(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    async with services.db.acquire() as conn:
        pending_id = await insert_pending_notification(
            conn, user.role_id, document.id, PendingNotificationGroup.inbox
        )

        # Notification is pending
        pending = await select_pending_notification_by_id(conn, pending_id)
        assert pending.document_id == document.id
        assert pending.created_by == user.role_id
        assert pending.group == PendingNotificationGroup.inbox
        assert pending.date_sent is None

        # Notification was sent
        date_sent = utc_now()
        await update_pending_as_sent(conn, date_sent)
        sent = await select_pending_notification_by_id(conn, pending_id)
        assert sent.id == pending.id
        assert sent.date_sent == date_sent
