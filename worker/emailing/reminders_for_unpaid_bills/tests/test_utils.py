import pytest
from dateutil.relativedelta import relativedelta

from app.billing import types as billing_types
from app.lib import datetime_utils as dt_utils
from app.services import services
from app.tests import common
from worker.emailing.reminders_for_unpaid_bills import utils
from worker.emailing.reminders_for_unpaid_bills.tests import common as bill_tests_common


async def test_bill_without_rate_services(aiohttp_client):
    """
    Given a bill without rate services (documents/extensions)
    When calling should_remind_about_unpaid_bill function
    Expected to return False
    """
    # Arrange
    app, client, user = await common.prepare_client(aiohttp_client)

    # Arrange bill without rate_services
    async with services.db.acquire() as conn:
        bill = await bill_tests_common.prepare_unpaid_bill(
            conn=conn,
            user=user,
            documents=True,
            services_type=billing_types.BillServicesType.documents,
        )

    # Act
    result = await utils.should_remind_about_unpaid_bill(conn, bill)

    # Assert
    assert result == ('Bill without rate services', False)


class TestShouldRemindAboutArchiveBill:
    @pytest.mark.parametrize(
        'bill_date_created, expected_reason, expected_result',
        (
            [
                # Should not remind because company has newer combined bill
                dt_utils.local_now() + relativedelta(days=10),
                'Company has newer combined bill',
                False,
            ],
            [
                # Should remind about archive because combined bill was generated before archive
                dt_utils.local_now() - relativedelta(days=10),
                None,
                True,
            ],
        ),
    )
    async def test_company_has_newer_combined_bill(
        self,
        aiohttp_client,
        bill_date_created,
        expected_reason,
        expected_result,
    ):
        """
        Given a company with/without newer combined bill
        When calling should_remind_about_unpaid_bill
        Expected to return valid result
        """
        # Arrange
        app, client, user = await common.prepare_client(aiohttp_client)

        async with services.db.acquire() as conn:
            # Arrange archive bill
            bill = await bill_tests_common.prepare_unpaid_bill(
                conn=conn,
                user=user,
                archive_rate=billing_types.AccountRate.archive_small,
            )

            # Arrange combined bill
            await bill_tests_common.prepare_unpaid_bill(
                conn=conn,
                user=user,
                web_rate=billing_types.AccountRate.latest_start(),
                archive_rate=billing_types.AccountRate.archive_small,
                date_created=bill_date_created,
            )

            # Act
            result = await utils.should_remind_about_unpaid_bill(conn, bill)

        # Assert
        assert (expected_reason, expected_result) == result

    @pytest.mark.parametrize(
        'bill_date_created, expected_reason, expected_result',
        (
            [
                # Should not remind because larger archive bill within last 20 days exists
                dt_utils.local_now() - relativedelta(days=10),
                'Company has bill for larger single archive',
                False,
            ],
            [
                # Should remind because there is larger archive bill exists but it's
                # older than 20 days
                dt_utils.local_now() - relativedelta(days=21),
                None,
                True,
            ],
            [
                # Should remind because there are no larger archive bill
                None,
                None,
                True,
            ],
        ),
    )
    async def test_company_has_bill_for_larger_single_archive(
        self,
        aiohttp_client,
        bill_date_created,
        expected_reason,
        expected_result,
    ):
        """
        Given a company with/without larger single archive bill
        When calling should_remind_about_unpaid_bill
        Expected to return valid result
        """
        # Arrange
        app, client, user = await common.prepare_client(aiohttp_client)

        async with services.db.acquire() as conn:
            # Arrange archive bill
            bill = await bill_tests_common.prepare_unpaid_bill(
                conn=conn,
                user=user,
                archive_rate=billing_types.AccountRate.archive_small,
            )

            if bill_date_created:
                # Arrange larger archive bill
                await bill_tests_common.prepare_unpaid_bill(
                    conn=conn,
                    user=user,
                    archive_rate=billing_types.AccountRate.archive_big,
                    date_created=bill_date_created,
                )

            # Act
            result = await utils.should_remind_about_unpaid_bill(conn, bill)

        # Assert
        assert (expected_reason, expected_result) == result

    @pytest.mark.parametrize(
        """
        bill_archive_rate,
        active_company_archive_rate,
        planned_company_archive_rate,
        active_company_archive_rate_date_expired,
        expected_reason,
        expected_result,
        """,
        (
            [
                # Should remind.
                # Company has similar active archive rate
                # but it expires soon without planned rate.
                billing_types.AccountRate.archive_small,  # Bill rate
                billing_types.AccountRate.archive_small,  # Active rate
                None,  # Planned rate
                dt_utils.local_now() + relativedelta(days=15),  # Active rate expiry
                None,
                True,
            ],
            [
                # Shouldn't remind.
                # Company has similar active archive rate
                # and it expires soon with existing planned rate.
                billing_types.AccountRate.archive_small,  # Bill rate
                billing_types.AccountRate.archive_small,  # Active rate
                billing_types.AccountRate.archive_small,  # Planned rate
                dt_utils.local_now() + relativedelta(days=15),  # Active rate expiry
                'Company has similar planned archive rate',
                False,
            ],
            [
                # Shouldn't remind
                # Company already has similar active archive rate
                # and it doesn't expire soon.
                billing_types.AccountRate.archive_small,  # Bill rate
                billing_types.AccountRate.archive_small,  # Active rate
                None,  # Planned rate
                dt_utils.local_now() + relativedelta(days=100),  # Active rate expiry
                'Company has similar active archive rate',
                False,
            ],
            [
                # Shouldn't remind.
                # Company already has larger archive rate
                # but it expires soon without planned rate
                billing_types.AccountRate.archive_small,  # Bill rate
                billing_types.AccountRate.archive_big,  # Active rate
                None,
                dt_utils.local_now() + relativedelta(days=15),
                'Company has higher active archive rate',
                False,
            ],
            [
                # Shouldn't remind
                # Company already has similar archive rate
                # and it expires soon with existing higher planned rate
                billing_types.AccountRate.archive_small,  # Bill rate
                billing_types.AccountRate.archive_small,  # Active rate
                billing_types.AccountRate.archive_big,  # Planned rate
                dt_utils.local_now() + relativedelta(days=15),
                'Company has higher planned archive rate',
                False,
            ],
            [
                # Should remind
                # Company hasn't similar archive rate
                billing_types.AccountRate.archive_small,  # Bill rate
                None,  # Active rate
                None,  # Planned rate
                None,
                None,
                True,
            ],
        ),
    )
    async def test_company_has_archive_rate(
        self,
        aiohttp_client,
        bill_archive_rate,
        active_company_archive_rate,
        active_company_archive_rate_date_expired,
        planned_company_archive_rate,
        expected_reason,
        expected_result,
    ):
        """
        Given a company with/without similar/higher expiring/planned archive rate
        When calling should_remind_about_unpaid_bill
        Expected to return valid result
        """
        # Arrange
        app, client, user = await common.prepare_client(aiohttp_client)

        async with services.db.acquire() as conn:
            # Arrange archive bill
            bill = await bill_tests_common.prepare_unpaid_bill(
                conn=conn,
                user=user,
                archive_rate=bill_archive_rate,
            )

            if active_company_archive_rate:
                await common.prepare_billing_account(
                    conn=conn,
                    company_id=user.company_id,
                    rate=active_company_archive_rate,
                    status=billing_types.CompanyRateStatus.active,
                    type_=billing_types.AccountType.client_rate,
                    date_expired=active_company_archive_rate_date_expired,
                )

            if planned_company_archive_rate:
                await common.prepare_billing_account(
                    conn=conn,
                    company_id=user.company_id,
                    rate=planned_company_archive_rate,
                    status=billing_types.CompanyRateStatus.new,
                    type_=billing_types.AccountType.client_rate,
                )

            # Act
            result = await utils.should_remind_about_unpaid_bill(conn, bill)

        # Assert
        assert (expected_reason, expected_result) == result


class TestShouldRemindAboutWebBill:
    @pytest.mark.parametrize(
        """
        bill_web_rate,
        active_account_rate,
        planned_account_rate,
        active_account_rate_date_expired,
        expected_reason,
        expected_result,
        """,
        (
            [
                # Company has similar active web rate
                # but it expires soon without planned one.
                billing_types.AccountRate.latest_start(),  # Bill rate
                billing_types.AccountRate.latest_start(),  # Active rate
                None,  # Planned rate
                dt_utils.local_now() + relativedelta(days=15),  # Active rate expiry
                None,
                True,
            ],
            [
                # Company has similar active web rate
                # but it does not expire soon
                billing_types.AccountRate.latest_start(),  # Bill rate
                billing_types.AccountRate.latest_start(),  # Active rate
                None,  # Planned rate
                dt_utils.local_now() + relativedelta(days=100),  # Active rate expiry
                'Company has similar active web rate',
                False,
            ],
            [
                # Company has similar active web rate
                # but it expires soon with planned one
                billing_types.AccountRate.latest_start(),  # Bill rate
                billing_types.AccountRate.latest_start(),  # Active rate
                billing_types.AccountRate.latest_start(),  # Planned rate
                dt_utils.local_now() + relativedelta(days=15),  # Active rate expiry
                'Company has similar planned web rate',
                False,
            ],
            [
                # Company has higher active web rate
                billing_types.AccountRate.latest_start(),  # Bill rate
                billing_types.AccountRate.latest_pro(),  # Active rate
                None,  # Planned rate
                dt_utils.local_now() + relativedelta(days=15),  # Active rate expiry
                'Company has higher active web rate',
                False,
            ],
            [
                # Company has higher planned web rate
                billing_types.AccountRate.latest_start(),  # Bill rate
                None,  # Active rate
                billing_types.AccountRate.latest_pro(),  # Planned rate
                None,  # Active rate expiry
                'Company has higher planned web rate',
                False,
            ],
            [
                # Company doesn't have any active or planned rates
                billing_types.AccountRate.latest_start(),  # Bill rate
                None,  # Active rate
                None,  # Planned rate
                None,  # Active rate expiry
                None,
                True,
            ],
        ),
    )
    async def test_company_has_web_rate(
        self,
        aiohttp_client,
        bill_web_rate,
        active_account_rate,
        planned_account_rate,
        active_account_rate_date_expired,
        expected_reason,
        expected_result,
    ):
        """
        Given a company with/without similar/higher expiring/planned web rate
        When calling should_remind_about_unpaid_bill
        Expected to return valid result
        """
        # Arrange
        app, client, user = await common.prepare_client(aiohttp_client)

        async with services.db.acquire() as conn:
            # Arrange archive bill
            bill = await bill_tests_common.prepare_unpaid_bill(
                conn=conn,
                user=user,
                web_rate=bill_web_rate,
            )

            if active_account_rate:
                await common.prepare_billing_account(
                    conn=conn,
                    company_id=user.company_id,
                    rate=active_account_rate,
                    status=billing_types.CompanyRateStatus.active,
                    type_=billing_types.AccountType.client_rate,
                    date_expired=active_account_rate_date_expired,
                )

            if planned_account_rate:
                await common.prepare_billing_account(
                    conn=conn,
                    company_id=user.company_id,
                    rate=planned_account_rate,
                    status=billing_types.CompanyRateStatus.new,
                    type_=billing_types.AccountType.client_rate,
                )

            # Act
            result = await utils.should_remind_about_unpaid_bill(conn, bill)

        # Assert
        assert (expected_reason, expected_result) == result

    @pytest.mark.parametrize(
        """
        first_bill_rate,
        second_bill_rate,
        expected_reason,
        expected_result,
        """,
        (
            [
                # Shouldn't remind.
                # Company has bill for higher web rate
                billing_types.AccountRate.latest_start(),  # First bill rate
                billing_types.AccountRate.latest_pro(),  # Second bill rate
                'Company has bill for higher web rate',
                False,
            ],
            [
                # Should remind.
                # Company has bill for similar web rate
                billing_types.AccountRate.latest_start(),  # First bill rate
                billing_types.AccountRate.latest_start(),  # Second bill rate
                None,
                True,
            ],
            [
                # Should remind.
                # Company has bill for lower web rate
                billing_types.AccountRate.latest_pro(),  # First bill rate
                billing_types.AccountRate.latest_start(),  # Second bill rate
                None,
                True,
            ],
            [
                # Should remind.
                # Company has no any other bills
                billing_types.AccountRate.latest_pro(),  # First bill rate
                None,  # Second bill rate
                None,
                True,
            ],
        ),
    )
    async def test_company_has_bill_for_higher_web_rate(
        self,
        aiohttp_client,
        first_bill_rate,
        second_bill_rate,
        expected_reason,
        expected_result,
    ):
        """
        Given a company with/without similar/higher web rate bill
        When calling should_remind_about_unpaid_bill
        Expected to return valid result
        """

        # Arrange
        app, client, user = await common.prepare_client(aiohttp_client)

        async with services.db.acquire() as conn:
            # Arrange archive bill
            first_bill = await bill_tests_common.prepare_unpaid_bill(
                conn=conn,
                user=user,
                web_rate=first_bill_rate,
            )

            if second_bill_rate:
                await bill_tests_common.prepare_unpaid_bill(
                    conn=conn,
                    user=user,
                    web_rate=second_bill_rate,
                )

            # Act
            result = await utils.should_remind_about_unpaid_bill(conn, first_bill)

        # Assert
        assert (expected_reason, expected_result) == result


class TestShouldRemindAboutCombinedBill:
    @pytest.mark.parametrize(
        """
        bill_web_rate,
        bill_archive_rate,
        active_web_rate,
        active_archive_rate,
        active_rate_expiry,
        planned_web_rate,
        planned_archive_rate,
        expected_reason,
        expected_result,
        """,
        (
            [
                # Company have similar active web and archive rates
                # which do not expire soon, without planned rate
                billing_types.AccountRate.latest_start(),  # Bill web rate
                billing_types.AccountRate.archive_small,  # Bill archive rate
                billing_types.AccountRate.latest_start(),  # Active web rate
                billing_types.AccountRate.archive_small,  # Active archive rate
                dt_utils.local_now() + relativedelta(days=100),  # Active rate expiry
                None,
                None,
                'Company has similar active web and archive rate',
                False,
            ],
            [
                # Company have similar active web rate
                # which do not expire soon, without planned rate
                billing_types.AccountRate.latest_start(),  # Bill web rate
                billing_types.AccountRate.archive_small,  # Bill archive rate
                billing_types.AccountRate.latest_start(),  # Active web rate
                None,  # Active archive rate
                dt_utils.local_now() + relativedelta(days=100),  # Active rate expiry
                None,
                None,
                'Company has similar active web rate',
                False,
            ],
            [
                # Company have similar active web and archive rates
                # which expire soon, without planned rate
                billing_types.AccountRate.latest_start(),  # Bill web rate
                billing_types.AccountRate.archive_small,  # Bill archive rate
                billing_types.AccountRate.latest_start(),  # Active web rate
                billing_types.AccountRate.archive_small,  # Active archive rate
                dt_utils.local_now() + relativedelta(days=15),  # Active rate expiry
                None,
                None,
                None,
                True,
            ],
            [
                # Company have similar active web and archive rates
                # which expire soon, with planned rate
                billing_types.AccountRate.latest_start(),  # Bill web rate
                billing_types.AccountRate.archive_small,  # Bill archive rate
                billing_types.AccountRate.latest_start(),  # Active web rate
                billing_types.AccountRate.archive_small,  # Active archive rate
                dt_utils.local_now() + relativedelta(days=15),  # Active rate expiry
                billing_types.AccountRate.latest_start(),  # Planned web rate
                billing_types.AccountRate.archive_small,  # Planned archive rate
                'Company has similar planned web and archive rate',
                False,
            ],
            [
                # Company have higher active web rate
                billing_types.AccountRate.latest_start(),  # Bill web rate
                billing_types.AccountRate.archive_small,  # Bill archive rate
                billing_types.AccountRate.latest_pro(),  # Active web rate
                billing_types.AccountRate.archive_small,  # Active archive rate
                dt_utils.local_now() + relativedelta(days=15),  # Active rate expiry
                None,  # Planned web rate
                None,  # Planned archive rate
                'Company has higher active web rate',
                False,
            ],
            [
                # Company have higher planned web rate
                billing_types.AccountRate.latest_start(),  # Bill web rate
                billing_types.AccountRate.archive_small,  # Bill archive rate
                billing_types.AccountRate.latest_start(),  # Active web rate
                billing_types.AccountRate.archive_small,  # Active archive rate
                dt_utils.local_now() + relativedelta(days=15),  # Active rate expiry
                billing_types.AccountRate.latest_pro(),  # Planned web rate
                billing_types.AccountRate.archive_big,  # Planned archive rate
                'Company has higher planned web rate',
                False,
            ],
        ),
    )
    async def test_company_has_combined_rate(
        self,
        aiohttp_client,
        bill_web_rate,
        bill_archive_rate,
        active_web_rate,
        active_archive_rate,
        active_rate_expiry,
        planned_web_rate,
        planned_archive_rate,
        expected_reason,
        expected_result,
    ):
        """
        Given a company with/without similar/higher active/planned combined rate
        When calling should_remind_about_unpaid_bill
        Expected to return valid result
        """
        # Arrange
        app, client, user = await common.prepare_client(aiohttp_client)

        async with services.db.acquire() as conn:
            # Arrange archive bill
            bill = await bill_tests_common.prepare_unpaid_bill(
                conn=conn,
                user=user,
                web_rate=bill_web_rate,
                archive_rate=bill_archive_rate,
                services_type=billing_types.BillServicesType.web_and_archive,
            )

            if active_web_rate:
                await common.prepare_billing_account(
                    conn=conn,
                    company_id=user.company_id,
                    rate=active_web_rate,
                    status=billing_types.CompanyRateStatus.active,
                    type_=billing_types.AccountType.client_rate,
                    date_expired=active_rate_expiry,
                )

            if active_archive_rate:
                await common.prepare_billing_account(
                    conn=conn,
                    company_id=user.company_id,
                    rate=active_archive_rate,
                    status=billing_types.CompanyRateStatus.active,
                    type_=billing_types.AccountType.client_rate,
                    date_expired=active_rate_expiry,
                )

            if planned_web_rate:
                await common.prepare_billing_account(
                    conn=conn,
                    company_id=user.company_id,
                    rate=planned_web_rate,
                    status=billing_types.CompanyRateStatus.new,
                    type_=billing_types.AccountType.client_rate,
                )

            if planned_archive_rate:
                await common.prepare_billing_account(
                    conn=conn,
                    company_id=user.company_id,
                    rate=planned_archive_rate,
                    status=billing_types.CompanyRateStatus.new,
                    type_=billing_types.AccountType.client_rate,
                )

            # Act
            result = await utils.should_remind_about_unpaid_bill(conn, bill)

        # Assert
        assert (expected_reason, expected_result) == result

    @pytest.mark.parametrize(
        """
        first_bill_web_rate,
        first_bill_archive_rate,
        second_bill_web_rate,
        second_bill_archive_rate,
        expected_reason,
        expected_result,
        """,
        (
            [
                # Shouldn't remind.
                # Company has bill for higher web rate
                billing_types.AccountRate.latest_start(),  # First bill web rate
                billing_types.AccountRate.archive_small,  # First bill archive rate
                billing_types.AccountRate.latest_pro(),  # Second bill web rate
                billing_types.AccountRate.archive_big,  # Second bill archive rate
                'Company has bill for higher web rate',
                False,
            ],
            [
                # Should remind.
                # Company has bill for similar web rate
                billing_types.AccountRate.latest_start(),  # First bill web rate
                billing_types.AccountRate.archive_small,  # First bill archive rate
                billing_types.AccountRate.latest_start(),  # Second bill web rate
                billing_types.AccountRate.archive_small,  # Second bill archive rate
                None,
                True,
            ],
            [
                # Should remind.
                # Company has bill for lower web rate
                billing_types.AccountRate.latest_pro(),  # First bill web rate
                billing_types.AccountRate.archive_big,  # First bill archive rate
                billing_types.AccountRate.latest_start(),  # Second bill web rate
                billing_types.AccountRate.archive_small,  # Second bill archive rate
                None,
                True,
            ],
            [
                # Should remind.
                # Company has no any other bills
                billing_types.AccountRate.latest_pro(),  # First bill web rate
                billing_types.AccountRate.archive_big,  # First bill archive rate
                None,  # Second bill web rate
                None,  # Second bill archive rate
                None,
                True,
            ],
        ),
    )
    async def test_company_has_bill_for_higher_web_rate(
        self,
        aiohttp_client,
        first_bill_web_rate,
        first_bill_archive_rate,
        second_bill_web_rate,
        second_bill_archive_rate,
        expected_reason,
        expected_result,
    ):
        """
        Given a company with/without similar/higher combined bill
        When calling should_remind_about_unpaid_bill
        Expected to return valid result
        """
        # Arrange
        app, client, user = await common.prepare_client(aiohttp_client)

        async with services.db.acquire() as conn:
            # Arrange archive bill
            first_bill = await bill_tests_common.prepare_unpaid_bill(
                conn=conn,
                user=user,
                web_rate=first_bill_web_rate,
                archive_rate=first_bill_archive_rate,
                services_type=billing_types.BillServicesType.web_and_archive,
            )

            if second_bill_web_rate:
                await bill_tests_common.prepare_unpaid_bill(
                    conn=conn,
                    user=user,
                    web_rate=second_bill_web_rate,
                    archive_rate=second_bill_archive_rate,
                    services_type=billing_types.BillServicesType.web_and_archive,
                )

            # Act
            result = await utils.should_remind_about_unpaid_bill(conn, first_bill)

        # Assert
        assert (expected_reason, expected_result) == result
