import logging

from aiohttp import web

from app.auth.db import (
    count_company_roles,
    select_company,
    update_company_by_id,
)
from app.auth.utils import is_fop
from app.lib.types import DataDict
from app.registration.utils import send_jobs_about_company_update
from app.services import services
from app.youcontrol.client import (
    YouControlClient,
)
from app.youcontrol.db import is_youcontrol_company_exists
from app.youcontrol.exceptions import (
    YouControlResponseException,
)
from app.youcontrol.utils import (
    fetch_youcontrol_company_employees,
    save_youcontrol_company_edr,
)
from worker.types import RETRY_EXCEPTIONS
from worker.utils import retry_config


@retry_config(
    max_attempts=20,
    exceptions=(YouControlResponseException, *RETRY_EXCEPTIONS),
    delay_minutes=5,
    raise_on_exception=False,
)
async def sync_company_info_from_youcontrol(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Get some info from you control.
    Such as activity_field and company size.

    May be run when new company is created or manually with admin route.
    """

    edrpou: str = data['edrpou']

    # This parameter for super-admin API only. In normal cases, we try to avoid making
    # several requests to YouControl API for the same company to reduce costs.
    force_fetch: bool = data.get('force_fetch', False)

    if not YouControlClient.is_enabled():
        logger.info('[Sync company info] YouControl is not enabled', extra={'edrpou': edrpou})
        return

    async with services.db.acquire() as conn:
        company = await select_company(conn=conn, edrpou=edrpou)
        if not company:
            logger.info('[Sync company info] not found company. Skipping', extra={'edrpou': edrpou})
            return

        count = await count_company_roles(conn, company_id=company.id)
        if not count:
            logger.info(
                '[Sync company info] company does not have active roles. Skipping',
                extra={'edrpou': edrpou, 'count': count},
            )
            return

        is_already_synced = await is_youcontrol_company_exists(conn, company_id=company.id)
        if is_already_synced and not force_fetch:
            logger.info(
                '[Sync company info] company info is already synced. Skipping',
                extra={'edrpou': edrpou},
            )
            return

        company_data: DataDict = {}

        # We can fetch and save information about the company from YouControl,
        # but we update a company only if company data is missing.
        response_edr = await save_youcontrol_company_edr(conn, company=company)
        if (
            company.activity_field is None
            and response_edr is not None
            and (activity_field := response_edr.activity_name)
        ):
            company_data['activity_field'] = activity_field

        # For FOPs, we don't need information about the number of employees because. Most likely,
        # it will be just 1 (the owner of the company), but FOPs make up approximately 70-80% of
        # all companies in our service.
        if not is_fop(edrpou):
            employees_number = await fetch_youcontrol_company_employees(company=company)
            if company.employees_number is None and employees_number is not None:
                company_data['employees_number'] = employees_number

        if company_data:
            await update_company_by_id(
                conn=conn,
                company_id=company.id,
                data=company_data,
            )
            await send_jobs_about_company_update(company_id=company.id)

    logger.info('[Sync company info] finished', extra={'edrpou': edrpou, **company_data})
