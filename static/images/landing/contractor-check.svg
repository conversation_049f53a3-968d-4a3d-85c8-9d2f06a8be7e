<svg width="180" height="158" viewBox="0 0 180 158" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0)">
<path d="M94.2904 158C46.9542 158 8.58072 153.374 8.58072 147.668C8.58072 141.962 46.9542 137.336 94.2904 137.336C141.626 137.336 180 141.962 180 147.668C180 153.374 141.626 158 94.2904 158Z" fill="#F4F6F8"/>
<path d="M171.029 79.8285H132.026C130.953 79.8285 130.076 80.7057 130.076 81.7779V127.589C130.076 128.661 130.953 129.539 132.026 129.539H171.029C172.102 129.539 172.979 128.661 172.979 127.589V81.7779C172.979 80.7057 172.102 79.8285 171.029 79.8285Z" fill="white" stroke="#333333" stroke-width="2"/>
<path d="M165.666 87.6262H126.663C125.59 87.6262 124.713 88.5034 124.713 89.5756V135.387C124.713 136.459 125.59 137.336 126.663 137.336H165.666C166.739 137.336 167.616 136.459 167.616 135.387V89.5756C167.616 88.5034 166.739 87.6262 165.666 87.6262Z" fill="white" stroke="#333333" stroke-width="2"/>
<path d="M84.247 0.974692C90.8776 0.68228 95.558 3.99628 97.9957 5.65329C108.527 12.9636 114.182 27.9741 114.085 41.3276C113.987 59.9445 103.164 72.8106 90.8776 87.1388C90.8776 87.1388 79.4691 99.9074 61.6251 109.655C60.26 110.434 58.8949 111.117 57.2373 111.701C46.6089 115.795 36.078 113.163 29.6425 110.824C31.1051 109.557 32.9577 107.608 34.6154 104.976C36.7606 101.467 37.7356 98.153 38.2232 95.9111C37.3456 97.4707 35.2004 100.59 31.3001 102.539C26.7172 104.878 22.4269 104.391 19.8917 104.196C15.1138 103.709 11.5059 101.857 9.26325 100.492C11.701 98.6403 15.3088 95.2289 18.039 90.1604C19.5016 87.4312 20.3792 84.7995 20.8667 82.6551C19.3066 83.4349 15.8938 84.7995 11.3109 84.5071C5.94798 84.1172 2.34018 81.4855 0.975067 80.4133C19.6966 70.9586 27.3998 60.1394 31.0076 52.0493C35.3954 42.1073 34.9079 33.335 43.8786 18.6169C49.3391 9.2597 53.727 6.14064 58.1148 4.87352C63.9653 3.21652 69.2308 4.77605 72.156 5.84823C73.9112 4.38617 78.2015 1.2671 84.247 0.974692Z" fill="#694925" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M37.6381 150.202C36.9556 147.766 39.7833 144.841 43.8787 143.574C47.974 142.405 51.7768 143.282 52.4594 145.621C53.1419 148.058 50.3142 150.982 46.2189 152.249C42.221 153.614 38.2232 152.639 37.6381 150.202Z" fill="#333333"/>
<path d="M89.5124 153.224C85.4171 152.542 82.2968 150.202 82.6869 147.863C83.0769 145.621 86.7822 144.257 90.9751 144.939C95.1679 145.621 98.2882 147.96 97.8006 150.3C97.3131 152.639 93.6078 153.906 89.5124 153.224Z" fill="#333333"/>
<path d="M86.6847 68.9118L88.9274 81.7779C115.742 85.8717 129.101 70.4713 128.516 66.9624C127.833 63.4534 108.137 74.6625 86.6847 68.9118Z" fill="#00C5FF" stroke="#333333" stroke-width="2"/>
<path d="M67.3781 69.0092C42.3185 81.0956 41.1484 102.052 43.9761 104.781C46.8039 107.413 55.8721 86.359 77.519 78.6588L67.3781 69.0092Z" fill="#00C5FF" stroke="#333333" stroke-width="2"/>
<path d="M102.969 106.341L52.1669 107.9C52.0694 84.702 63.3803 65.6952 77.4215 65.3053C91.4626 64.7205 102.871 83.24 102.969 106.341Z" fill="#00C5FF" stroke="#333333" stroke-width="2"/>
<path d="M102.969 105.561C103.066 126.712 87.3673 151.664 83.467 147.571C79.2741 143.184 80.9317 127.102 80.9317 127.102L74.2037 127.297C74.2037 127.297 68.6457 132.56 67.4756 133.632C63.3803 137.434 51.1918 148.838 47.779 146.596C46.0238 145.426 47.6815 140.748 49.3391 133.34C51.7768 122.228 52.1669 113.163 52.1669 107.023L102.969 105.561Z" fill="white" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M48.0715 103.027C46.4139 102.637 43.1961 102.832 41.7335 105.073C40.4659 107.023 39.9783 109.46 41.441 112.189C42.9036 115.015 45.7313 116.185 47.6815 115.015C49.6317 113.846 50.1192 110.629 48.6566 107.998C48.5591 107.803 48.4616 107.608 48.2665 107.413C49.2416 107.315 50.0217 106.73 50.3142 105.853C50.7043 104.781 49.7292 103.416 48.0715 103.027Z" fill="#F2CFBD" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linejoin="round"/>
<path d="M128.126 62.8686C126.956 62.2838 125.395 62.966 124.518 64.5256C123.64 65.9876 123.055 69.3991 124.908 71.2511C126.566 72.9081 128.906 73.9802 132.026 73.103C135.146 72.3232 137.291 69.8865 136.609 67.6446C135.926 65.4028 132.904 64.2332 129.783 65.1104C129.588 65.1104 129.296 65.2079 129.101 65.3053C129.198 64.1357 128.808 63.2585 128.126 62.8686Z" fill="#F2CFBD" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linejoin="round"/>
<path d="M38.6132 40.4503C39.9783 36.6489 44.2687 34.797 47.974 36.2591C51.6793 37.6237 53.4344 41.9124 51.8743 45.7137C50.5092 49.5151 46.2188 51.367 42.5135 49.905C38.9057 48.4429 37.0531 44.2517 38.6132 40.4503Z" fill="#F2CFBD" stroke="#333333" stroke-width="2" stroke-miterlimit="10"/>
<path d="M45.1463 43.8618C45.0488 28.4614 59.5775 15.5953 77.519 15.0105C95.4605 14.4257 109.989 26.4145 110.184 41.7174C110.282 57.1178 95.753 69.9839 77.8115 70.5688C59.675 71.1536 45.1463 59.2622 45.1463 43.8618Z" fill="#F2CFBD" stroke="#333333" stroke-width="2" stroke-miterlimit="10"/>
<path d="M75.3738 51.4645C75.0812 51.367 74.7887 51.6594 74.8862 51.9518C75.1788 54.1937 77.0314 55.7532 79.1766 55.6557C81.3218 55.5582 83.1744 53.8038 83.4669 51.7569C83.4669 51.4645 83.2719 51.1721 82.9794 51.2695C82.0043 51.4645 80.7367 51.6594 79.3716 51.7569C77.8115 51.8544 76.4464 51.6594 75.3738 51.4645Z" fill="#333333"/>
<path d="M61.4301 49.905C61.4301 48.1505 63.3803 46.6885 65.818 46.4935C68.2557 46.4935 70.2058 47.7606 70.2058 49.6126C70.2058 51.367 68.2557 52.8291 65.818 53.024C63.3803 53.1215 61.4301 51.6595 61.4301 49.905ZM92.9252 52.2443C90.4875 52.2443 88.5374 50.8797 88.5374 49.1252C88.5374 47.3707 90.4875 45.9087 92.9252 45.7137C95.3629 45.7137 97.3131 46.9809 97.3131 48.8328C97.3131 50.5873 95.3629 52.1468 92.9252 52.2443Z" fill="#F99E6B"/>
<path d="M90.0975 6.62798C104.334 5.26339 113.012 20.1764 113.499 20.8587C119.447 30.8982 118.472 43.7643 110.964 53.7063C109.599 40.4503 104.626 34.5046 100.531 31.483C96.338 28.4614 92.0477 28.5589 85.2221 23.8803C80.5417 20.7612 77.4214 17.1548 75.3738 14.6206C76.9339 12.7686 82.1018 7.40775 90.0975 6.62798Z" fill="#B58146" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M62.2102 9.84453C50.1192 9.64959 42.5135 19.4941 40.4659 22.4182C36.273 28.4614 35.493 34.6021 35.0054 38.5984C34.2254 44.8365 35.0054 50.0025 35.7855 53.3165C40.7584 43.7643 46.8039 38.5984 51.4843 35.7717C58.3098 31.5805 64.7454 30.9957 70.2058 24.5626C73.3261 20.9562 74.7887 17.0574 75.5688 14.3282C73.2286 12.8661 68.4507 9.942 62.2102 9.84453Z" fill="#B58146" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M67.1831 31.6779C66.5981 31.7754 66.1105 31.9704 65.428 32.3602C64.5504 32.7501 63.7703 33.3349 63.2828 33.7248" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M87.8548 30.2159C88.5374 30.3134 89.1224 30.4109 89.9025 30.6058C90.9751 30.8982 91.8527 31.2881 92.5352 31.5805" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M92.1452 43.7643C90.8527 43.7643 89.805 42.717 89.805 41.425C89.805 40.1331 90.8527 39.0858 92.1452 39.0858C93.4376 39.0858 94.4854 40.1331 94.4854 41.425C94.4854 42.717 93.4376 43.7643 92.1452 43.7643Z" fill="#333333"/>
<path d="M66.5005 43.7643C65.2081 43.7643 64.1603 42.717 64.1603 41.425C64.1603 40.1331 65.2081 39.0858 66.5005 39.0858C67.793 39.0858 68.8407 40.1331 68.8407 41.425C68.8407 42.717 67.793 43.7643 66.5005 43.7643Z" fill="#333333"/>
<path d="M160.206 95.5213H121.203C120.13 95.5213 119.252 96.3985 119.252 97.4707V143.282C119.252 144.354 120.13 145.231 121.203 145.231H160.206C161.278 145.231 162.156 144.354 162.156 143.282V97.4707C162.156 96.3985 161.278 95.5213 160.206 95.5213Z" fill="white" stroke="#333333" stroke-width="2"/>
<path d="M154 121L127 121" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M154 133L135 133" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M154 127L132 127" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M132.778 107.095C132.778 105.73 131.608 104.756 130.34 104.756C129.073 104.756 128 105.925 128 107.29M153.77 107.095C153.77 105.73 152.698 104.756 151.333 104.756C150.065 104.756 148.992 105.925 148.992 107.29" stroke="#333333" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M137.39 111.195C137.195 111.195 137 111.39 137 111.585C137.195 113.242 138.658 114.509 140.315 114.411C141.973 114.314 143.338 113.047 143.631 111.39C143.631 111.195 143.436 111 143.241 111C142.46 111.195 141.583 111.292 140.51 111.39C139.243 111.39 138.17 111.292 137.39 111.195Z" fill="#333333"/>
<path d="M127 112.481C127 111.117 128.463 109.947 130.315 109.947C132.168 109.947 133.631 110.922 133.631 112.384C133.631 113.748 132.168 114.918 130.315 114.918C128.56 115.015 127 113.846 127 112.481ZM152.113 115.015C150.26 115.015 148.797 113.943 148.797 112.579C148.797 111.214 150.26 110.044 152.113 110.044C153.965 110.044 155.428 111.019 155.428 112.481C155.428 113.748 153.868 114.918 152.113 115.015Z" fill="#E8A076"/>
<path d="M122.178 152.737C114.154 152.737 107.649 146.234 107.649 138.213C107.649 130.193 114.154 123.69 122.178 123.69C130.202 123.69 136.706 130.193 136.706 138.213C136.706 146.234 130.202 152.737 122.178 152.737Z" fill="#FFF200" stroke="#333333" stroke-width="2"/>
<path d="M115 138.583C116.667 140.722 118.431 142.861 120.098 145C120.882 143.153 121.961 141.208 123.333 139.167C125.588 135.764 127.941 133.042 130 131" stroke="#333333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0">
<rect width="180" height="158" fill="white" transform="matrix(-1 0 0 1 180 0)"/>
</clipPath>
</defs>
</svg>
