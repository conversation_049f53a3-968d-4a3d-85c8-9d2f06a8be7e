import asyncio
import logging
import signal
from functools import wraps

from app.app import create_app, setup_timezone
from app.lib.brokers import KafkaClient
from app.services import setup_services

from .types import AppRequiredDecorator, AppRequiredFunc, MainFunc
from .utils import log_extra


class GracefulExit(SystemExit):
    code = 0


def raise_graceful_exit() -> None:
    raise GracefulExit()


def app_required(logger: logging.Logger) -> AppRequiredDecorator:
    """Simplify instantiating app for cron and script main functions."""

    def decorator(func: AppRequiredFunc) -> MainFunc:
        @wraps(func)
        async def wrapper() -> None:
            setup_timezone()

            loop = asyncio.get_event_loop()
            loop.add_signal_handler(signal.SIGINT, raise_graceful_exit)
            loop.add_signal_handler(signal.SIGTERM, raise_graceful_exit)

            app = create_app()
            try:
                await app.startup()
                setup_services(app)
                await func(app)
            except (<PERSON><PERSON><PERSON><PERSON><PERSON>, KeyboardInterrupt):
                logger.info('Gracefully shut down cron or worker')
            except Exception:
                logger.exception('Unhandled exception on running cron or worker', extra=log_extra())
                raise
            finally:
                # stop kafka consumer if it was started
                kafka: KafkaClient | None = app.get('kafka')
                if kafka is not None:
                    await kafka.consumer.stop()
                await app.shutdown()
                await app.cleanup()
                logger.info('Worker or Cron is about to exit')

        return wrapper

    return decorator
