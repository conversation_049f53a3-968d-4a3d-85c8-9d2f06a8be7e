#!/usr/bin/env bash

. ./spinner.sh

COUNT=`
  PGPASSWORD=$POSTGRES_PASSWORD psql -h postgres -p 5432 -U $POSTGRES_USER \
  -d $POSTGRES_DB -X -A -t \
  -c "select count(*) from information_schema.tables where table_schema = 'public';"\
`
echo
if [ "$COUNT" -eq 0 ]; then
  start_spinner "Restore dump to local db"
  # Restore the dump to the local database from .sql file
  PGPASSWORD=$POSTGRES_PASSWORD psql -h postgres -p 5432 -U $POSTGRES_USER \
    -d $POSTGRES_DB -f trunk.sql
  stop_spinner $?

  echo "Update documents"
  PGPASSWORD=$POSTGRES_PASSWORD psql -h postgres -p 5432 -U $POSTGRES_USER \
    -d $POSTGRES_DB -c 'UPDATE documents SET is_protected = true;'
  echo "Clean up"
else
  echo 'DB already exists. Clear postgres containter and rerun postgres-copy-dev'
  echo '$ docker-compose rm -fs postgres; docker-compose run --rm postgres-copy-dev'
fi

echo
