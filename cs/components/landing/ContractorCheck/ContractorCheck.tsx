import React, { ChangeEvent, ReactNode, useState } from 'react';
import MediaQuery from 'react-responsive';

import cn from 'classnames';
import { t } from 'ttag';

import { Nullable } from '../../../types/general';

import Button from '../../ui/button/button';
import Hint from '../../ui/hint/hint';
import Icon from '../../ui/icon/icon';
import Input from '../../ui/input/input';
import PseudoLink from '../../ui/pseudolink/pseudolink';

import { MEDIA_WIDTH } from '../../../lib/constants';
import { useIsMounted } from '../../../lib/reactHelpers/hooks';
import eventTracking from '../../../services/analytics/eventTracking';
import io from '../../../services/io';
import ContractorsUploadPopup from './ContractorsUploadPopup';
import LimitErrorPopup from './LimitErrorPopup';

import SvgSearch from './images/search.svg';
import SvgSmileError from './images/smile-error.svg';
import SvgSmileNo from './images/smile-no.svg';
import SvgSmileYes from './images/smile-yes.svg';

import css from './ContractorCheck.css';

type Props = {
    size?: 'big';
    onSearchCb?: () => void;
};

type CheckResult = {
    edrpou: string;
    name: string;
};

type Status = 'success' | 'not_registered' | 'error';

const STATUS_TO_SMILE_MAP = {
    success: SvgSmileYes,
    not_registered: SvgSmileNo,
    error: SvgSmileError,
};

const trackEvent = (action: string, label?: string) => {
    eventTracking.sendEvent('landing-check-contractor', action, label);
};

const getMessagePropsFromPercentage = (
    percentage: number,
): [Status, string, string?] => {
    if (percentage === 0) {
        return [
            'not_registered',
            t`0% ваших контрагентів використовують "Вчасно".`,
            t`Здається, ви за природою інноватор! Зареєструйтеся та запросіть усіх до нас!`,
        ];
    } else if (percentage < 10) {
        return [
            'success',
            t`${percentage}% ваших контрагентів використовують "Вчасно".`,
            t`Гарний початок!  Зареєструйтеся та запросіть решту!`,
        ];
    } else {
        return [
            'success',
            t`Ура! ${percentage}% ваших контрагентів вже користуються “Вчасно”`,
        ];
    }
};

const ContractorCheck: React.FC<React.PropsWithChildren<Props>> = ({
    children,
    size,
    onSearchCb,
}) => {
    const [edrpou, setEdrpou] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [status, setStatus] = useState<Nullable<Status>>(null);
    const [message, setMessage] = useState<string | ReactNode>(null);
    const [resultsList, setResultsList] = useState<Array<CheckResult>>([]);
    const [registrationBannerText, setRegistrationBannerText] = useState('');
    const [isUploadPopupOpen, setUploadPopupOpen] = useState(false);
    const [isLimitErrorPopupOpen, setLimitErrorPopupOpen] = useState(false);

    const isMounted = useIsMounted();

    const clearResults = () => {
        if (status) {
            setStatus(null);
            setMessage(null);
            setResultsList([]);
            setRegistrationBannerText('');
        }
    };

    const onInputChange = (evt: ChangeEvent<HTMLInputElement>) => {
        clearResults();
        setEdrpou(evt.currentTarget.value);
    };

    const showMessage = (type: Status, title: string, text?: string) => {
        setStatus(type);
        setMessage(
            <div>
                <div className={css.messageTitle}>{title}</div>
                <div className={css.messageText}>{text ?? null}</div>
            </div>,
        );

        if (type === 'error') {
            trackEvent('error', title);
        }
    };

    const showUploadPopup = () => {
        clearResults();
        setUploadPopupOpen(true);
        trackEvent('show-popup-check-contractor-list');
    };

    const onCheck = async () => {
        clearResults();

        if (edrpou.trim() === '') {
            showMessage(
                'error',
                t`Введіть ЄДРПОУ або ІПН компанії в поле пошуку`,
            );
            return;
        }

        if (onSearchCb) {
            onSearchCb();
        }

        setIsLoading(true);
        try {
            eventTracking.sendToGTM({
                category: 'vchasno_main',
                action: 'perevirka',
            });
            const { is_registered, name } = await io.post(
                '/internal-api/check/company',
                { edrpou },
                true,
            );

            if (!isMounted()) {
                return;
            }

            if (is_registered) {
                showMessage('success', t`Ура! Контрагент вже у «Вчасно»`);
                setResultsList([{ edrpou, name }]);
            } else {
                showMessage(
                    'not_registered',
                    t`Контрагент ще не користується «Вчасно»`,
                );
            }
            trackEvent(
                'check-1-contractor',
                is_registered ? 'found' : 'not found',
            );
        } catch (err) {
            showMessage('error', err.message);
        }
        setIsLoading(false);
    };

    const onUploadContactsFile = async (file: File) => {
        setIsLoading(true);
        try {
            eventTracking.sendToGTM({
                category: 'vchasno_main',
                action: 'perevirka',
            });
            const checkResult = await io.postFile(
                '/internal-api/check/company/upload',
                file,
            );

            if (!isMounted()) {
                return;
            }

            const percentage = parseInt(checkResult.percentage, 10);
            showMessage(...getMessagePropsFromPercentage(percentage));

            if (checkResult.companies.length > 0) {
                setResultsList(checkResult.companies);
            }

            if (percentage >= 10) {
                setRegistrationBannerText(
                    checkResult.rows_total < 10
                        ? t`Зареєструйтеся, щоб почати обмінюватися документами!`
                        : t`Щоб переглянути всі зареєстровані компанії у Вчасно, будь ласка  зареєструйтеся`,
                );
            }

            trackEvent('import-contractor-list', `${percentage}%`);
        } catch (err) {
            // err.code: "too_many_request"
            if (err.status === 429) {
                setLimitErrorPopupOpen(true);
            }
            showMessage('error', err.message);
        }
        setIsLoading(false);
    };

    const inputProps = {
        size,
        type: 'text',
        placeholder: t`Введіть ЄДРПОУ або ІПН компанії`,
        value: edrpou,
        onChange: onInputChange,
    };

    const buttonProps = {
        size,
        width: 'full',
        isLoading,
        onClick: onCheck,
    } as const;

    const content = children ?? (
        <div className={css.text}>
            {t`Щоб перевірити одразу всіх контрагентів,`}{' '}
            <PseudoLink onClick={showUploadPopup}>
                {t`завантажте перелік контактів`}
            </PseudoLink>
            <Hint black position="right">
                {t`Ви можете завантажити таблицю у форматі`}
                <br />
                {t`CSV або XLSX з ЄДРПОУ/ІПН всіх контрагентів`}
            </Hint>
        </div>
    );

    const registrationButton = (
        <a href="/auth/registration" target="_blank" rel="noopener noreferrer">
            <Button width="full" theme="blue">
                {t`Реєстрація`}
            </Button>
        </a>
    );

    const messageBlock = status && (
        <div
            className={cn(css.message, {
                [css.messageSuccess]: status === 'success',
            })}
        >
            <div className={css.smileIcon}>
                <Icon glyph={STATUS_TO_SMILE_MAP[status]} />
            </div>
            {message}
            <div className={css.messageButton}>
                {resultsList.length > 1 ? (
                    <PseudoLink onClick={() => setResultsList([])}>
                        {t`Закрити таблицю`}
                    </PseudoLink>
                ) : (
                    status !== 'error' &&
                    !registrationBannerText &&
                    registrationButton
                )}
            </div>
        </div>
    );

    const resultsTable = resultsList.length > 0 && (
        <div className={css.resultsList}>
            <table className={css.resultsTable}>
                <tbody>
                    <tr className={css.resultsTableHeader}>
                        <th className={css.resultsName}>
                            <b>{t`Назва компанії`}</b>
                        </th>
                        <th className={css.resultsEdrpou}>
                            <b>{t`ЄДРПОУ/ІПН`}</b>
                        </th>
                    </tr>
                    {resultsList.map((res) => (
                        <tr key={res.edrpou} className={css.resultsTableRow}>
                            <td className={css.resultsName}>{res.name}</td>
                            <td className={css.resultsEdrpou}>{res.edrpou}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );

    const registrationBanner = registrationBannerText && (
        <div className={css.message}>
            <div className={css.messageText}>{registrationBannerText}</div>
            <div className={css.messageButton}>{registrationButton}</div>
        </div>
    );

    return (
        <>
            <div className={css.search}>
                <div className={css.inputWrapper}>
                    <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                        <Input leadingIcon={SvgSearch} {...inputProps} />
                    </MediaQuery>
                    <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                        <Input position="left" {...inputProps} />
                    </MediaQuery>
                </div>
                <div className={css.buttonWrapper}>
                    <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                        <Button theme="cta" {...buttonProps}>
                            {t`Перевірити`}
                        </Button>
                    </MediaQuery>
                    <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                        <Button theme="blue" position="right" {...buttonProps}>
                            <div className={css.icon}>
                                <Icon glyph={SvgSearch} />
                            </div>
                        </Button>
                    </MediaQuery>
                </div>
            </div>
            {content}
            {messageBlock}
            {resultsTable}
            {registrationBanner}
            <ContractorsUploadPopup
                isOpen={isUploadPopupOpen}
                isUploadProcess={isLoading}
                onClose={() => setUploadPopupOpen(false)}
                onUploadContactsFile={onUploadContactsFile}
            />
            <LimitErrorPopup
                isOpen={isLimitErrorPopupOpen}
                onClose={() => setLimitErrorPopupOpen(false)}
            />
        </>
    );
};

export default ContractorCheck;
