import React from 'react';
import MediaQuery from 'react-responsive';

import { t } from 'ttag';

import Button from '../../ui/button/button';
import PopupMobile from '../../ui/popup/mobile/popup';
import Popup from '../../ui/popup/popup';

import { MEDIA_WIDTH } from '../../../lib/constants';

import css from './LimitErrorPopup.css';

type Props = {
    isOpen: boolean;
    onClose: () => void;
};

const LimitErrorPopup: React.FC<React.PropsWithChildren<Props>> = ({
    isOpen,
    onClose,
}) => {
    if (!isOpen) {
        return null;
    }

    const content = (
        <div className={css.root}>
            <img
                src={`${config.STATIC_HOST}/images/landing/contractor-check.svg`}
                className={css.image}
                alt=""
            />
            <div>
                <div className={css.title}>
                    {t`Ви успішно перевірили 3 файли своїх контрагентів!`}
                </div>
                <div className={css.text}>
                    {t`Нажаль ви не можете перевірити більше 3 файлів за день, будь ласка спробуйте завтра`}
                </div>
                <Button width="full" theme="blue" onClick={onClose}>
                    {t`Повернутися на головну сторінку`}
                </Button>
            </div>
        </div>
    );

    return (
        <>
            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <Popup active centered onClose={onClose}>
                    {content}
                </Popup>
            </MediaQuery>
            <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                <PopupMobile active onClose={onClose}>
                    {content}
                </PopupMobile>
            </MediaQuery>
        </>
    );
};

export default LimitErrorPopup;
