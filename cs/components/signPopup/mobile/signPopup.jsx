import React from 'react';

import cn from 'classnames';
import PropTypes from 'prop-types';
import { msgid, ngettext, t } from 'ttag';

import { SignPopupStatus } from '../../../services/enums';

import {
    getRemainSideSignaturesCount,
    isExtraSignature,
    shouldOnlySign,
} from '../../../services/documents/utils';

import Button from '../../ui/button/button';
import Input from '../../ui/input/input';
import Message from '../../ui/message/message';
import PopupMobile from '../../ui/popup/mobile/popup';
import ProgressBar from '../../ui/progressBar/progressBar';
import Steps from '../../ui/steps/steps';

import IitSignWidget from '../../IitSignWidget/IitSignWidget';
import KepSignForm from '../../KepSigner/KepSignForm';
import RequiredFieldsResolver from '../../RequiredFieldsResolver/RequiredFieldsResolver';
import SignDocumentDiia from '../../SignDocumentDiia/SignDocumentDiia';
import DelnotWarningPopup from '../../delnotWarningPopup/delnotWarningPopup';
import DocumentActionInfoMessage from '../../documentActionInfoMessage/documentActionInfoMessage';
import HelperMobile from '../../helper/mobile/helper';
import PrivateKeyUploader from '../../privateKeyUploader/privateKeyUploader';
import RecipientFormContainer from '../../recipientForm/recipientFormContainer';
import SignFormMobile from '../../signForm/mobile/signForm';
import OverdraftError from '../OverdraftError/OverdraftError';

// styles
import css from './signPopup.css';

class SignPopup extends React.Component {
    static propTypes = {
        isActive: PropTypes.bool.isRequired,
        isUploadedPK: PropTypes.bool.isRequired,
        isUploadedStamp: PropTypes.bool.isRequired,
        isPKEditDisabled: PropTypes.bool.isRequired,
        isStampEditDisabled: PropTypes.bool.isRequired,
        isPKChecked: PropTypes.bool.isRequired,
        isStampChecked: PropTypes.bool.isRequired,
        isCheckPKProcess: PropTypes.bool.isRequired,
        isCheckStampProcess: PropTypes.bool.isRequired,
        isSignAndSubmitProcess: PropTypes.bool.isRequired,
        isRememberedKeys: PropTypes.bool.isRequired,
        isMultiSign: PropTypes.bool,
        isLoadPKCertsFromFile: PropTypes.bool.isRequired,
        isLoadStampCertsFromFile: PropTypes.bool.isRequired,
        isPKCAServerEditDisabled: PropTypes.bool.isRequired,
        isStampCAServerEditDisabled: PropTypes.bool.isRequired,
        isRecipientEmailHidden: PropTypes.bool.isRequired,
        isSignerServiceLoaded: PropTypes.bool.isRequired,

        pkCertificateFiles: PropTypes.object,
        stampCertificateFiles: PropTypes.object,
        pkInfo: PropTypes.object,
        stampInfo: PropTypes.object,
        certificatePKInfo: PropTypes.object,
        certificateStampInfo: PropTypes.object,

        docs: PropTypes.array,
        currentRoleId: PropTypes.string.isRequired,
        currentCompanyEdrpou: PropTypes.string.isRequired,
        caServers: PropTypes.array,
        multiSignResult: PropTypes.array,

        pkCAServerIdx: PropTypes.number,
        stampCAServerIdx: PropTypes.number,
        signedDocCount: PropTypes.number,
        totalDocsCount: PropTypes.number,

        pkFileName: PropTypes.string,
        pkPassword: PropTypes.string,
        stampFileName: PropTypes.string,
        stampPassword: PropTypes.string,
        popupStatus: PropTypes.string,
        edrpouRecipient: PropTypes.string,
        emailRecipient: PropTypes.string,
        edrpouOwner: PropTypes.string,
        emailOwner: PropTypes.string,
        errorMessage: PropTypes.string,
        pkErrorMessage: PropTypes.string,
        stampErrorMessage: PropTypes.string,

        toApp: PropTypes.func.isRequired,
        hidePopup: PropTypes.func.isRequired,
        initSignService: PropTypes.func.isRequired,
        submitPK: PropTypes.func.isRequired,
        setPKPassword: PropTypes.func.isRequired,
        submitPKCertificates: PropTypes.func.isRequired,
        submitStamp: PropTypes.func.isRequired,
        setStampPassword: PropTypes.func.isRequired,
        submitStampCertificates: PropTypes.func.isRequired,
        changeCAServer: PropTypes.func.isRequired,
        readKey: PropTypes.func.isRequired,
        sign: PropTypes.func.isRequired,
        submitRecipient: PropTypes.func.isRequired,
        signAndSubmit: PropTypes.func.isRequired,
        retrySignAndSubmit: PropTypes.func.isRequired,
        multiSign: PropTypes.func.isRequired,
        toggleRememberKeys: PropTypes.func.isRequired,
        onDelnotWarningAgree: PropTypes.func.isRequired,
        onSubmitKeysFromStorage: PropTypes.func.isRequired,
        isOnlyExtraSignatureDocuments: PropTypes.bool,

        //confirm close
        isNeedConfirmClosed: PropTypes.bool,
        isShowConfirmClose: PropTypes.bool,

        vchasnoTheme: PropTypes.string,
    };

    componentDidMount() {
        this.props.initSignService();
    }

    getChildren = () => {
        const doc = this.props.docs[0];
        const email = this.props.emailRecipient || this.props.emailOwner;
        const edrpou = this.props.edrpouRecipient || this.props.edrpouOwner;

        let signedOnlyDocSignaturesCount = {};

        switch (this.props.popupStatus) {
            case SignPopupStatus.CHOOSE_RECIPIENT:
                return (
                    <div>
                        <Steps step={1} total={3} />
                        <div
                            className={css.title}
                        >{t`Призначте контрагента`}</div>
                        <RecipientFormContainer
                            fullWidthButton
                            buttonText={t`Призначити контрагента`}
                            buttonTheme="blue"
                            formTrackingName="doc_sign_step_1"
                            docId={doc.id}
                            defaultEmail={this.props.emailRecipient}
                            defaultEdrpou={this.props.edrpouRecipient}
                            defaultEmailHidden={
                                this.props.isRecipientEmailHidden
                            }
                            onSubmit={this.props.submitRecipient}
                        />
                    </div>
                );
            case SignPopupStatus.UPLOAD_PRIVATE_KEY:
                return (
                    <div>
                        <Steps step={2} total={3} />
                        <div className={css.title}>{t`Завантажте ключ`}</div>
                        <PrivateKeyUploader
                            title=""
                            isMultiSign={this.props.isMultiSign}
                            isSignerServiceLoaded={
                                this.props.isSignerServiceLoaded
                            }
                            formTrackingName="doc_sign_step_2"
                            errorMessage={this.props.errorMessage}
                            onClick={() => this.pkFileInput.click()}
                            onIitWidgetClick={this.props.loadIitSignWidget}
                            onSubmitKeysFromStorage={
                                this.props.onSubmitKeysFromStorage
                            }
                        />
                        <HelperMobile />
                    </div>
                );
            case SignPopupStatus.CHOOSE_DOCUMENT_FIELDS: {
                return <RequiredFieldsResolver />;
            }
            case SignPopupStatus.CHECK_PRIVATE_KEY:
                return (
                    <div>
                        <Steps step={3} total={3} />
                        <div className={css.title}>
                            {t`Зчитайте інформацію з ключів`}
                        </div>
                        <SignFormMobile
                            docId={doc.id}
                            isUploadedPK={this.props.isUploadedPK}
                            isUploadedStamp={this.props.isUploadedStamp}
                            isPKEditDisabled={this.props.isPKEditDisabled}
                            isStampEditDisabled={this.props.isStampEditDisabled}
                            isPKChecked={this.props.isPKChecked}
                            isStampChecked={this.props.isStampChecked}
                            isCheckPKProcess={this.props.isCheckPKProcess}
                            isCheckStampProcess={this.props.isCheckStampProcess}
                            isSignAndSubmitProcess={
                                this.props.isSignAndSubmitProcess
                            }
                            isRememberedKeys={this.props.isRememberedKeys}
                            isMultiSign={this.props.isMultiSign}
                            isLoadPKCertsFromFile={
                                this.props.isLoadPKCertsFromFile
                            }
                            isLoadStampCertsFromFile={
                                this.props.isLoadStampCertsFromFile
                            }
                            isPKCAServerEditDisabled={
                                this.props.isPKCAServerEditDisabled
                            }
                            isStampCAServerEditDisabled={
                                this.props.isStampCAServerEditDisabled
                            }
                            isSignOnly={shouldOnlySign(
                                doc,
                                this.props.currentRoleId,
                                this.props.isMultiSign,
                            )}
                            isInternal={this.props.docs.every(
                                (docItem) => docItem.isInternal,
                            )}
                            pkCertificateFiles={this.props.pkCertificateFiles}
                            stampCertificateFiles={
                                this.props.stampCertificateFiles
                            }
                            pkInfo={this.props.pkInfo}
                            stampInfo={this.props.stampInfo}
                            certificatePKInfo={this.props.certificatePKInfo}
                            certificateStampInfo={
                                this.props.certificateStampInfo
                            }
                            formTrackingName="doc_sign_step_3"
                            pkFileName={this.props.pkFileName}
                            pkPassword={this.props.pkPassword}
                            stampFileName={this.props.stampFileName}
                            stampPassword={this.props.stampPassword}
                            caServers={this.props.caServers}
                            pkCAServerIdx={this.props.pkCAServerIdx}
                            stampCAServerIdx={this.props.stampCAServerIdx}
                            pkErrorMessage={this.props.pkErrorMessage}
                            stampErrorMessage={this.props.stampErrorMessage}
                            errorMessage={this.props.errorMessage}
                            onChangeCAServer={this.handleChangeCAServer}
                            onChangePKCertificates={() =>
                                this.pkCertificatesInput.click()
                            }
                            onChangeStampCertificates={() =>
                                this.stampCertificatesInput.click()
                            }
                            onReadKey={this.props.readKey}
                            onSign={this.props.sign}
                            onChangePK={() => this.pkFileInput.click()}
                            onSetPKPassword={this.props.setPKPassword}
                            onChangeStamp={() => this.stampFileInput.click()}
                            onSetStampPassword={this.props.setStampPassword}
                            onSignAndSubmit={this.props.signAndSubmit}
                            onMultiSign={this.props.multiSign}
                            onToggleRememberKeys={this.props.toggleRememberKeys}
                            onClose={this.handleClose}
                            isOnlyExtraSignatureDocuments={
                                this.props.isOnlyExtraSignatureDocuments
                            }
                        />
                    </div>
                );
            case SignPopupStatus.SIGN_IN_PROGRESS:
                return (
                    <div className={css.signProgress}>
                        <ProgressBar
                            showCounter
                            title={t`Підписано та надіслано документів`}
                            current={this.props.signedDocCount}
                            total={this.props.totalDocsCount}
                        />
                    </div>
                );
            case SignPopupStatus.SIGNED_SUCCESSFUL:
                const isExtraSign = isExtraSignature(
                    doc,
                    this.props.currentRoleId,
                    this.props.currentCompanyEdrpou,
                );

                if (!this.props.isMultiSign && !isExtraSign) {
                    const remainSignatures = getRemainSideSignaturesCount(doc);
                    if (remainSignatures > 1) {
                        signedOnlyDocSignaturesCount = {
                            total: doc.signatures.length + 1,
                            remain: remainSignatures - 1,
                        };
                    }
                }
                return (
                    <DocumentActionInfoMessage
                        fullWidthButton
                        isMultiAction={this.props.isMultiSign}
                        docs={this.props.docs}
                        currentRoleId={this.props.currentRoleId}
                        multiActionResult={this.props.multiSignResult}
                        signedOnlyDocSignaturesCount={
                            signedOnlyDocSignaturesCount
                        }
                        totalDocsCount={this.props.totalDocsCount}
                        buttonText={ngettext(
                            msgid`Повернутись до документа`,
                            `Повернутись до документів`,
                            `Повернутись до документів`,
                            this.props.docs.length,
                        )}
                        fileName={doc.title}
                        email={email}
                        edrpou={edrpou}
                        onClose={this.handleClose}
                    />
                );
            case SignPopupStatus.SHARED_DOCUMENT_VIEW_OVERDRAFT_ERROR:
                return <OverdraftError />;
            case SignPopupStatus.SIGN_ERROR:
                return (
                    <div>
                        <Message>{t`Йой! Щось пішло не так.`}</Message>
                        <Message type="error" sizeSmall>
                            <div>
                                {t`Документи не вдалося підписати та надіслати`}
                                {this.props.errorMessage
                                    ? ` - ${this.props.errorMessage}`
                                    : ''}
                                .
                            </div>
                            <div>{t`Повторіть спробу.`}</div>
                        </Message>
                        <div className={css.buttonBlock}>
                            <Button
                                typeContour
                                theme="blue"
                                onClick={() => {
                                    this.handleClose();
                                    this.props.toApp();
                                }}
                                width="full"
                            >
                                {t`Повернутись до документів`}
                            </Button>
                        </div>
                    </div>
                );
            case SignPopupStatus.DIIA_SIGN:
                return <SignDocumentDiia />;
            case SignPopupStatus.KEP_SIGN:
                return <KepSignForm />;
            case SignPopupStatus.SIGN_IIT_WIDGET:
                return <IitSignWidget />;
            default:
                return null;
        }
    };

    handleChangeCAServer = (idx, isPK) => {
        this.props.changeCAServer(idx, isPK);
        if (isPK) {
            this.pkCertificatesInput.value = null;
        } else {
            this.stampCertificatesInput.value = null;
        }
    };

    handlePKFileChange = (evt) => {
        const file = evt.currentTarget.files[0];
        if (file) {
            this.props.submitPK(file);
        }
        this.pkCertificatesInput.value = null;
    };

    handleStampFileChange = async (evt) => {
        const file = evt.currentTarget.files[0];
        if (file) {
            if (this.props.isStampChecked && !this.props.isUploadedPK) {
                await this.props.onResetSignSteps();
                await this.props.submitPK(file);
            } else {
                await this.props.submitStamp(file);
            }
        }
        this.stampCertificatesInput.value = null;
    };

    handlePKCertificatesChange = (evt) => {
        const files = evt.currentTarget.files;
        if (files.length > 0) {
            this.props.submitPKCertificates(files);
        }
    };

    handleStampCertificatesChange = (evt) => {
        const files = evt.currentTarget.files;
        if (files.length > 0) {
            this.props.submitStampCertificates(files);
        }
    };

    handleClose = () => {
        if (
            this.props.flags['ENABLE_SIGNATURE_CANCELLATION_CONFIRMATION'] &&
            this.props.isNeedConfirmClosed
        ) {
            this.props.setShowConfirmClose(true);
            return;
        }

        this.props.hidePopup();
    };

    render() {
        if (
            this.props.popupStatus === SignPopupStatus.INIT ||
            this.props.popupStatus === SignPopupStatus.KEP_NEW_SIGN
        ) {
            return null;
        }

        const hasNotInternalDocument = this.props.docs.some(
            (doc) => !doc.isInternal,
        );
        let headerData;
        if (this.props.isMultiSign) {
            headerData = hasNotInternalDocument
                ? {
                      title: t`Підписання та надіслання документів`,
                      subtitle: '',
                  }
                : { title: t`Підписання документів`, subtitle: '' };
        } else {
            headerData = {
                title: t`Підписання документу`,
                subtitle: this.props.docs[0].title,
            };
        }

        if (this.props.popupStatus === SignPopupStatus.DELNOT_WARNING) {
            return (
                <DelnotWarningPopup
                    onAction={this.props.onDelnotWarningAgree}
                    onClose={this.props.hidePopup}
                />
            );
        }

        return (
            <PopupMobile
                active={this.props.isActive}
                title={headerData.title}
                subtitle={headerData.subtitle}
                isCloseByButtonOnly
                onClose={this.handleClose}
                greyColor
                hiddenCloseButton={this.props.isShowConfirmClose}
                className={cn({
                    [css.popupWhiteBackground]:
                        this.props.popupStatus ===
                            SignPopupStatus.SIGN_IIT_WIDGET ||
                        this.props.popupStatus === SignPopupStatus.DIIA_SIGN,
                })}
            >
                <Input
                    hidden
                    type="file"
                    refFn={(el) => {
                        this.pkFileInput = el;
                    }}
                    onChange={this.handlePKFileChange}
                />
                <Input
                    hidden
                    type="file"
                    refFn={(el) => {
                        this.stampFileInput = el;
                    }}
                    onChange={this.handleStampFileChange}
                />
                <Input
                    hidden
                    multiple
                    type="file"
                    refFn={(el) => {
                        this.pkCertificatesInput = el;
                    }}
                    onChange={this.handlePKCertificatesChange}
                />
                <Input
                    hidden
                    multiple
                    type="file"
                    refFn={(el) => {
                        this.stampCertificatesInput = el;
                    }}
                    onChange={this.handleStampCertificatesChange}
                />
                {this.getChildren()}
            </PopupMobile>
        );
    }
}

export default SignPopup;
