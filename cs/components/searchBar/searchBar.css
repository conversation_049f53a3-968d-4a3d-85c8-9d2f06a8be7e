.root,
.rootActive {
    position: relative;
    display: table;
    width: 100%;
}

.rootSmall .input {
    min-height: 40px;
}

.rootSmall .input,
.rootSmall .button {
    padding: 10px 15px;
}

.rootWithIconButton .input {
    padding-left: 50px;
    border-radius: var(--border-radius);
}

.rootWithIconButton .clearIconHolder {
    right: 20px;
}

.icon,
.iconMobile {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 16px;
    height: 16px;
    margin: auto;
    color: var(--dark-pigeon-color);
    pointer-events: none;
}

.iconMobile {
    display: none;
}

@media all and (max-width: 768px) {
    .iconMobile {
        display: block;
    }
}

@media all and (max-width: 768px) {
    .buttonText {
        width: 0;
        visibility: hidden;
    }
}

.inputHolder,
.buttonHolder {
    display: table-cell;
    vertical-align: middle;
}

.inputHolder {
    width: 100%;
}

.input,
.button {
    display: block;
    box-sizing: border-box;
    padding: 10px 15px;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    color: var(--content-color);
    font-size: 13px;
    line-height: 18px;
    vertical-align: middle;
}

.input {
    width: 100%;
    min-height: 50px;
    -moz-appearance: none;
    -webkit-appearance: none;
    -webkit-border-radius: 8px 0 0 8px;
    border-radius: 8px 0 0 8px;
    outline: 2px solid transparent;
    transition: border 0.3s, outline 0.3s;
}

.input::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

.input::-webkit-search-decoration {
    -webkit-appearance: none;
}

.input::-ms-clear {
    display: none;
}

.clearIconHolder {
    position: absolute;
    top: 0;
    right: 108px;
    bottom: 0;
    height: 10px;
    margin: auto;
}

@media all and (max-width: 768px) {
    .clearIconHolder {
        right: 70px;
    }
}

.buttonHolder {
    position: relative;
    width: 1px;
}

.button {
    padding: 15px 25px;
    margin-left: -1px;
    border-radius: 0 8px 8px 0;
    user-select: none;
}

.button:active {
    outline: none;
}

@media all and (max-width: 768px) {
    .button {
        position: relative;
        padding-right: 28px;
    }
}

.input:hover,
.rootActive .input {
    border-color: var(--primary-cta-color);
    outline: 2px solid var(--light-orange-bg);
}

.rootActive .button {
    border-color: var(--primary-cta-color);
    background: var(--primary-cta-color);
    box-shadow: inset 0 -2px 0 0 rgba(0, 0, 0, 0.1);
    color: #fff;
    cursor: pointer;
}

.rootActive .button:hover {
    border-color: var(--primary-cta-color);
    background: var(--primary-cta-color);
}

.rootActive .button:active {
    box-shadow: inset 0 2px 0 0 rgba(0, 0, 0, 0.1);
}

.rootActive .button:active .buttonText {
    margin-bottom: -2px;
}

.rootActive .iconMobile {
    color: #fff;
}
