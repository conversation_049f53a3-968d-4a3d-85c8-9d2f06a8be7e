import React, { FC, ReactElement, useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { useDispatch, useSelector } from 'react-redux';

import { t } from 'ttag';

import Icon from '../ui/icon/icon';

import { usePortalContainerRef } from '../../hooks/usePortalContainerRef';
import { INTEGRATION_RATE_CONSULTATION_URL } from '../../lib/constants';
import { useOnClickOutside } from '../../lib/reactHelpers/hooks';
import eventTracking from '../../services/analytics/eventTracking';
import {
    closeSidebar,
    getIsGuidSidebarOpen,
    onSidebarMount,
} from './guideSidebarReducer';

import SvgClose from './images/close.svg';

import css from './guideSidebar.css';

interface BlockData {
    img: string;
    title: string;
    text: string;
    help: string;
    video?: string;
    button?: ReactElement;
}

const INFO_BLOCKS: BlockData[] = [
    {
        img: `${config.STATIC_HOST}/images/guide-sidebar/sign.svg`,
        title: t`Підписання та надсилання документів`,
        text: t`Надсилайте та підписуйте вхідні документи`,
        video:
            'https://youtube.com/playlist?list=PLEhTlRpsSJZ4fHnhQNlAhCFMq2tc5Qnij',
        help: 'https://help.vchasno.com.ua/upload-sign-send',
    },
    {
        img: `${config.STATIC_HOST}/images/guide-sidebar/integration.svg`,
        title: t`Інтеграція`,
        text: t`Налаштуйте інтеграцію з 1С, CRM або сайтом`,
        help: 'https://help.vchasno.com.ua/integration',
        button: (
            <a
                href={`${INTEGRATION_RATE_CONSULTATION_URL}?utm_source=vchasno&utm_campaign=vchasno_guide_sidebar`}
                target="_blank"
                rel="noopener noreferrer"
                className={[css.linkButton, css.videoLink].join(' ')}
            >{t`Замовити консультацію`}</a>
        ),
    },
    {
        img: `${config.STATIC_HOST}/images/guide-sidebar/contractor.svg`,
        title: t`Налаштування роботи з контрагентами`,
        text: t`Перевірте, хто зареєстрований та запросіть у сервіс решту`,
        video:
            'https://youtube.com/playlist?list=PLEhTlRpsSJZ5idDN-3TILspIL8oEYl0j3',
        help: 'https://help.vchasno.com.ua/edoc-flow-contractors',
    },
    {
        img: `${config.STATIC_HOST}/images/guide-sidebar/colleagues.svg`,
        title: t`Налаштування роботи зі співробітниками`,
        text: t`Запросіть та налаштуйте права доступу`,
        video:
            'https://youtube.com/playlist?list=PLEhTlRpsSJZ6LPQa1jPZV3M4M9esC1hv1',
        help: 'https://help.vchasno.com.ua/edoc-flow-colleagues',
    },
    {
        img: `${config.STATIC_HOST}/images/guide-sidebar/coordination.svg`,
        title: t`Внутрішній документообіг`,
        text: t`Запросіть колег та налаштуйте права доступу. Погодьте документи, перш ніж підписати`,
        video:
            'https://youtube.com/playlist?list=PLEhTlRpsSJZ6CI-rF0Xq_W7mL0NEIvQka',
        help: 'https://help.vchasno.com.ua/edoc-flow-internal',
    },
    {
        img: `${config.STATIC_HOST}/images/guide-sidebar/tags.svg`,
        title: t`Ярлики`,
        text: t`Групуйте документи за спільними ознаками, наче у папки`,
        video: 'https://www.youtube.com/watch?v=WGmz9PxfQSQ',
        help: 'https://help.vchasno.com.ua/tagsdoc',
    },
    {
        img: `${config.STATIC_HOST}/images/guide-sidebar/additional-fields.svg`,
        title: t`Додаткові параметри`,
        text: t`Додайте суму, дату оплати, створіть власний статус, тощо.`,
        video: 'https://youtu.be/jNgGAiwOEbQ',
        help: 'https://help.vchasno.com.ua/doc-addition-fields',
    },
    {
        img: `${config.STATIC_HOST}/images/guide-sidebar/script.svg`,
        title: t`Сценарії документів`,
        text: t`Автоматизуйте опрацювання документів`,
        video: 'https://youtu.be/grXtqsdUO2U',
        help: 'https://help.vchasno.com.ua/coordination-and-sign-script',
    },
    {
        img: `${config.STATIC_HOST}/images/guide-sidebar/internal-docs.svg`,
        title: t`Внутрішні документи`,
        text: t`Акти вводу в експлуатацію, переміщення тмц, службові записки, заяви на відпустку, тощо.`,
        video: 'https://youtu.be/fOVKJYuEklY',
        help: 'https://help.vchasno.com.ua/insidedoc',
    },
];

const trackEvent = (action: string, label?: string) => {
    eventTracking.sendEvent('guide_sidebar', action, label);
};

const trackEventHandler = (action: string, label?: string) => () =>
    trackEvent(action, label);

const renderBlock = (data: BlockData) => (
    <div key={data.title} className={css.block}>
        <img src={data.img} className={css.image} alt={data.title} />
        <div>
            <div className={css.blockTitle}>{data.title}</div>
            <div className={css.subText}>{data.text}</div>
            <div className={css.buttons}>
                {data.button || null}
                {data.video && (
                    <a
                        href={data.video}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={[css.linkButton, css.videoLink].join(' ')}
                        onClick={trackEventHandler('click_video', data.title)}
                    >{t`Дивитись відео`}</a>
                )}
                <a
                    href={data.help}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={[css.linkButton, css.helpLink].join(' ')}
                    onClick={trackEventHandler('click_manual', data.title)}
                >{t`Читати довідку`}</a>
            </div>
        </div>
    </div>
);

const GuideSidebar: FC<React.PropsWithChildren<unknown>> = () => {
    const dispatch = useDispatch();
    const isOpen = useSelector(getIsGuidSidebarOpen);
    const containerRef = usePortalContainerRef();
    const ref = useRef(null);

    const onClose = () => dispatch(closeSidebar());

    useOnClickOutside(ref, onClose);

    useEffect(() => {
        if (isOpen) {
            trackEvent('show');
        }
    }, [isOpen]);

    useEffect(() => {
        dispatch(onSidebarMount());
    }, []);

    return (
        <>
            {containerRef.current &&
                ReactDOM.createPortal(
                    <div
                        ref={ref}
                        className={isOpen ? css.sidebarAnimated : css.sidebar}
                        data-qa="qa_features_and_service"
                    >
                        <div className={css.header}>
                            <div className={css.closeIcon} onClick={onClose}>
                                <Icon glyph={SvgClose} />
                            </div>
                            <div className={css.headerTitle}>
                                {t`Корисні можливості сервісу`}
                            </div>
                            <div className={css.subText}>
                                {t`Прискорюйте роботу з документами`}
                            </div>
                        </div>
                        {INFO_BLOCKS.map(renderBlock)}
                        <div className={css.bottomBlock}>
                            <a
                                href="https://help.vchasno.com.ua"
                                target="_blank"
                                rel="noopener noreferrer"
                                className={css.bottomLink}
                                onClick={trackEventHandler(
                                    'click_go_to_manual',
                                )}
                            >
                                {t`Перейти у довідку`}
                            </a>
                        </div>
                    </div>,
                    containerRef.current,
                )}
        </>
    );
};

export default GuideSidebar;
