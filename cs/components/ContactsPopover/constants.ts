import { stripPhone } from 'lib/utils';

import {
    vipSupportDisplayEmail,
    vipSupportTelegramBot,
} from '../../constants/support';

import SvgKyivstarLogo from './images/kyivstar.svg';
import SvgLetter from './images/letter.svg';
import SvgLifecellLogo from './images/lifecell.svg';
import SvgPhone from './images/phone.svg';
import SvgTelegramLogo from './images/telegram.svg';
import SvgViberLogo from './images/viber.svg';
import SvgVodafoneLogo from './images/vodafone.svg';

export const phones = [
    {
        label: config.SUPPORT_PHONE,
        href: `tel:${stripPhone(config.SUPPORT_PHONE)}`,
        icon: SvgPhone,
    },
    {
        label: config.SUPPORT_PHONE_KYIVSTAR,
        href: `tel:${stripPhone(config.SUPPORT_PHONE_KYIVSTAR)}`,
        icon: SvgKyivstarLogo,
    },
    {
        label: config.SUPPORT_PHONE_VODAFONE,
        href: `tel:${stripPhone(config.SUPPORT_PHONE_VODAFONE)}`,
        icon: SvgVodafoneLogo,
    },
    {
        label: config.SUPPORT_PHONE_LIFECELL,
        href: `tel:${stripPhone(config.SUPPORT_PHONE_LIFECELL)}`,
        icon: SvgLifecellLogo,
    },
];

export const socials = [
    {
        label: 'Telegram',
        href: `https://t.me/${config.SUPPORT_TELEGRAM}`,
        icon: SvgTelegramLogo,
    },
    {
        label: 'Viber',
        href: `viber://pa?chatURI=${config.SUPPORT_VIBER}`,
        icon: SvgViberLogo,
    },
    {
        label: config.SUPPORT_EMAIL,
        href: `mailto:${config.SUPPORT_EMAIL}`,
        icon: SvgLetter,
    },
];

export const socialsForUnPayedFOP = [
    {
        label: 'Telegram',
        href: `https://t.me/${config.SUPPORT_TELEGRAM}`,
        icon: SvgTelegramLogo,
    },
    {
        label: 'Viber',
        href: `viber://pa?chatURI=${config.SUPPORT_VIBER}`,
        icon: SvgViberLogo,
    },
    {
        label: config.SUPPORT_EMAIL,
        href: `mailto:${config.SUPPORT_EMAIL}`,
        icon: SvgLetter,
    },
];

export const socialsForVip = [
    {
        label: 'Telegram',
        href: `https://t.me/${vipSupportTelegramBot}`,
        icon: SvgTelegramLogo,
    },
    {
        label: vipSupportDisplayEmail,
        href: `mailto:${vipSupportDisplayEmail}`,
        icon: SvgLetter,
    },
];
