import React from 'react';
import { BrowserRouter, useLocation } from 'react-router-dom';

import ShortLogoDark from 'icons/short-logo-dark.svg';
import ShortLogoLight from 'icons/short-logo-light.svg';

import { LandingProjectSource } from '../../../services/enums';

import Icon from '../../ui/icon/icon';

import { useThemeContext } from '../../../contexts/theme';
import { useLandingProjectSource } from '../../../hooks/useLandingProjectSource';

import SvgShortLogoEdi from '../images/short-logo-edi.svg';
import SvgShortLogoKasa from '../images/short-logo-kasa.svg';
import SvgShortLogoKep from '../images/short-logo-kep.svg';
import SvgShortLogoTTN from '../images/short-logo-ttn.svg';

const LOGO_MAP: Record<LandingProjectSource, React.ReactElement> = {
    [LandingProjectSource.MAIN]: <Icon glyph={ShortLogoDark} />,
    [LandingProjectSource.EDI]: <Icon glyph={SvgShortLogoEdi} />,
    [LandingProjectSource.KASA]: <Icon glyph={SvgShortLogoKasa} />,
    [LandingProjectSource.KEP]: <Icon glyph={SvgShortLogoKep} />,
    [LandingProjectSource.TTN]: <Icon glyph={SvgShortLogoTTN} />,
};

const MultiBrandLogo: React.FC = () => {
    const location = useLocation();
    const { pathname } = location;

    const landingProjectSource = useLandingProjectSource();
    const { theme } = useThemeContext();

    const source = pathname.includes('/app')
        ? LandingProjectSource.MAIN
        : landingProjectSource;

    if (source === LandingProjectSource.MAIN) {
        if (theme === 'dark') {
            return <Icon glyph={ShortLogoLight} />;
        }

        if (theme === 'spring') {
            return <Icon glyph={ShortLogoDark} />;
        }
    }

    return LOGO_MAP[source];
};

export default () => (
    <BrowserRouter>
        <MultiBrandLogo />
    </BrowserRouter>
);
