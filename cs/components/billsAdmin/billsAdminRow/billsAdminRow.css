.panel:first-child {
    border-top: 1px solid var(--default-border);
}

.panel {
    border-bottom: 1px solid var(--default-border);
    cursor: pointer;
}

.panel:hover {
    background-color: var(--grey-bg);
}

.planCell {
    height: 40px;
    padding: 3px 10px;
    text-align: center;
    vertical-align: middle;
}

.plan {
    display: flex;
    min-height: 40px;
    box-sizing: border-box;
    align-items: center;
}

.trashIconWrap:global(.vchasno-ui-button.--secondary) {
    padding: 10px 10px;
    border: 0;
    background-color: transparent;
    color: var(--grey-color);
}

.trashIconWrap:global(.vchasno-ui-button.--secondary):hover {
    background-color: transparent;
    color: #F54F4F;
}

.trashIcon {
    display: block;
    width: 20px;
    height: 20px;
}
