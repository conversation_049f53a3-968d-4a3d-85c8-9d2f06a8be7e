import React from 'react';

import { <PERSON><PERSON><PERSON><PERSON>, Button, FlexBox } from '@vchasno/ui-kit';

import { loadCompanyDataType } from 'components/companyAdminCard/types';
import TrashIcon from 'icons/delete.svg';
import { formatDate } from 'lib/date';
import { ACCOUNT_RATE_TITLE_MAP } from 'services/billing';
import { AccountRate } from 'services/enums';
import { t } from 'ttag';
import {
    BillAcount,
    BillDataForActivate,
    BillPaymentStatusMap,
    inactiveStatuses,
    noActionsStatuses,
} from 'types/billing';
import Icon from 'ui/icon';

import BillsAdminStatus from '../billsAdminStatus/billsAdminStatus';

import css from './billsAdminRow.css';

interface BillsAdminRow {
    bill: BillAcount;
    disabledButtons: boolean;
    onActivate: (billForActivate: BillAcount) => void;
    scrollToTopRatesBlock: () => void;
    scrollToTopBillingBlock: () => void;
    setBillDataForActivateFromBilling: (billData: BillDataForActivate) => void;
    changeBillPaymentStatus: loadCompanyDataType;
}

const BillsAdminRow: React.FC<BillsAdminRow> = ({
    bill,
    disabledButtons,
    onActivate,
    scrollToTopRatesBlock,
    scrollToTopBillingBlock,
    setBillDataForActivateFromBilling,
    changeBillPaymentStatus,
}) => {
    const handleClickBillForActivate = () => {
        if (bill.rate || bill.services.length > 1) {
            scrollToTopRatesBlock();
            onActivate(bill);
        } else {
            scrollToTopBillingBlock();
            setBillDataForActivateFromBilling({
                billId: bill.number,
                countDocuments: bill.countDocuments,
                amount: bill.amount,
                billNumber: bill.id,
            });
        }
    };

    const getTitleRate = (currentBill: BillAcount) => {
        if (currentBill.services.length === 1) {
            return (
                ACCOUNT_RATE_TITLE_MAP[currentBill.rate as AccountRate.OLD] ??
                'Документи'
            );
        } else {
            return currentBill.services
                .map((rate) => {
                    return ACCOUNT_RATE_TITLE_MAP[rate.rate as AccountRate.OLD];
                })
                .join(' + ');
        }
    };

    return (
        <tr className={css.panel}>
            <td className={css.planCell}>{bill.number}</td>
            <td className={css.planCell}>{getTitleRate(bill)}</td>
            <td className={css.planCell}>{bill.amount}</td>
            <td className={css.planCell}>
                {formatDate(bill.dateCreated) || '-'}
            </td>
            <td className={css.planCell}>
                <BillsAdminStatus status={bill.paymentStatus} />
            </td>
            <td className={css.planCell}>
                <FlexBox gap={12} align="center" justify="center">
                    {inactiveStatuses.includes(
                        bill.paymentStatus as typeof inactiveStatuses[number],
                    ) && (
                        <div>
                            <Button
                                disabled={disabledButtons}
                                theme="secondary"
                                onClick={handleClickBillForActivate}
                            >{t`Активувати`}</Button>
                        </div>
                    )}
                    {bill.isActive && (
                        <div>
                            <Button
                                disabled={disabledButtons}
                                theme="secondary"
                                onClick={() => {
                                    changeBillPaymentStatus({
                                        billID: bill.id,
                                        status: BillPaymentStatusMap.REFUND,
                                        prevStatus: bill.paymentStatus,
                                    });
                                }}
                            >{t`Повернення`}</Button>
                        </div>
                    )}
                    {!noActionsStatuses.includes(
                        bill.paymentStatus as typeof noActionsStatuses[number],
                    ) && (
                        <BlackTooltip title={t`Скасувати рахунок`}>
                            <Button
                                disabled={disabledButtons}
                                suppressPadding
                                theme="secondary"
                                className={css.trashIconWrap}
                                onClick={() => {
                                    changeBillPaymentStatus({
                                        billID: bill.id,
                                        status: BillPaymentStatusMap.CANCELED,
                                        prevStatus: bill.paymentStatus,
                                    });
                                }}
                            >
                                <Icon
                                    glyph={TrashIcon}
                                    className={css.trashIcon}
                                />
                            </Button>
                        </BlackTooltip>
                    )}
                </FlexBox>
            </td>
        </tr>
    );
};

export default BillsAdminRow;
