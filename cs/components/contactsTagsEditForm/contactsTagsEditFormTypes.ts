import { Thunk } from '../../types';

import { ITag } from '../ui/tags/tagsTypes';

export interface OwnProps {
    contactId: string;
    contactTags: ITag[];
    placeholder: JSX.Element;
    onSubmitCallback: () => Thunk;
    onClose: () => void;
}

export interface State extends OwnProps {
    isLoading: boolean;
    isSuggestionsShown: boolean;
    errorMessage: string;
    title: string;
    allTags: ITag[];
    newTags: ITag[];
    selectedTags: ITag[];
    suggestedTags: ITag[];
    tagsToDelete: ITag[];
    tags?: ITag[];
}

export interface ActionsTypes {
    setInitialState: (state: Record<string, unknown>) => void;
    onAddNewTag: (value: string) => void;
    onAutosuggestTags: (value: string) => void;
    onCancel: () => void;
    onCloseSuggestions: () => void;
    onDeleteTagFromList: (tag: ITag) => void;
    onSubmit: (id: string, callback?: CallableFunction) => void;
    onSuggestionClick: (suggestion: { id: string }) => void;
}

export type Props = State & ActionsTypes;
