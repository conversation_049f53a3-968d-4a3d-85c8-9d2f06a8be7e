import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { t } from 'ttag';

import { FieldActions } from './types';

import {
    getFiltersAmountErrors,
    getFiltersAmountGte,
    getFiltersAmountLte,
    getFiltersIsLoadingStatus,
    getFiltersQueryDocAmountGte,
    getFiltersQueryDocAmountLte,
} from '../../../../selectors/filter.selectors';
import filtersActionCreators from '../../../filters/filtersActionCreators';

import Button from '../../../ui/button/button';
import CollapsibleGroup from '../../../ui/collapsibleGroup/collapsibleGroup';

import { getCorrectAmount } from '../../../../lib/numbers';
import eventTracking from '../../../../services/analytics/eventTracking';
import DocumentAmount from '../../../DocumentAmount/DocumentAmount';

import css from './SumOfDocumentsBlock.css';

const trackEvent = (action: string, label?: string) => {
    eventTracking.sendEvent('amount_filter', action, label);
};

const SumOfDocumentsBlock: React.FC<React.PropsWithChildren<unknown>> = () => {
    const dispatch = useDispatch();
    const queryAmountGte = useSelector(getFiltersQueryDocAmountGte);
    const queryAmountLte = useSelector(getFiltersQueryDocAmountLte);
    const amountGte = useSelector(getFiltersAmountGte);
    const amountLte = useSelector(getFiltersAmountLte);
    const isLoading = useSelector(getFiltersIsLoadingStatus);
    const docAmountErrors = useSelector(getFiltersAmountErrors);

    const onHeaderClick = (isCollapsed: boolean): void =>
        trackEvent(isCollapsed ? 'open_block' : 'close_block');

    const onBlur = (
        e: React.FocusEvent<HTMLInputElement, Element>,
        action: FieldActions,
    ): void => trackEvent(action, getCorrectAmount(e.target.value));

    return (
        <CollapsibleGroup
            isOpened={Boolean(queryAmountGte || queryAmountLte)}
            onHeaderClick={onHeaderClick}
            title={
                queryAmountGte || queryAmountLte ? (
                    <b>{t`Сума документа, грн`}</b>
                ) : (
                    t`Сума документа, грн`
                )
            }
            hint={t`Сума документа з урахуванням податків`}
        >
            <div className={css.content}>
                <DocumentAmount
                    id="from"
                    placeholder={t`Від`}
                    value={amountGte}
                    onValueChange={(value) =>
                        dispatch(filtersActionCreators.onChangeAmountGte(value))
                    }
                    errorText={docAmountErrors.from}
                    fixedDecimalScale
                    onBlur={(e) => onBlur(e, FieldActions.TYPE_AMOUNT_FROM)}
                    isNeedClearButton
                />
                <DocumentAmount
                    id="to"
                    placeholder={t`До`}
                    value={amountLte}
                    onValueChange={(value) =>
                        dispatch(filtersActionCreators.onChangeAmountLte(value))
                    }
                    errorText={docAmountErrors.to}
                    fixedDecimalScale
                    onBlur={(e) => onBlur(e, FieldActions.TYPE_AMOUNT_TO)}
                    isNeedClearButton
                />
                <Button
                    type="submit"
                    isLoading={isLoading}
                    onClick={() => {
                        eventTracking.sendToGTM({
                            event: 'select_filters',
                            category: 'doc_sum',
                        });
                        dispatch(filtersActionCreators.setDocsAmountFilters());
                    }}
                    width="full"
                    theme="cta"
                >
                    {t`Застосувати`}
                </Button>
            </div>
        </CollapsibleGroup>
    );
};

export default SumOfDocumentsBlock;
