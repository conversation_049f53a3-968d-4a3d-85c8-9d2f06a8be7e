import React from 'react';

import cn from 'classnames';
import { t } from 'ttag';

import {
    RateDescription,
    featureLabelPopoverMap,
    webFeatureList,
} from '../landing/Rates/utils';

import Button from '../ui/button/button';
import Hint from '../ui/hint/hint';

import { INTEGRATION_RATE_CONSULTATION_URL } from '../../lib/constants';
import eventTracking from '../../services/analytics/eventTracking';
import FeatureValuePresenter from '../FeatureValuePresenter';
import { featureLabelMap } from '../landing/Rates/constants';

import css from './RateTable.css';

export interface RateTableProps {
    className?: string;
    isLanding?: boolean;
    rates: RateDescription[];
}

const RateTable: React.FC<React.PropsWithChildren<RateTableProps>> = ({
    className,
    rates,
    isLanding = false,
}) => {
    return (
        <table className={cn(css.root, className)}>
            <colgroup>
                <col className={css.firstCol} />
                {rates.map((item) => (
                    <col
                        key={item.title}
                        className={cn(css.rateCol, {
                            [css.colHighlight]: item.isHighlight,
                        })}
                    />
                ))}
            </colgroup>
            <thead>
                <tr>
                    <th />
                    {rates.map((item) => (
                        <th
                            key={item.title}
                            className={cn({
                                [css.topHeadHighlight]: item.isHighlight,
                            })}
                        >
                            <span className={css.title}>{item.title}</span>
                        </th>
                    ))}
                </tr>
                <tr>
                    <th />
                    {rates.map((item) => (
                        <th key={item.title}>
                            <span className={css.subtitle}>
                                {item.subtitle}
                            </span>
                        </th>
                    ))}
                </tr>
                <tr>
                    <th />
                    {rates.map((item) => (
                        <th key={item.title}>
                            <span
                                className={cn(css.price, {
                                    [css.noPrice]: !item.price,
                                })}
                            >
                                {item.price ? item.price : t`Договірна`}
                                <span className={css.pricePeriod}>
                                    {item.priceTime}
                                </span>
                            </span>
                        </th>
                    ))}
                </tr>
                <tr>
                    <th />
                    {rates.map((item) => (
                        <th key={item.title}>
                            <span className={css.pricePerYear}>
                                {item.pricePerYear || ' '}
                            </span>
                        </th>
                    ))}
                </tr>
                <tr>
                    <th />
                    {rates.map((item) => (
                        <th key={item.specification}>
                            <span className={css.specification}>
                                {item.specification || ' '}
                            </span>
                        </th>
                    ))}
                </tr>
                <tr>
                    <th>
                        <a
                            href={INTEGRATION_RATE_CONSULTATION_URL}
                            rel="noopener noreferrer"
                            target="_blank"
                            onClick={() =>
                                eventTracking.sendEvent(
                                    'rates',
                                    isLanding
                                        ? 'get-btn-click-on-landing'
                                        : 'get-btn-click-on-rate-page',
                                    'consult',
                                )
                            }
                        >
                            <Button width="full" theme="blue">
                                {t`Консультація по тарифам`}
                            </Button>
                        </a>
                    </th>
                    {rates.map((item) => (
                        <th key={item.title}>{item.links}</th>
                    ))}
                </tr>
            </thead>
            <tbody>
                {webFeatureList.map((feature) => (
                    <tr key={feature}>
                        <td>
                            {featureLabelMap[feature]}
                            {featureLabelPopoverMap[feature] && (
                                <Hint black position="right">
                                    {featureLabelPopoverMap[feature]}
                                </Hint>
                            )}
                        </td>
                        {rates.map((item) => (
                            <td key={item.title}>
                                <FeatureValuePresenter
                                    value={item.features[feature]}
                                />
                            </td>
                        ))}
                    </tr>
                ))}
            </tbody>
        </table>
    );
};

export default RateTable;
