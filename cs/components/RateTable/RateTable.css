.root {
    width: 100%;
    margin: 0 auto;
    background-color: var(--white-bg);
    border-collapse: collapse;
    border-radius: 10px;
}

.root th {
    padding: 0 10px;
}

.root td {
    padding: 10px;
}

.root tbody td {
    text-align: center;
    vertical-align: middle;
}

.root tbody td:first-of-type {
    text-align: start;
}

.root tbody tr:first-of-type td {
    padding-top: 20px;
}

.root tbody tr:last-of-type td {
    padding-bottom: 30px;
}

.root tbody tr {
    transition: background-color 0.3s;
}

.root tbody tr:hover {
    background-color: #fffcde;
}

.root thead {
    border-bottom: 1px solid rgba(243, 246, 248, 1);
}

.root tbody td:first-of-type,
.root thead th:first-of-type {
    padding-right: 30px;
    padding-left: 30px;
}

.root thead tr:first-of-type th {
    padding-top: 20px;
}

.root thead tr:nth-of-type(5n) th {
    padding-top: 10px;
}

.root thead tr:last-of-type th {
    padding-top: 15px;
    padding-bottom: 20px;
}

.root col {
    min-width: 175px;
    border: 1px solid rgba(243, 246, 248, 1);
}

.title {
    display: block;
    color: var(--content-color);
    font-size: 22px;
    font-style: normal;
    font-weight: 700;
    line-height: 26px;
}

.subtitle {
    display: block;
    margin-top: 3px;
    color: #00c5ff;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    text-align: center;
}

.price {
    display: block;
    margin-top: 15px;
    color: var(--content-color);
    font-size: 36px;
    font-weight: 900;
    line-height: 42px;
}

.noPrice {
    font-size: 24px;
    line-height: 28px;
}

.pricePerYear {
    color: #9aaabf;
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
    text-align: center;
}

.specification {
    color: var(--pigeon-color);
    font-size: 12px;
    line-height: 14px;
    text-align: center;
}

.pricePeriod {
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
}

.firstCol {
    width: 270px;
    text-align: start;
}

.rateCol {
    border-left: 1px solid rgba(243, 246, 248, 1);
}

.colHighlight {
    background: #fffcde;
}

.topHeadHighlight {
    position: relative;
}

.topHeadHighlight::before {
    position: absolute;
    top: -10px;
    left: 0;
    display: block;
    width: 100%;
    height: 0;
    border-top: 10px solid #fffcde;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    content: '';
}
