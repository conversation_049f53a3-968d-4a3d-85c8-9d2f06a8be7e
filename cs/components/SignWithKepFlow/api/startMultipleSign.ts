import { encryptedPost } from 'components/KepSigner/api';
import { format } from 'date-fns';

import { CLOUD_SIGNER_URL } from '../constants';

type StartMultipleSignResponse =
    | { status: 1 }
    | { errorCode: number; errorMessage: string };

export const startMultipleSign = async (payload: {
    clientId: string;
    operationId: string;
    email: string;
    descriptions: string[];
    hashes: string[];
    sendNotification?: boolean;
}) => {
    const response = await encryptedPost<StartMultipleSignResponse>(
        `${CLOUD_SIGNER_URL}/ss/sign`,
        {
            clientId: payload.clientId,
            email: payload.email,
            operationId: payload.operationId,
            time: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
            originatorDescription: 'Вчасно.ЕДО',
            operationDescriptions: payload.descriptions,
            hashes: payload.hashes,
            signatureAlgorithmName: 'DSTU4145',
            signatureFormat: 'PKCS7',
            sendNotification: payload.sendNotification ?? true,
        },
    );

    if ('errorCode' in response) {
        throw new Error(response.errorMessage);
    }

    return response;
};
