.root {
    overflow: auto;
    width: 772px;
    max-width: calc(100vw - 20px);
    height: 600px;
    max-height: calc(100vh - 20px);
    box-sizing: border-box;
    padding: 12px;
    margin: 40px auto;
    border-radius: 12px;
}

.root>div:last-of-type {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

.overlay>div {
    overflow: hidden;
    align-items: flex-start;
    justify-content: center;
    padding-bottom: 0;
}

.backBtn {
    position: absolute;
    z-index: 1;
    top: 8px;
    left: 8px;
    background: var(--white-bg);
    border-radius: 50%;
}

.backBtn:hover {
    background: var(--pigeon-bg);
}

.appOverlay {
    position: absolute;
    display: flex;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    padding: 30px;
    background: linear-gradient(169.58deg, rgba(214, 233, 255, 0.5) 6.26%, rgba(255, 217, 182, 0.5) 56.6%, rgba(255, 148, 152, 0.5) 95.36%);
    border-radius: 12px;
    inset: 0;
}

:global(.vchasno-dark-theme) .appOverlay {
    background: var(--white);
}

.drawer {
    position: absolute;
    bottom: 0;
    display: flex;
    width: 100%;
    height: 434px;
    box-sizing: border-box;
    justify-content: center;
    backdrop-filter: blur(3px);
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, #FFF 32%);
    border-radius: 12px;
}

:global(.vchasno-dark-theme) .drawer {
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, #000 32%);
}

.appExample {
    width: 252px;
    height: 516px;
    background-image: url(../../assets/app-example.png);
    background-repeat: no-repeat;
    background-size: contain;
}

.drawerContent {
    display: flex;
    width: 320px;
    flex-direction: column;
    align-items: center;
}

.appIcon {
    position: relative;
    top: -12px;
    width: 52px;
    height: 52px;
    margin-bottom: 20px;
}

.headerTitle {
    margin-bottom: 40px;
    color: var(--content-color);
    font-size: 20px;
    font-weight: 500;
    line-height: 24px;
    text-align: center;
}

.qrsContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 36px;
}

.qrContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.qr {
    display: flex;
    width: 140px;
    height: 140px;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--default-border);
    background: #fff;
    border-radius: 8px;
}

.platform {
    display: block;
    width: 140px;
    height: 42px;
    color: var(--content-color);
}

.platform:hover {
    color: var(--content-color);
}

:global(.vchasno-dark-theme) .platform {
    --content-color: var(--dark-3-color);
}

@media screen and (max-width: 768px) {
    .root {
        width: 100%;
        height: auto;
        max-height: calc(100vh - 20px);
        padding: 52px 20px;
        margin: 10px auto;
    }

    .appOverlay {
        display: none;
    }

    .overlay>div {
        align-items: flex-end;
    }

    .appIcon {
        top: 0;
    }

    .drawer {
        position: static;
        height: 100%;
        background: var(--white);
    }

    .qr {
        display: none;
    }

    .platform {
        width: 150px;
        height: 45px;
    }

    .qrsContainer {
        flex-direction: column;
        gap: 12px;
    }

    .backBtn {
        display: none;
    }

}
