import React, { FC, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import SuccessPopupBasic from 'components/SignSuccessPopup/SuccessPopupBasic';
import signPopupActionCreators from 'components/signPopup/signPopupActionCreators';
import signPopupActions from 'components/signPopup/signPopupActions';
import { getCurrentCompanyIsFop } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';

import { CloudCertificateInfo, SignResult } from './types';

import {
    convertToLegacyFormat,
    redirectToVchasnoKepProxy,
    shouldShowKepAdPopup,
} from './utils';

import DownloadKepAppPopup from './components/DownloadKepAppPopup';
import GetKepAdPopup from './components/GetKepAdPopup';
import InitPopup from './components/InitPopup';
import KepActivatingPopup from './components/KepActivatingPopup';
import SelectKepPopup from './components/SelectKepPopup';
import SignPopup from './components/SignPopup';
import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from './constants';
import { useDetermineFeatureRollout, useListenForAutoStart } from './hooks';
import {
    signWithKepFlowActions,
    signWithKepFlowSelectors,
} from './signWithKepFlowSlice';

const SignWithKepFlow: FC = () => {
    const dispatch = useDispatch();

    const docsToSign = useSelector(signWithKepFlowSelectors.selectDocsToSign);
    const meta = useSelector(signWithKepFlowSelectors.selectFlowMetaInfo);
    const currentUserInfo = useSelector(
        signWithKepFlowSelectors.selectCurrentUserInfo,
    );
    const isCurrentCompanyFop = useSelector(getCurrentCompanyIsFop);
    const currentView = useSelector(signWithKepFlowSelectors.selectCurrentView);

    const [signatureCerts, setSignatureCerts] = useState<
        CloudCertificateInfo[]
    >([]);
    const [stampCerts, setStampCerts] = useState<CloudCertificateInfo[]>([]);

    const [selectedCertificates, setSelectedCertificates] = useState<
        CloudCertificateInfo[]
    >([]);

    // shows an ad (if available) or proceeds to fallback sign method
    const handleNoAvailableCerts = () => {
        if (shouldShowKepAdPopup(currentUserInfo)) {
            dispatch(signWithKepFlowActions.setView('get_kep_ad'));
        } else {
            handleChooseFallbackSignMethod();
        }
    };

    const handleLoadCertificates = (payload: {
        signatureCerts: CloudCertificateInfo[];
        stampCerts: CloudCertificateInfo[];
        isMobileAppLogged: boolean;
    }) => {
        setSignatureCerts(payload.signatureCerts);
        setStampCerts(payload.stampCerts);
        dispatch(
            signWithKepFlowActions.setIsMobileAppLogged(
                payload.isMobileAppLogged,
            ),
        );

        // if kep activation is in progress, show the warning popup and prevent any further actions
        if (meta.isWaitingForKepActivation) {
            dispatch(
                signWithKepFlowActions.setView('kep_activation_in_progress'),
            );
            return;
        }

        const hasSignatureCerts = payload.signatureCerts.length > 0;
        const hasStampCerts = payload.stampCerts.length > 0;
        const hasOnlyOneSignatureCert = payload.signatureCerts.length === 1;
        const hasAnyCerts = hasSignatureCerts || hasStampCerts;

        // proceed to fallback if no certificates are available
        if (!hasAnyCerts) {
            handleNoAvailableCerts();
            return;
        }

        // FOP company flow
        // 1. no signature certs -> proceed to fallback
        // 2. at least one signature cert -> select first cert and go to sign view
        if (isCurrentCompanyFop) {
            if (hasSignatureCerts) {
                setSelectedCertificates([payload.signatureCerts[0]]);
                dispatch(signWithKepFlowActions.setView('sign'));
            } else {
                handleNoAvailableCerts();
            }
            return;
        }

        // Legal company flow
        if (hasOnlyOneSignatureCert && !hasStampCerts) {
            setSelectedCertificates([payload.signatureCerts[0]]);
            dispatch(signWithKepFlowActions.setView('sign'));
            return;
        }

        dispatch(signWithKepFlowActions.setView('select_kep'));
    };

    const handleSelectKep = (payload: {
        signature: CloudCertificateInfo | null;
        stamp: CloudCertificateInfo | null;
    }) => {
        setSelectedCertificates((prev) => {
            const newCertificates = [...prev];

            payload.signature && newCertificates.push(payload.signature);
            payload.stamp && newCertificates.push(payload.stamp);

            return newCertificates;
        });
        dispatch(signWithKepFlowActions.setView('sign'));
    };

    const handleClose = () => {
        dispatch(signWithKepFlowActions.close());
        dispatch(signPopupActionCreators.hidePopup());
    };

    const handleSuccessPopupShown = () => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.SUCCESS_SHOW,
        });
    };

    const handleChooseFallbackSignMethod = async () => {
        dispatch(signWithKepFlowActions.close());
        dispatch({
            type: signPopupActions.SIGN_POPUP__RESTORE_FROM_NEW_KEP_SIGN,
        });
    };

    const handleCreateNewKep = () => {
        redirectToVchasnoKepProxy({ isLegal: !isCurrentCompanyFop });
    };

    const handleDownloadKepApp = () => {
        dispatch(signWithKepFlowActions.setView('download_kep_app'));
    };

    const handleSignComplete = (signResults: SignResult[]) => {
        const signatures = convertToLegacyFormat(signResults);

        dispatch(
            signPopupActionCreators.signAndSubmit(null, null, { signatures }),
        );
        dispatch(signWithKepFlowActions.close());
    };

    return (
        <>
            {currentView === 'init' && (
                <InitPopup
                    onLoad={handleLoadCertificates}
                    onClose={handleClose}
                />
            )}

            {currentView === 'get_kep_ad' && (
                <GetKepAdPopup
                    onClose={handleChooseFallbackSignMethod}
                    onChooseFallbackSignMethod={handleChooseFallbackSignMethod}
                    onCreateNewKep={handleCreateNewKep}
                    onDownloadKepApp={handleDownloadKepApp}
                />
            )}

            {currentView === 'select_kep' && (
                <SelectKepPopup
                    signatureCerts={signatureCerts}
                    stampCerts={stampCerts}
                    onClose={handleClose}
                    onSelect={handleSelectKep}
                    onChooseFallbackSignMethod={handleChooseFallbackSignMethod}
                />
            )}

            {currentView === 'sign' && (
                <SignPopup
                    certificates={selectedCertificates}
                    onClose={handleClose}
                    onChooseFallbackSignMethod={handleChooseFallbackSignMethod}
                    onCreateNewKep={handleCreateNewKep}
                    onSignComplete={handleSignComplete}
                    documents={docsToSign}
                />
            )}

            {currentView === 'download_kep_app' && (
                <DownloadKepAppPopup
                    onClose={handleClose}
                    onBack={() =>
                        dispatch(signWithKepFlowActions.setView('get_kep_ad'))
                    }
                />
            )}

            {currentView === 'kep_activation_in_progress' && (
                <KepActivatingPopup
                    onClose={handleClose}
                    onChooseFallbackSignMethod={handleChooseFallbackSignMethod}
                />
            )}

            {currentView === 'success' && (
                <SuccessPopupBasic
                    onAnimationComplete={handleClose}
                    onClose={handleClose}
                    onMount={handleSuccessPopupShown}
                />
            )}
        </>
    );
};

const SignWithKepFlowContainer = () => {
    const isActive = useSelector(signWithKepFlowSelectors.selectIfFlowActive);

    useListenForAutoStart();
    useDetermineFeatureRollout();

    if (!isActive) {
        return null;
    }

    return <SignWithKepFlow />;
};

export default SignWithKepFlowContainer;
