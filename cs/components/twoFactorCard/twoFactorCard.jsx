import React from 'react';

import { Switch } from '@vchasno/ui-kit';

import PropTypes from 'prop-types';
import { t } from 'ttag';

import PseudoLink from '../ui/pseudolink/pseudolink';

import css from './twoFactorCard.css';

const TwoFactorCard = (props) => {
    return (
        <div className={css.root}>
            <div className={css.row}>
                <div className={css.label}>
                    <span>{t`Телефон`}</span>
                </div>
                <div className={css.cell}>
                    <span data-qa="qa_phone_number">
                        {props.phone || (
                            <span className={css.unknown}>{t`Не вказано`}</span>
                        )}
                    </span>
                </div>
            </div>
            <div className={css.row}>
                <div className={css.label} />
                <div className={css.cell}>
                    {props.isPhoneVerified ? (
                        <div className={css.success}>✓ {t`Підтверджений`}</div>
                    ) : (
                        props.phone && (
                            <div className={css.handler}>
                                <PseudoLink
                                    onClick={props.onBeginChangePhone}
                                    dataQa="qa_accept_phone"
                                >
                                    {t`Підтвердити телефон`}
                                </PseudoLink>
                            </div>
                        )
                    )}
                    <div className={css.handler}>
                        <PseudoLink
                            onClick={() => props.onEditPhone(true)}
                            dataQa="qa_change_phone"
                        >
                            {props.phone
                                ? t`Змінити телефон`
                                : t`Вказати телефон`}
                        </PseudoLink>
                    </div>
                </div>
            </div>
            {props.phone && (
                <div className={css.row}>
                    <div className={css.label} />
                    <div className={css.cell}>
                        <Switch
                            size="sm"
                            label={t`Двофакторна аутентифікація`}
                            value={props.is2FAEnabledInProfile}
                            onChange={
                                props.is2FAEnabledInProfile
                                    ? props.onDisable2FA
                                    : props.onEnable2FA
                            }
                            disabled={
                                props.is2FAEnabledInProfile
                                    ? props.is2FAEnabledByCompanyRule ||
                                      props.is2FAEnabledByRecipientRule
                                    : undefined
                            }
                            dataQa={
                                props.is2FAEnabledInProfile
                                    ? 'qa_turn_off'
                                    : 'qa_turn_on'
                            }
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

TwoFactorCard.propTypes = {
    phone: PropTypes.string,
    isPhoneVerified: PropTypes.bool.isRequired,
    is2FAEnabledInProfile: PropTypes.bool.isRequired,
    is2FAEnabledByCompanyRule: PropTypes.bool.isRequired,
    is2FAEnabledByRecipientRule: PropTypes.bool.isRequired,
    onEditPhone: PropTypes.func.isRequired,
    onEnable2FA: PropTypes.func.isRequired,
    onDisable2FA: PropTypes.func.isRequired,
    onBeginChangePhone: PropTypes.func.isRequired,
};

export default TwoFactorCard;
