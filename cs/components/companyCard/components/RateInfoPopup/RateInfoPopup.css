.container {
    padding: 40px 40px 50px 40px;
}

.contentContainer {
    min-width: 630px;
}

.title {
    margin-bottom: 20px;
    color: var(--content-color);
    font-size: 32px;
    font-weight: 700;
    line-height: 38px;
}

.description {
    margin-bottom: 30px;
    color: var(--slate-grey-color);
    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
}

.endDateBadge {
    display: flex;
    align-items: center;
    padding: 3px 10px;
    margin-bottom: 35px;
    color: var(--green-alt-color);
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
}

.checkIcon {
    width: 7px;
    height: 5px;
    margin-right: 4px;
}

.daysLeft {
    width: 240px;
    margin-top: 30px;
    margin-bottom: 20px;
}

.horizontalSeparator {
    width: 100%;
    border-top: 1px solid var(--default-border);
    margin-bottom: 40px;
}

.permissionsContainer {
    display: flex;
    height: 365px;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: flex-start;
    gap: 20px 90px;
}

.fullWidthPermissionsContainer {
    height: auto;
    gap: 20px;
}

.chooseRateBtn {
    width: 340px;
    margin-top: 40px;
}

@media all and (max-width: 768px) {
    .permissionsContainer {
        height: auto;
    }

    .contentContainer {
        min-width: auto;
    }

    .chooseRateBtn {
        width: 100%;
    }

    .daysLeft {
        width: 100%;
    }
}
