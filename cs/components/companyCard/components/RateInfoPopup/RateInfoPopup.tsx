import React, { <PERSON> } from 'react';
import MediaQuery from 'react-responsive';

import cn from 'classnames';
import { AccountRate } from 'services/enums';
import { t } from 'ttag';

import { Nullable } from '../../../../types/general';
import { RateInfoData } from '../CompanyRatesSettings/types';

import Button from '../../../ui/button/button';
import Icon from '../../../ui/icon/icon';
import MobilePopup from '../../../ui/popup/mobile/popup';
import Popup from '../../../ui/popup/popup';

import { MEDIA_WIDTH } from '../../../../lib/constants';
import { openInNewTab } from '../../../../lib/navigation';
import { RATES_PAGE_LINK } from '../../../../lib/routing/constants';
import StatusText from '../../../statusText/statusText';
import DaysLeft from '../CompanyRateCard/components/RateTitleContainer/components/DaysLeft/DaysLeft';
import RateLimit from './components/RateLimit/RateLimit';
import RatePermission from './components/RatePermission/RatePermission';

import CheckSvg from '../CompanyRateCard/components/RateTitleContainer/components/ActivationFrom/images/check.svg';

import css from './RateInfoPopup.css';

interface RateInfoPopupProps {
    isOpen: boolean;
    infoData: Nullable<RateInfoData>;
    onClose: () => void;
}

const RateInfoPopup: FC<RateInfoPopupProps> = ({
    isOpen,
    infoData,
    onClose,
}) => {
    if (!infoData) {
        return null;
    }

    const {
        title,
        rate,
        endDate,
        daysLeft,
        daysSpentPercent,
        description,
        limits,
        permissions,
        fullWidthPermissions,
    } = infoData;

    const chooseRateHandler = () => {
        openInNewTab(RATES_PAGE_LINK);
    };

    const renderPopupContent = () => (
        <div className={css.contentContainer}>
            <h2 className={css.title}>{title}</h2>
            {description && (
                <span className={css.description}>{description}</span>
            )}
            {endDate && (
                <StatusText
                    className={css.endDateBadge}
                    statusColor="Green"
                    variant="badge"
                >
                    <Icon className={css.checkIcon} glyph={CheckSvg} />
                    {t`Дійсний до ${endDate}`}
                </StatusText>
            )}
            {!!daysLeft && (
                <DaysLeft
                    className={css.daysLeft}
                    daysLeft={parseInt(daysLeft)}
                    progressPercent={daysSpentPercent || 0}
                />
            )}
            <div className={css.horizontalSeparator} />
            <div
                className={cn(css.permissionsContainer, {
                    [css.fullWidthPermissionsContainer]: fullWidthPermissions,
                })}
            >
                {limits && (
                    <>
                        <RateLimit
                            label={t`Підписання та надсилання вихідних документів`}
                            value={
                                limits?.maxDocumentsCountForSent ||
                                rate === AccountRate.FREE
                                    ? `- ${
                                          limits.maxDocumentsCountForSent
                                      } ${t`на рік`}`
                                    : t`- без обмежень`
                            }
                        />
                        <RateLimit
                            label={t`Підписання вхідних документів`}
                            value={t`- без обмежень`}
                        />
                        <RateLimit
                            label={t`Перегляд документів`}
                            value={
                                limits?.maxDocumentsCountForView
                                    ? `- ${
                                          limits.maxDocumentsCountForView
                                      } ${t`останніх документів`}`
                                    : t`- без обмежень`
                            }
                        />
                        {limits?.maxEmployeesCount && (
                            <RateLimit
                                label={t`Доступних співробітників`}
                                value={`- ${limits.maxEmployeesCount}`}
                            />
                        )}
                    </>
                )}
                {permissions &&
                    Object.entries(
                        permissions,
                    ).map(([permissionTitle, value]) => (
                        <RatePermission
                            title={permissionTitle}
                            isAvailable={value}
                            fullWidth={fullWidthPermissions}
                            key={permissionTitle}
                        />
                    ))}
            </div>
            <Button
                className={css.chooseRateBtn}
                theme="cta"
                onClick={chooseRateHandler}
            >
                {t`Обрати інший тарифний план`}
            </Button>
        </div>
    );

    return (
        <>
            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <Popup
                    className={css.container}
                    active={isOpen}
                    onClose={onClose}
                    fullContent
                >
                    {renderPopupContent()}
                </Popup>
            </MediaQuery>
            <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                <MobilePopup active={isOpen} onClose={onClose}>
                    {renderPopupContent()}
                </MobilePopup>
            </MediaQuery>
        </>
    );
};

export default RateInfoPopup;
