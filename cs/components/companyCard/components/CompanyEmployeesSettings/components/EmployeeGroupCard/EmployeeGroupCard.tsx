import React, { FC, MouseEvent, useState } from 'react';

import { t } from 'ttag';

import Icon from '../../../../../ui/icon/icon';
import Popover from '../../../../../ui/popover/popover';

import FlexBox from '../../../../../FlexBox/FlexBox';
import EmployeeGroupCardMenuItem from '../EmployeeGroupCardMenuItem/EmployeeGroupCardMenuItem';

import DeleteIcon from './images/delete.svg';
import EditIcon from './images/edit.svg';
import MenuIcon from './images/more.svg';
import PeopleSvg from './images/people.svg';

import css from './EmployeeGroupCard.css';

interface EmployeeGroupCardProps {
    groupId: string;
    groupName: string;
    groupMembersCount: number;
    onEditGroup: (groupId: string) => void;
    onDeleteGroup: (groupId: string, groupName: string) => void;
    isShowMenu?: boolean;
}

const EmployeeGroupCard: FC<EmployeeGroupCardProps> = ({
    groupId,
    groupName,
    groupMembersCount,
    onEditGroup,
    onDeleteGroup,
    isShowMenu,
}) => {
    const [isOpenMenuPopover, setIsOpenMenuPopover] = useState<boolean>(false);

    const onToggleMenuPopover = (event?: MouseEvent<HTMLDivElement>) => {
        event?.stopPropagation();
        setIsOpenMenuPopover((prevState) => !prevState);
    };

    const handleEditGroupClick = () => onEditGroup(groupId);

    const handleDeleteGroupClick = (event?: MouseEvent<HTMLDivElement>) => {
        event?.stopPropagation();
        onDeleteGroup(groupId, groupName);
    };

    return (
        <div className={css.container} onClick={handleEditGroupClick}>
            <FlexBox
                className={css.content}
                justify="space-between"
                align="center"
                wrap="nowrap"
            >
                <FlexBox gap={10} align="center">
                    <div className={css.peopleIconContainer}>
                        <Icon glyph={PeopleSvg} />
                    </div>
                    <span className={css.groupName}>{groupName}</span>
                </FlexBox>
                <FlexBox gap={18} align="center">
                    <div className={css.membersCount}>{groupMembersCount}</div>
                    {isShowMenu && (
                        <Popover
                            tooltipClassName={css.menuTooltip}
                            isOpened={isOpenMenuPopover}
                            onClose={onToggleMenuPopover}
                            button={
                                <div
                                    className={css.menuIconContainer}
                                    onClick={onToggleMenuPopover}
                                >
                                    <Icon glyph={MenuIcon} />
                                </div>
                            }
                        >
                            <ul>
                                <EmployeeGroupCardMenuItem
                                    className={css.editMenuItem}
                                    Icon={
                                        <Icon
                                            className={css.editIcon}
                                            glyph={EditIcon}
                                        />
                                    }
                                    onClick={handleEditGroupClick}
                                >
                                    <span>{t`Редагувати`}</span>
                                </EmployeeGroupCardMenuItem>
                                <EmployeeGroupCardMenuItem
                                    className={css.deleteMenuItem}
                                    Icon={
                                        <Icon
                                            className={css.deleteIcon}
                                            glyph={DeleteIcon}
                                        />
                                    }
                                    onClick={handleDeleteGroupClick}
                                >
                                    <span>{t`Видалити`}</span>
                                </EmployeeGroupCardMenuItem>
                            </ul>
                        </Popover>
                    )}
                </FlexBox>
            </FlexBox>
        </div>
    );
};

export default EmployeeGroupCard;
