.container {
    cursor: pointer;
}

.content {
    min-width: 365px;
    height: 60px;
    box-sizing: border-box;
    padding: 10px 10px 10px 12px;
    border: 1px solid var(--default-border);
    border-radius: var(--border-radius);
}

.peopleIconContainer {
    display: flex;
    width: 43px;
    height: 40px;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    background-color: var(--grey-bg);
    border-radius: 43px;
}

.peopleIconContainer svg {
    width: 20px;
    height: 20px;
}

.groupName {
    overflow: hidden;
    max-width: 195px;
    color: var(--content-color);
    font-size: 14px;
    font-weight: 500;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.membersCount {
    padding: 2px 7px;
    background-color: #e6f2f9;
    border-radius: var(--border-radius);
    color: var(--link-color);
}

.menuTooltip {
    left: -39px;
    width: 160px;
    box-sizing: border-box;
    padding: 10px;
    border-color: var(--default-border);
    margin-top: 10px;
    box-shadow: none;
}

.menuTooltip::before {
    border-bottom-color: var(--default-border);
}

.menuTooltip::before,
.menuTooltip::after {
    left: 108px;
}

.menuIconContainer {
    display: flex;
    width: 30px;
    height: 30px;
    align-items: center;
    justify-content: center;
}

.menuIconContainer:hover {
    background-color: var(--grey-bg);
    border-radius: 30px;
    cursor: pointer;
}

.menuIconContainer svg {
    width: 20px;
    height: 20px;
    color: var(--content-color);
}

.editMenuItem svg,
.deleteMenuItem svg {
    width: 20px;
    height: 20px;
}

.editMenuItem svg {
    color: var(--grey-color);
}

.deleteMenuItem svg {
    color: var(--red-alt-color);
}

.editMenuItem span,
.deleteMenuItem span {
    font-size: 14px;
    font-weight: 400;
}

.editMenuItem span {
    color: var(--content-color);
}

.deleteMenuItem span {
    color: var(--red-alt-color);
}
