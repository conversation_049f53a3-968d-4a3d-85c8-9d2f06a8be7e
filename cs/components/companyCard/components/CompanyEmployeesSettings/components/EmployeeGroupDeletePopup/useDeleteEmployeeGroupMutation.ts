import { useMutation } from '@tanstack/react-query';

import { queryClient } from 'lib/queries';
import { GET_EMPLOYEE_GROUP_LIST_QUERY } from 'lib/queriesConstants';
import { deleteEmployeeGroup } from 'services/groups';

export const useDeleteEmployeeGroupMutation = () => {
    return useMutation({
        mutationFn: (params: Parameters<typeof deleteEmployeeGroup>) =>
            deleteEmployeeGroup(...params),
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: [GET_EMPLOYEE_GROUP_LIST_QUERY],
            });
        },
    });
};
