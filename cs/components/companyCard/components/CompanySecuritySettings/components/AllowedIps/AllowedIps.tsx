import React from 'react';

import { FlexBox } from '@vchasno/ui-kit';

import { t } from 'ttag';
import Card from 'ui/card/card';

import AllowedIpsForm from './AllowedIpsForm';
import AllowedIpsHint from './AllowedIpsHint';
import AllowedIpsList from './AllowedIpsList';

import companyCardCss from '../../../../companyCard.css';
import css from './AllowedIps.css';

const AllowedIps = () => (
    <Card
        wideContent
        title={t`Надати доступ тільки з певних IP адрес`}
        titleStyles={companyCardCss.cardTitle}
    >
        <FlexBox className={css.container}>
            <FlexBox direction="column" className={css.form}>
                <AllowedIpsForm />
                <AllowedIpsList />
            </FlexBox>
            <AllowedIpsHint />
        </FlexBox>
    </Card>
);

export default AllowedIps;
