.container {
    gap: 32px;
}

.input {
    width: 100%;
}

.allowedIp {
    padding: 7px 6px 7px 16px;
    background-color: var(--grey-bg);
    border-radius: 15px;
}

.form {
    flex: 1;
}

.hint {
    flex: 1;
    padding: 24px 20px;
    background-color: var(--grey-bg);
    border-radius: 8px;
    font-size: 14px;
}

.hintMainText {
    font-weight: 500;
}

.hintList {
    padding-left: 20px;
    list-style: outside;
}

.formContainer {
    gap: 12px;
}

@media all and (max-width: 768px) {
    .container {
        flex-direction: column;
        gap: 20px;
    }

    .form {
        order: 2;
        gap: 32px;
    }

    .formContainer {
        flex-direction: column;
        gap: 2px;
    }

    .input {
        max-width: none;
    }

    .input :global(.vchasno-ui-input__wrapper) {
        min-height: 40px;
    }

    .button:global(.vchasno-ui-button) {
        width: 100%;
        min-height: 40px;
    }

    .hint {
        order: 1;
    }
}
