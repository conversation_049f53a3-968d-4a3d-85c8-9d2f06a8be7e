import React, { FC, useState } from 'react';
import MediaQuery from 'react-responsive';
import { Link } from 'react-router-dom';

import cn from 'classnames';
import { FeatureShow } from 'components/FeatureDisplay';
import { DATE_FORMAT, MEDIA_WIDTH } from 'lib/constants';
import { formatDate } from 'lib/date';
import { t } from 'ttag';

import { ICompany } from '../../../../types/user';

import Button from '../../../ui/button/button';
import Icon from '../../../ui/icon/icon';

import FlexBox from '../../../FlexBox/FlexBox';
import IconPerson from '../../../iconPerson/iconPerson';
import CompaniesListPopover from '../CompaniesListPopover/CompaniesListPopover';

import blueArrowTop from '../../images/blue-arrow-top.svg';

import css from './CompanySettingsMenu.css';

interface CompanySettingsMenuProps {
    company: ICompany;
}

const CompanySettingsMenu: FC<CompanySettingsMenuProps> = ({ company }) => {
    const [isOpenPopover, setIsOpenPopover] = useState<boolean>(false);

    const onTogglePopover = () => setIsOpenPopover((prevState) => !prevState);

    return (
        <div className={css.container}>
            <div className={css.currentCompany}>
                <div className={css.icon}>
                    <IconPerson isLegal />
                </div>
                <div className={css.mainCompanyData}>
                    <FlexBox direction="column" gap={6}>
                        <span className={css.companyName}>{company.name}</span>
                        <span className={css.companyEdrpou}>
                            {company.edrpou}
                        </span>
                        <FeatureShow feature="ENABLE_DECEMBER_2024_PROMOTION">
                            <FlexBox gap={5} className={css.companyDateCreated}>
                                <span>{t`Дата реєстрації:`}</span>
                                <span>
                                    {formatDate(
                                        company.dateCreated,
                                        DATE_FORMAT,
                                    )}
                                </span>
                            </FlexBox>
                        </FeatureShow>
                    </FlexBox>
                    <div>
                        <MediaQuery minWidth={MEDIA_WIDTH.mobile + 1}>
                            <CompaniesListPopover
                                isOpenPopover={isOpenPopover}
                                onTogglePopover={onTogglePopover}
                            >
                                <Button
                                    className={css.myCompaniesButton}
                                    typeContour
                                    theme="blue"
                                    onClick={onTogglePopover}
                                    icon={
                                        <div
                                            className={cn(css.arrowIcon, {
                                                [css.arrowIconDown]: !isOpenPopover,
                                            })}
                                        >
                                            <Icon glyph={blueArrowTop} />
                                        </div>
                                    }
                                >{t`Мої компанії`}</Button>
                            </CompaniesListPopover>
                        </MediaQuery>
                        <MediaQuery maxWidth={MEDIA_WIDTH.mobile}>
                            <Link
                                className={css.link}
                                to={'/app/settings/companies'}
                            >{t`Мої компанії`}</Link>
                        </MediaQuery>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CompanySettingsMenu;
