.container {
    background-color: var(--white-bg);
    border-radius: 8px 8px 0 0;
}

:global(.vchasno-autumn-theme) .container {
    background-color: var(--autumn-3-color);
}

:global(.vchasno-spring-theme) .container {
    background-color: var(--spring-3-color);
}

.currentCompany {
    display: flex;
    min-height: 100%;
    flex-wrap: nowrap;
    align-items: center;
    padding: 20px 30px;
    gap: 15px;
}

.icon {
    width: 50px;
    min-width: 50px;
    height: 50px;
    min-height: 50px;
}

.icon > div {
    min-width: 100%;
    min-height: 100%;
}

.mainCompanyData {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
}

.companyName {
    color: var(--content-color);
    font-size: 14px;
    font-weight: 700;
}

.companyEdrpou {
    color: var(--slate-grey-color);
    font-size: 14px;
    font-weight: 400;
}

.companyDateCreated {
    color: var(--slate-grey-color);
}

.myCompaniesButton {
    display: flex;
    min-width: 160px;
    align-items: center;
    justify-content: space-between;
}

.myCompaniesButton:global(.vchasno-ui-button) {
    min-height: 40px;
}

.myCompaniesButton span {
    font-size: 14px;
}

.arrowIcon {
    width: 10px;
    height: 6px;
}

.arrowIconDown {
    transform: rotate(180deg);
}

.link {
    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
}

@media (max-width: 480px) {
    .mainCompanyData {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }
}
