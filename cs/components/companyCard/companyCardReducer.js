import actions from './companyCardActions';

import { Company } from '../../records/user';

const initState = {
    isCompanyDataLoaded: false,
    isEditingGeneralInfo: false,
    currentCompanyRole: {},
    company: new Company(),
    errorMessageGeneralInfo: '',
    roleId: '',
};

const companyCardReducer = (state = initState, action) => {
    switch (action.type) {
        case actions.COMPANY_CARD__SET_COMPANY_DATA:
            return {
                ...state,
                isCompanyDataLoaded: true,
                currentCompanyRole: action.currentCompanyRole,
                company: action.company,
                roleId: action.roleId,
                currentUser: action.currentUser,
            };

        case actions.COMPANY_CARD__CLEAR_COMPANY_DATA:
            return { ...initState };
        case actions.COMPANY_CARD__EDIT_GENERAL_INFO:
            return { ...state, isEditingGeneralInfo: true };
        case actions.COMPANY_CARD__CANCEL_EDIT_GENERAL_INFO:
        case actions.COMPANY_CARD__SUBMIT_GENERAL_INFO:
            return {
                ...state,
                isEditingGeneralInfo: false,
                errorMessageGeneralInfo: '',
            };
        case actions.COMPANY_CARD__SHOW_ERROR_GENERAL_INFO:
            return { ...state, errorMessageGeneralInfo: action.errorMessage };
        case actions.COMPANY_CARD__SET_LOADING:
            return { ...state, isCompanyDataLoaded: !action.isLoading };
        case actions.COMPANY_CARD__SET_COMPANY:
            return {
                ...state,
                company: action.company,
            };

        default:
            return state;
    }
};

export default companyCardReducer;
