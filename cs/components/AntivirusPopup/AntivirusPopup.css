.popupOverlay {
    z-index: 9999;
}

.container {
    display: flex;
    padding: 26px 35px;
    text-align: left;
}

.image {
    width: 140px;
    margin-right: 52px;
}

.content {
    max-width: 390px;
}

.title {
    margin-bottom: 10px;
    color: var(--red-color);
    font-size: 18px;
    font-weight: 700;
    line-height: 25px;
}

.mainText {
    line-height: 16px;
}

.buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 32px;
    gap: 10px;
}

.button {
    flex: 1 1 0;
    padding: 12px 2px;
    font-size: 13px;
}

@media all and (max-width: 768px) {
    .container {
        flex-direction: column;
        align-items: center;
        padding: 0;
    }

    .image {
        margin-right: 0;
        margin-bottom: 32px;
    }

    .buttons {
        flex-direction: column;
    }
}
