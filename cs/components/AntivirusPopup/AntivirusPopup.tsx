import React, { FC } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import cn from 'classnames';
import { t } from 'ttag';

import {
    getCurrentUserRole,
    getCurrentUserRoleId,
} from '../../selectors/app.selectors';

import { getPopupMainText } from './helpers';

import Button from '../ui/button/button';
import buttonCss from '../ui/button/button.css';
import Icon from '../ui/icon/icon';
import Popup from '../ui/popup/popup';

import { useAntivirusPopup } from './useAntivirusPopup';

import SvgMain from './images/main_image_popup.svg';

import css from './AntivirusPopup.css';

const AntivirusPopup: FC = () => {
    const history = useHistory();
    const currentRole = useSelector(getCurrentUserRole);
    const currentRoleId = useSelector(getCurrentUserRoleId);

    const documentSettingsLink = `/app/settings/companies/${currentRoleId}/main`;

    const {
        href,
        isOpen,
        linkButtonRef,
        isUserHasAccessForDownload,
        onClose,
        onClickDownload,
    } = useAntivirusPopup();

    const isAdmin = currentRole.isAdmin;

    // check if it's iframe in EDI
    const isIframeWindow = window !== top;

    const handleClickGoToOption = () => {
        if (isIframeWindow) {
            window.open(documentSettingsLink, '_blank');
            return;
        }

        history.push(documentSettingsLink);
        onClose();
    };

    const mainText = getPopupMainText(isAdmin, isUserHasAccessForDownload);

    return (
        <Popup
            inPortal
            centered
            onClose={onClose}
            active={isOpen}
            overlayClassName={css.popupOverlay}
        >
            <div className={css.container}>
                <div className={css.image}>
                    <Icon glyph={SvgMain} />
                </div>
                <div className={css.content}>
                    <h4 className={css.title}>
                        {t`Документ потенційно небезпечний!`}
                    </h4>
                    <p className={css.mainText}>{mainText}</p>
                    <div className={css.buttons}>
                        <Button
                            theme="blue"
                            className={css.button}
                            onClick={onClose}
                        >
                            {t`Скасувати завантаження`}
                        </Button>
                        {isAdmin && !isUserHasAccessForDownload && (
                            <Button
                                typeContour
                                theme="blue"
                                className={css.button}
                                onClick={handleClickGoToOption}
                            >
                                {t`Перейти до налаштувань`}
                            </Button>
                        )}
                        {isUserHasAccessForDownload && (
                            <>
                                {href && (
                                    <a
                                        download
                                        className={cn(
                                            css.button,
                                            buttonCss.root,
                                            buttonCss.themeBlue,
                                            buttonCss.typeContour,
                                        )}
                                        href={href}
                                        ref={linkButtonRef}
                                        onClick={onClickDownload}
                                    >{t`Завантажити`}</a>
                                )}
                                {!href && (
                                    <Button
                                        typeContour
                                        theme="blue"
                                        className={css.button}
                                        onClick={onClickDownload}
                                    >
                                        {t`Завантажити`}
                                    </Button>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </div>
        </Popup>
    );
};

export default AntivirusPopup;
