import React, { FC } from 'react';
import { connect } from 'react-redux';

import { t } from 'ttag';

import { DocumentField } from '../../services/documentFields/types';
import { Nullable } from '../../types/general';
import { StoreState } from '../../types/store';
import { FieldsSettings } from './types';

import TextShorten from '../ui/textShorten/textShorten';

import { formatDate } from '../../lib/date';

import css from './documentFieldsList.css';

interface OwnProps {
    settings?: Nullable<FieldsSettings>;
}

interface StateProps {
    documentFields: DocumentField[];
}

type Props = OwnProps & StateProps;

const getFieldsMapping = (
    documentFields: DocumentField[],
): Map<string, DocumentField> => {
    const map = new Map();
    documentFields.forEach((field) => map.set(field.id, field));
    return map;
};

const DocumentFieldsList: FC<React.PropsWithChildren<Props>> = ({
    settings,
    documentFields,
}: Props) => {
    if (!settings || !settings.fields.length) {
        return <>{t`Не вказано`}</>;
    }
    const fieldsMapping = getFieldsMapping(documentFields);
    return (
        <ul className={css.root}>
            {settings.fields.map((setting) => {
                const field = fieldsMapping.get(setting.field_id);
                if (!field) {
                    return null;
                }
                const value =
                    field && field.type === 'date'
                        ? formatDate(setting.value)
                        : setting.value;

                return (
                    <li key={field.id}>
                        <TextShorten>
                            {`${field.name}: `}
                            {value ? (
                                <span>{value}</span>
                            ) : (
                                <span
                                    className={css.info}
                                >{`(${t`не вказано`})`}</span>
                            )}
                        </TextShorten>
                    </li>
                );
            })}
        </ul>
    );
};

const mapStateToProps = (state: StoreState): StateProps => ({
    documentFields: state.app.documentFields,
});

const connector = connect<StateProps, Record<string, unknown>, OwnProps>(
    mapStateToProps,
);

export default connector(DocumentFieldsList);
