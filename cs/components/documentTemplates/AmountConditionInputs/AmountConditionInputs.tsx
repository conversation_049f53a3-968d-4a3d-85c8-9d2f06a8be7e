import React from 'react';

import { t } from 'ttag';

import {
    composeAmountConditionListValue,
    parseAmountConditionListValue,
} from '../utils';

import DocumentAmount from '../../DocumentAmount/DocumentAmount';

import css from './AmountConditionInputs.css';

interface AmountConditionProps {
    value: string;
    onChange: (value: string) => void;
}

const AmountConditionInputs: React.FC<
    React.PropsWithChildren<AmountConditionProps>
> = ({ value, onChange }) => {
    const { le, ge } = parseAmountConditionListValue(value);
    const isAmountsConflict = ge && le && Number(le) <= Number(ge);

    const setLeValue = (_le: string) => {
        onChange(composeAmountConditionListValue({ le: _le, ge }));
    };

    const setGeValue = (_ge: string) => {
        onChange(composeAmountConditionListValue({ le, ge: _ge }));
    };

    return (
        <div className={css.root}>
            <label htmlFor="ge_document_amount" className={css.label}>
                {t`Від`}
            </label>
            <DocumentAmount
                id="ge_document_amount"
                hintText={t`Введіть цифрами`}
                value={ge}
                onValueChange={setGeValue}
                errorText={
                    isAmountsConflict ? t`"Від" має бути меншим` : undefined
                }
            />
            <label htmlFor="le_document_amount" className={css.label}>
                {t`До`}
            </label>
            <DocumentAmount
                id="le_document_amount"
                hintText={t`Введіть цифрами`}
                value={le}
                onValueChange={setLeValue}
                errorText={
                    isAmountsConflict ? t`"До" має бути більшим` : undefined
                }
            />
        </div>
    );
};

export default AmountConditionInputs;
