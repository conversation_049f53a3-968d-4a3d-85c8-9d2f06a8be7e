.searchInput {
    width: 100%;
    min-width: 200px;
    max-width: 460px;
}

.controlsContainer {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 26px -10px 20px;
}

.controlsContainer > * {
    flex-shrink: 0;
    margin: 10px;
}

.listFooter {
    display: table;
    width: 100%;
    margin-top: 10px;
    table-layout: fixed;
}

.itemsPerPage {
    display: table-cell;
    text-align: right;
    vertical-align: middle;
}

.itemsPerPageTitle {
    display: inline-block;
    margin-right: 5px;
}

.itemsPerPageDropdown {
    display: inline-block;
    width: 65px;
}

@media screen and (max-width: 480px) {
    .searchInput {
        width: 95%;
    }
}
