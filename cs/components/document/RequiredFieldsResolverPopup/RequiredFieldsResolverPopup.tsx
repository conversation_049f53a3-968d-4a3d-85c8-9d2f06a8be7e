import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { t } from 'ttag';

import { getIsOpenRequiredFieldsResolverPopup } from '../../../selectors/document.selectors';
import actions from '../documentActions';

import Popup from '../../ui/popup/popup';

import RequiredFieldsResolver from '../../RequiredFieldsResolver/RequiredFieldsResolver';

import css from './RequiredFieldsResolverPopup.css';

const RequiredFieldsResolverPopup: React.FC = () => {
    const dispatch = useDispatch();

    const isOpenRequiredFieldsResolverPopup = useSelector(
        getIsOpenRequiredFieldsResolverPopup,
    );

    const onClose = () => {
        dispatch({
            type: actions.DOCUMENT__SET_REQUIRED_FIELDS_POPUP_STATUS,
            isOpenRequiredFieldsResolverPopup: false,
        });
    };

    return (
        <Popup
            title={t`Заповніть обов'язкові поля`}
            active={isOpenRequiredFieldsResolverPopup}
            onClose={onClose}
            className={css.popup}
            isDraggable={isOpenRequiredFieldsResolverPopup}
        >
            <RequiredFieldsResolver />
        </Popup>
    );
};

export default RequiredFieldsResolverPopup;
