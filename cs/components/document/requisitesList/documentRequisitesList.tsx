import React, { Fragment } from 'react';

import { ScrollableBox } from '@vchasno/ui-kit';

import cn from 'classnames';
import GroupItem from 'components/document/toolbar/SignersReviewersItem/GroupItem';
import { getSignerGroupBySignatureRoleId } from 'lib/ts/helpers';
import {
    DocumentFlowItem,
    Signature,
    Signer,
} from 'services/documents/ts/types';
import { t } from 'ttag';
import Icon from 'ui/icon/icon';
import TextShorten from 'ui/textShorten/textShorten';

import { formatFullName } from '../../../lib/helpers';
import { getSignaturesGroupedArray } from '../utils';
import { flowCanSign, getFlowOrder, isViewOnlyFlow } from './utils';

import SignatureInfo from '../../signatureInfo/signatureInfo';

import SvgHint from './images/hint.svg';

import css from './documentRequisitesList.css';

interface DocumentRequisitesListProps {
    companyEdrpou: string;
    signers: Signer[];
    signatures: Signature[];
    flows: DocumentFlowItem[];
}

const DocumentRequisitesList: React.FC<DocumentRequisitesListProps> = ({
    companyEdrpou,
    signers = [],
    signatures = [],
    flows = [],
}) => {
    let uncompletedSignatures;
    let signersFragment;
    const groupedSignatures = getSignaturesGroupedArray(signatures);
    const signaturesToDisplay = signers.length
        ? groupedSignatures.filter(
              (item) =>
                  item[0].keyOwnerEdrpou !== companyEdrpou &&
                  item[0].stampOwnerEdrpou !== companyEdrpou,
          )
        : groupedSignatures;

    if (signers.length > 0) {
        uncompletedSignatures = signers
            .filter((signer) => {
                if (signer.roleId) {
                    return !signatures.some(
                        (signature) => signature.roleId === signer.roleId,
                    );
                } else {
                    return !signatures.some(
                        (signature) =>
                            signature.roleId === signer.groupSignerId,
                    );
                }
            })
            .sort((signer1, signer2) => {
                return signer1.order && signer2.order
                    ? signer1.order - signer2.order
                    : 0;
            });

        const currentCompanySignatures = signatures.filter(
            (item) =>
                companyEdrpou === item.keyOwnerEdrpou ||
                companyEdrpou === item.stampOwnerEdrpou,
        );
        signersFragment = (
            <Fragment>
                {currentCompanySignatures.map((item) => {
                    const signerGroup = getSignerGroupBySignatureRoleId(
                        signers,
                        item,
                    );

                    return (
                        <div key={`sign-${item.id}`}>
                            <SignatureInfo
                                signerGroup={signerGroup}
                                signature={item}
                                isOwner
                            />
                        </div>
                    );
                })}
                {uncompletedSignatures.map((item, index) => {
                    const isDeferredSignature = item.order && index !== 0;
                    const key = item.roleId || item.groupId;
                    const renderContent = () => {
                        if (item.groupId) {
                            return (
                                <GroupItem mode="listItem" group={item.group} />
                            );
                        } else
                            return (
                                <>
                                    <TextShorten>
                                        {item.userEmail ?? item.role.user.email}
                                    </TextShorten>
                                    <TextShorten>
                                        {formatFullName(item.role.user)}
                                    </TextShorten>
                                </>
                            );
                    };
                    return (
                        <div
                            className={
                                isDeferredSignature
                                    ? css.authorOrdered
                                    : css.author
                            }
                            key={`signer-${key}`}
                        >
                            <div className={css.counter}>
                                {item.order || (
                                    <div className={css.orderNone}>
                                        <Icon glyph={SvgHint} />
                                    </div>
                                )}
                            </div>
                            {renderContent()}
                            {!isDeferredSignature && (
                                <div className={css.info}>
                                    {t`Очікується підпис`}
                                </div>
                            )}
                        </div>
                    );
                })}
            </Fragment>
        );
    }
    const inCompletedAndViewOnlyFlows = flows.filter(
        (flow) => !flow.isComplete || isViewOnlyFlow(flow),
    );

    return (
        <div className={css.root}>
            {signers.length > 0 && (
                <div className={css.group}>
                    <h5 className={css.groupTitle}>
                        {t`Підписи вашої компанії`}:
                    </h5>
                    {signersFragment}
                </div>
            )}
            {flows.length > 0 && (
                <div className={css.group}>
                    <h5 className={css.groupTitle}>{t`Контрагенти:`}</h5>
                </div>
            )}
            <ScrollableBox scrollHeight={400}>
                {signatures.length > 0 &&
                    signaturesToDisplay.map((companySignatures) => {
                        const signatureOwnerEdrpou =
                            companySignatures[0].keyOwnerEdrpou ||
                            companySignatures[0].stampOwnerEdrpou;
                        const isOwner = companyEdrpou === signatureOwnerEdrpou;
                        return (
                            <div
                                className={css.group}
                                key={`sign-group-${signatureOwnerEdrpou}`}
                            >
                                {!flows.length && (
                                    <h5 className={css.groupTitle}>
                                        {isOwner
                                            ? t`Підписи вашої компанії:`
                                            : t`Підписи контрагента:`}
                                    </h5>
                                )}
                                {companySignatures.map((item) => (
                                    <div
                                        className={cn(css.sign, {
                                            [css.alertSign]: !item.isValid,
                                        })}
                                        key={`sign-info-${item.id}`}
                                    >
                                        <SignatureInfo
                                            signature={item}
                                            isOwner={isOwner}
                                        />
                                    </div>
                                ))}
                            </div>
                        );
                    })}
            </ScrollableBox>
            <div>
                {inCompletedAndViewOnlyFlows.length > 0 &&
                    inCompletedAndViewOnlyFlows.map((item) => {
                        const viewOnly = isViewOnlyFlow(item);
                        const order = getFlowOrder(item);

                        return (
                            <div
                                className={cn({
                                    [css.authorOrdered]: order,
                                    [css.author]: !order,
                                    [css.authorViewOnly]: viewOnly,
                                    [css.canSign]: flowCanSign(flows, item),
                                })}
                                key={`flow-${item.id}`}
                            >
                                <div className={css.counter}>
                                    {order || (
                                        <div className={css.orderNone}>
                                            <Icon glyph={SvgHint} />
                                        </div>
                                    )}
                                </div>
                                <TextShorten>
                                    <b>{item.edrpou}</b>
                                </TextShorten>
                                {item.receivers.emails &&
                                    item.receivers.emails.map((email) => (
                                        <TextShorten key={email}>
                                            {email}
                                        </TextShorten>
                                    ))}
                                <div className={css.info}>
                                    {viewOnly
                                        ? t`Не підписує документ`
                                        : t`Очікується підпис`}
                                </div>
                            </div>
                        );
                    })}
            </div>
            {!flows.length && !signatures.length && !signers.length && (
                <div className={css.info}>{t`Підписів поки немає`}...</div>
            )}
        </div>
    );
};
export default DocumentRequisitesList;
