import React, { Component } from 'react';
import { connect } from 'react-redux';
import MediaQuery from 'react-responsive';
import { withRouter } from 'react-router-dom';

import AppModeDisplay from 'components/AppModeDisplay';
import DocumentAiSummaryBlock from 'components/DocumentAISummaryBlock';
import { FeatureShow } from 'components/FeatureDisplay';
import HideForEDI from 'components/hideForEDI/hideForEDI';
import { MEDIA_WIDTH } from 'lib/constants';
import PropTypes from 'prop-types';
import { UserPropType } from 'records/user';
import eventTracking from 'services/analytics/eventTracking';
import {
    isDeleteRequestInitiator,
    isDeleteRequestReceiver,
} from 'services/documents/utils';
import { ApplicationMode } from 'services/enums';
import { EventCapture } from 'services/posthog';
import { mapStateToApplicationMode, mapStateToCurrentUser } from 'store/utils';

import PopupMobile from '../../ui/popup/mobile/popup';
import SidePanel from '../../ui/sidePanel/sidePanel';

import ChangeRecipientPopup from '../../changeRecipientPopup/changeRecipientPopup';
import DocumentHeader from '../../documentHeader/documentHeader';
import DocumentViewer from '../../documentViewer/documentViewer';
import PageTitle from '../../pageTitle/pageTitle';
import ReviewHistoryPopup from '../../reviewHistoryPopup/reviewHistoryPopup';
import RequiredFieldsResolverPopup from '../RequiredFieldsResolverPopup/RequiredFieldsResolverPopup';
import ReviewsSignsBlockContainer from '../ReviewsSignsBlock/ReviewsSignsBlockContainer';
import DocumentEditor from '../edit/DocumentEditor';
import DocumentToolbar from '../toolbar/documentToolbar';
import MobileSignToolbar from '../toolbar/mobileSignToolbar';

// styles
import css from './document.css';

class Document extends Component {
    static propTypes = {
        applicationMode: PropTypes.string,
        currentUser: UserPropType,
        commentAdded: PropTypes.bool,
        isEditMode: PropTypes.bool,
        isChangeRecipientPopupOpened: PropTypes.bool,
        isCommentPopupOpened: PropTypes.bool,
        isDeletePopupOpened: PropTypes.bool,
        isInput: PropTypes.bool,
        isInternal: PropTypes.bool,
        isPdfLoading: PropTypes.bool,
        isSignSummaryLoading: PropTypes.bool,
        isRecipientEmailHidden: PropTypes.bool,

        doc: PropTypes.object.isRequired,
        toolbar: PropTypes.object,
        signButtons: PropTypes.object,
        viewSession: PropTypes.object,
        viewerVersion: PropTypes.number,
        documentActions: PropTypes.object.isRequired,

        comments: PropTypes.array,

        commentLevel: PropTypes.string,
        companyEdrpou: PropTypes.string,
        documentKind: PropTypes.string,
        dateCreated: PropTypes.string,
        dateDelivered: PropTypes.string,
        dateDocument: PropTypes.string,
        dateListing: PropTypes.string,
        dateUpdated: PropTypes.string,
        dateFinished: PropTypes.string,
        emailRecipient: PropTypes.string,
        edrpouRecipient: PropTypes.string,
        id: PropTypes.string,
        status: PropTypes.string,
        statusColor: PropTypes.string,
        statusTitle: PropTypes.string,
        title: PropTypes.string,
        type: PropTypes.string,
        userEmail: PropTypes.string,

        onChangeRecipient: PropTypes.func.isRequired,
        onCloseChangeRecipientPopup: PropTypes.func.isRequired,
        onCloseCommentPopup: PropTypes.func.isRequired,
        onCloseDeletePopup: PropTypes.func.isRequired,
        onCommentDoc: PropTypes.func.isRequired,
        onDeleteDoc: PropTypes.func.isRequired,
        onLoadDocument: PropTypes.func.isRequired,
        setEditMode: PropTypes.func.isRequired,
        onDeleteReview: PropTypes.func.isRequired,
        onDeleteLink: PropTypes.func.isRequired,
        onLinksUpdate: PropTypes.func.isRequired,
        onOpenAddReviewersPopup: PropTypes.func.isRequired,
        onOpenChangeDocumentPopup: PropTypes.func.isRequired,
        onOpenCommentPopup: PropTypes.func.isRequired,
        onOpenDeletePopup: PropTypes.func.isRequired,
        onOpenReviewHistoryPopup: PropTypes.func.isRequired,
        onDownloadPdf: PropTypes.func.isRequired,
        onDownloadSignSummary: PropTypes.func.isRequired,
        onReview: PropTypes.func.isRequired,
        onOpenChangeRecipientPopup: PropTypes.func.isRequired,
        onOpenTagsEditPopup: PropTypes.func.isRequired,
        onTagSelect: PropTypes.func.isRequired,
        onUploadChildDocument: PropTypes.func.isRequired,
        onOpenCreateDeleteRequestPopup: PropTypes.func.isRequired,
        onOpenResolveDeleteRequestPopup: PropTypes.func.isRequired,
        onRejectDeleteRequest: PropTypes.func.isRequired,
        onAcceptDeleteRequest: PropTypes.func.isRequired,
        onViewSessionOpen: PropTypes.func.isRequired,
        onCancelDeleteRequestClick: PropTypes.func.isRequired,
        onCancelDeleteRequestVote: PropTypes.func.isRequired,

        // Extra fields
        documentFields: PropTypes.array.isRequired,
    };

    state = {
        isOpenedToolbar: true,
        isOpenedToolbarMobile: false,
        isOpenedSignToolbarMobile: false,
    };

    handleSendOpenEvent = () => {
        const category =
            this.props.doc.parent && this.props.doc.parent.id
                ? 'sub_document'
                : 'document';
        eventTracking.sendEvent(category, 'details_button_click');
    };

    handleOpenToolbar = () => {
        this.setState({ isOpenedToolbar: true });
        this.handleSendOpenEvent();
    };

    handleCloseToolbar = () => {
        this.setState({ isOpenedToolbar: false });
        eventTracking.sendEvent('sidebar_right', 'sidebar_details_close');
    };

    handleOpenPopupMobile = () => {
        this.setState({ isOpenedToolbarMobile: true });
        this.handleSendOpenEvent();
    };

    handleClosePopupMobile = () => {
        this.setState({ isOpenedToolbarMobile: false });
    };

    handleOpenSignPopupMobile = () => {
        this.setState({ isOpenedSignToolbarMobile: true });
    };

    handleCloseSignPopupMobile = () => {
        this.setState({ isOpenedSignToolbarMobile: false });
    };

    handleCloseEditor = () => {
        this.props.setEditMode(false);
        this.props.history.push(
            `/app/documents/${this.props.doc.id}/drafts/${this.props.doc.drafts[0].id}`,
        );
        this.props.onLoadDocument(this.props.doc.id);
    };

    handlePublish = () => {
        this.props.setEditMode(false);
        this.props.history.push(`/app/documents/${this.props.doc.id}`);
    };

    render() {
        if (!this.props.id) return null;
        const { id: docId, doc, title, type } = this.props;
        const {
            currentUser: {
                currentRole: {
                    company: { edrpou },
                },
            },
        } = this.props;
        const docTitle = `${type && type !== title ? `${type}: ` : ''}${title}`;
        const isSignSessionMode =
            this.props.applicationMode === ApplicationMode.SIGN_SESSION;
        const isSharedDocumentViewMode =
            this.props.applicationMode === ApplicationMode.SHARED_DOCUMENT_VIEW;
        const isDelRequestReceiver = isDeleteRequestReceiver([doc], edrpou);
        const isDelRequestInitiator = isDeleteRequestInitiator([doc], edrpou);

        const toolbar = (
            <DocumentToolbar
                isInput={this.props.isInput}
                isInternal={this.props.isInternal}
                isCommentPopupOpened={this.props.isCommentPopupOpened}
                doc={doc}
                statusColor={this.props.statusColor}
                statusTitle={this.props.statusTitle}
                dateCreated={this.props.dateCreated}
                dateDelivered={this.props.dateDelivered}
                dateDocument={this.props.dateDocument}
                dateListing={this.props.dateListing}
                dateFinished={this.props.dateFinished}
                commentLevel={this.props.commentLevel}
                companyEdrpou={this.props.companyEdrpou}
                commentAdded={this.props.commentAdded}
                comments={this.props.comments}
                documentTitle={docTitle}
                userEmail={this.props.userEmail}
                onCloseCommentPopup={this.props.onCloseCommentPopup}
                onCommentDocument={this.props.onCommentDoc}
                onOpenCommentPopup={this.props.onOpenCommentPopup}
                onOpenAddReviewersPopup={this.props.onOpenAddReviewersPopup}
                onOpenReviewHistoryPopup={this.props.onOpenReviewHistoryPopup}
                onReview={this.props.onReview}
                onDeleteReview={this.props.onDeleteReview}
                onDeleteLink={this.props.onDeleteLink}
                onLinksUpdate={this.props.onLinksUpdate}
                onOpenChangeRecipientPopup={
                    this.props.onOpenChangeRecipientPopup
                }
                onOpenTagsEditPopup={this.props.onOpenTagsEditPopup}
                onTagSelect={this.props.onTagSelect}
                onUploadChildDocument={this.props.onUploadChildDocument}
                isDeleteRequestReceiver={isDelRequestReceiver}
                isDeleteRequestInitiator={isDelRequestInitiator}
                onOpenCreateDeleteRequestPopup={
                    this.props.onOpenCreateDeleteRequestPopup
                }
                onOpenResolveDeleteRequestPopup={
                    this.props.onOpenResolveDeleteRequestPopup
                }
                onRejectDeleteRequest={this.props.onRejectDeleteRequest}
                onAcceptDeleteRequest={this.props.onAcceptDeleteRequest}
                onCancelDeleteRequestClick={
                    this.props.onCancelDeleteRequestClick
                }
                onCancelDeleteRequestVote={this.props.onCancelDeleteRequestVote}
                onOpenChangeDocumentPopup={this.props.onOpenChangeDocumentPopup}
                documentFields={this.props.documentFields}
            >
                {this.props.toolbar}
            </DocumentToolbar>
        );

        const mobileSignToolbar = (
            <MobileSignToolbar
                isInput={this.props.isInput}
                isInternal={this.props.isInternal}
                doc={doc}
                statusColor={this.props.statusColor}
                statusTitle={this.props.statusTitle}
                dateCreated={this.props.dateCreated}
                dateDelivered={this.props.dateDelivered}
                dateDocument={this.props.dateDocument}
                dateListing={this.props.dateListing}
                dateFinished={this.props.dateFinished}
                commentLevel={this.props.commentLevel}
                companyEdrpou={this.props.companyEdrpou}
                documentTitle={docTitle}
                userEmail={this.props.userEmail}
                onOpenAddReviewersPopup={this.props.onOpenAddReviewersPopup}
                onOpenReviewHistoryPopup={this.props.onOpenReviewHistoryPopup}
                onReview={this.props.onReview}
                onDeleteReview={this.props.onDeleteReview}
                onDeleteLink={this.props.onDeleteLink}
                onLinksUpdate={this.props.onLinksUpdate}
                onOpenChangeRecipientPopup={
                    this.props.onOpenChangeRecipientPopup
                }
                onUploadChildDocument={this.props.onUploadChildDocument}
                onOpenChangeDocumentPopup={this.props.onOpenChangeDocumentPopup}
                documentFields={this.props.documentFields}
            >
                {this.props.toolbar}
            </MobileSignToolbar>
        );

        const isMinimalClassRoot =
            isSignSessionMode || isSharedDocumentViewMode;
        const documentHeaderTrackingLabel =
            isSignSessionMode || isSharedDocumentViewMode
                ? `${doc.edrpouOwner} ${doc.companyNameOwner}`
                : '';
        const getDocumentHeaderTrackingName = () => {
            if (isSignSessionMode) {
                return 'sign_session';
            }
            if (isSharedDocumentViewMode) {
                return 'shared_document_view';
            }
            return '';
        };

        return (
            <div className={isMinimalClassRoot ? css.rootMinimal : css.root}>
                <EventCapture event="document_view" />
                <div className={css.content}>
                    <PageTitle>{docTitle}</PageTitle>
                    <DocumentHeader
                        doc={doc}
                        status={this.props.status}
                        isDeletePopupOpened={this.props.isDeletePopupOpened}
                        isDisabledDelete={
                            !this.props.documentActions.delete &&
                            !this.props.documentActions.deleteRequest
                        }
                        isOpenedToolbar={this.state.isOpenedToolbar}
                        isPdfLoading={this.props.isPdfLoading}
                        isSignSummaryLoading={this.props.isSignSummaryLoading}
                        onCloseDeletePopup={this.props.onCloseDeletePopup}
                        onDeleteDocuments={this.props.onDeleteDoc}
                        onOpenDeletePopup={this.props.onOpenDeletePopup}
                        onOpenChangeDocumentPopup={
                            this.props.onOpenChangeDocumentPopup
                        }
                        onOpenPopupMobile={this.handleOpenPopupMobile}
                        onOpenSignPopupMobile={this.handleOpenSignPopupMobile}
                        onOpenToolbar={this.handleOpenToolbar}
                        onDownloadPdf={this.props.onDownloadPdf}
                        onDownloadSignSummary={this.props.onDownloadSignSummary}
                        statusColor={doc.statusColor}
                        statusTitle={doc.statusTitle}
                        title={docTitle}
                        trackingLabel={documentHeaderTrackingLabel}
                        trackingName={getDocumentHeaderTrackingName()}
                        isDeleteRequestReceiver={isDelRequestReceiver}
                        isDeleteRequestInitiator={isDelRequestInitiator}
                        onOpenCreateDeleteRequestPopup={
                            this.props.onOpenCreateDeleteRequestPopup
                        }
                        onOpenResolveDeleteRequestPopup={
                            this.props.onOpenResolveDeleteRequestPopup
                        }
                        roleId={this.props.currentUser.roleId}
                        edrpou={this.props.currentUser.currentCompany.edrpou}
                    />
                    <ReviewsSignsBlockContainer />
                    <FeatureShow feature="AI_DOCUMENT_SUMMARY_BLOCK">
                        <AppModeDisplay hideSignSession hideSharedDocumentView>
                            <HideForEDI>
                                <DocumentAiSummaryBlock doc={doc} />
                            </HideForEDI>
                        </AppModeDisplay>
                    </FeatureShow>
                    {this.props.isEditMode && (
                        <DocumentEditor
                            onClose={this.handleCloseEditor}
                            onPublish={this.handlePublish}
                            doc={this.props.doc}
                        />
                    )}
                    <DocumentViewer
                        doc={doc}
                        key={this.props.viewerVersion}
                        viewSession={this.props.viewSession}
                        onViewSessionOpen={this.props.onViewSessionOpen}
                    />
                </div>
                <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                    <SidePanel
                        isHideCloseButton={isSharedDocumentViewMode}
                        isClosed={!this.state.isOpenedToolbar}
                        onClose={this.handleCloseToolbar}
                    >
                        {toolbar}
                    </SidePanel>
                </MediaQuery>
                <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                    {this.state.isOpenedToolbarMobile && (
                        <PopupMobile
                            active={this.state.isOpenedToolbarMobile}
                            onClose={this.handleClosePopupMobile}
                            className={css.popupMobile}
                        >
                            {toolbar}
                        </PopupMobile>
                    )}
                </MediaQuery>
                <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                    {this.state.isOpenedSignToolbarMobile && (
                        <PopupMobile
                            active={this.state.isOpenedSignToolbarMobile}
                            onClose={this.handleCloseSignPopupMobile}
                        >
                            {mobileSignToolbar}
                        </PopupMobile>
                    )}
                </MediaQuery>
                {this.props.isChangeRecipientPopupOpened && (
                    <ChangeRecipientPopup
                        isChangeRecipientPopupOpened={
                            this.props.isChangeRecipientPopupOpened
                        }
                        hint={
                            ['READY', 'SENT', 'SIGNED_AND_SENT'].indexOf(
                                this.props.status,
                            ) >= 0
                        }
                        documentTitle={docTitle}
                        documentId={docId}
                        email={this.props.emailRecipient}
                        edrpou={this.props.edrpouRecipient}
                        isEmailHidden={this.props.isRecipientEmailHidden}
                        onCloseChangeRecipientPopup={
                            this.props.onCloseChangeRecipientPopup
                        }
                        onChangeRecipient={this.props.onChangeRecipient}
                    />
                )}
                <ReviewHistoryPopup />
                <RequiredFieldsResolverPopup />
            </div>
        );
    }
}

const mapStateToProps = (state) => ({
    ...mapStateToApplicationMode(state),
    ...mapStateToCurrentUser(state),
});

export default connect(mapStateToProps)(withRouter(Document));
