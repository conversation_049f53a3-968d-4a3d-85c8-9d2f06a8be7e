import { MutableRefObject, useEffect, useRef } from 'react';

import { useSocketHandler } from 'hooks/useSocketHandler';

import {
    SocketChangeStateUnoMessage,
    UNO_STATE_CHANGE_DATA_PREFIX,
    UnoCommand,
} from './constants';

const makeUnoCommandSocketDataPrefix = (command: UnoCommand): string =>
    `${UNO_STATE_CHANGE_DATA_PREFIX}${command}` as const;

const isUnoStateChangeData = (
    data: string | ArrayBuffer,
    commands: UnoCommand[],
): boolean => {
    if (typeof data !== 'string') {
        return false;
    }

    return commands.some((command) =>
        (data as SocketChangeStateUnoMessage).startsWith(
            makeUnoCommandSocketDataPrefix(command),
        ),
    );
};

type UnoCommandHandlerMap = Record<
    UnoCommand,
    (value: string | number | boolean | null, receiveMsgCount: number) => void
>;

type CountRef = Partial<Record<UnoCommand, number>>;

const handleUnoSocketMessage = (
    handlerMap: MutableRefObject<UnoCommandHandlerMap>,
    countRef: MutableRefObject<CountRef>,
) => (event: MessageEvent<SocketChangeStateUnoMessage>) => {
    if (
        !isUnoStateChangeData(
            event.data,
            Object.keys(handlerMap.current) as UnoCommand[],
        )
    ) {
        return;
    }

    const [, commandNameWithValue] = event.data.split(
        UNO_STATE_CHANGE_DATA_PREFIX,
    ) as [string, `${UnoCommand}=${string}`];

    const [commandName, value] = commandNameWithValue.split('=') as [
        UnoCommand,
        string,
    ];

    if (countRef.current[commandName] === undefined) {
        countRef.current[commandName] = 0;
    }

    // зберігаємо кількість отриманих команд
    countRef.current[commandName] = countRef.current[commandName] + 1;

    try {
        handlerMap.current[commandName]?.(
            JSON.parse(value),
            countRef.current[commandName],
        );
    } catch (error) {
        console.error(
            'useCollaboraSocketUnoState > handleUnoSocketMessage: Error while handling uno socket message',
            error,
        );
    }
};

export const useCollaboraSocketUnoState = (map: UnoCommandHandlerMap) => {
    const countRef = useRef<CountRef>({});
    const mapRef = useRef(map);

    useEffect(() => {
        mapRef.current = map;
    }, [map]);

    return useSocketHandler(handleUnoSocketMessage(mapRef, countRef));
};
