.root {
    display: flex;
    height: 30px;
    box-sizing: border-box;
    align-items: center;
    padding: 3px 8px 3px 3px;
    background-color: var(--grey-bg);
    border-radius: 15px;
    gap: 8px;
    line-height: 30px;
    transition: background-color 0.3s;

    &:hover {
        background-color: var(--default-border);

        .avatar,
        *[data-group] {
            background-color: var(--grey-border);
        }
    }
}

.removeIcon {
    display: block;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    color: var(--grey-color);
    cursor: pointer;

    &:hover {
        color: var(--red-color);
    }
}
