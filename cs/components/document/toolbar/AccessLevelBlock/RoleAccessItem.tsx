import React from 'react';

import { BlackTooltip, FlexBox, Text } from '@vchasno/ui-kit';

import cn from 'classnames';
import AvatarIcon from 'components/AvatarIcon/AvatarIcon';
import { t } from 'ttag';
import Icon from 'ui/icon';

import RemoveSvg from '../../../../icons/cross.svg';

import css from './RoleAccessItem.css';

export interface RoleAccessItemProps {
    className?: string;
    title: string;
    subText?: string | null;
    onRemove?: () => void;
    removeHint?: string;
    variant?: 'user' | 'group';
    startElement?: React.ReactNode;
}

const RoleAccessItem: React.FC<RoleAccessItemProps> = ({
    className,
    title,
    subText,
    onRemove,
    removeHint = t`Видалити доступ перегляду`,
    variant,
    startElement,
}) => {
    return (
        <FlexBox className={cn(css.root, className)}>
            <AvatarIcon
                title={title}
                variant={variant}
                className={css.avatar}
            />
            {startElement}
            <Text ellipsis style={{ flexGrow: 1 }}>
                {title && <Text key="title">{title}</Text>}
                {subText && (
                    <Text
                        style={{ marginLeft: 4 }}
                        key="subTitle"
                        type="secondary"
                    >
                        {subText}
                    </Text>
                )}
            </Text>
            {onRemove && (
                <BlackTooltip title={removeHint} disableInteractive>
                    <span className={css.removeIcon} onClick={onRemove}>
                        <Icon glyph={RemoveSvg} />
                    </span>
                </BlackTooltip>
            )}
        </FlexBox>
    );
};

export default RoleAccessItem;
