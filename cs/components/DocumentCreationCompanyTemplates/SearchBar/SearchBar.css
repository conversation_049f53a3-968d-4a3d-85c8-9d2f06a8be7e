.root {
    display: flex;
    width: 100%;
    box-sizing: border-box;
    gap: 10px;
}

.searchIcon {
    width: 20px;
    height: 20px;
    color: var(--grey-color);
    transition: color 0.3s;
}

label:hover .searchIcon,
label:focus-within .searchIcon{
    color: var(--vchasno-ui-input-border-color-focused);
}

@supports (backdrop-filter: blur(8px)) {
    .sticky {
        position: sticky;
        z-index: 3;
        top: 0;
        box-sizing: content-box;
        padding: 20px;
        margin: -20px;
        backdrop-filter: blur(8px);
        border-radius: var(--border-radius);
    }
}

.categorySelect {
    min-width: 300px;
}

@media (max-width: 1024px) {
    .sticky {
        top: var(--mobile-header-height);
    }
}

@media (max-width: 768px) {
    .root {
        flex-wrap: wrap;
    }

    .categorySelect {
        width: 100%;
    }
}
