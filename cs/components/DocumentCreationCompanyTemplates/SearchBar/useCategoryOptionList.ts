import { useMemo } from 'react';

import { composeCategoryOptionList } from 'components/DocumentCreationCompanyTemplates/TemplateCategoryController/utils';
import { useDocCategoryInfinityQuery } from 'hooks/useDocCategoryInfinityQuery';
import { useDocCategoryListByIdsQuery } from 'hooks/useDocCategoryListByIdsQuery';
import { useDocCategorySearchQuery } from 'hooks/useDocCategorySearchQuery';
import { getIsInternalDocCategory } from 'services/documentCategories/utils';
import { t } from 'ttag';

interface CategoryOptionListOption {
    search?: string;
    onlyPublic?: boolean;
    ids?: Array<string | number>;
}

export const useCategoryGroupedOptionList = (
    options: CategoryOptionListOption = {},
) => {
    const search = options.search || '';

    // цей хук запускає запит на сервер для пошуку категорій, і не використовує ніякої пагінації
    const searchQuery = useDocCategorySearchQuery({
        title: search,
        onlyPublic: options.onlyPublic,
    });
    // цей хук робить запит без пошукових параметрів, але з пагінацією (використовується для нескінченний скроллу)
    const infiniteQuery = useDocCategoryInfinityQuery({
        onlyPublic: options.onlyPublic,
    });
    // цей хук робить запит на сервер для отримання категорій по їх id (використовується для відображення категорій, які вже вибрані)
    const idsQuery = useDocCategoryListByIdsQuery(options.ids || []);

    const groupedOptions = useMemo(() => {
        // якщо активний пошук підставляємо результати пошукового запиту, якщо ні - результати пагінацією
        const allData = search
            ? searchQuery.data?.documentCategories || []
            : infiniteQuery.data?.pages.flatMap(
                  (page) => page.documentCategories,
              ) || [];

        if (!search && idsQuery.data) {
            idsQuery.data.documentCategories.forEach((category) => {
                if (!allData.find((item) => item.id === category.id)) {
                    allData.push(category);
                }
            });
        }

        const iternalCategories = allData.filter((item) =>
            getIsInternalDocCategory(item),
        );

        const externalCategories = allData.filter(
            (item) => !getIsInternalDocCategory(item),
        );

        return [
            {
                label: t`Зовнішні типи`,
                options: composeCategoryOptionList(externalCategories),
            },
            {
                label: t`Внутрішні типи`,
                options: composeCategoryOptionList(iternalCategories),
            },
        ];
    }, [infiniteQuery.data, searchQuery.data, search, idsQuery.data]);

    const loadNextPage = async () => {
        if (infiniteQuery.hasNextPage) {
            await infiniteQuery.fetchNextPage();
        }
    };

    return {
        infiniteQuery,
        searchQuery,
        idsQuery,
        groupedOptions,
        loadNextPage,
    };
};
