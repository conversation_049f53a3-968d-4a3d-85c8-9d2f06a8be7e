import React from 'react';
import { Controller, FormProvider } from 'react-hook-form';

import { <PERSON>ert, Button, FlexBox, TextInput, Title } from '@vchasno/ui-kit';

import { useEditTemplateForm } from 'components/DocumentCreationCompanyTemplates/EditModal/form';
import { EditTemplateForm } from 'components/DocumentCreationCompanyTemplates/EditModal/types';
import { useEditTemplateMutation } from 'components/DocumentCreationCompanyTemplates/EditModal/useEditTemplateMutation';
import TemplateCategoryController from 'components/DocumentCreationCompanyTemplates/TemplateCategoryController';
import { ApiErrorResponseBody } from 'components/DocumentEdit/types';
import { getApiErrorMessage } from 'components/DocumentEdit/utils';
import { t } from 'ttag';
import Popup from 'ui/popup/popup';

import { useEditModal } from './EditModalProvider';

const EditModal: React.FC = () => {
    const { item, close } = useEditModal();
    const methods = useEditTemplateForm({
        title: item?.title || '',
        category: item?.category || null,
    });
    const editTemplateMutation = useEditTemplateMutation();

    const onSubmit = async (data: EditTemplateForm) => {
        if (!item) {
            return;
        }
        const editPayload = {
            title: data.title,
            category_id: data.category === null ? null : Number(data.category),
        };

        await editTemplateMutation
            .mutateAsync([item.id, editPayload])
            .then(close);
    };

    return (
        <Popup onClose={close} active={Boolean(item)} rounded>
            <Title level={3}>{t`Перейменувати`}</Title>
            <form
                id="template-edit-form"
                onSubmit={methods.handleSubmit(onSubmit)}
                style={{
                    marginTop: 24,
                    minHeight: 300,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 10,
                    width: '360px',
                }}
            >
                <Controller
                    name="title"
                    control={methods.control}
                    render={({ field, fieldState }) => (
                        <TextInput
                            {...field}
                            label={t`Назва`}
                            wide
                            error={fieldState.error?.message}
                        />
                    )}
                />
                <FormProvider {...methods}>
                    <TemplateCategoryController
                        wide
                        isClearable
                        label={t`Тип`}
                        hideEmptyMeta
                    />
                </FormProvider>

                {editTemplateMutation.isError && (
                    <Alert type="error" wide>
                        {getApiErrorMessage(
                            editTemplateMutation.error as ApiErrorResponseBody,
                        )}
                    </Alert>
                )}
            </form>
            <FlexBox>
                <Button
                    wide
                    size="lg"
                    theme="secondary"
                    onClick={close}
                >{t`Скасувати`}</Button>
                <Button
                    wide
                    form="template-edit-form"
                    type="submit"
                    loading={methods.formState.isSubmitting}
                    disabled={
                        !methods.formState.isValid &&
                        methods.formState.isSubmitted
                    }
                    size="lg"
                    theme="primary"
                >{t`Зберегти`}</Button>
            </FlexBox>
        </Popup>
    );
};

export default EditModal;
