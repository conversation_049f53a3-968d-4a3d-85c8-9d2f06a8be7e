import { useMutation, useQueryClient } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import { GET_CREATION_TEMPLATE_LIST } from 'lib/queriesConstants';
import { uploadFileToTemplates } from 'services/creationTemplates';
import { t } from 'ttag';

export const useUploadFileToTemplateMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (params: Parameters<typeof uploadFileToTemplates>) =>
            uploadFileToTemplates(...params),
        onSuccess: () => {
            snackbarToast.success(t`Шаблон успішно імпортовано`);
            queryClient.invalidateQueries([GET_CREATION_TEMPLATE_LIST]);
        },
        onError: () => {
            snackbarToast.error(t`Помилка імпорту шаблону`);
        },
    });
};
