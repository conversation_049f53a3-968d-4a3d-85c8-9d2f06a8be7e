import { useTemplateMakeFavoriteMutation } from 'components/DocumentCreationCompanyTemplates/TemplateList/useTemplateMakeFavoriteMutation';
import { useTemplateMakeUnFavoriteMutation } from 'components/DocumentCreationCompanyTemplates/TemplateList/useTemplateMakeUnFavoriteMutation';
import eventTracking from 'services/analytics/eventTracking';
import { CreationTemplate } from 'services/creationTemplates';

interface FavoriteHandlerParams {
    onFavoriteChange?: (isFavorite: boolean) => void;
    analyticsAddFavoriteEvent?: Parameters<
        typeof eventTracking.sendToGTMV4
    >[0]['event'];
}

export const useFavoriteHandler = (params: FavoriteHandlerParams = {}) => {
    const templateMakeFavoriteMutation = useTemplateMakeFavoriteMutation();
    const templateMakeUnFavoriteMutation = useTemplateMakeUnFavoriteMutation();

    return (item: CreationTemplate) => async () => {
        item.isFavorite
            ? templateMakeUnFavoriteMutation.mutate([item.id], {
                  onSuccess: () => {
                      params.onFavoriteChange?.(false);
                  },
              })
            : templateMakeFavoriteMutation.mutate([item.id], {
                  onSuccess: () => {
                      params.onFavoriteChange?.(true);
                      if (params.analyticsAddFavoriteEvent) {
                          // клік на видалити з обраних
                          eventTracking.sendToGTMV4({
                              event: params.analyticsAddFavoriteEvent,
                          });
                      }
                  },
              });
    };
};
