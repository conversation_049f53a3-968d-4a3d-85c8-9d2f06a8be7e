import { useMutation, useQueryClient } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import { GET_CREATION_TEMPLATE_LIST } from 'lib/queriesConstants';
import { removeCreationTemplateFromFavorite } from 'services/creationTemplates';
import { t } from 'ttag';

export const useTemplateMakeUnFavoriteMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (
            params: Parameters<typeof removeCreationTemplateFromFavorite>,
        ) => removeCreationTemplateFromFavorite(...params),
        onSuccess: () => {
            snackbarToast.info(t`Шаблон успішно видалено з обраних`);
            queryClient.invalidateQueries([GET_CREATION_TEMPLATE_LIST]);
        },
        onError: () => {
            snackbarToast.error(t`Не вдалося видалити шаблон з обраних`);
        },
    });
};
