import type { CollaboraIframeFont } from './types';

interface FontConfig {
    fontName: string;
    fontUrl: string;
}

export const fontConfigMap: Record<CollaboraIframeFont, FontConfig> = {
    Roboto: {
        fontName: 'Roboto, sans-serif',
        fontUrl:
            'https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap',
    },
};
