import React from 'react';
import { useSelector } from 'react-redux';
import { Redirect, Switch } from 'react-router-dom';

import Rate from 'components/CheckoutRates/Web/Rate/Rate';
import {
    getIsAnyPayRate,
    getIsCompanyHasOnlyFreeRate,
} from 'selectors/app.selectors';
import {
    getCheckoutRates,
    getFreeRateSelector,
    getWebRatesToExtend,
    getWebRatesToPurchase,
} from 'selectors/checkout.selectors';

import { RATES } from '../constants';

import css from './Web.css';

const Web: React.FC = () => {
    const rates = useSelector(getCheckoutRates);
    const ratesToPurchase = useSelector(getWebRatesToPurchase);
    const ratesToExtend = useSelector(getWebRatesToExtend);
    const isCompanyHasOnlyFreeRate = useSelector(getIsCompanyHasOnlyFreeRate);
    const freeRate = useSelector(getFreeRateSelector);
    const isAnyPayRate = useSelector(getIsAnyPayRate);

    if (rates && !ratesToPurchase.length && !ratesToExtend.length) {
        return (
            <Switch>
                <Redirect to={`/app/checkout-rates/${RATES.integration}`} />
            </Switch>
        );
    }

    return (
        <div>
            {isCompanyHasOnlyFreeRate && (
                <div className={css.rate}>
                    <Rate
                        isExtendRate
                        rate={{
                            ...freeRate,
                            ...(isAnyPayRate && { documents_to_send: 0 }),
                        }}
                    />
                </div>
            )}
            {ratesToExtend.map((rate) => (
                <div className={css.rate} key={rate.rate}>
                    <Rate isExtendRate rate={rate} />
                </div>
            ))}
            {ratesToPurchase.map((rate, index) => (
                <div className={css.rate} key={rate.rate}>
                    <Rate rate={rate} isRecommended={index === 0} />
                </div>
            ))}
        </div>
    );
};

export default Web;
