import { useSelector } from 'react-redux';

import { getCurrentUserRole } from 'selectors/app.selectors';

import { CommonRolePermissions } from '../../types/user';

export const usePermissionHasCurrentUserRoleFilter = () => {
    const currentRole = useSelector(getCurrentUserRole);

    return (permission: CommonRolePermissions): boolean => {
        return Boolean(currentRole.isAdmin || currentRole[permission]);
    };
};
