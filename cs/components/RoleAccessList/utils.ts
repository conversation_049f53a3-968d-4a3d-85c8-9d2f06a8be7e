import { roleAdminPermissionList } from 'components/RoleAccessList/lists';
import { t } from 'ttag';
import {
    AdminRole,
    AdministrationRolePermissions,
    CommonRolePermissions,
    IRole,
} from 'types/user';

export const isEmployeeRoleOperationAdmin = (
    role: Partial<AdminRole & AdministrationRolePermissions>,
): boolean => {
    if (!role || role.isAdmin) {
        // isAdmin - це основна адміністративна роль, яка вища за операційного адміністратора та не тотожна їй
        return false;
    }

    // Якщо у ролі є хоча б одне з прав адміністратора, то це Операційний менеджер
    return roleAdminPermissionList.some((permission) => role[permission]);
};

export const getEmployeeAdminTitle = (
    role: Partial<AdminRole & AdministrationRolePermissions>,
): string => {
    if (role?.isAdmin) {
        return t`Адміністратор`;
    }

    if (isEmployeeRoleOperationAdmin(role)) {
        return t`Операційний менеджер`;
    }

    return '';
};

export const getRoleAccessPermissionWarning = (
    permission: CommonRolePermissions,
    employeeRole: IRole,
) => {
    return (
        permission === 'canDownloadActions' &&
        !(
            employeeRole.canViewDocument && employeeRole.canViewPrivateDocument
        ) &&
        !employeeRole.canDownloadActions
    );
};
