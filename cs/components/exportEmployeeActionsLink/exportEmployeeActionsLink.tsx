import React, { FC } from 'react';
import { useDispatch } from 'react-redux';

import { QUERY_DATE_FORMAT } from 'lib/constants';
import moment from 'moment';
import io from 'services/io';
import { t } from 'ttag';

import notificationCenterActionCreators from '../notificationCenter/notificationCenterActionCreators';

import PseudoLink from '../ui/pseudolink/pseudolink';

const TODAY = moment().format(QUERY_DATE_FORMAT);
const DAY_IN_PREVIOUS_MONTH = moment()
    .subtract(29, 'days')
    .format(QUERY_DATE_FORMAT);

interface ExportEmployeeActionsLinkProps {
    className?: string;
}

const ExportEmployeeActionsLink: FC<
    React.PropsWithChildren<ExportEmployeeActionsLinkProps>
> = (props) => {
    const dispatch = useDispatch();

    const onExport = async (): Promise<void> => {
        try {
            await io.post('/internal-api/user-actions/request-report', {
                date_from: DAY_IN_PREVIOUS_MONTH,
                date_to: TODAY,
            });
            dispatch(
                notificationCenterActionCreators.addNotification({
                    title: t`Формування дій користувачів`,
                    type: 'text',
                    textType: 'info',
                    text: t`Історія дій формується, на вашу пошту буде відправлено лист з прикріпленими даними`,
                    showCloseButton: true,
                    autoClose: 5000,
                }),
            );
        } catch (err) {
            dispatch(
                notificationCenterActionCreators.addNotification({
                    title: t`Сталася помилка`,
                    type: 'text',
                    textType: 'error',
                    text: err?.reason || err?.message || err || '',
                    autoClose: 5000,
                }),
            );
        }
    };

    return (
        <>
            <PseudoLink {...props} onClick={() => onExport()}>
                {t`Надіслати на пошту історію дій користувачів за останні 30 днів`}
            </PseudoLink>
        </>
    );
};

export default ExportEmployeeActionsLink;
