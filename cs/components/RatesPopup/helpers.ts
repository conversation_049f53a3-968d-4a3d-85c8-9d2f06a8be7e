import { UNLIMITED_RATES_SET } from 'services/billing/constants';
import { RateStatus } from 'services/enums';

import { BillingAcount, CompanyRate } from '../../types/billing';

import eventTracking from '../../services/analytics/eventTracking';

export const sendShowPopupAnalytics = (label: string) => {
    eventTracking.sendToGTM({
        category: 'tariff_limit_pop-up',
        action: 'show',
        label,
    });
};

export const sendPopupButtonClickAnalytics = () => {
    eventTracking.sendToGTM({
        category: 'tariff_limit_pop-up',
        action: 'tariff_button_click',
    });
};

export const getRateDocumentsLeft = (
    rate: CompanyRate | Pick<CompanyRate, 'rate'>,
    billingAccounts: BillingAcount[],
) =>
    billingAccounts?.reduce(
        (acc, billingAccount) =>
            billingAccount.rate === rate?.rate
                ? acc + billingAccount.unitsLeft
                : acc,
        0,
    );

export const getRateTotalDocuments = (
    rate: CompanyRate | Pick<CompanyRate, 'rate'>,
    billingAccounts: BillingAcount[],
    rateStatus?: RateStatus,
    initialValue = 0,
) =>
    billingAccounts
        ?.filter((billingAccount) =>
            rateStatus ? rateStatus === billingAccount.status : true,
        )
        .reduce(
            (acc, billingAccount) =>
                billingAccount.rate === rate?.rate
                    ? acc + billingAccount.units
                    : acc,
            initialValue,
        );

export const getLeftDocumentsPercent = (
    rate: CompanyRate,
    billingAccounts: BillingAcount[],
) => {
    if (rate && UNLIMITED_RATES_SET.has(rate?.rate)) {
        return null;
    }

    const totalDocuments = getRateTotalDocuments(rate, billingAccounts);

    const leftDocuments = getRateDocumentsLeft(rate, billingAccounts);

    return Math.floor(leftDocuments && (leftDocuments * 100) / totalDocuments);
};

// Тут якась колізія з isRateDocumentsTerminate з cs/services/billing.js - досить схожий функціонал і назва
export const getIsRateDocumentsTerminate = (
    rate: CompanyRate | Pick<CompanyRate, 'rate'>,
    billingAccounts: BillingAcount[],
    limit?: number,
) => {
    if (rate && UNLIMITED_RATES_SET.has(rate?.rate)) {
        return false;
    }

    const totalDocuments = getRateTotalDocuments(rate, billingAccounts);

    // Якщо значення 0, то не потрібно перевіряти (Безліміт кількість документів)
    if (!totalDocuments) {
        return false;
    }

    const leftDocuments = getRateDocumentsLeft(rate, billingAccounts);

    const terminateLimit = limit ?? (totalDocuments * 10) / 100;

    return Boolean(
        typeof leftDocuments === 'number' && leftDocuments < terminateLimit,
    );
};

export const getIsRateDocumentsExpired = (
    rate: CompanyRate,
    billingAccounts: BillingAcount[],
) =>
    rate &&
    !UNLIMITED_RATES_SET.has(rate?.rate) &&
    !getRateDocumentsLeft(rate, billingAccounts);
