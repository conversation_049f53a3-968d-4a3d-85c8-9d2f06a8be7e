import { Thunk } from '../../types';
import { IRole } from '../../types/user';

import actions from './tagsAccessPopupActions';

import { isTagAlreadyInList, isTagNameAlreadyInList } from '../../lib/helpers';

import { ITag } from '../ui/tags/tagsTypes';

import { getCurrentCompanyRoles } from '../../services/user';
import { User } from './tagsAccessPopupTypes';

interface DerivedTag {
    id: string;
}
interface NewTag {
    name: string;
}

export default (
    tag: string,
    selector:
        | 'additionalFieldsAccessPopup'
        | 'additionalFieldsAccessPopup'
        | 'tagsAccessPopup',
) => {
    const onOpenPopup = (role?: IRole): Thunk => {
        return (dispatch) => {
            const presetUsersData = role ? [role] : [];
            const presetTagsData =
                role && role.tags && role.tags.length ? role.tags : [];
            dispatch({
                type: actions.ACCESS_POPUP__OPEN,
                tags: presetTagsData,
                usersList: presetUsersData,
                tag,
            });
        };
    };

    const onCloseSuggestions = (): Thunk => {
        return (dispatch) => {
            dispatch({
                type: actions.ACCESS_POPUP__CLOSE_SUGGESTIONS_TAGS,
                tag,
            });
        };
    };

    const onSuggestionClick = (suggestion: ITag): Thunk => {
        return (dispatch, getState) => {
            const {
                [selector]: { selectedTags, tags = [] },
            } = getState();
            const isTagInList = isTagAlreadyInList(suggestion.id, [
                ...selectedTags,
                ...tags,
            ]);
            const updatedSelectedTags: ITag[] | undefined = isTagInList
                ? selectedTags
                : [...selectedTags, suggestion];

            dispatch({
                type: actions.ACCESS_POPUP__CHANGE_SELECTED_LIST,
                selectedTags: updatedSelectedTags,
                tag,
            });

            dispatch(onCloseSuggestions());
        };
    };

    const onDeleteTagFromList = ({ id, name }: ITag): Thunk => {
        return (dispatch, getState) => {
            const {
                [selector]: { newTags, selectedTags, tagsToDelete, tags },
            } = getState();

            // currently the logic is shared between Tags and AdditionalFields
            // which do not coalesce into one type
            const derivedSelectedTags: Array<DerivedTag> = selectedTags;

            const isSelectedTag: DerivedTag | undefined =
                derivedSelectedTags &&
                derivedSelectedTags.find((item: DerivedTag) => item.id === id);
            if (isSelectedTag) {
                const updatedSelectedTags: DerivedTag[] | undefined =
                    derivedSelectedTags &&
                    derivedSelectedTags.filter(
                        (item: DerivedTag) => item.id !== id,
                    );

                dispatch({
                    type: actions.ACCESS_POPUP__CHANGE_SELECTED_LIST,
                    selectedTags: updatedSelectedTags,
                    tag,
                });
            } else if (!id && newTags) {
                dispatch({
                    type: actions.ACCESS_POPUP__CHANGE_NEW_TAGS,
                    newTags:
                        newTags &&
                        newTags.filter((item: NewTag) => item.name !== name),
                    tag,
                });
            } else if (tags) {
                dispatch({
                    type: actions.ACCESS_POPUP__CHANGE_TAGS_TO_DELETE,
                    tagsToDelete: [...tagsToDelete, { id, name }],
                    selectedTags:
                        derivedSelectedTags &&
                        derivedSelectedTags.filter(
                            (item: DerivedTag) => item.id !== id,
                        ),
                    tags: tags.filter((item: ITag) => item.id !== id),
                    tag,
                });
            }
        };
    };

    const onClose = (): Thunk => {
        return (dispatch) => {
            dispatch({ type: actions.ACCESS_POPUP__CLOSE, tag });
        };
    };

    const onAddNewTag = (value: string): Thunk => {
        return (dispatch, getState) => {
            const { [selector]: state } = getState();
            const newTags = state.newTags;

            if (value) {
                dispatch({
                    type: actions.ACCESS_POPUP__CHANGE_NEW_TAGS,
                    newTags: isTagNameAlreadyInList(value, newTags)
                        ? newTags
                        : [...newTags, { id: '', name: value }],
                    tag,
                });
            }
            dispatch(onCloseSuggestions());
        };
    };

    const onCloseUsersSuggestions = (): Thunk => {
        return (dispatch) => {
            dispatch({
                type: actions.ACCESS_POPUP__CLOSE_USER_SUGGESTIONS,
                tag,
            });
        };
    };

    const onUsersSuggestionClick = (suggestion: User): Thunk => {
        return (dispatch, getState) => {
            const {
                [selector]: { usersList },
            } = getState();
            const isUserAlreadyInList = usersList
                .map((user: IRole) => user.id)
                .includes(suggestion.id);
            const updatedUsersList = isUserAlreadyInList
                ? usersList
                : [...usersList, suggestion];

            dispatch({
                type: actions.ACCESS_POPUP__CHANGE_USERS_LIST,
                usersList: updatedUsersList,
                tag,
            });
        };
    };

    const onRemoveUser = (id: string): Thunk => {
        return (dispatch, getState) => {
            const {
                [selector]: { usersList },
            } = getState();

            const updatedUsersList = usersList.filter((user: IRole) => {
                return user.id !== id;
            });

            dispatch({
                type: actions.ACCESS_POPUP__CHANGE_USERS_LIST,
                usersList: updatedUsersList,
                tag,
            });

            dispatch(onCloseUsersSuggestions());
        };
    };

    const onAccessValueChange = (value: string): Thunk => {
        return async (dispatch) => {
            dispatch({
                type: actions.ACCESS_POPUP__CHANGE_USER_VALUE,
                accessValue: value,
                tag,
            });
        };
    };

    const onAutosuggestUsers = (search: string): Thunk => {
        return async (dispatch, getState) => {
            const {
                app: {
                    currentUser: { currentRole },
                },
                [selector]: { usersList },
            } = getState();

            const { currentCompanyRoles } = await getCurrentCompanyRoles({
                search,
            });

            const alreadyInListIds =
                usersList.map((user: IRole) => user.id) || [];
            const currentUserData =
                currentCompanyRoles.length > 0 &&
                currentCompanyRoles.find(
                    (role: IRole) => role.id === currentRole.id,
                );
            const users = currentCompanyRoles.length
                ? currentCompanyRoles.filter(
                      (role: IRole) =>
                          role.id !== currentRole.id &&
                          !alreadyInListIds.includes(role.id),
                  )
                : [];

            const suggestionUsers =
                alreadyInListIds.includes(currentRole.id) || !currentUserData
                    ? users
                    : [currentUserData, ...users];
            if (suggestionUsers && suggestionUsers.length > 0) {
                dispatch({
                    type: actions.ACCESS_POPUP__SHOW_SUGGESTIONS_USERS,
                    suggestionUsers,
                    tag,
                });
            } else {
                dispatch({
                    type: actions.ACCESS_POPUP__CLOSE_USER_SUGGESTIONS,
                    suggestionUsers: [],
                    tag,
                });
            }
        };
    };

    return {
        onOpenPopup,
        onCloseSuggestions,
        onSuggestionClick,
        onDeleteTagFromList,
        onClose,
        onAddNewTag,
        onCloseUsersSuggestions,
        onUsersSuggestionClick,
        onRemoveUser,
        onAccessValueChange,
        onAutosuggestUsers,
    };
};
