import { IRole } from '../../types/user';

import { ITag } from '../ui/tags/tagsTypes';

export interface State {
    isActive: boolean;
    isDocumentPage: boolean;
    isLoading: boolean;
    isSuggestionsShown: boolean;
    isUsersSuggestionsShown: false;
    errorMessage: string;
    accessValue: string;
    title: string;
    allTags: ITag[];
    newTags: ITag[];
    docIds: string[];
    selectedTags: ITag[];
    suggestedTags: ITag[];
    suggestionUsers?: IRole[];
    tagsToDelete: ITag[];
    usersList: IRole[];
    tags?: ITag[];
}

export interface ActionsTypes {
    onAddNewTag: (value: string) => void;
    onAccessValueChange: (value: string) => void;
    onAutosuggestTags: (value: string) => void;
    onAutosuggestUsers: (value: string) => void;
    onClose: () => void;
    onCloseSuggestions: () => void;
    onCloseUsersSuggestions: () => void;
    onDeleteTagFromList: (tag: ITag) => void;
    onRemoveUser: (id: string) => void;
    onSubmit: () => void;
    onSuggestionClick: (tag: ITag) => void;
    onUsersSuggestionClick: (suggestion: User) => void;
}

export interface User {
    id: string;
    name: string;
    email: string;
}

export type Props = State & ActionsTypes;
