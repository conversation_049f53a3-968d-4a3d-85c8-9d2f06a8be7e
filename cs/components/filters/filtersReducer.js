import { QUERY_DATE_FORMAT } from 'lib/constants';
import { toDate } from 'lib/date';
import { getLocationQuery } from 'lib/url';
import { DateFilterPresets } from 'services/enums';

import actions from './filtersActions';

import { getCurrentSignFolders } from './utils';

let query = {};

// TODO: Browser API location usage is discouraged, consider obtainig location of react-router or not keeping copy of query
if (window && window.location) {
    query = getLocationQuery(window.location);
}

const initState = {
    isExportPopupActive: false,
    isNotDeliveredChecked: false,
    tags: [],
    selectedTags: [],
    query,
    dateFrom: query.date_from
        ? toDate(query.date_from, QUERY_DATE_FORMAT)
        : null,
    dateTo: query.date_to ? toDate(query.date_to, QUERY_DATE_FORMAT) : null,
    signFolder: getCurrentSignFolders(query),
    documentCategory: query.categories ? query.categories : null,
    reviewFolder: query.review_folder ? query.review_folder : null,
    dateFilterPreset: DateFilterPresets.ALL,
    isDeliveryFilterStateDisabled: false,
    isWithoutTagsChecked: query.without_tags === 'true' && !query.tag,
    amountGte: query.amount_gte || null,
    amountLte: query.amount_lte || null,
    isLoading: false,
    amountGteError: '',
    amountLteError: '',
};

const filtersReducer = (state = initState, action) => {
    switch (action.type) {
        case actions.FILTERS__MOUNT:
            return {
                ...state,
                isNotDeliveredChecked: action.isNotDeliveredChecked,
                tags: action.tags,
                selectedTags: action.selectedTags,
            };
        case actions.FILTERS__CHANGE_QUERY:
            return { ...state, query: action.query };
        case actions.FILTERS__SET_DATE_FROM:
            return { ...state, dateFrom: action.date };
        case actions.FILTERS__SET_DATE_TO:
            return { ...state, dateTo: action.date };
        case actions.FILTERS_CHANGE_AMOUNT_GTE:
            return {
                ...state,
                amountGte: action.amountGte,
                amountGteError: '',
                amountLteError: '',
            };
        case actions.FILTERS_CHANGE_AMOUNT_LTE:
            return {
                ...state,
                amountLte: action.amountLte,
                amountGteError: '',
                amountLteError: '',
            };
        case actions.FILTERS__SET_AMOUNT_ERROR:
            return {
                ...state,
                amountGteError: action.amountGteError,
                amountLteError: action.amountLteError,
            };
        case actions.FILTERS__SET_LOADING:
            return { ...state, isLoading: action.isLoading };

        case actions.FILTERS__SET_EXPORT_POPUP_STATE:
            return {
                ...state,
                isExportPopupActive: action.isExportPopupActive,
            };
        case actions.FILTERS__TOGGLE_NOT_DELIVERED_CHECKED:
            return {
                ...state,
                isNotDeliveredChecked: action.isNotDeliveredChecked,
            };
        case actions.FILTERS__TOGGLE_WITHOUT_TAGS_CHECKED:
            return {
                ...state,
                isWithoutTagsChecked: action.isWithoutTagsChecked,
            };
        case actions.FILTERS__SET_DELIVERY_FILTER_STATE:
            return {
                ...state,
                isDeliveryFilterStateDisabled:
                    action.isDeliveryFilterStateDisabled,
            };
        case actions.FILTERS__SET_SIGN_FILTER:
            return { ...state, signFolder: action.signFolder };
        case actions.FILTERS__SET_TYPE_BY_NAME:
            return { ...state, documentCategory: action.documentCategory };
        case actions.FILTERS__SET_REVIEW_FILTER:
            return { ...state, reviewFolder: action.reviewFolder };

        case actions.FILTERS__RELOAD_TAGS:
            return { ...state, tags: action.tags };
        case actions.FILTERS__SET_TAG_FILTER_STATE:
            return {
                ...state,
                selectedTags: action.selectedTags,
                isWithoutTagsChecked: false,
            };
        case actions.FILTERS__RESET_ALL_FILTERS:
            return initState;

        default:
            return state;
    }
};

export default filtersReducer;
