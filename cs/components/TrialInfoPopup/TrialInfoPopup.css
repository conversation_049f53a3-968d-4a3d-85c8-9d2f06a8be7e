.root {
    display: flex;
    max-width: 800px;
    min-height: 620px;
}

.title {
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;
    text-align: left;
    word-break: break-word;
    word-wrap: break-word;
}

.contentWrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
}

.sideMenu {
    padding: 0 40px;
    background-color: var(--grey-bg);
    border-bottom-left-radius: var(--border-radius);
    border-top-left-radius: var(--border-radius);
}

.mainContent {
    padding: 40px;
    margin: auto;
}

.text {
    margin-bottom: 28px;
    color: var(--slate-grey-color);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.centerBlock {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
}

.content {
    display: flex;
    min-height: 340px;
    flex: 1;
}

.buttonGetStarted {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
}

@media screen and (max-width: 560px) {
    .sideMenu {
        padding: 10px;
    }

    .mainContent {
        padding: 10px;
    }
}
