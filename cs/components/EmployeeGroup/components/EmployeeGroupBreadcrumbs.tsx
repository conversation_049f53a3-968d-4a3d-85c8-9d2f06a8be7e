import React, { FC } from 'react';
import { useSelector } from 'react-redux';

import { useEmployeeGroupContext } from 'components/EmployeeGroup/context';
import { c, t } from 'ttag';

import {
    getCurrentCompanyName,
    getCurrentUserRoleId,
} from '../../../selectors/app.selectors';

import Breadcrumbs from '../../ui/breadcrumbs/breadcrumbs';

const EmployeeGroupBreadcrumbs: FC = () => {
    const currentCompanyName = useSelector(getCurrentCompanyName);
    const currentUserRoleId = useSelector(getCurrentUserRoleId);
    const { initialGroupName } = useEmployeeGroupContext();

    const formattedGroupName = initialGroupName ? `"${initialGroupName}"` : '';

    const breadcrumbs = [
        {
            link: '/app/settings',
            name: t`Налаштування`,
        },
        {
            link: '/app/settings/companies',
            name: t`<PERSON><PERSON><PERSON> компанії`,
        },
        {
            link: `/app/settings/companies/${currentUserRoleId}`,
            name: c('companyName list').t`Компанія ${currentCompanyName}`,
        },
        {
            link: `/app/settings/groups`,
            name: t`Команда співробітників ${formattedGroupName}`,
        },
    ];

    return <Breadcrumbs items={breadcrumbs} />;
};

export default EmployeeGroupBreadcrumbs;
