import { useMutation } from '@tanstack/react-query';

import { queryClient } from 'lib/queries';
import {
    GET_EMPLOYEE_GROUP_LIST_QUERY,
    GET_EMPLOYEE_GROUP_QUERY,
} from 'lib/queriesConstants';
import { deleteEmployeeGroupMembers } from 'services/groups';

export const useDeleteEmployeeGroupMembersMutation = () => {
    return useMutation({
        mutationFn: (params: Parameters<typeof deleteEmployeeGroupMembers>) =>
            deleteEmployeeGroupMembers(...params),
        onSuccess: (_data, variables) => {
            const employeeGroupId = variables[0];

            queryClient.invalidateQueries({
                queryKey: [GET_EMPLOYEE_GROUP_LIST_QUERY],
            });
            queryClient.invalidateQueries({
                queryKey: [GET_EMPLOYEE_GROUP_QUERY, employeeGroupId],
                refetchType: 'none',
            });
        },
    });
};
