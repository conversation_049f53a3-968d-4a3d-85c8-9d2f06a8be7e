.root {
    box-sizing: border-box;
    padding: 40px 30px;
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
}

.header {
    margin-bottom: 35px;
}

.arrowBackIconContainer {
    position: relative;
    display: flex;
    width: 40px;
    height: 40px;
    align-items: center;
    justify-content: center;
    background-color: var(--grey-color);
    border-radius: 8px;
    cursor: pointer;
}

.arrowBackIconContainer svg {
    height: 10px;
    color: var(--grey-color);
}

.title {
    color: var(--content-color);
    font-size: 24px;
    font-weight: 500;
    line-height: 28px;
}

.membersCount {
    margin-top: 4px;
    color: var(--slate-grey-color);
    font-size: 14px;
}

.saveBtn span {
    font-size: 14px;
}

.inputWrapper input {
    font-size: 14px;
}

.alert {
    margin-top: 30px;
}

.boldText {
    font-weight: 500;
}

.addMembersTitle {
    margin-top: 40px;
    margin-bottom: 15px;
    color: var(--content-color);
    font-size: 16px;
    font-weight: 500;
    line-height: 18px;
}

.link {
    color: var(--link-color);
}
