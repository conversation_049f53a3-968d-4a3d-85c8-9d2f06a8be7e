import React from 'react';

import { redirect } from 'lib/navigation';
import { t } from 'ttag';
import Button from 'ui/button/button';

import css from './sharedDocumentAccessDenied.css';

const IMAGE_PATH = `${config.STATIC_HOST}/images/tetyanka7.png`;

const SharedDocumentViewAccessDenied = () => (
    <div className={css.root}>
        <div className={css.imageHolder}>
            <img src={IMAGE_PATH} alt="" />
        </div>
        <div className={css.main}>
            <h2
                className={css.title}
            >{t`Для доступу до документа необхідно авторизуватись.`}</h2>
            <div className={css.button}>
                <Button
                    typeContour
                    theme="blue"
                    onClick={() => redirect('/auth')}
                >
                    {t`Авторизуватись`}
                </Button>
            </div>
        </div>
    </div>
);

export default SharedDocumentViewAccessDenied;
