import { ProjectsPopoverProps } from '@vchasno/ui-kit';

export const enrichUrlWithUTM = (url: string) => {
    const link = new URL(url);

    link.searchParams.set('utm_source', 'vchasno');
    link.searchParams.set('utm_campaign', 'vchasno');

    return link.href;
};

export const getProjectUrlMap = () => {
    const urls: ProjectsPopoverProps['urls'] = {};

    if (config.EDI_HOST) {
        urls.edi = config.EDI_HOST;
    }

    if (config.KASSA_HOST) {
        urls.kasa = config.KASSA_HOST;
    }

    if (config.TTN_HOST) {
        urls.ttn = config.TTN_HOST;
    }

    if (config.KEP_HOST) {
        urls.kep = config.KEP_HOST;
    }

    return urls;
};
