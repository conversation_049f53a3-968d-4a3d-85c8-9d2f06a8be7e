import React, { useState } from 'react';

import SvgDots from 'icons/dots.svg';
import IconButton from 'ui/iconButton/iconButton';

import DocumentActionsSheet from '../DocumentActionsSheet';

const DocumentsAnotherActions: React.FC = () => {
    const [isOpenDocsActionsSheet, setIsOpenDocsActionsSheet] = useState(false);

    const onCloseDocsActionsSheet = () => {
        setIsOpenDocsActionsSheet(false);
    };

    const onOpenDocsActionsSheet = () => {
        setIsOpenDocsActionsSheet(true);
    };

    return (
        <>
            <IconButton svg={SvgDots} onClick={onOpenDocsActionsSheet} />
            <DocumentActionsSheet
                isOpen={isOpenDocsActionsSheet}
                onClose={onCloseDocsActionsSheet}
            />
        </>
    );
};

export default DocumentsAnotherActions;
