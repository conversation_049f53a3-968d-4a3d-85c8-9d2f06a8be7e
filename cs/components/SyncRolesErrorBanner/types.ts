import actions from './syncRolesErrorBannerActions';

export interface SyncRolesErrorInfo {
    code: string;
    reason: string;
    details?: Record<string, string>;
}
export interface SyncRolesErrorBannerState {
    displayStatus: 'hidden' | 'visible';
    edrpou: string;
    errorInfo: SyncRolesErrorInfo;
}

export type SyncRolesErrorBannerAction =
    | {
          type: typeof actions.SYNC_ROLES_ERROR_BANNER__SET_DISPLAY_STATUS;
          payload: SyncRolesErrorBannerState['displayStatus'];
      }
    | {
          type: typeof actions.SYNC_ROLES_ERROR_BANNER__SET_EDRPOU;
          payload: SyncRolesErrorBannerState['edrpou'];
      }
    | {
          type: typeof actions.SYNC_ROLES_ERROR_BANNER__SET_ERROR_INFO;
          payload: SyncRolesErrorBannerState['errorInfo'];
      }
    | {
          type: typeof actions.SYNC_ROLES_ERROR_BANNER__RESET;
          payload: never;
      };
