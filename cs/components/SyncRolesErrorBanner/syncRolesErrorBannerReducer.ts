import { SyncRolesErrorBannerAction, SyncRolesErrorBannerState } from './types';

import actions from './syncRolesErrorBannerActions';

import { DISPLAY_STATUSES } from './constants';

const initState: SyncRolesErrorBannerState = {
    displayStatus: DISPLAY_STATUSES.HIDDEN,
    edrpou: '',
    errorInfo: {
        code: '',
        reason: '',
    },
};

const syncRolesErrorBannerReducer = (
    state = initState,
    action: SyncRolesErrorBannerAction,
) => {
    switch (action.type) {
        case actions.SYNC_ROLES_ERROR_BANNER__SET_DISPLAY_STATUS:
            return {
                ...state,
                displayStatus: action.payload,
            };
        case actions.SYNC_ROLES_ERROR_BANNER__SET_EDRPOU:
            return {
                ...state,
                edrpou: action.payload,
            };
        case actions.SYNC_ROLES_ERROR_BANNER__SET_ERROR_INFO:
            return {
                ...state,
                errorInfo: action.payload,
            };
        case actions.SYNC_ROLES_ERROR_BANNER__RESET:
            return initState;
        default:
            return state;
    }
};

export default syncRolesErrorBannerReducer;
