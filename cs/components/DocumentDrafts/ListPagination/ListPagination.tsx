import React from 'react';

import { Pagination } from '@vchasno/ui-kit';

import { useDraftFetch } from 'components/DocumentDrafts/DraftFetchProvider';

const ListPagination: React.FC = () => {
    const { setPage, count, limit, offset } = useDraftFetch();

    return (
        <Pagination
            total={Math.ceil(count / limit)}
            scrollOnChange
            hideOnSinglePage
            current={Math.ceil(offset / limit) + 1}
            onChange={setPage}
        />
    );
};

export default ListPagination;
