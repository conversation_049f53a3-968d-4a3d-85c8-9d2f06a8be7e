import { useMutation, useQueryClient } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import { GET_DOCUMENT_DRAFT_LIST } from 'lib/queriesConstants';
import eventTracking from 'services/analytics/eventTracking';
import { deleteDraft } from 'services/creationTemplates';
import { t } from 'ttag';

export const useDeleteDraftMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (params: Parameters<typeof deleteDraft>) =>
            deleteDraft(...params),
        onSuccess: () => {
            snackbarToast.success(t`Чернетку успішно видалено!`);
            queryClient.invalidateQueries([GET_DOCUMENT_DRAFT_LIST]);
            eventTracking.sendToGTMV4({
                event: 'ec_draft_delete',
            });
        },
        onError: () => {
            snackbarToast.error(t`Помилка видалення чернетки!`);
        },
    });
};
