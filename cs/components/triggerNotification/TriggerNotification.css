.iconHolder {
    position: relative;
}

.button {
    position: relative;
    display: flex;
    width: 40px;
    height: 40px;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: inherit;
    cursor: pointer;
    transition: background-color 0.1s ease;
}

.button:hover {
    background-color: var(--grey-bg);
    color: inherit;
}

.button svg {
    width: 20px;
    height: 20px;
}

.root {
    width: 500px;
    box-sizing: border-box;
    padding: 16px 10px;
}

.blankMessageRoot {
    width: 300px;
}

.blankMessage {
    position: relative;
    padding: 14px 30px 14px 15px;
    background: var(--light-blue-bg);
    border-radius: 10px;
    color: var(--dark-pigeon-color);
    font-size: 14px;
    line-height: 16px;
}

.blankMessage::before {
    position: absolute;
    right: auto;
    bottom: 100%;
    left: 20px;
    display: block;
    width: 0;
    border-right: 7px solid transparent;
    border-bottom: 7px solid var(--light-blue-bg);
    border-left: 7px solid transparent;
    margin: auto;
    content: '';
}

.badge {
    display: table;
    width: 14px;
    height: 14px;
    background-color: var(--green-color);
    border-radius: 50%;
    color: #fff;
}

.badgeCount {
    display: table-cell;
    font-size: 9px;
    text-align: center;
    vertical-align: middle;
}

.buttonBadge {
    position: absolute;
    top: 7px;
    right: 5px;
}

.block {
    padding: 12px 20px;
    border-bottom: 1px solid var(--default-border);
}

.header {
    margin-bottom: 10px;
    margin-left: 10px;
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
}

.blankMessageRoot .header {
    margin-bottom: 15px;
}

.footer {
    margin-top: 17px;
    margin-left: 10px;
    font-size: 14px;
    font-weight: 400;
    line-height: 16px;
}

.footer a {
    color: #008adb;
}

.headerNotification {
    margin-bottom: 4px;
    margin-left: 10px;
    color: var(--grey-color);
}

.notification {
    padding: 17px 20px 10px;
    border: 1px solid #e5edf1;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    line-height: 16px;
    transition: background 0.1s ease-out;
}

.newNotification {
    border: 1px solid transparent;
    background: var(--grey-bg);
}

.notification:hover {
    background: var(--pigeon-bg);
}

.notificationTitle {
    font-weight: bold;
}

.notificationFooter {
    padding-top: 10px;
    border-top: 1px solid var(--default-border);
    margin-top: 15px;
}

.notificationDate {
    display: inline-block;
}

.notificationUrl {
    display: inline-block;
    padding-left: 16px;
    border-left: 1px solid var(--default-border);
    margin-left: 16px;
}

.popover {
    right: 0;
}
