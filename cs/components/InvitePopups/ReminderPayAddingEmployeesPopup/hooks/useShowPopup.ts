import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { differenceInDays } from 'date-fns';
import { getLocalStorageItem } from 'lib/webStorage';
import {
    getCurrentCompanyActiveEmployeesTrial,
    getCurrentCompanyEdrpou,
} from 'selectors/app.selectors';

import { isShowedReminderPayPopupName } from '../ReminderPayAddingEmployeesPopup.constants';
import {
    setDates,
    setShowedStateToStorage,
} from '../ReminderPayAddingEmployeesPopup.functions';
import { ReminderPayAddingEmployeesStorageProps } from '../ReminderPayAddingEmployeesPopup.types';

export const useShowPopup = () => {
    const edrpouCurrentCompany = useSelector(getCurrentCompanyEdrpou);
    const activeEmployeesTrial = useSelector(
        getCurrentCompanyActiveEmployeesTrial,
    );

    const [isOpen, setIsOpen] = useState(false);

    const { today, endDateTrial } = setDates(
        activeEmployeesTrial?.date_expiring || '',
    );

    const daysLeft = differenceInDays(endDateTrial?.getTime(), today.getTime());

    useEffect(() => {
        if (!edrpouCurrentCompany || !activeEmployeesTrial) {
            return;
        }

        const isShowed =
            (getLocalStorageItem(
                isShowedReminderPayPopupName,
            ) as ReminderPayAddingEmployeesStorageProps)?.[
                edrpouCurrentCompany
            ] || false;

        if (daysLeft <= 2 && !isShowed) {
            setShowedStateToStorage(edrpouCurrentCompany, true);
            setIsOpen(true);
        }
    }, [edrpouCurrentCompany, activeEmployeesTrial]);

    return {
        isOpen: isOpen,
        daysLeft: daysLeft,
        setIsOpen: setIsOpen,
    };
};
