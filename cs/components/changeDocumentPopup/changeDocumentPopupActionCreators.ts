import { ChangeEvent, FormEvent } from 'react';

import { toDate } from 'lib/date';
import {
    getIsReviewRequestElementGroup,
    getIsSignerGroup,
    isTagAlreadyInList,
    isTagNameAlreadyInList,
    moveArrayItem,
    sortByOrder,
} from 'lib/helpers';
import {
    DocumentParameter,
    DocumentSettingParameter,
} from 'services/documentFields/types';
import { validateDocumentParametersValues } from 'services/documentFields/utils';
import { getDocumentTemplates } from 'services/documentTemplates';
import {
    createDocumentTags,
    disconnectDocumentsAndTags,
} from 'services/documents/api';
import { updateDocument } from 'services/documents/ts/api';
import { Document, Signer } from 'services/documents/ts/types';
import {
    canAddSigners,
    getReviewProcess,
    getSignProcess,
} from 'services/documents/utils';
import {
    DocumentCategory,
    DocumentReviewProcess,
    DocumentSignProcess,
} from 'services/enums';
import { getEmployeeGroups } from 'services/groups';
import {
    getCurrentCompanyRoles,
    getCurrentCompanyTags,
    getTagsForDocumentFilter,
} from 'services/user';
import { c, t } from 'ttag';
import { ITag } from 'ui/tags/tagsTypes';

import { Thunk } from '../../types';
import { Nullable } from '../../types/general';
import {
    IRole,
    IUser,
    OrderedIRole,
    SignerReviewerGroup,
} from '../../types/user';
import { Template } from '../documentTemplates/types';

import documentActionCreators from '../document/documentActionCreators';
import documentListActionCreators from '../documentList/documentListActionCreators';
import notificationActionCreators from '../notificationCenter/notificationCenterActionCreators';
import actions from './changeDocumentPopupActions';

import { fieldSettingToParameters } from '../documentTemplates/utils';
import { getModifiedListFromTemplate } from '../uploader/utils';
import { getElementIsEmployeeGroup } from '../uploaderPopup/SignersReviewersAutosuggestionList/utils';
import {
    canEditReviewProcessType,
    canEditSignProcess,
    getIsRoleSignedDocument,
    getSuggestion,
} from './utils';

import eventTracking from '../../services/analytics/eventTracking';
import { SignerReviewerType } from '../uploader/constants';
import {
    MenuID,
    RoleWithSignedDocument,
    State,
} from './changeDocumentPopupTypes';
import { EDITABLE_INFO_STATUSES } from './constants';
import { getDocumentReviewSettings, getDocumentUpdatedData } from './helper';

function onClose(): Thunk {
    return (dispatch) => {
        dispatch({ type: actions.CHANGE_DOCUMENT_POPUP__CLOSE });
    };
}

function getPreparedDocumentDataForUpdate(): Thunk {
    return async (dispatch, getState) => {
        const {
            app: { documentFields, currentUser },
            changeDocumentPopup: {
                docAmount,
                docDate,
                docNumber,
                docTitle,
                docCategory,
                documents,
                signProcess,
                signersList,
                previousSignersList,
                newTags,
                selectedTags,
                documentParameters,
                previousSignProcess,
                reviewersList,
                isReviewRequired,
                reviewProcess,
                deleteReviewersList,
                viewersList,
            },
        } = getState();

        // TODO remove this sort, when change list logic to logic from AddReviewersPopup
        const getIsUserSignedSomeDocument = (role: IRole) =>
            documents.some((document) =>
                getIsRoleSignedDocument(role, document),
            );

        function sortSignersListByRoleSignedDocument(
            list: RoleWithSignedDocument[],
        ) {
            const signedDocs = list.filter((item) => item.isRoleSignedDocument);
            const unsignedDocs = list.filter(
                (item) => !item.isRoleSignedDocument,
            );
            return signedDocs.concat(unsignedDocs);
        }

        const signersListWithSignedValue = signersList.map((item) => ({
            ...item,
            isRoleSignedDocument: getIsUserSignedSomeDocument(item),
        }));

        const sortedSigners = sortSignersListByRoleSignedDocument(
            signersListWithSignedValue,
        );

        const signersRolesAndGroups = sortedSigners.map((item) => ({
            id: item.id,
            type: getElementIsEmployeeGroup(item)
                ? SignerReviewerType.GROUP
                : SignerReviewerType.ROLE,
        }));
        const isParallelSigning =
            signProcess === DocumentSignProcess.PARALLEL_PROCESS_TYPE;

        const sendInfo =
            documents.length === 1 &&
            documents.every((doc) =>
                EDITABLE_INFO_STATUSES.includes(doc.status),
            );
        const sendSigners = documents.every((doc) => {
            return canAddSigners(doc, currentUser);
        });

        const tagSettings: Record<string, Record<string, string[]>> = {};

        const reviewSettings = {
            reviewers: reviewersList.map((signer) => ({
                id: signer.id,
                type: getElementIsEmployeeGroup(signer)
                    ? SignerReviewerType.GROUP
                    : SignerReviewerType.ROLE,
            })),
            is_required: isReviewRequired,
            is_ordered:
                reviewProcess === DocumentReviewProcess.ORDERED_PROCESS_TYPE,
        };
        const viewersSettings = viewersList.length
            ? { add_viewers: viewersList.map((role) => role.id) }
            : null;

        // Validate extra data
        let parameters: Nullable<DocumentSettingParameter[]>;
        try {
            parameters = validateDocumentParametersValues(
                documentFields,
                documentParameters,
            );
        } catch (err) {
            dispatch({
                type: actions.CHANGE_DOCUMENT_POPUP__SET_SUBMIT_INFO,
                isSubmitting: false,
                errorMessage: err.message,
            });
            return;
        }
        const doc = documents[0];
        const documentId = doc.id;
        const isParallelPreviousSigning =
            previousSignProcess === DocumentSignProcess.PARALLEL_PROCESS_TYPE;

        // No change for parallel sign process
        const sameSignersParallel =
            isParallelSigning &&
            signersList.length === previousSignersList.length &&
            signersList.every((value) => {
                return previousSignersList.includes(value);
            }) &&
            previousSignersList.every((value) => {
                return signersList.includes(value);
            });
        // No change for ordered sign process
        const sameSignerOrdered =
            !isParallelSigning && signersList === previousSignersList;

        const hasSignersChanges =
            isParallelSigning !== isParallelPreviousSigning ||
            !(sameSignersParallel || sameSignerOrdered);

        const docAmountPenni = docAmount && (+docAmount * 100).toFixed();

        const documentSetting = sendInfo
            ? {
                  date_document: docDate,
                  number: docNumber,
                  title: docTitle,
                  category: docCategory,
                  amount: docAmountPenni,
              }
            : null;

        if (newTags.length) {
            const documentsIds = documents.map((docItem) => docItem.id);

            try {
                await createDocumentTags({
                    documents_ids: documentsIds,
                    names: newTags.map((newTag) => newTag.name),
                });
                newTags.forEach((tag) =>
                    eventTracking.sendToGTM({
                        category: 'tag',
                        action: 'create_tag',
                        label: `${tag.name}`,
                    }),
                );
            } catch (err) {
                dispatch({
                    type: actions.CHANGE_DOCUMENT_POPUP__SET_SUBMIT_INFO,
                    isSubmitting: false,
                    errorMessage: err.message,
                });
                return;
            }
        }

        // user can delete tags only for 1 document
        // for documents > 1 can only add tags
        if (documents.length === 1) {
            const tagsToDelete = doc.tags.filter(
                (tag) =>
                    !selectedTags.some(
                        (selectedTag) => selectedTag.id === tag.id,
                    ),
            );
            const tagsIdsToDelete = tagsToDelete.map((tag) => tag.id);
            if (tagsToDelete.length) {
                await disconnectDocumentsAndTags({
                    documents_ids: [documentId],
                    tags_ids: tagsIdsToDelete,
                });
                tagsToDelete.forEach((tag) => {
                    eventTracking.sendEvent('tag', 'delete', `${tag.name}`);
                });
            }
            if (selectedTags.length > 0) {
                const filteredDocTags = doc.tags.filter(
                    (tag: ITag) => !tagsIdsToDelete.includes(tag.id),
                );
                tagSettings.tags_settings = {
                    tags: [
                        ...new Set([
                            ...selectedTags.map((item) => item.id),
                            ...filteredDocTags.map((item) => item.id),
                        ]),
                    ],
                };
            }
        }

        const initDocParameters: DocumentParameter[] = doc.parameters || [];
        const formDocFieldsIds = documentParameters.map(
            (parameter) => parameter.fieldId,
        );
        const deletedFields = initDocParameters
            .map((parameter) => parameter.fieldId)
            .filter((fieldId) => !formDocFieldsIds.includes(fieldId));

        return {
            ...tagSettings,
            update_sign_process: sendSigners && hasSignersChanges,
            signer_entities: signersRolesAndGroups,
            parallel_signing: isParallelSigning,
            document_settings: documentSetting,
            reviews_settings: getDocumentReviewSettings(
                doc,
                reviewSettings,
                deleteReviewersList,
            ),
            viewers_settings: viewersSettings,
            parameters_settings: parameters
                ? {
                      update_parameters: parameters,
                      delete_parameters: deletedFields,
                  }
                : null,
        };
    };
}

function onSubmit(evt: FormEvent, sendEventTracking?: VoidFunction): Thunk {
    return async (dispatch, getState) => {
        evt.preventDefault();

        const {
            changeDocumentPopup: {
                activeDocumentTemplate,
                documents,
                isDocumentPage,
                selectedTags,
                selectedItemIndex,
                prevDocumentEditableData,
            },
        } = getState();

        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__SET_SUBMIT_INFO,
            isSubmitting: true,
            errorMessage: '',
        });

        const sendInfo =
            documents.length === 1 &&
            documents.every((doc) =>
                EDITABLE_INFO_STATUSES.includes(doc.status),
            );

        const tagSettings: Record<string, Record<string, string[]>> = {};

        // Case for one document
        if (documents.length === 1) {
            const doc = documents[0];
            const documentId = doc.id;

            const documentData = await dispatch(
                getPreparedDocumentDataForUpdate(),
            );
            const updatedDocumentData = getDocumentUpdatedData(
                documentData,
                prevDocumentEditableData,
            );

            try {
                await updateDocument(documentId, updatedDocumentData);
                const parameters =
                    updatedDocumentData.parameters_settings?.update_parameters;

                if (selectedItemIndex === 'extraFields' && parameters) {
                    eventTracking.sendEvent(
                        'document',
                        'fill-additional-fields',
                    );
                }

                if (activeDocumentTemplate) {
                    eventTracking.sendEvent(
                        'sign-and-coordination-script',
                        'assign-to-doc-manually',
                    );
                }
            } catch (error) {
                dispatch({
                    type: actions.CHANGE_DOCUMENT_POPUP__SET_SUBMIT_INFO,
                    isSubmitting: true,
                    errorMessage: error.message,
                });
                return;
            }

            if (sendEventTracking) {
                sendEventTracking();
            }

            if (isDocumentPage) {
                dispatch(
                    documentActionCreators.onLoadDocument(documentId, false),
                );
            } else {
                dispatch(documentListActionCreators.onLoadDocuments());
            }

            dispatch(onClose());
            return;
        }

        // Case for editing multiple documents
        const [updateNotification] = dispatch(
            notificationActionCreators.addNotification({
                title: t`Оновлення документів`,
                type: 'progress',
                progressTotalCount: documents.length,
            }),
        );

        // Update parameters only in case when user select template with parameters
        const shouldUpdateParameters =
            activeDocumentTemplate?.fieldsSettings?.fields.length;

        let processedDocuments = 0;
        let failedDocuments = 0;

        const documentData = await dispatch(getPreparedDocumentDataForUpdate());
        const updatedDocumentData = getDocumentUpdatedData(
            documentData,
            prevDocumentEditableData,
        );
        const parameters =
            updatedDocumentData.parameters_settings?.update_parameters;
        for (const doc of documents) {
            if (selectedTags.length > 0) {
                tagSettings.tags_settings = {
                    tags: [
                        ...new Set([
                            ...selectedTags.map((item) => item.id),
                            ...doc.tags.map((item) => item.id),
                        ]),
                    ],
                };
            }

            try {
                await updateDocument(doc.id, {
                    ...updatedDocumentData,
                    ...tagSettings,
                    update_info: sendInfo,
                    parameters_settings:
                        shouldUpdateParameters && parameters
                            ? {
                                  update_parameters: parameters,
                                  delete_parameters: [],
                              }
                            : null,
                });

                if (shouldUpdateParameters && parameters) {
                    eventTracking.sendEvent(
                        'document',
                        'fill-additional-fields',
                    );
                }

                processedDocuments += 1;

                if (activeDocumentTemplate) {
                    eventTracking.sendEvent(
                        'sign-and-coordination-script',
                        'assign-to-doc-manually',
                    );
                }
            } catch (error) {
                failedDocuments += 1;
            }
            updateNotification({
                progressCurrentCount: processedDocuments + failedDocuments,
            });
        }

        const messageText = failedDocuments
            ? c('update document')
                  .t`${processedDocuments} з ${documents.length} документів було оновлено`
            : t`Документи успішно оновлено`;

        updateNotification({
            type: 'text',
            textType: failedDocuments ? 'warning' : 'success',
            text: messageText,
            showCloseButton: true,
            autoClose: 7000,
        });

        dispatch(documentListActionCreators.onLoadDocuments());

        dispatch(onClose());
    };
}

function onChangeDate(date: Date): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__INFO_CHANGE_DATE,
            docDate: date,
        });
    };
}

function onChangeField(evt: ChangeEvent): Thunk {
    return (dispatch) => {
        const target = evt.target as HTMLInputElement;
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__INFO_CHANGE_FIELD,
            [target.name]: target.value,
        });
    };
}

function onChangeAmount(value: string): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__INFO_CHANGE_FIELD,
            docAmount: value,
        });
    };
}

function onChangeCategory(category: string | number): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__ORDER_CHANGE_CATEGORY,
            docCategory: category,
        });
    };
}

function onInitPrevDocumentData(): Thunk {
    return async (dispatch) => {
        const prevDocumentEditableData = await dispatch(
            getPreparedDocumentDataForUpdate(),
        );
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__INIT_PREV_DOCUMENT_DATA,
            prevDocumentEditableData,
        });
    };
}

function onShowMultipleDocuments(
    documents: Document[],
    isDocumentPage: boolean,
    selectedItemIndex?: MenuID,
): Thunk {
    return async (dispatch, getState) => {
        const {
            app: {
                currentUser: { currentRole },
            },
        } = getState();
        const reviewProcessEditable = canEditReviewProcessType(
            documents,
            currentRole,
        );

        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__SHOW,
            isDocumentPage,
            documents,
            currentCompany: currentRole.company,
            selectedItemIndex: selectedItemIndex || 'signers',
            signersList: [],
            reviewProcessEditable,
        });
    };
}

function onShowSingleDocument(
    documents: Document[],
    isDocumentPage: boolean,
    selectedItemIndex?: MenuID,
    isActive?: boolean,
): Thunk {
    return async (dispatch, getState) => {
        const {
            app: {
                currentUser: { currentRole },
            },
        } = getState();
        const reviewProcessEditable = canEditReviewProcessType(
            documents,
            currentRole,
        );
        const doc = documents[0];
        const signers: Signer[] = (doc && doc.signers) || [];

        const signersList = signers
            .slice()
            .sort(sortByOrder)
            .map((signer) => {
                if (!getIsSignerGroup(signer)) {
                    return {
                        id: signer.roleId,
                        user: signer.role.user as IUser,
                    } as IRole;
                } else {
                    return {
                        id: signer.group.id,
                        members: signer.group.members,
                        name: signer.group.name,
                    } as SignerReviewerGroup;
                }
            });
        const isOrderedSignProcess = signers.every(
            (signer) => signer.order !== null,
        );
        const isOrderedReviewProcess = doc.reviewRequests.every(
            (review) => review.order !== null,
        );

        const reviewersList = doc.reviewRequests
            .slice()
            .sort(sortByOrder)
            .map((request) => {
                if (!getIsReviewRequestElementGroup(request)) {
                    return {
                        id: request.toRoleId,
                        order: request.order,
                        user: request.toRole.user,
                    } as OrderedIRole;
                } else {
                    return {
                        id: request.toGroupId,
                        order: request.order,
                        name: request.toGroup?.name || '',
                        members: request.toGroup?.members || [],
                    };
                }
            });

        const category = doc.category || DocumentCategory.OTHER;
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__SHOW,
            isDocumentPage,
            documents,
            isActive,
            currentCompany: currentRole.company,
            docTitle: doc.title,
            docNumber: doc.number,
            docAmount: doc.amount,
            docStatus: doc.status,
            docDate: toDate(doc.dateDocument),
            docCategory: category.toString(),
            selectedItemIndex: selectedItemIndex || 'info',
            signersList,
            signProcess: getSignProcess(isOrderedSignProcess),
            previousSignersList: signersList,
            previousSignProcess: getSignProcess(isOrderedSignProcess),
            reviewProcessEditable,
            documentParameters: doc.parameters || [],
            id: doc.id,
            selectedTags: doc.tags,
            reviewersList,
            reviewProcess: getReviewProcess(isOrderedReviewProcess),
            isReviewRequired: doc.reviewSetting?.isRequired,
        });
    };
}

function onDocumentTemplateInit(): Thunk {
    return async (dispatch, getState) => {
        const {
            changeDocumentPopup: { documents },
            app: { currentUser },
        } = getState();
        const [documentTemplates, tags]: [
            Template[],
            ITag[],
        ] = await Promise.all([
            getDocumentTemplates(),
            getTagsForDocumentFilter(),
        ]);
        const enableEditSignProcess = canEditSignProcess(
            currentUser,
            documents,
        );

        const documentTemplateOptions = documentTemplates
            .filter((template) => {
                // Remove template with signers settings, when not possible
                // to edit sign process
                return !(template.signersSettings && !enableEditSignProcess);
            })
            .map((template) => ({
                value: template.id,
                label: template.name,
            }));

        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__SET_DOCUMENT_TEMPLATES,
            documentTemplates,
            documentTemplateOptions,
            tags,
        });
    };
}

function onShow({
    documents,
    isDocumentPage,
    selectedItemIndex,
    isActive,
}: Pick<
    State,
    'documents' | 'isDocumentPage' | 'selectedItemIndex' | 'isActive'
>): Thunk {
    return async (dispatch) => {
        if (documents.length === 1) {
            dispatch(
                onShowSingleDocument(
                    documents,
                    isDocumentPage,
                    selectedItemIndex,
                    isActive,
                ),
            );
        } else {
            dispatch(
                onShowMultipleDocuments(
                    documents,
                    isDocumentPage,
                    selectedItemIndex,
                ),
            );
        }

        // todo remove this 2 lines after NEW_EDIT_DOCUMENT_UI flag will be removed
        dispatch(onDocumentTemplateInit());
        dispatch(onInitPrevDocumentData());
    };
}

function onSignerDataChange(value: string): Thunk {
    return async (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SIGNER_VALUE,
            signerValue: value,
        });
    };
}

function onCloseSignersSuggestions(): Thunk {
    return (dispatch) => {
        dispatch({
            type:
                actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_SIGNERS,
            showSignersSuggestion: false,
        });
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_SIGNERS,
            showSignersSuggestion: [],
        });
    };
}

function onSignersSuggestionClick(suggestion: IRole): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { signersList },
        } = getState();
        const isSignerAlreadyInList = signersList
            .map((user) => user.id)
            .includes(suggestion.id);
        const updatedSignersList = isSignerAlreadyInList
            ? signersList
            : [...signersList, suggestion];
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SIGNERS_LIST,
            signersList: updatedSignersList,
        });
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SIGNER_VALUE,
            signerValue: '',
        });
        dispatch(onCloseSignersSuggestions());
    };
}

function onRearrangeSignersItems(currentIndex: number, indexTo: number): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { signersList },
        } = getState();
        const updatedSignersList = moveArrayItem(
            signersList,
            currentIndex,
            indexTo,
        );
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SIGNERS_LIST,
            signersList: updatedSignersList,
        });
    };
}

function onRemoveSigner(id: string): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { signersList },
        } = getState();
        const updatedSignersList = signersList.filter((user) => user.id !== id);
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SIGNERS_LIST,
            signersList: updatedSignersList,
        });
        dispatch(onCloseSignersSuggestions());
    };
}

function onAutosuggestSigners(search: string): Thunk {
    return async (dispatch, getState) => {
        const {
            changeDocumentPopup: { signersList },
        } = getState();
        const currentCompanyRolesArgs = {
            search,
            canSignAndRejectDocument: true,
        };
        const { currentCompanyRoles } = await getCurrentCompanyRoles(
            currentCompanyRolesArgs,
        );
        const currentEmployeeGroups = await getEmployeeGroups({ name: search });

        const filledEmployeeGroups = currentEmployeeGroups.groups.filter(
            (group: SignerReviewerGroup) => group.members.length > 0,
        );
        const currentRolesAndGroups = [
            ...currentCompanyRoles,
            ...filledEmployeeGroups,
        ];
        const alreadyInListIds = signersList.map((user) => user.id) || [];
        const suggestions = currentRolesAndGroups.filter(
            (role: IRole) => !alreadyInListIds.includes(role.id),
        );
        if (suggestions && suggestions.length > 0) {
            dispatch({
                type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_SIGNERS,
                signersSuggestions: suggestions,
            });
            dispatch({
                type:
                    actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_SIGNERS,
                showSignersSuggestion: true,
            });
        } else {
            dispatch({
                type:
                    actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_SIGNERS,
                showSignersSuggestion: false,
            });
            dispatch({
                type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_SIGNERS,
                showSignersSuggestion: [],
            });
        }
    };
}

function onSignProcessTypeChange(value: string): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SIGN_PROCESS_TYPE,
            signProcess: value,
        });
    };
}

function onCloseReviewerSuggestions(): Thunk {
    return (dispatch) => {
        dispatch({
            type:
                actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_REVIEWERS,
            isReviewersSuggestionsShown: false,
        });
    };
}

// reviewer action creators
function onReviewerSuggestionClick(suggestion: IRole): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { reviewersList },
        } = getState();
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWERS_LIST,
            reviewersList: [...reviewersList, suggestion],
        });
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWER_VALUE,
            reviewerValue: '',
        });
        dispatch(onCloseReviewerSuggestions());
    };
}

function onRemoveReviewer(id: string): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { reviewersList, deleteReviewersList },
        } = getState();
        const newReviewerList = reviewersList.filter((role) => role.id !== id);
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWERS_LIST,
            reviewersList: newReviewerList,
            deleteReviewersList: [...deleteReviewersList, id],
        });
        dispatch(onCloseReviewerSuggestions());
    };
}

function onReviewerDataChange(value: string): Thunk {
    return async (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWER_VALUE,
            reviewerValue: value,
        });
    };
}

function onAutosuggestReviewers(search: string): Thunk {
    return async (dispatch, getState) => {
        const {
            changeDocumentPopup: { reviewersList },
        } = getState();
        const { currentCompanyRoles } = await getCurrentCompanyRoles({
            search,
        });

        const currentEmployeeGroups = await getEmployeeGroups({ name: search });

        const filledEmployeeGroups = currentEmployeeGroups.groups.filter(
            (group: SignerReviewerGroup) => group.members.length > 0,
        );

        const currentRolesAndGroups = [
            ...currentCompanyRoles,
            ...filledEmployeeGroups,
        ];

        const idSet = new Set(reviewersList.map((user) => user.id) || []);
        const suggestions = currentRolesAndGroups.filter(
            (role) => !idSet.has(role.id),
        );

        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_REVIEWERS,
            reviewersSuggestions: suggestions,
        });

        if (suggestions.length > 0) {
            dispatch({
                type:
                    actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_REVIEWERS,
                isReviewersSuggestionsShown: true,
            });
        } else {
            dispatch(onCloseReviewerSuggestions());
        }
    };
}

function onToggleIsRequiredReview(checked: boolean): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__TOGGLE_IS_REVIEW_REQUIRED,
            isReviewRequired: checked,
        });
    };
}

function onReviewerProcessTypeChange(value: DocumentReviewProcess): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__TOGGLE_REVIEW_PROCESS_TYPE,
            reviewProcess: value,
        });
    };
}

function onRearrangeReviewersItems(
    currentIndex: number,
    indexTo: number,
): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { reviewersList },
        } = getState();
        const updatedReviewersList = moveArrayItem(
            reviewersList,
            currentIndex,
            indexTo,
        );
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWERS_LIST,
            reviewersList: updatedReviewersList,
        });
    };
}

function onCloseViewersSuggestion(): Thunk {
    return (dispatch) => {
        dispatch({
            type:
                actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_VIEWERS,
            isViewersSuggestionShown: false,
        });
    };
}

// viewer action creators
function onViewersSuggestionClick(suggestion: IRole): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { viewersList },
        } = getState();
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_VIEWERS_LIST,
            viewersList: [...viewersList, suggestion],
        });
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_VIEWER_VALUE,
            viewerValue: '',
        });
        dispatch(onCloseViewersSuggestion());
    };
}

function onRemoveViewer(id: string): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { viewersList },
        } = getState();
        const newViewerList = viewersList.filter((role) => role.id !== id);
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_VIEWERS_LIST,
            viewersList: newViewerList,
        });
        dispatch(onCloseViewersSuggestion());
    };
}

function onViewerDataChange(value: string): Thunk {
    return async (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_VIEWER_VALUE,
            viewerValue: value,
        });
    };
}

function onAutosuggestViewers(search: string): Thunk {
    return async (dispatch, getState) => {
        const {
            changeDocumentPopup: { viewersList },
        } = getState();
        const suggestions = await getSuggestion(search, viewersList);
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_VIEWERS,
            viewersSuggestion: suggestions,
        });
        if (suggestions.length > 0) {
            dispatch({
                type:
                    actions.CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_VIEWERS,
                isViewersSuggestionShown: true,
            });
        } else {
            dispatch(onCloseViewersSuggestion());
        }
    };
}

function onDocumentTemplateChange(id: string): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: {
                documentTemplates,
                activeDocumentTemplate,
                documents,
                selectedTags,
                tags,
            },
            app: {
                currentUser: { currentRole },
            },
        } = getState();

        const updatedTemplate = documentTemplates.find(
            (template: Template) => template.id === id,
        );

        if (
            (!activeDocumentTemplate && !updatedTemplate) ||
            (activeDocumentTemplate &&
                updatedTemplate &&
                activeDocumentTemplate.id === updatedTemplate.id)
        ) {
            // do nothing if no scenario or same scenario as before is selected
            return;
        }

        const updatedFields: Record<string, unknown> = {
            activeDocumentTemplate: updatedTemplate,
        };
        let warningMessage = '';
        if (updatedTemplate) {
            updatedFields.viewersList = updatedTemplate.viewerRoles;
            updatedFields.signersList = getModifiedListFromTemplate(
                updatedTemplate.signers,
            );
            updatedFields.reviewersList = getModifiedListFromTemplate(
                updatedTemplate.reviewers,
            );
            updatedFields.documentParameters = fieldSettingToParameters(
                updatedTemplate.fieldsSettings,
            );
            const reviewSettings = updatedTemplate.reviewSettings;
            if (reviewSettings) {
                if (!canEditReviewProcessType(documents, currentRole)) {
                    updatedFields.reviewersList = [];
                    warningMessage = t`Сценарій неможливо застосувати до процесу погодження,
                        оскільки один з обраних документів вже має розпочатий процес
                        погодження`;
                }
                updatedFields.reviewProcess = getReviewProcess(
                    reviewSettings.is_ordered,
                );
                updatedFields.isReviewRequired = reviewSettings.is_required;
            }
            if (updatedTemplate.signersSettings) {
                updatedFields.signProcess = getSignProcess(
                    updatedTemplate.signersSettings.is_ordered,
                );
            }
            if (
                updatedTemplate.tagsSettings &&
                Array.isArray(updatedTemplate.tagsSettings.tags)
            ) {
                updatedFields.selectedTags = [
                    ...selectedTags,
                    ...tags.filter(
                        (tag: ITag) =>
                            updatedTemplate.tagsSettings?.tags.includes(
                                tag.name,
                            ) &&
                            !selectedTags
                                .map((selectedTag) => selectedTag.name)
                                .includes(tag.name),
                    ),
                ];
            }
        }

        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__SET_ACTIVE_DOCUMENT_TEMPLATE,
            ...updatedFields,
            ...(!updatedFields.selectedTags && documents.length === 1
                ? {
                      selectedTags: selectedTags.filter(
                          (tag) =>
                              ![
                                  ...(activeDocumentTemplate?.tagsSettings
                                      ?.tags || []),
                              ].includes(tag.name),
                      ),
                  }
                : {}),
            warningMessage,
        });
    };
}

function onMenuItemClick(index: string): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__ON_MENU_CLICK,
            selectedItemIndex: index,
        });
    };
}

function onDocumentParametersChange(data: DocumentParameter[]): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__SET_DOCUMENT_PARAMETERS,
            data,
        });
    };
}

// tags action creators
function onTagsCloseSuggestions(): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SUGGESTION,
            suggestedTags: [],
            isTagsSuggestionsShown: false,
        });
    };
}

function onTagsAutosuggest(search: string): Thunk {
    return async (dispatch, getState) => {
        const {
            changeDocumentPopup: { allTags },
        } = getState();
        // not to repeat request for all tags with search = ''
        if (!search && allTags.length) {
            dispatch({
                type: actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SUGGESTION,
                isTagsSuggestionsShown: true,
                suggestedTags: allTags,
            });
            return;
        }
        const suggestedTags = await getCurrentCompanyTags({ search });
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SUGGESTION,
            suggestedTags,
            isTagsSuggestionsShown: true,
            allTags: !search && suggestedTags,
        });
    };
}

function onAddNewTag(value = ''): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { newTags },
        } = getState();
        if (value && !isTagNameAlreadyInList(value, newTags)) {
            dispatch({
                type: actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_NEW,
                newTags: [...newTags, { id: '', name: value }],
            });
        }
        dispatch(onTagsCloseSuggestions());
    };
}

function onTagsDelete(tag: ITag): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { newTags, selectedTags },
        } = getState();
        const isSelectedTag = selectedTags.find(
            (item: ITag) => item.id === tag.id,
        );
        if (isSelectedTag) {
            dispatch({
                type: actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SELECTED,
                selectedTags: selectedTags.filter(
                    (item: ITag) => item.id !== tag.id,
                ),
            });
            return;
        }
        dispatch({
            type: actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_NEW,
            newTags: newTags.filter((item: ITag) => item.name !== tag.name),
        });
    };
}

function onTagsSuggestionClick(suggestion: { id: string }): Thunk {
    return (dispatch, getState) => {
        const {
            changeDocumentPopup: { selectedTags },
        } = getState();
        if (!isTagAlreadyInList(suggestion.id, selectedTags)) {
            dispatch({
                type: actions.CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SELECTED,
                selectedTags: [...selectedTags, suggestion],
            });
        }
        dispatch(onTagsCloseSuggestions());
    };
}

export default {
    onSubmit,
    onChangeDate,
    onChangeField,
    onChangeCategory,
    onChangeAmount,
    onShow,
    onClose,
    onSignerDataChange,
    onSignersSuggestionClick,
    onRearrangeSignersItems,
    onRemoveSigner,
    onAutosuggestSigners,
    onCloseSignersSuggestions,
    onSignProcessTypeChange,
    onMenuItemClick,

    onReviewerSuggestionClick,
    onCloseReviewerSuggestions,
    onRemoveReviewer,
    onReviewerDataChange,
    onAutosuggestReviewers,
    onToggleIsRequiredReview,
    onReviewerProcessTypeChange,
    onRearrangeReviewersItems,

    onViewersSuggestionClick,
    onCloseViewersSuggestion,
    onRemoveViewer,
    onViewerDataChange,
    onAutosuggestViewers,

    onDocumentTemplateChange,
    onDocumentParametersChange,

    onTagsCloseSuggestions,
    onTagsAutosuggest,
    onAddNewTag,
    onTagsDelete,
    onTagsSuggestionClick,
};
