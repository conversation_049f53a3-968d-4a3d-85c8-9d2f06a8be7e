export default {
    CHANGE_DOCUMENT_POPUP__INFO_CHANGE_FIELD:
        'CHANGE_DOCUMENT_POPUP__INFO_CHANGE_FIELD',
    CHANGE_DOCUMENT_POPUP__INFO_CHANGE_DATE:
        'CHAN<PERSON>_DOCUMENT_POPUP__INFO_CHANGE_DATE',
    CHANGE_DOCUMENT_POPUP__CLOSE: 'CHANGE_DOCUMENT_POPUP__CLOSE',
    CHANGE_DOCUMENT_POPUP__SHOW: 'CHANGE_DOCUMENT_POPUP__SHOW_POPUP',
    CHANGE_DOCUMENT_POPUP__ON_MENU_CLICK:
        'CHANGE_DOCUMENT_POPUP__ON_MENU_CLICK',
    CHANGE_DOCUMENT_POPUP__SET_SUBMIT_INFO:
        'CHANGE_DOCUMENT_POPUP__SET_SUBMIT_INFO',

    CHANGE_DOCUMENT_POPUP__ORDER_CHANGE_CATEGORY:
        'CHANGE_DOCUMENT_POPUP__ORDER_CHANGE_CATEGORY',

    // document signer actions
    CHANGE_DOCUMENT_POPUP__CHANGE_SIGNER_VALUE:
        'CHANGE_DOCUMENT_POPUP__CHANGE_SIGNER_VALUE',
    CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_SIGNERS:
        'CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_SIGNERS',
    CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_SIGNERS:
        'CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_SIGNERS',
    CHANGE_DOCUMENT_POPUP__CHANGE_SIGNERS_LIST:
        'CHANGE_DOCUMENT_POPUP__CHANGE_SIGNERS_LIST',
    CHANGE_DOCUMENT_POPUP__TOGGLE_SIGN_PROCESS_TYPE:
        'CHANGE_DOCUMENT_POPUP__TOGGLE_SIGN_PROCESS_TYPE',

    // reviewers actions
    CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWER_VALUE:
        'CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWER_VALUE',
    CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_REVIEWERS:
        'CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_REVIEWERS',
    CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_REVIEWERS:
        'CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_REVIEWERS',
    CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWERS_LIST:
        'CHANGE_DOCUMENT_POPUP__CHANGE_REVIEWERS_LIST',
    CHANGE_DOCUMENT_POPUP__TOGGLE_IS_REVIEW_REQUIRED:
        'CHANGE_DOCUMENT_POPUP__TOGGLE_IS_REVIEW_REQUIRED',
    CHANGE_DOCUMENT_POPUP__TOGGLE_REVIEW_PROCESS_TYPE:
        'CHANGE_DOCUMENT_POPUP__TOGGLE_REVIEW_PROCESS_TYPE',

    // viewers actions
    CHANGE_DOCUMENT_POPUP__CHANGE_VIEWER_VALUE:
        'CHANGE_DOCUMENT_POPUP__CHANGE_VIEWER_VALUE',
    CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_VIEWERS:
        'CHANGE_DOCUMENT_POPUP__CHANGE_SUGGESTION_VIEWERS',
    CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_VIEWERS:
        'CHANGE_DOCUMENT_POPUP__TOGGLE_SHOW_SUGGESTIONS_VIEWERS',
    CHANGE_DOCUMENT_POPUP__CHANGE_VIEWERS_LIST:
        'CHANGE_DOCUMENT_POPUP__CHANGE_VIEWERS_LIST',

    // templates actions
    CHANGE_DOCUMENT_POPUP__SET_DOCUMENT_TEMPLATES:
        'CHANGE_DOCUMENT_POPUP__SET_DOCUMENT_TEMPLATES',
    CHANGE_DOCUMENT_POPUP__SET_ACTIVE_DOCUMENT_TEMPLATE:
        'CHANGE_DOCUMENT_POPUP__SET_ACTIVE_DOCUMENT_TEMPLATE',

    CHANGE_DOCUMENT_POPUP__SET_DOCUMENT_PARAMETERS:
        'CHANGE_DOCUMENT_POPUP__SET_DOCUMENT_PARAMETERS',

    // tags actions
    CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_NEW:
        'CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_NEW',
    CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SELECTED:
        'CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SELECTED',
    CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SUGGESTION:
        'CHANGE_DOCUMENT_POPUP__TAGS_CHANGE_SUGGESTION',

    CHANGE_DOCUMENT_POPUP__INIT_PREV_DOCUMENT_DATA:
        'CHANGE_DOCUMENT_POPUP__INIT_PREV_DOCUMENT_DATA',

    CHANGE_DOCUMENT_POPUP__SET_DOCUMENTS:
        'CHANGE_DOCUMENT_POPUP__SET_DOCUMENTS',
};
