import React, { FC } from 'react';

import Title from 'components/KepSigner/components/Title/Title';
import { t } from 'ttag';

import css from './ConfirmCancel.css';

export const ConfirmCancel: FC = () => {
    return (
        <div>
            <div className={css.image}>
                <img
                    src={`${config.STATIC_HOST}/images/confirm_cancel.svg`}
                    alt="confirm cancel"
                />
            </div>
            <Title
                className={css.title}
            >{t`Ви впевнені, що хочете відмінити підписання?`}</Title>
            <div className={css.text}>
                {t`Якщо ви закриєте вікно, то процес підписаня Вчасно.КЕП буде перервано. Не закривайте вікно поки документ повіністю не підписано`}
            </div>
        </div>
    );
};
