import React from 'react';
import { useSelector } from 'react-redux';

import { Checkbox, Title } from '@vchasno/ui-kit';

import CheckboxHintWrapper from 'components/RoleAccessList/CheckboxHintWrapper';
import GridLayout from 'components/RoleAccessList/GridLayout';
import PermissionGroup from 'components/RoleAccessList/PermissionGroup';
import {
    administrativeEmailSettingsList,
    commonDocumentEmailSettingsList,
    documentCompletedEmailSettingsList,
} from 'components/emailNotificationSettings/lists';
import {
    disableEmailSettingToHint,
    emailSettingToApiPayloadKey,
    emailSettingToHint,
    emailSettingToLabel,
} from 'components/emailNotificationSettings/mappers';
import { useEmailSettingDisplayFilter } from 'components/emailNotificationSettings/useEmailSettingDisplayFilter';
import { isVchasnoCompany } from 'lib/helpers';
import { getCurrentCompany, getCurrentUserRole } from 'selectors/app.selectors';
import { t } from 'ttag';

import { IRole, RoleEmailSettings } from '../../types/user';
import { EmployeeSectionsWithCheckboxes } from '../employee/types';

interface EmailNotificationSettingsProps {
    role: IRole;
    onChange: (
        id: string,
        accessData: Record<string, boolean>,
        newEditedSection: string,
    ) => void;
}

const EmailNotificationSettings = ({
    role,
    onChange,
}: EmailNotificationSettingsProps) => {
    const emailSettingDisplayFilter = useEmailSettingDisplayFilter();
    const currentCompany = useSelector(getCurrentCompany);
    const currentRole = useSelector(getCurrentUserRole);

    const isGlobalAdmin = isVchasnoCompany(currentCompany.edrpou);
    // якщо роль не має компанії, то це розглядається роль зі Вчасно
    const isRoleCompanyVchasno = role.company
        ? isVchasnoCompany(role.company.edrpou)
        : true;
    const canEditEmailNotificationSettings =
        isGlobalAdmin && !isRoleCompanyVchasno
            ? currentRole.canEditClientData
            : true;

    const renderSettings = (settings: RoleEmailSettings) => {
        let disabled = !canEditEmailNotificationSettings;

        //  що його можна включати тільки якщо у користувача є право перегляду всіх документів зі спільним доступом
        if (
            settings === 'canReceiveInboxAsDefault' &&
            !role.canViewDocument &&
            !role.isAdmin
        ) {
            disabled = true;
        }

        return (
            <CheckboxHintWrapper
                key={settings}
                hint={
                    disabled
                        ? disableEmailSettingToHint[settings]
                        : emailSettingToHint[settings]
                }
            >
                <Checkbox
                    key={settings}
                    checked={Boolean(role[settings])}
                    label={emailSettingToLabel[settings]}
                    disabled={disabled}
                    onChange={() =>
                        onChange(
                            role.id,
                            {
                                [emailSettingToApiPayloadKey[settings]]: !role[
                                    settings
                                ],
                            },
                            EmployeeSectionsWithCheckboxes.EMAIL_NOTIFICATION_SETTINGS,
                        )
                    }
                />
            </CheckboxHintWrapper>
        );
    };

    return (
        <>
            <Title level={3}>{t`Налаштування сповіщень`}</Title>
            <GridLayout>
                <PermissionGroup title={t`Сповіщення про дії з документами`}>
                    {commonDocumentEmailSettingsList
                        .filter(emailSettingDisplayFilter)
                        .map(renderSettings)}
                </PermissionGroup>
                <PermissionGroup
                    title={t`Сповіщення по завершенню роботи із документом`}
                >
                    {documentCompletedEmailSettingsList
                        .filter(emailSettingDisplayFilter)
                        .map(renderSettings)}
                </PermissionGroup>
                <PermissionGroup title={t`Адміністративні сповіщення`}>
                    {administrativeEmailSettingsList
                        .filter(emailSettingDisplayFilter)
                        .map(renderSettings)}
                </PermissionGroup>
            </GridLayout>
        </>
    );
};

export default EmailNotificationSettings;
