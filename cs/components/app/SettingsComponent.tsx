import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Route, Switch } from 'react-router-dom';

import { EmployeeGroupProvider } from 'components/EmployeeGroup/context';
import appActionCreators from 'components/app/appActionCreators';
import CompanyCard from 'components/companyCard/companyCard';
import { EmployeeGroupsListProvider } from 'components/companyCard/components/CompanyEmployeesSettings/components/EmployeeGroupsList/context';
import Loading from 'components/loading/loading';
import { getCurrentUserRoleId } from 'selectors/app.selectors';

import EmployeeGroup from '../EmployeeGroup';
import BannerEditForm from '../bannerEditForm/bannerEditForm';
import BannersList from '../bannersList/bannersList';
import CompaniesList from '../companiesList/companiesList';
import CompanyAdminCardContainer from '../companyAdminCard/companyAdminCardContainer';
import ContactListContainer from '../contactList/contactListContainer';
import DocumentSettings from '../documentSettings/documentSettings';
import EmployeeContainer from '../employee/employeeContainer';
import Profile from '../profile/Profile';
import Settings from '../settings/settings';
import SpecialAbilities from '../specialAbilities/specialAbilities';
import StatisticsContainer from '../statistics/statisticsContainer';
import StatisticsCompaniesListContainer from '../statisticsCompaniesList/statisticsCompaniesListContainer';
import StatisticsUserContainer from '../statisticsUser/statisticsUserContainer';
import StatisticsUsersListContainer from '../statisticsUsersList/statisticsUsersListContainer';

const SettingsComponent = () => {
    const dispatch = useDispatch();
    const [isLoadingDataForConfig, setIsLoadingDataForConfig] = useState(false);
    const currentRoleId = useSelector(getCurrentUserRoleId);

    useEffect(() => {
        const getDataForConfig = async () => {
            await dispatch(appActionCreators.getCurrentCompanyRolesForConfig());
            setIsLoadingDataForConfig(true);
        };

        getDataForConfig();
    }, [currentRoleId]);

    if (!isLoadingDataForConfig) {
        return <Loading />;
    }

    return (
        <Settings>
            <Switch>
                <Route exact path="/app/settings" component={Profile} />
                <Route
                    exact
                    path="/app/settings/companies"
                    component={CompaniesList}
                />
                <Route
                    exact
                    path="/app/settings/companies/:roleId/employees/:employeeRoleId"
                    render={({ match }) => (
                        <CompanyCard>
                            <EmployeeContainer match={match} />
                        </CompanyCard>
                    )}
                />
                <Route
                    exact
                    path="/app/settings/document-settings"
                    component={DocumentSettings}
                />
                <Route
                    exact
                    path="/app/settings/contacts"
                    component={ContactListContainer}
                />
                <Route
                    exact
                    path="/app/settings/statistics"
                    component={StatisticsContainer}
                />
                <Route
                    exact
                    path="/app/settings/statistics/users/search/:search"
                    component={StatisticsUsersListContainer}
                />
                <Route
                    exact
                    path="/app/settings/statistics/users/search/:search/:userId"
                    component={StatisticsUserContainer}
                />
                <Route
                    exact
                    path="/app/settings/statistics/companies/search/:search"
                    component={StatisticsCompaniesListContainer}
                />
                <Route
                    exact
                    path="/app/settings/statistics/companies/search/:search/:companyId"
                    component={CompanyAdminCardContainer}
                />
                <Route
                    exact
                    path="/app/settings/statistics/companies/search/:search/:companyId/:userId"
                    component={StatisticsUserContainer}
                />
                <Route
                    exact
                    path="/app/settings/special-abilities"
                    component={SpecialAbilities}
                />
                <Route exact path="/app/settings/banner">
                    <BannersList />
                    <BannerEditForm />
                </Route>
                <EmployeeGroupsListProvider>
                    <Route
                        path="/app/settings/companies/:roleId"
                        component={CompanyCard}
                    />
                    <Route path={'/app/settings/groups/:employeeGroupId?'}>
                        <EmployeeGroupProvider>
                            <EmployeeGroup />
                        </EmployeeGroupProvider>
                    </Route>
                </EmployeeGroupsListProvider>
            </Switch>
        </Settings>
    );
};

export default SettingsComponent;
