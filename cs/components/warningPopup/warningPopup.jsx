import React from 'react';
import MediaQuery from 'react-responsive';

import PropTypes from 'prop-types';

import PopupMobile from '../ui/popup/mobile/popup';
import Popup from '../ui/popup/popup';

import { MEDIA_WIDTH } from '../../lib/constants';
import WebWorkerSupportPopupContent from './WebWorkerSupportPopupContent';
import { warningPopupContent } from './enum';

const WarningPopup = (props) => {
    const renderPopupContent = () => {
        if (props.warningPopupType === warningPopupContent.WORKER_SUPPORT) {
            return <WebWorkerSupportPopupContent />;
        }
    };

    return (
        <div>
            <MediaQuery minWidth={MEDIA_WIDTH.tablet + 1}>
                <Popup active={props.isActive} onClose={props.onClose}>
                    {renderPopupContent()}
                </Popup>
            </MediaQuery>
            <MediaQuery maxWidth={MEDIA_WIDTH.tablet}>
                <PopupMobile active={props.isActive} onClose={props.onClose}>
                    {renderPopupContent()}
                </PopupMobile>
            </MediaQuery>
        </div>
    );
};

WarningPopup.propTypes = {
    isActive: PropTypes.bool,
    warningPopupType: PropTypes.string,
    onClose: PropTypes.func.isRequired,
};

export default WarningPopup;
