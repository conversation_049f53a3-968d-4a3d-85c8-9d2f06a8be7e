import React from 'react';

import { Pagination as UIKITPagination } from '@vchasno/ui-kit';

import { useHistoryPagination } from 'hooks/useHistoryPagination';
import PropTypes from 'prop-types';
import { ES_MAX_DOCUMENTS_COUNT_RESULT } from 'services/documents/constants';
import { t } from 'ttag';

import Dropdown from '../ui/dropdown/dropdown';

import ListCounter from '../listCounter/listCounter';

// styles
import css from './listFooter.css';

const ListFooter = (props) => {
    const historyPagination = useHistoryPagination();

    const totalDocuments =
        props.total > ES_MAX_DOCUMENTS_COUNT_RESULT
            ? ES_MAX_DOCUMENTS_COUNT_RESULT
            : props.total;

    return (
        <div className={css.root}>
            <div className={css.pagination}>
                <UIKITPagination
                    gapStep={totalDocuments > 100 ? 10 : 5}
                    scrollOnChange
                    total={Math.ceil(totalDocuments / props.itemsPerPage)}
                    current={historyPagination.currentPage}
                    hideOnSinglePage
                    onChange={(page) => {
                        historyPagination.setPage(page);
                        props.onPageCounterClick(page);
                    }}
                />
            </div>
            <div className={css.counter}>
                {props.total > 0 && (
                    <ListCounter
                        currentPage={props.currentPage}
                        itemsPerPage={props.itemsPerPage}
                        totalCount={props.total}
                    />
                )}
                <div className={css.perPageContainer}>
                    <div
                        onClick={() => {
                            if (config.DEBUG) {
                                // ts-ignore-next-line
                                testSentryError();
                            }
                        }}
                        className={css.perPageTitle}
                    >{t`Показувати документів`}</div>
                    <div className={css.perPageDropdown}>
                        <Dropdown
                            options={props.documentsPerPageOptions}
                            value={props.itemsPerPage}
                            onChange={props.onDocumentsPerPageChange}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

ListFooter.propTypes = {
    total: PropTypes.number,
    currentPage: PropTypes.number,
    hasNextPage: PropTypes.bool,
    useSimplePagination: PropTypes.bool,
    onPageCounterClick: PropTypes.func,
    onDocumentsPerPageChange: PropTypes.func,
    itemsPerPage: PropTypes.number,
    documentsPerPageOptions: PropTypes.array,
};

export default ListFooter;
