import { useEffect, useMemo, useState } from 'react';

import {
    DEBOUNCE_DELAY,
    DOC_CATEGORIES_LIMIT_PER_PAGE,
} from 'components/DocCategorySelect/constants';
import { IDocCategorySelectOption } from 'components/DocCategorySelect/types';
import {
    fetchDocCategoriesByIds,
    getDocCategoriesForSelectOptions,
    getDocCategoryForSelectOption,
    getIsOptionsHasDefaultValue,
    getSelectOptionSet,
} from 'components/DocCategorySelect/utils';
import { DocumentCategory } from 'gql-types';
import { useDocCategoriesQuery } from 'hooks/useDocCategoriesQuery';
import { useDebounce } from 'lib/reactHelpers/hooks';
import {
    getExternalDocCategories,
    getInternalDocCategories,
    getIsInternalDocCategory,
} from 'services/documentCategories/utils';
import { t } from 'ttag';

import { Nullable } from '../../types/general';

export const useSelectOptions = ({
    onlyInternal = false,
    onlyPublic = false,
    preRequestCategoryDetails,
    value = '',
}: {
    value?: string | string[];
    onlyInternal?: boolean;
    onlyPublic?: boolean;
    preRequestCategoryDetails?: Nullable<DocumentCategory>;
}) => {
    const [page, setPage] = useState(0);
    const [title, setTitle] = useState('');
    const [isTitleChanged, setIsTitleChanged] = useState(false);
    const [options, setOptions] = useState<IDocCategorySelectOption[]>([]);
    const debounceTitle = useDebounce(title, DEBOUNCE_DELAY);

    const offset = page * DOC_CATEGORIES_LIMIT_PER_PAGE;

    const { data } = useDocCategoriesQuery({
        title: debounceTitle,
        offset: offset,
        limit: DOC_CATEGORIES_LIMIT_PER_PAGE,
        onlyInternal,
        onlyPublic,
    });

    const handleChangeTitle = (newTitle: string) => {
        setIsTitleChanged(true);
        setTitle(newTitle);
        setPage(0);
    };

    const getIsFilteredOption = (option: IDocCategorySelectOption): boolean => {
        if (onlyPublic) {
            return !getIsInternalDocCategory(option);
        }

        return true;
    };

    const getDocCategoriesByIds = async () => {
        const docCategoriesByIds = await fetchDocCategoriesByIds({
            title: '',
            offset: offset,
            limit: DOC_CATEGORIES_LIMIT_PER_PAGE,
            ids: value,
            onlyInternal,
            onlyPublic,
        });

        return docCategoriesByIds?.documentCategories || [];
    };

    const updateOptions = async (documentCategories: DocumentCategory[]) => {
        const optionsForSelect = getDocCategoriesForSelectOptions(
            documentCategories,
        );

        const optionsBeforeUpdate = isTitleChanged
            ? optionsForSelect
            : options.concat(optionsForSelect);

        const newOptions = optionsBeforeUpdate.filter(getIsFilteredOption);

        if (preRequestCategoryDetails?.id) {
            const preRequestCategoryDetailsForSelect = getDocCategoryForSelectOption(
                preRequestCategoryDetails,
            );

            newOptions.push(preRequestCategoryDetailsForSelect);
        }

        // get category title from db, if we have only 'id' value
        if (
            !preRequestCategoryDetails &&
            value &&
            !getIsOptionsHasDefaultValue(newOptions, value)
        ) {
            const docCategoriesByIds = await getDocCategoriesByIds();
            const docCategoriesByIdsForSelect = getDocCategoriesForSelectOptions(
                docCategoriesByIds,
            );

            newOptions.push(...docCategoriesByIdsForSelect);
        }

        // "Set" needed, because options from request and options from "preRequestCategoryDetails" can be the same
        const optionsSet = getSelectOptionSet(newOptions);

        setIsTitleChanged(false);
        setOptions(optionsSet);
    };

    useEffect(() => {
        if (!data?.documentCategories) {
            return;
        }

        updateOptions(data.documentCategories);
    }, [JSON.stringify(data), onlyInternal, onlyPublic]);

    const onMenuScrollBottom = () => {
        if (options.length < data?.count) {
            setPage((prevValue) => prevValue + 1);
        }
    };

    const onResetTitle = () => {
        setTitle('');
    };

    const onResetPage = () => {
        setPage(0);
    };

    const externalDocCategories = getExternalDocCategories(options);
    const internalDocCategories = getInternalDocCategories(options);

    const optionsForSelect = useMemo(() => {
        if (!internalDocCategories.length || !externalDocCategories.length) {
            return options;
        }

        return [
            {
                label: t`Зовнішні типи`,
                options: externalDocCategories,
            },
            {
                label: t`Внутрішні типи`,
                options: internalDocCategories,
            },
        ];
    }, [externalDocCategories, internalDocCategories]);

    return {
        options,
        optionsForSelect,
        handleChangeTitle,
        onResetTitle,
        onResetPage,
        onMenuScrollBottom,
    };
};
