import { yupResolver } from '@hookform/resolvers/yup';

import { STATIC_ERROR_PHRASES } from 'components/auth/constants';
import * as yup from 'yup';

import { Verify2FAFormFields } from './types';

const verify2FAFormValidationSchema: yup.ObjectSchema<Verify2FAFormFields> = yup
    .object({
        code: yup.string().required(STATIC_ERROR_PHRASES.REQUIRED_FIELD_ERROR),
        trusted: yup.boolean().default(false),
    })
    .required();

export const verify2FAFormResolver = yupResolver(verify2FAFormValidationSchema);
