import { Location } from 'history';
import { getLocationQuery } from 'lib/url';
import {
    getLocalStorageItem,
    removeLocalStorageItem,
    setLocalStorageItem,
} from 'lib/webStorage';

import { LS_KEY_AUTH_INITIAL_FORM } from './constants';

export const INVITE_EMAIL_LOCAL_STORAGE_KEY = 'inviteEmailParams';

export const INVITE_EMAIL_PARAMS = [
    'utm_campaign',
    'utm_medium',
    'utm_source',
    'invited_type',
    'invited_edrpou',
];

export const saveInviteEmailParamsToLocalStorage = (location: Location) => {
    const query = getLocationQuery(location);
    const filteredInviteEmailParams = Object.entries(query).reduce(
        (result, [paramKey, paramValue]) => {
            if (INVITE_EMAIL_PARAMS.includes(paramKey)) {
                result[paramKey] = paramValue;
            }

            return result;
        },
        {} as Record<string, string>,
    );

    if (Object.keys(filteredInviteEmailParams).length) {
        setLocalStorageItem(
            INVITE_EMAIL_LOCAL_STORAGE_KEY,
            filteredInviteEmailParams,
        );
    }
};

export class AuthFormStorageItem {
    authType: 'phone' | 'email';
    login: string; // phone or email
}

/**
 * We use local storage to pass data between the pages of the multi-page auth flow
 */
export const loadAuthFormStorageItem = (): AuthFormStorageItem | null => {
    const item = getLocalStorageItem(LS_KEY_AUTH_INITIAL_FORM);
    if (!item) {
        return null;
    }

    // previously we stored only email in local storage, here we convert it to the new format
    // to not break the old flows
    if (item.email) {
        return {
            authType: 'email',
            login: item.email,
        };
    }

    return item as AuthFormStorageItem;
};

export const clearAuthFormStorageItem = () => {
    removeLocalStorageItem(LS_KEY_AUTH_INITIAL_FORM);
};

export const saveAuthFormStorageItem = (data: AuthFormStorageItem) => {
    setLocalStorageItem(LS_KEY_AUTH_INITIAL_FORM, data);
};
