import React from 'react';

import cn from 'classnames';

import { Document } from '../../services/documents/ts/types';

import css from './statusText.css';

type Props = {
    isWrapping?: boolean;
    statusColor?: Document['statusColor'];
    title?: string;
    handleClick?: (e: React.SyntheticEvent) => void;
    className?: string;
    variant?: 'text' | 'badge';
};

const StatusText: React.FC<React.PropsWithChildren<Props>> = (props) => {
    const statusClasses = cn(css.root, props.className, {
        [css.rootRed]: props.statusColor === 'Red',
        [css.rootOrange]: props.statusColor === 'Orange',
        [css.rootGreen]: props.statusColor === 'Green',
        [css.rootWrap]: props.isWrapping,
        [css.badge]: props.variant,
    });

    return (
        <div
            className={statusClasses}
            title={props.title}
            onClick={(event) => {
                if (props.handleClick) {
                    props.handleClick(event);
                }
            }}
        >
            {props.children}
        </div>
    );
};

export default StatusText;
