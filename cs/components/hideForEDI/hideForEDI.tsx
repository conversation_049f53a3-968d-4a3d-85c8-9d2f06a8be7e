import React from 'react';

import { useUrlQueryParam } from '../../hooks/useUrlQueryParam';

interface HideForEDIProps {
    children: React.ReactNode;
}

const HideForEDI: React.FC<React.PropsWithChildren<HideForEDIProps>> = ({
    children,
}) => {
    const isEDI = useUrlQueryParam('is_edi') === '1';

    if (isEDI) {
        return null;
    }

    return <>{children}</>;
};

export default HideForEDI;
