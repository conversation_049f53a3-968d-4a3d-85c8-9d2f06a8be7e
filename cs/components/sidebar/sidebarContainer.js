import React from 'react';
import { connect } from 'react-redux';

import PropTypes from 'prop-types';

import actionCreators from './sidebarActionCreators';

import Sidebar from './sidebar';

function mapStateToProps(state) {
    return {
        ...state.sidebar,
    };
}

const mapDispatchToProps = actionCreators;

const SidebarContainer = (props) => {
    return <Sidebar {...props}>{props.children}</Sidebar>;
};

SidebarContainer.propTypes = {
    isFixedTop: PropTypes.bool,
    isFixedBottom: PropTypes.bool,
    isBottom: PropTypes.bool,
    headerHeight: PropTypes.number,
    footerHeight: PropTypes.number,
    onCheckPosition: PropTypes.func.isRequired,
    onGetSidebarHeight: PropTypes.func.isRequired,
    setRef: PropTypes.func.isRequired,
};

export default connect(mapStateToProps, mapDispatchToProps)(SidebarContainer);
