import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';

import { AnimatePresence } from 'framer-motion';
import { getAppFlags } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';

import { useKepMobileAppAd } from './KepMobileAppAd.hooks';
import { AppSegment } from './components/AppSegment';
import { AppSignSegment } from './components/AppSignSegment';
import { CtaSegment } from './components/CtaSegment';
import { FeaturesSegment } from './components/FeaturesSegment';
import { PhoneFrame, PhoneFramePosition } from './components/PhoneFrame';
import { SolutionSegment } from './components/SolutionSegment';
import { TiredSegment } from './components/TiredSegment';
import { ToggleButton, ToggleButtonMode } from './components/ToggleButton';

enum AnimationSegment {
    IDLE = 'IDLE',
    TIRED = 'TIRED',
    SOLUTION = 'SOLUTION',
    APP = 'APP',
    APP_SIGN = 'APP_SIGN',
    FEATURES = 'FEATURES',
    CTA = 'CTA',
}

const KepMobileAppAd = () => {
    const { data, error, isLoading } = useKepMobileAppAd();
    const features = useSelector(getAppFlags);
    const isDebugLogsEnabled = features['ENABLE_KEP_DEBUG_LOGS'];
    const abortController = useRef(new AbortController());
    const shouldShowAd = !isLoading && !error && !!data;

    const [phonePosition, setPhonePosition] = useState<PhoneFramePosition>(
        PhoneFramePosition.HIDDEN,
    );
    const [toggleButtonMode, setToggleButtonMode] = useState<ToggleButtonMode>(
        ToggleButtonMode.HIDDEN,
    );
    const [currentSegment, setCurrentSegment] = useState<AnimationSegment>(
        AnimationSegment.IDLE,
    );

    const wait = (ms: number) =>
        new Promise((resolve, reject) => {
            abortController.current.signal.addEventListener('abort', reject);

            setTimeout(resolve, ms);
        });

    const animate = async () => {
        abortController.current = new AbortController();

        setPhonePosition(PhoneFramePosition.PARTIALLY_VISIBLE);
        setCurrentSegment(AnimationSegment.TIRED);
        setToggleButtonMode(ToggleButtonMode.CLOSE_PARTIAL);

        await wait(6000);

        setCurrentSegment(AnimationSegment.SOLUTION);
        await wait(5000);

        setCurrentSegment(AnimationSegment.APP);
        await wait(4000);

        setPhonePosition(PhoneFramePosition.FULLY_VISIBLE);
        setToggleButtonMode(ToggleButtonMode.CLOSE_FULL);
        setCurrentSegment(AnimationSegment.APP_SIGN);
        await wait(5000);

        setCurrentSegment(AnimationSegment.FEATURES);
        await wait(3000);

        setCurrentSegment(AnimationSegment.CTA);
    };

    const restartAnimation = async () => {
        try {
            eventTracking.sendToGTM({
                event: 'kep_mobile_app_ad_expand',
            });
            abortController.current.abort();
            await animate();
        } catch (err) {
            if (isDebugLogsEnabled) {
                console.log('Manual animation aborted', err);
            }
        }
    };

    const stopAnimation = async () => {
        eventTracking.sendToGTM({
            event: 'kep_mobile_app_ad_hide',
        });
        abortController.current.abort();
        setPhonePosition(PhoneFramePosition.HIDDEN);
        setCurrentSegment(AnimationSegment.IDLE);
        setToggleButtonMode(ToggleButtonMode.EXPAND);
    };

    const handleClickPhoneFrame = async () => {
        if (phonePosition !== PhoneFramePosition.HIDDEN) {
            return;
        }

        await restartAnimation();
    };

    const handleToggleAnimation = async () => {
        if (toggleButtonMode === ToggleButtonMode.HIDDEN) {
            return;
        }

        if (toggleButtonMode === ToggleButtonMode.EXPAND) {
            await restartAnimation();
        } else {
            await stopAnimation();
        }
    };

    useEffect(() => {
        const run = async () => {
            try {
                eventTracking.sendToGTM({
                    event: 'kep_mobile_app_ad_show',
                });
                await wait(5000);
                abortController.current.signal.throwIfAborted();
                await animate();
            } catch (err) {
                if (isDebugLogsEnabled) {
                    console.log('Initial animation aborted', err);
                }
            }
        };

        if (shouldShowAd) {
            run();
        }

        return () => {
            abortController.current.abort();
            abortController.current = new AbortController();
            setPhonePosition(PhoneFramePosition.HIDDEN);
            setCurrentSegment(AnimationSegment.IDLE);
            setToggleButtonMode(ToggleButtonMode.HIDDEN);
        };
    }, [shouldShowAd]);

    if (!shouldShowAd) {
        return null;
    }

    return (
        <>
            <PhoneFrame
                position={phonePosition}
                onClick={handleClickPhoneFrame}
            >
                <AnimatePresence exitBeforeEnter initial={false}>
                    {currentSegment === AnimationSegment.TIRED && (
                        <TiredSegment key={AnimationSegment.TIRED} />
                    )}

                    {currentSegment === AnimationSegment.SOLUTION && (
                        <SolutionSegment key={AnimationSegment.SOLUTION} />
                    )}

                    {currentSegment === AnimationSegment.APP && (
                        <AppSegment key={AnimationSegment.APP} />
                    )}

                    {(currentSegment === AnimationSegment.APP_SIGN ||
                        currentSegment === AnimationSegment.FEATURES ||
                        currentSegment === AnimationSegment.CTA) && (
                        <AppSignSegment key={AnimationSegment.APP_SIGN} />
                    )}
                </AnimatePresence>
            </PhoneFrame>

            <AnimatePresence exitBeforeEnter initial={false}>
                {currentSegment === AnimationSegment.FEATURES && (
                    <FeaturesSegment key={AnimationSegment.FEATURES} />
                )}

                {currentSegment === AnimationSegment.CTA && (
                    <CtaSegment
                        key={AnimationSegment.CTA}
                        hasKeps={data.hasKeps}
                        hasMobileApp={data.hasMobileApp}
                    />
                )}
            </AnimatePresence>

            <ToggleButton
                mode={toggleButtonMode}
                onToggle={handleToggleAnimation}
            />
        </>
    );
};

export default KepMobileAppAd;
