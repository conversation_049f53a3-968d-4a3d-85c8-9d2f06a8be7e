import React from 'react';

import { motion } from 'framer-motion';
import { t } from 'ttag';

import { TypedText } from '../TypedText';

import glowingStarSrc from '../../assets/glowing-star.gif';

import css from './AppSegment.css';

export const AppSegment = () => {
    return (
        <div className={css.root}>
            <motion.img
                variants={{
                    hidden: { opacity: 0 },
                    visible: { opacity: 1, transition: { duration: 1 } },
                }}
                initial="hidden"
                animate="visible"
                exit="hidden"
                className={css.star}
                src={glowingStarSrc}
                alt={t`Пульсуюча зірка`}
                width={112}
                height={112}
            />

            <TypedText delay={1} text={[t`Застосунок`, t`Вчасно.КЕП`]} />
        </div>
    );
};
