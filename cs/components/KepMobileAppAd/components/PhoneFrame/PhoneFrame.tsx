import React from 'react';

import { Transition, motion } from 'framer-motion';
import { t } from 'ttag';

import iphoneFrameSrc from '../../assets/iphone-frame.png';

import css from './PhoneFrame.css';

export enum PhoneFramePosition {
    HIDDEN = 'hidden',
    PARTIALLY_VISIBLE = 'partially_visible',
    FULLY_VISIBLE = 'fully_visible',
}

type PhoneFrameProps = {
    children?: React.ReactNode;
    position: PhoneFramePosition;
    onClick?: () => void;
};

const transition: Transition = {
    type: 'spring',
    stiffness: 260,
    damping: 20,
};

export const PhoneFrame: React.FC<PhoneFrameProps> = ({
    children,
    position,
    onClick,
}) => {
    return (
        <motion.div
            onClick={onClick}
            className={css.root}
            variants={{
                [PhoneFramePosition.HIDDEN]: {
                    bottom: '-150px',
                    right: '-225px',
                    rotate: '-16deg',
                    transition,
                },
                [PhoneFramePosition.PARTIALLY_VISIBLE]: {
                    bottom: '-150px',
                    right: '40px',
                    rotate: '-16deg',
                    transition,
                },
                [PhoneFramePosition.FULLY_VISIBLE]: {
                    bottom: '10px',
                    right: '75px',
                    rotate: '0deg',
                    transition,
                },
            }}
            initial="hidden"
            animate={position}
        >
            <img
                className={css.frame}
                src={iphoneFrameSrc}
                alt={t`Рамка телефону`}
            />
            <div className={css.content}>{children}</div>
        </motion.div>
    );
};
