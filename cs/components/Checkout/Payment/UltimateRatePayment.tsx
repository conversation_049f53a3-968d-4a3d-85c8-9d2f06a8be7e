import React from 'react';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';

import { Button, FlexBox, Text, Title } from '@vchasno/ui-kit';

import CompanyEmployeeNumberSelect from 'components/CompanyEmployeeNumber/CompanyEmployeeNumberSelect';
import {
    FormData,
    useCompanyEmployeeNumberForm,
} from 'components/CompanyEmployeeNumber/useCompanyEmployeeNumberForm';
import { useContactSalesMutation } from 'components/CompanyEmployeeNumber/useContactSalesMutation';
import { getCurrentCompanyIsFop } from 'selectors/app.selectors';
import { t } from 'ttag';
import Card from 'ui/card/card';
import Icon from 'ui/icon';

import CheckedSVG from '../../../icons/checked.svg';

const ultimateFeatureList = [
    t`додаткові параметри документів`,
    t`сценарії документів`,
    t`обов’язкові поля`,
];

interface UltimateRatePaymentProps {
    onClick?: VoidFunction;
}

const UltimateRatePayment: React.FC<UltimateRatePaymentProps> = ({
    onClick,
}) => {
    const contactSalesMutation = useContactSalesMutation();
    const methods = useCompanyEmployeeNumberForm();
    const companyIsFOP = useSelector(getCurrentCompanyIsFop);

    const onSubmit = async (data: FormData) => {
        if (companyIsFOP) {
            await contactSalesMutation.mutateAsync([]);
        } else {
            await contactSalesMutation.mutateAsync([data.selectedOption.value]);
        }
    };

    return (
        <Card wideContent>
            <Title
                level={2}
                style={{
                    fontSize: 24,
                    lineHeight: '28px',
                    marginBottom: 30,
                }}
            >{t`Тариф «Максимальний»`}</Title>
            <form noValidate onSubmit={methods.handleSubmit(onSubmit)}>
                <FlexBox direction="column" gap={20}>
                    <Text strong style={{ fontSize: 16, lineHeight: '18px' }}>
                        {t`Відкрийте доступ до всіх платних функцій сервісу:`}
                    </Text>
                    <FlexBox tagName="ul" direction="column" gap={10}>
                        {ultimateFeatureList.map((feature) => (
                            <FlexBox key={feature} tagName="li" align="center">
                                <Icon
                                    glyph={CheckedSVG}
                                    style={{
                                        width: 14,
                                        height: 14,
                                        color: 'var(--green-color)',
                                    }}
                                />
                                <Text>{feature}</Text>
                            </FlexBox>
                        ))}
                    </FlexBox>
                    {!companyIsFOP && (
                        <Controller
                            control={methods.control}
                            name="selectedOption"
                            render={({ field }) => (
                                <CompanyEmployeeNumberSelect
                                    value={field.value}
                                    onChange={field.onChange}
                                />
                            )}
                        />
                    )}
                    <Button
                        type="submit"
                        onClick={() => {
                            onClick?.();
                        }}
                    >{t`Замовити`}</Button>
                </FlexBox>
            </form>
        </Card>
    );
};

export default UltimateRatePayment;
