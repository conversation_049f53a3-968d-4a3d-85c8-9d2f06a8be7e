import React, { useEffect, useMemo } from 'react';
import { connect, useSelector } from 'react-redux';

import cn from 'classnames';
import { redirect } from 'lib/navigation';
import { getSessionStorageItem } from 'lib/webStorage';
import {
    getCurrentCompanyEdrpou,
    getCurrentUserRoleId,
    getIsSharedDocumentViewMode,
} from 'selectors/app.selectors';
import { Document } from 'services/documents/ts/types';
import {
    getIsOnlyExtraSignatureDocuments,
    getRemainSideSignaturesCount,
    isExtraSignature,
} from 'services/documents/utils';
import { t } from 'ttag';

import { StoreState } from '../../types/store';
import { SignPopupState } from '../signPopup/types';

import { mapSignStateToCounts } from './utils';

import Alert from '../ui/Alert/Alert';
import Message from '../ui/message/message';

import eventTracking from '../../services/analytics/eventTracking';
import {
    DocumentActionInfoLayoutMultiple,
    DocumentActionInfoLayoutSingle,
} from '../documentActionInfoMessage/DocumentActionInfoLayout/presets';
import SignUnsuccesfulResults from '../documentActionInfoMessage/SignUnsuccesfulResults/SignUnsuccesfulResults';
import Loading from '../loading/loading';
import SuccessDocumentActionMessageContainer from '../successDocumentActionMessage/SuccessDocumentActionMessageContainer';
import SuccessDocumentActionMessageList from '../successDocumentActionMessage/SuccessDocumentActionMessageList/SuccessDocumentActionMessageList';
import BalanceReplenishmentButton from './BalanceReplenishmentButton';
import DocumentSignMessage from './DocumentSignMessage';
import SignResultsViewTemplate from './SignResultsViewTemplate/SignResultsViewTemplate';

import css from './SuccesfulSignView.css';

type OwnProps = { onClose: () => Promise<void> };

type StateProps = {
    actionName: string;
    isMass: boolean;
    signedSuccessfullyCount: number;
    extraSignedSuccessfullyCount: number;
    showSignOnlyCounterDetails: boolean;
    isInternalDocuments: boolean;
    email: string;
    isEmailHidden: boolean;
    docs: Document[];
    edrpou: string;
    signedOnlyDocSignaturesCount: {
        remain: number;
    };
    successfulResultsCount: number;
    totalDocsCount: number;
    isOverlimitError: false;
    signedAndSendSuccessfullyCount: number;
    errorMessage: string;
    unsuccessfulResults: SignPopupState['multiSignResult'];
};

const SuccesfulSignView: React.FC<
    React.PropsWithChildren<StateProps & OwnProps>
> = ({
    onClose,
    actionName,
    isMass,
    signedSuccessfullyCount,
    extraSignedSuccessfullyCount,
    showSignOnlyCounterDetails,
    isInternalDocuments,
    email,
    isEmailHidden,
    docs,
    edrpou,
    signedOnlyDocSignaturesCount,
    successfulResultsCount,
    totalDocsCount,
    isOverlimitError,
    signedAndSendSuccessfullyCount,
    errorMessage,
    unsuccessfulResults,
}) => {
    const currentRoleId = useSelector(getCurrentUserRoleId);
    const currentCompanyEdrpou = useSelector(getCurrentCompanyEdrpou);
    const isSharedDocumentViewMode = useSelector(getIsSharedDocumentViewMode);

    useEffect(() => {
        eventTracking.sendToGTMV4({ event: 'ec_edo_sign_doc' });
    }, []);

    useEffect(() => {
        if (isSharedDocumentViewMode) {
            // sign-session context effect
            // user should be logged in (with auth cookies) and need redirection to application context
            const redirectUrl = getSessionStorageItem(
                'DIIA:add_unregistered_signature:next_url',
            );

            if (redirectUrl) {
                redirect(redirectUrl);
            }
        }
    }, [isSharedDocumentViewMode]);

    const isOnlyExtraSignatureDocuments = useMemo(
        () =>
            getIsOnlyExtraSignatureDocuments(
                docs,
                currentRoleId,
                currentCompanyEdrpou,
            ),
        [docs],
    );

    if (!isMass) {
        return (
            <SignResultsViewTemplate
                title={t`Ви успішно ${actionName} документ!`}
                content={
                    <div className={css.textBlock}>
                        <DocumentSignMessage className={css.paragraph} />
                        <SuccessDocumentActionMessageContainer
                            isSignedOnly={
                                signedSuccessfullyCount > 0 ||
                                showSignOnlyCounterDetails
                            }
                            isInternal={isInternalDocuments}
                        >
                            {isSharedDocumentViewMode && (
                                <div className={css.loader}>
                                    <div>
                                        <Loading
                                            type="spinner"
                                            spinnerSize={40}
                                        />
                                    </div>
                                </div>
                            )}
                            <SuccessDocumentActionMessageList
                                documentTitle={docs[0].title}
                                email={email}
                                edrpou={edrpou}
                                isEmailHidden={isEmailHidden}
                            />
                        </SuccessDocumentActionMessageContainer>
                        {showSignOnlyCounterDetails &&
                            signedOnlyDocSignaturesCount.remain && (
                                <div className={css.documentActionInfo}>
                                    <DocumentActionInfoLayoutSingle
                                        signedOnlyDocSignaturesCount={
                                            signedOnlyDocSignaturesCount
                                        }
                                        isInternalDocuments={
                                            isInternalDocuments
                                        }
                                    />
                                </div>
                            )}
                    </div>
                }
            />
        );
    }

    if (successfulResultsCount === 0) {
        return (
            <SignResultsViewTemplate
                withError
                title={t`Вам не вдалося підписати і надіслати документи`}
                content={
                    <>
                        <div className={cn(css.statusBlock, css.lgBottomMg)}>
                            {isOverlimitError && (
                                <Alert theme="warning">
                                    {t`У вас закінчились документи на балансі`}
                                    . <br />
                                    {t`Кількість документів, що не вдалося обробити через нестачу`}
                                    :
                                    {` ${
                                        totalDocsCount - successfulResultsCount
                                    }`}
                                </Alert>
                            )}
                        </div>
                        {isOverlimitError && (
                            <BalanceReplenishmentButton
                                className={css.billButton}
                            />
                        )}
                        <SignUnsuccesfulResults
                            errorMessage={errorMessage}
                            unsuccessfulResults={unsuccessfulResults}
                        />
                    </>
                }
            />
        );
    }

    const successfullyCount = successfulResultsCount || signedSuccessfullyCount;

    return (
        <SignResultsViewTemplate
            title={t` Ви успішно ${actionName} документи:`}
            content={
                <div className={css.statusBlock}>
                    <div className={css.smBottomMg}>
                        <Message type="success">
                            {t`${successfullyCount} з ${totalDocsCount} документів`}
                        </Message>
                    </div>
                    {(signedSuccessfullyCount > 0 ||
                        successfulResultsCount > 0) && (
                        <DocumentSignMessage
                            isPlural
                            className={cn(css.paragraph, css.smBottomMg)}
                        />
                    )}
                    {isOverlimitError && (
                        <div className={css.smBottomMg}>
                            <Alert theme="warning">
                                {t`У вас закінчились документи на балансі`}
                                . <br />
                                {t`Кількість документів, що не вдалося обробити через нестачу`}
                                : {totalDocsCount - successfulResultsCount}
                            </Alert>
                        </div>
                    )}
                    {isOverlimitError && (
                        <BalanceReplenishmentButton
                            className={css.billButton}
                        />
                    )}
                    {!isOnlyExtraSignatureDocuments && (
                        <DocumentActionInfoLayoutMultiple
                            totalDocsCount={totalDocsCount}
                            isOverlimitError={isOverlimitError}
                            signedAndSendSuccessfullyCount={
                                signedAndSendSuccessfullyCount
                            }
                            signedSuccessfullyCount={signedSuccessfullyCount}
                            extraSignedSuccessfullyCount={
                                extraSignedSuccessfullyCount
                            }
                            onClose={onClose}
                            isInternalDocuments={isInternalDocuments}
                        />
                    )}
                    <SignUnsuccesfulResults
                        errorMessage={errorMessage}
                        unsuccessfulResults={unsuccessfulResults}
                    />
                </div>
            }
        />
    );
};

const mapStateToProps = (state: StoreState) => {
    const {
        app: {
            currentUser: { currentCompany, currentRole },
        },
        signPopup,
    } = state;
    const doc = signPopup.docs[0];
    const remainSignatures = getRemainSideSignaturesCount(doc);
    const isExtraSign = isExtraSignature(
        doc,
        currentRole.id,
        currentCompany.edrpou,
    );
    let signedOnlyDocSignaturesCount: { total?: number; remain?: number } = {};

    if (!signPopup.isMultiSign && !isExtraSign) {
        const signaturesLength = doc.signatures.filter((item) => {
            const signatureEdrpou =
                item.keyOwnerEdrpou || item.stampOwnerEdrpou;
            return signatureEdrpou === currentCompany.edrpou;
        }).length;
        if (remainSignatures > 1) {
            signedOnlyDocSignaturesCount = {
                total: signaturesLength + 1,
                remain: remainSignatures - 1,
            };
        }
    }

    const {
        actionName,
        isOverlimitError,
        signedAndSendSuccessfullyCount,
        signedSuccessfullyCount,
        showSignOnlyCounterDetails,
        isInternalDocuments,
        successfulResultsCount,
        unsuccessfulResults,
        extraSignedSuccessfullyCount,
    } = mapSignStateToCounts({
        docs: signPopup.docs,
        currentRoleId: currentRole.id,
        currentCompanyEdrpou: currentCompany.edrpou,
        multiSignResult: signPopup.multiSignResult,
        isMultiSign: signPopup.isMultiSign,
        signedOnlyDocSignaturesCount,
    });

    // якщо це власник документу підписує то контекстом є власник а отримуваче буде Recipient
    // якщо на стороні Recipient документу то отримувачем буде Owner
    const isDocumentOnOwnerSide =
        currentCompany.edrpou === signPopup.edrpouOwner;

    const email = isDocumentOnOwnerSide
        ? signPopup.emailRecipient
        : signPopup.emailOwner;
    const edrpou = isDocumentOnOwnerSide
        ? signPopup.edrpouRecipient
        : signPopup.edrpouOwner;

    return {
        actionName,
        isMass: signPopup.isMultiSign,
        signedSuccessfullyCount,
        showSignOnlyCounterDetails,
        isInternalDocuments,
        email,
        docs: signPopup.docs,
        edrpou,
        signedOnlyDocSignaturesCount,
        successfulResultsCount,
        extraSignedSuccessfullyCount,
        totalDocsCount: signPopup.totalDocsCount,
        isOverlimitError,
        signedAndSendSuccessfullyCount,
        errorMessage: signPopup.errorMessage,
        unsuccessfulResults,
        isEmailHidden: signPopup.isRecipientEmailHidden,
    } as StateProps;
};

export default connect<StateProps, OwnProps>(mapStateToProps)(
    SuccesfulSignView,
);
