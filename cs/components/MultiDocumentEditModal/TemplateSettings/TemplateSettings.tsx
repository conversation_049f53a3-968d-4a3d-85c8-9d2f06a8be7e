import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import {
    BlackTooltip,
    FlexBox,
    SelectComponents,
    SelectProps,
} from '@vchasno/ui-kit';

import TemplateSettingsSelect, {
    TemplateOption,
    TemplateSettingsSelectProps,
} from 'components/DocumentsUploader/TemplateSettings/TemplateSettingsSelect';
import { useTemplateResetEffect } from 'components/DocumentsUploader/TemplateSettings/useTemplateResetEffect';
import { useUISectionsContext } from 'components/DocumentsUploader/useUISectionsContext';
import Icon from 'ui/icon';

import { MultiEditDocumentForm } from '../types';

import { getOptionDisableMessage } from './utils';

import { useMultiDocumentEditContext } from '../MultiDocumentEditContext';

import InfoSvg from '../../../icons/info.svg';

import css from './TemplateSettings.css';

const MultiDocumentEditTemplateSettings = () => {
    const [snapshot, setSnapshot] = useState<MultiEditDocumentForm | null>(
        null,
    );
    const methods = useFormContext<MultiEditDocumentForm>();
    const { docs } = useMultiDocumentEditContext();
    const [, { expandSections }] = useUISectionsContext();

    const templateDataRef = useTemplateResetEffect(() => {
        setSnapshot(null);
    });

    const components: SelectProps['components'] = {
        Option: (props) => {
            const disableReason = getOptionDisableMessage(
                (props.data as TemplateOption).source,
                docs,
            );
            return (
                <SelectComponents.Option {...props}>
                    <FlexBox justify="space-between">
                        {props.children}
                        {disableReason && (
                            <BlackTooltip
                                disableInteractive
                                title={disableReason}
                            >
                                <span className={css.icon}>
                                    <Icon glyph={InfoSvg} />
                                </span>
                            </BlackTooltip>
                        )}
                    </FlexBox>
                </SelectComponents.Option>
            );
        },
    };

    const expandSectionsEffect = (
        dataFromTemplate: Parameters<TemplateSettingsSelectProps['onApply']>[0],
    ) => {
        const expandedSections: Parameters<typeof expandSections>[0] = [];

        if (
            dataFromTemplate.employeesSigners.length > 0 ||
            dataFromTemplate.employeesViewers.length > 0 ||
            dataFromTemplate.employeesReviewers.length > 0
        ) {
            expandedSections.push('employees');
        }

        if (dataFromTemplate.selectedTags.length > 0) {
            expandedSections.push('tags');
        }

        if (dataFromTemplate.documentParameters.length > 0) {
            expandedSections.push('additionalParameters');
        }

        if (expandedSections.length) {
            expandSections(expandedSections);
        }
    };

    return (
        <TemplateSettingsSelect
            components={components}
            isOptionDisabled={({ source }: TemplateOption) =>
                Boolean(getOptionDisableMessage(source, docs))
            }
            onApply={(dataFromTemplate) => {
                templateDataRef.current = dataFromTemplate;
                const formValues = methods.getValues();
                methods.setValue('templateId', dataFromTemplate.templateId);

                if (!snapshot) {
                    setSnapshot(formValues);
                }

                methods.reset(
                    {
                        ...formValues,
                        ...dataFromTemplate,
                        // якщо відбувається підставляння параметрів зі сценарію - змінюємо режим оновлення на append
                        additionalParametersUpdateMode:
                            dataFromTemplate.documentParameters.length > 0
                                ? 'append'
                                : formValues.additionalParametersUpdateMode,
                        tagsUpdateMode:
                            dataFromTemplate.selectedTags.length > 0
                                ? 'append'
                                : formValues.tagsUpdateMode,
                    },
                    {
                        // важливо збергіати defaultValues - щоб використовувати для порівняння перед відправкою
                        keepDefaultValues: true,
                    },
                );

                expandSectionsEffect(dataFromTemplate);
            }}
            onReset={() => {
                if (snapshot) {
                    methods.reset(snapshot, {
                        keepDefaultValues: true,
                    });
                    setSnapshot(null);
                }
            }}
        />
    );
};

export default MultiDocumentEditTemplateSettings;
