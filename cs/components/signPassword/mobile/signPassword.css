.root {
    display: block;
}

.inputHolder {
    display: block;
    border: none;
    margin-bottom: 20px;
}

.buttonHolder {
    display: block;
    width: 100%;
    margin-top: 20px;
}

.input {
    display: block;
    width: 100%;
    box-sizing: border-box;
    padding: 10px 15px;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    border-radius: 8px 0 0 8px;
    box-shadow: none;
    font-family: 'Roboto', Arial, Tahoma, Helvetica, 'Liberation Sans', sans-serif;
    font-size: 13px;
    line-height: 18px;
    vertical-align: middle;
}

.input:focus {
    border-color: var(--primary-cta-color);
}

.error {
    margin-top: 4px;
    color: var(--red-color);
}

.info {
    padding: 10px;
    border: 3px solid var(--corporate-color);
    margin-top: 4px;
    border-radius: var(--border-radius);
    font-weight: bold;
}

.code {
    font-size: 18px;
}

.hidden {
    display: none;
}

.buttonContainer {
    position: relative;
}

.hintIcon {
    position: absolute;
    top: 7px;
    right: -20px;
    width: 20px;
}

.hint {
    width: 200px;
}

.alert {
    margin-top: 15px;
}
