import React, { FC } from 'react';
import { useMediaQuery } from 'react-responsive';

import {
    BusinessPaperlessBanner as BusinessPaperlessBannerShared,
    useBusinessPaperlessBannerContext,
} from '@vchasno/shared-components';

import { FeatureShow } from 'components/FeatureDisplay';
import { MEDIA_WIDTH } from 'lib/constants';

import css from './BusinessPaperlessBanner.css';

export const BusinessPaperlessBanner: FC = () => {
    const { isClose, setIsClose } = useBusinessPaperlessBannerContext();
    const isMobile = useMediaQuery({ maxWidth: MEDIA_WIDTH.tablet });

    if (isClose) {
        return null;
    }

    return (
        <FeatureShow feature="PAPERLESS_BANNER">
            <div className={css.wrapper}>
                <BusinessPaperlessBannerShared
                    onClose={setIsClose}
                    slider={!isMobile}
                    mobile={isMobile}
                    {...(isMobile && {
                        slide: 0,
                    })}
                />
            </div>
        </FeatureShow>
    );
};
