.root, .popup {
    min-width: 800px;
    min-height: 600px;
}

.root {
    display: flex;
}

.bgSection {
    position: relative;
    overflow: hidden;
    width: 50%;
    background: linear-gradient(80deg, #EEF6FF 0.21%, #FBEFFF 49.78%, #FFF2ED 99.34%);
    border-radius: var(--border-radius);
}

.phoneWrap {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.phone {
    width: 205px;
    aspect-ratio:  205 / 418;
}

.adMenu {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    max-height: 80%;
    box-sizing: border-box;
    padding: 40px 60px;
    border-top: 1px solid #fff;
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
}

.appSvg {
    position: absolute;
    top: 0;
    left: 50%;
    width: 60px;
    height: 60px;
    transform: translate(-50%, -50%);
}

.qrCode {
    display: block;
    width: 132px;
    max-width: 90%;
    aspect-ratio: 1 / 1;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.content {
    display: flex;
    width: 50%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.label {
    color: var(--content-color);
    font-size: 24px;
    font-weight: 500;
    line-height: 28px;
}

@media screen and (max-width: 768px) {
    .root, .popup {
        width: 100%;
        min-width: unset;
        height: 98vh;
        min-height: unset;
    }

    .bgSection {
        width: 100%;
    }

    .adMenu {
        min-height: 50%;
        padding-top: 80px;
    }

    .content, .desktopContent {
        display: none;
    }
}


