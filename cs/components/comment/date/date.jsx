// TODO: Refactor this to have common date component to rendering any dates
// within user interface
import React from 'react';

import PropTypes from 'prop-types';
import { t } from 'ttag';

import { formatDate } from '../../../lib/date';

import css from './date.css';

const CommentDate = ({ isLtr, date, edited, format }) => {
    const content = [
        formatDate(date, format),
        edited ? `(${t`Відредаговано`})` : '',
    ]
        .join(' ')
        .trim();

    return (
        <div className={isLtr ? css.rootLtr : css.root}>
            <time dateTime={date}>{content}</time>
        </div>
    );
};

CommentDate.defaultProps = {
    isLtr: false,
    edited: false,
};

CommentDate.propTypes = {
    isLtr: PropTypes.bool,
    edited: PropTypes.bool,
    date: PropTypes.string.isRequired,
    format: PropTypes.string.isRequired,
};

export default CommentDate;
