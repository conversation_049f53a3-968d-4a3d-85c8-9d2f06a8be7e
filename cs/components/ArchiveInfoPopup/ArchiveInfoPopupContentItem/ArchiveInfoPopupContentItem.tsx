import React from 'react';

import { FlexBox } from '@vchasno/ui-kit';

import Icon, { Glyph } from 'components/ui/icon';
import { t } from 'ttag';

import InDevelopmentIcon from './icon/InDevelopment.svg';

import css from './ArchiveInfoPopupContentItem.css';

interface ArchiveInfoPopupContentItemProps {
    item: {
        icon: Glyph;
        title: string;
        content: string;
        inDevelopment: boolean;
    };
}

const ArchiveInfoPopupContentItem: React.FC<ArchiveInfoPopupContentItemProps> = ({
    item: { icon, title, content, inDevelopment },
}) => {
    return (
        <div className={css.root}>
            <FlexBox align="center">
                <Icon glyph={icon} className={css.contentIcon} />
                <h3 className={css.itemTitle}>{title}</h3>
            </FlexBox>
            <div className={css.content}>{content}</div>
            {inDevelopment && (
                <FlexBox align="center" className={css.inDevelopment}>
                    <Icon
                        glyph={InDevelopmentIcon}
                        className={css.inDevelopmentIcon}
                    />

                    <div>{t`В розробці`}</div>
                </FlexBox>
            )}
        </div>
    );
};

export default ArchiveInfoPopupContentItem;
