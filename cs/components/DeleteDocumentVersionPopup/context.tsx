import React from 'react';

import { DocumentVersion } from '../../services/documents/ts/types';
import { Nullable } from '../../types/general';

import { useContextHandler } from './useContextHandler';

interface IContext {
    isActive: boolean;
    onOpen: (chooseVersion: DocumentVersion) => void;
    onClose: VoidFunction;
    version: Nullable<DocumentVersion>;
    handleDeleteVersion: VoidFunction;
}

const DeleteDocumentVersionPopupContext = React.createContext({} as IContext);

export const DeleteDocumentVersionPopupProvider: React.FC = (props) => {
    const { value } = useContextHandler();

    return (
        <DeleteDocumentVersionPopupContext.Provider value={value}>
            {props.children}
        </DeleteDocumentVersionPopupContext.Provider>
    );
};

export function useDeleteDocumentVersionPopupContext() {
    const context = React.useContext(DeleteDocumentVersionPopupContext);

    if (Object.keys(context).length === 0) {
        throw new Error(
            'useDeleteDocumentVersionPopupContext must be inside DeleteDocumentVersionPopupProvider',
        );
    }

    return context;
}
