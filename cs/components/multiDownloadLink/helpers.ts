import { DownloadDocumentsOptions } from '../../types/downloads';

export const getDocumentsDownloadOptions = (
    isVisualisation?: boolean,
    isDownloadArchive = false,
) => {
    // Download documents originals with signatures, each document in separate folder
    const options: DownloadDocumentsOptions = {
        inOneFolder: false,
        withSignatures: true,
        withXMLPreview: false, // TODO: Make `true` after the service URL2PDF will be optimized
        withXMLOriginal: true,
        isDownloadArchive,
    };

    if (isVisualisation) {
        // Download only documents previews in one folder
        options.inOneFolder = true;
        options.withSignatures = false;
        options.withXMLPreview = true;
        options.withXMLOriginal = false;
    }

    return options;
};
