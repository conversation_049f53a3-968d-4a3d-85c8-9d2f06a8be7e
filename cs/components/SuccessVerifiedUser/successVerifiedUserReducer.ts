import { Reducer } from 'react';

import { AnyAction } from 'redux';

import { SuccessVerifiedUserState } from './types';

import actions from './successVerifiedUserActions';

import { DISPLAY_STATUSES } from './constants';

const initState: SuccessVerifiedUserState = {
    displayStatus: DISPLAY_STATUSES.HIDDEN,
    phoneNumberForOTPMessage: '',
    isEditMode: false,
};

const successVerifiedUserReducer: Reducer<
    SuccessVerifiedUserState,
    AnyAction
> = (state = initState, action) => {
    switch (action.type) {
        case actions.SUCCESS_VERIFIED_USER__SET_DISPLAY_STATUS:
            return {
                ...state,
                displayStatus: action.displayStatus,
            };
        case actions.SUCCESS_VERIFIED_USER__SET_PHONE_NUMBER_FOR_OTP_MESSAGE:
            return {
                ...state,
                phoneNumberForOTPMessage: action.phoneNumberForOTPMessage,
            };
        case actions.SUCCESS_VERIFIED_USER__SET_EDIT_MODE:
            return {
                ...state,
                isEditMode: true,
            };
        case action.SUCCESS_VERIFIED_USER__RESET:
            return {
                ...initState,
            };
        default:
            return state;
    }
};

export default successVerifiedUserReducer;
