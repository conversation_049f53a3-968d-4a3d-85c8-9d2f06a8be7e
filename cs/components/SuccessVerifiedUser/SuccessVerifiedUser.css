.congratulationText h1 {
    color: var(--content-color);
    font-size: 32px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.congratulationText h4 {
    color: var(--slate-grey-color);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.greenBox {
    box-sizing: border-box;
    padding: 16px 24px;
    border: none;
    background-color: var(--pale-green-bg);
    border-radius: var(--border-radius);
}

.greenBox span {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.successIcon {
    max-width: 20px;
    max-height: 20px;
}

.additionalInfoFormTitleBox h3 {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
}

.additionalInfoFormTitleBox h4 {
    color: #6b8091;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

@media all and (max-width: 768px) {
    .congratulationText h1 {
        font-size: 24px;
    }
}
