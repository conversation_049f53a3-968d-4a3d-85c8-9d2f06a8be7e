import { useMutation } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import { contactSalesManagerWhenEmployeeLimitReached } from 'services/billing';
import { EmployeesNumber } from 'services/ts/billing';
import { t } from 'ttag';

export const useContactSalesMutation = () => {
    return useMutation({
        mutationFn: (params: [EmployeesNumber?]) =>
            contactSalesManagerWhenEmployeeLimitReached(
                params[0]
                    ? {
                          employeesNumber: params[0],
                      }
                    : undefined,
            ),
        onSuccess: () => {
            snackbarToast.success(t`Ми зв'яжемося з вами найближчим часом`);
        },
    });
};
