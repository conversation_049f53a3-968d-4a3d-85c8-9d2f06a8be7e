import React from 'react';

import { Select } from '@vchasno/ui-kit';

import { t } from 'ttag';

import { options } from './options';

interface CompanyEmployeeNumberSelectProps {
    value: typeof options[number];
    onChange: (option: typeof options[number]) => void;
}

const CompanyEmployeeNumberSelect: React.FC<CompanyEmployeeNumberSelectProps> = ({
    onChange,
    value,
}) => {
    return (
        <Select
            wide
            label={t`Кількість співробітників у компанії/бізнесі`}
            options={options}
            value={value}
            isSearchable={false}
            onChange={(option: typeof options[number]) => {
                onChange(option);
            }}
        />
    );
};

export default CompanyEmployeeNumberSelect;
