.root {
    display: flex;
    margin: 30px 0;
}

@media screen and (max-width: 768px) {
    .root {
        margin: 0;
    }
}

.content {
    flex: 1;
    padding-left: 20px;
    margin: 30px 0;
}

@media screen and (max-width: 768px) {
    .content {
        padding: 0 30px;
        margin: 0;
    }
}

.header {
    position: relative;
    margin-bottom: 20px;
    font-weight: bold;
}

.image {
    width: 160px;
    text-align: center;
}

.list {
    padding-right: 60px;
}

.step {
    position: relative;
    padding-left: 15px;
    margin-top: 15px;
    counter-increment: item;
}

.step::before {
    position: absolute;
    top: 0;
    left: 0;
    content: counter(item) '.';
    font-weight: bold;
}

.icon {
    position: absolute;
    top: 6px;
    right: 100%;
    width: 10px;
    height: 10px;
    margin-right: 4px;
}
