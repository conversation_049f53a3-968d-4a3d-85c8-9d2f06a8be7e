.container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.configItemWrapper {
    min-width: 280px;
    max-width: 300px;
    flex: 1;
}

.configItem {
    position: relative;
    display: flex;
    height: 80px;
    box-sizing: border-box;
    align-items: center;
    padding-left: 20px;
    border: 1px solid var(--default-border);
    border-radius: var(--border-radius);
    cursor: pointer;
}

.configItem:hover {
    background: var(--grey-bg);
}

.configItemImage {
    position: absolute;
    right: 18px;
    bottom: 0;
    width: 72px;
    height: 65px;
}

.input:checked + .inputLabel .configItem {
    background: var(--grey-bg);
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
}

.configItemCircle {
    width: 20px;
    height: 20px;
    margin-right: 6px;
}

.input:checked + .inputLabel .configItemCircle {
    border-color: var(--vchasno-ui-checkbox-bg-color);
}

.input:checked + .inputLabel .configItemCircle::after {
    display: block;
}
