import React from 'react';

import { BlackTooltip } from '@vchasno/ui-kit';

import SvgDownload from 'icons/doc-download.svg';
import { Document } from 'services/documents/ts/types';
import { t } from 'ttag';

import { DocumentListDocumentItem } from '../documentList/types';

import IconButton from '../ui/iconButton/iconButton';

import { useButtonHandler } from './useButtonHandler';

interface Props {
    doc: DocumentListDocumentItem | Document;
    versionId?: string;
    isInPopup?: boolean;
}

const DownloadDocumentVersionButton: React.FC<Props> = ({
    doc,
    versionId,
    isInPopup,
}) => {
    const { handleDownloadDocument } = useButtonHandler(
        doc,
        versionId,
        isInPopup,
    );

    return (
        <BlackTooltip title={t`Завантажити`} disableInteractive>
            <span onClick={handleDownloadDocument}>
                <IconButton svg={SvgDownload} />
            </span>
        </BlackTooltip>
    );
};

export default DownloadDocumentVersionButton;
