.root {
    display: table;
    min-width: 500px;
    max-width: 780px;
    margin-bottom: 20px;
    table-layout: fixed;
}

.padding:global(.vchasno-ui-button.--secondary) {
    padding: 0 5px;
}

@media all and (max-width: 780px) {
    .root {
        min-width: 100%;
    }
}

.imageHolder {
    display: table-cell;
    width: 250px;
    text-align: center;
    vertical-align: middle;
}

.main,
.mainMiddle {
    display: table-cell;
    padding-top: 20px;
}

@media all and (max-width: 768px) {
    .main {
        padding-top: 0;
    }
}

.mainMiddle {
    vertical-align: middle;
}

.title {
    margin: 10px 0 30px;
    font-size: 18px;
    font-weight: bold;
}

.text {
    margin-bottom: 10px;
}

.paragraph + .paragraph {
    margin-top: 20px;
}

.paragraphBold {
    font-weight: bold;
}

.verifyButtons {
    display: grid;
    width: 100%;
    min-height: 90px;
    margin: 0 auto;
    grid-gap: 10px;
    grid-template-columns: repeat(3, 160px);
}

@media all and (max-width: 780px) {
    .verifyButtons {
        grid-template-columns: repeat(2, 160px);
    }
}

.content {
    margin: 15px 0;
}

.error {
    margin: 50px 0;
}

.hint {
    color: var(--dark-pigeon-color);
}
