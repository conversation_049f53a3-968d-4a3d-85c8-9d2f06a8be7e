import React from 'react';
import { useFieldArray } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import {
    BlackTooltip,
    Button,
    FlexBox,
    FullScreenModal,
    TextInput,
    snackbarToast,
} from '@vchasno/ui-kit';

import AutofillTemplateSelect from 'components/DocumentCreation/AutofillTemplateSelect';
import { StoredReplaceFormField } from 'components/DocumentCreation/TemplateReplaceForm';
import {
    AutofillFiledForm,
    useAutofillForm,
} from 'components/DocumentCreation/useAutofillForm';
import { useTemplatePreview } from 'components/DocumentCreationCompanyTemplates/PreviewItem/useTemplatePreview';
import MultiBrandLogo from 'components/header/components/MultiBrandLogo';
import { getIsAutofillTemplateModalOpen } from 'selectors/documentCreation.selectors';
import { createDocumentFromTemplateWithSpecifiedFields } from 'services/creationTemplates';
import { setAutoFillTemplateModalOpen } from 'store/documentCreationSlice';
import { t } from 'ttag';
import CloseButton from 'ui/closeButton/closeButton';

import css from './DocumentCreationModal.css';

const DocumentAutofillTemplateModal: React.FC = () => {
    const dispatch = useDispatch();
    const history = useHistory();
    const isOpen = useSelector(getIsAutofillTemplateModalOpen);
    const [
        selectedAutofillForm,
        setSelectedAutofillForm,
    ] = React.useState<StoredReplaceFormField | null>(null);
    const [preview, previewQuery] = useTemplatePreview(
        selectedAutofillForm?.id
            ? `/internal-api/templates/${selectedAutofillForm.id}/preview`
            : '',
        Boolean(selectedAutofillForm?.id),
        {
            size: 1024,
        },
    );

    const methods = useAutofillForm(selectedAutofillForm);

    const watchFields = methods.watch('fields');
    const fieldsArray = useFieldArray({
        control: methods.control,
        name: 'fields',
        shouldUnregister: true,
    });
    const handleClose = () => dispatch(setAutoFillTemplateModalOpen(false));

    if (!isOpen) {
        return null;
    }

    const handleSubmit = async (data: AutofillFiledForm) => {
        try {
            const { id } = await createDocumentFromTemplateWithSpecifiedFields(
                data.templateId,
                {
                    title: data.title,
                    extra_fields: data.fields.reduce(
                        (acc, field) => ({
                            ...acc,
                            [field.name.toUpperCase()]: field.value,
                        }),
                        {},
                    ),
                },
            );

            history.push(`/app/documents/${id}?edit=true`);
            snackbarToast.success(t`Документ успішно створено`);
            handleClose();
        } catch (error) {
            snackbarToast.error(t`Помилка при створенні документа`);
            console.error(error);
        }
    };

    return (
        <FullScreenModal
            contentClassName={css.root}
            headerChildren={
                <>
                    <FlexBox gap={20} align="center" className={css.logoBox}>
                        <div className={css.logoWrapper}>
                            <MultiBrandLogo />
                        </div>
                    </FlexBox>
                    <FlexBox align="center">
                        <BlackTooltip
                            title={t`Можна продовжити редагування документу в редакторі`}
                        >
                            <Button
                                form="autofill-form"
                                theme="secondary"
                                type="submit"
                                disabled={methods.formState.isSubmitting}
                            >{t`Створити Чернетку`}</Button>
                        </BlackTooltip>
                        <Button
                            form="autofill-form"
                            type="submit"
                            disabled={methods.formState.isSubmitting}
                        >{t`Створити документ`}</Button>
                        <CloseButton
                            withHover
                            position="static"
                            onClose={handleClose}
                        />
                    </FlexBox>
                </>
            }
            showAnimation
        >
            <FlexBox
                direction="column"
                justify="space-between"
                className={css.container}
            >
                <form
                    id="autofill-form"
                    onSubmit={methods.handleSubmit(handleSubmit)}
                    noValidate
                    style={{
                        display: 'flex',
                        gap: 10,
                        alignItems: 'flex-start',
                    }}
                >
                    <FlexBox grow={1} direction="column">
                        <AutofillTemplateSelect
                            onSelected={setSelectedAutofillForm}
                        />
                        {fieldsArray.fields.map((field, index) => (
                            <TextInput
                                hideEmptyMeta
                                key={field.id}
                                required={field.validation.required}
                                label={field.name}
                                value={watchFields[index].value}
                                onChange={(e) => {
                                    methods.setValue(
                                        `fields.${index}.value` as const,
                                        e.target.value,
                                    );
                                    methods.trigger(
                                        `fields.${index}.value` as const,
                                    );
                                }}
                                error={
                                    methods.formState.errors.fields?.[index]
                                        ?.value?.message as string
                                }
                            />
                        ))}
                    </FlexBox>
                    <FlexBox
                        direction="column"
                        grow={0}
                        style={{
                            width: '60%',
                            backgroundColor: 'var(--default-border)',
                            position: 'sticky',
                            top: 10,
                        }}
                    >
                        {previewQuery.isFetched && preview && (
                            <img
                                style={{
                                    display: 'inline-flex',
                                    border: '1px solid var(--default-border)',
                                    borderRadius: 4,
                                }}
                                src={preview.url}
                                alt="preview"
                            />
                        )}
                    </FlexBox>
                </form>
            </FlexBox>
        </FullScreenModal>
    );
};

export default DocumentAutofillTemplateModal;
