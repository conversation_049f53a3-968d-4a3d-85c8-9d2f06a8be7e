export type ReplaceFormFieldType = 'text' | 'date' | 'number';

export interface ReplaceFormFieldValidation {
    required: boolean;
}

export interface ReplaceFormField {
    name: string;
    type: ReplaceFormFieldType;
    value: any;
    validation: ReplaceFormFieldValidation;
}

export interface StoredReplaceFormField {
    id: string;
    title: ReplaceFormFieldType;
    fields: ReplaceFormField[];
}
