import React, { useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import {
    Button,
    FlexBox,
    Text,
    TextInput,
    Title,
    snackbarToast,
} from '@vchasno/ui-kit';

import EditorModal from 'components/DocumentCreation/EditorModal';
import TemplateReplaceForm from 'components/DocumentCreation/TemplateReplaceForm';
import { EditTemplateForm } from 'components/DocumentCreationCompanyTemplates/EditModal/types';
import { useEditTemplateMutation } from 'components/DocumentCreationCompanyTemplates/EditModal/useEditTemplateMutation';
import FavoriteIcon from 'components/DocumentCreationCompanyTemplates/FavoriteIcon';
import SingleActionIcon from 'components/DocumentCreationCompanyTemplates/PreviewItem/SingleActionIcon';
import TemplateCategoryController from 'components/DocumentCreationCompanyTemplates/TemplateCategoryController';
import { useFavoriteHandler } from 'components/DocumentCreationCompanyTemplates/TemplateList/useFavoriteHandler';
import {
    useDeleteTemplate,
    useDuplicateTemplate,
} from 'components/DocumentCreationCompanyTemplates/TemplateList/usePreviewMenuActions';
import { FeatureShow } from 'components/FeatureDisplay';
import CheckedSVG from 'icons/checked.svg';
import eventTracking from 'services/analytics/eventTracking';
import { CreationTemplate } from 'services/creationTemplates';
import { t } from 'ttag';
import CloseButton from 'ui/closeButton/closeButton';
import Icon from 'ui/icon';

import { useCollaboraQuery } from './useCollaboraQuery';

import EditSVG from '../../../icons/pen.svg';

import css from './TemplateEditor.css';

export interface TemplateEditorProps {
    template: CreationTemplate;
    onClose: VoidFunction;
    isEdit?: boolean;
    hasEditPermission?: boolean;
    onFavoriteChange?: (isFavorite: boolean) => void;
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({
    onClose,
    template,
    isEdit = true,
    hasEditPermission = false,
    onFavoriteChange,
}) => {
    // needs for feature="COLLABORA_MARKERS", no effect on the current task
    const [replaceFormVisible, setReplaceFormVisible] = React.useState(false);
    const deleteTemplateActionConfig = useDeleteTemplate({
        onDelete: onClose,
        analyticsEvent: 'ec_template_delete_page',
    });
    const favoriteHandler = useFavoriteHandler({
        onFavoriteChange,
        analyticsAddFavoriteEvent: 'ec_template_add_fav_page',
    });

    const [editMode, setEditMode] = React.useState(isEdit);
    const duplicateTemplateActionConfig = useDuplicateTemplate({
        analyticsEvent: 'ec_template_duplicate_page',
    });
    const editTemplateMutation = useEditTemplateMutation();
    const methods = useFormContext<EditTemplateForm>();
    const collaboraQuery = useCollaboraQuery(template.id, editMode);

    const onSubmit = async (data: EditTemplateForm) => {
        const editPayload = {
            title: data.title,
            category_id: data.category === null ? null : Number(data.category),
        };

        await editTemplateMutation
            .mutateAsync([template.id, editPayload])
            .catch(() =>
                snackbarToast.error(t`Помилка при збереженні метаданих`),
            );

        onClose();
    };

    // підставляємо значення у поля форми із шаблону
    useEffect(() => {
        methods.reset({
            title: template.title,
            category: template.category,
        });
    }, [template]);

    const saveMessage = (
        <Text type="secondary">
            <Icon glyph={CheckedSVG} className={css.checkIcon} />{' '}
            {t`Всі заповнені дані автоматично зберігаються`}
        </Text>
    );

    // режим редагування
    if (editMode) {
        return (
            <EditorModal
                key="edit"
                sidebarOpen={replaceFormVisible}
                collaboraQuery={collaboraQuery}
                renderHeader={({ isModified }) => (
                    <>
                        <FlexBox align="center" grow={1}>
                            <form
                                className={css.form}
                                onSubmit={methods.handleSubmit(onSubmit)}
                                id="modal-edit-template"
                            >
                                <Controller
                                    control={methods.control}
                                    render={({ field, fieldState }) => (
                                        <TextInput
                                            {...field}
                                            hideEmptyMeta
                                            error={fieldState.error?.message}
                                        />
                                    )}
                                    name="title"
                                />
                                <TemplateCategoryController
                                    hideEmptyMeta
                                    isClearable
                                    placeholder={t`Тип документу`}
                                />
                            </form>
                            {isModified && saveMessage}
                        </FlexBox>
                        <FlexBox align="center" justify="end">
                            <Button
                                form="modal-edit-template"
                                loading={methods.formState.isSubmitting}
                                theme="secondary"
                                type="submit"
                                onClick={() => {
                                    eventTracking.sendToGTMV4({
                                        event: 'ec_template_edit_end',
                                    });
                                }}
                            >{t`Завершити`}</Button>
                        </FlexBox>
                    </>
                )}
            >
                <FeatureShow feature="COLLABORA_MARKERS">
                    <TemplateReplaceForm.Button
                        template={template}
                        onShow={() => setReplaceFormVisible(true)}
                    />
                    <TemplateReplaceForm
                        template={template}
                        title="Поля автопідстановки"
                        visible={replaceFormVisible}
                        onHide={() => setReplaceFormVisible(false)}
                    />
                </FeatureShow>
            </EditorModal>
        );
    }

    const favoriteAction = (
        <FlexBox gap={8} align="center">
            <Text>
                {template.isFavorite ? t`Обраний` : t`Позначити як обраний`}
            </Text>
            <FavoriteIcon
                onClick={favoriteHandler(template)}
                active={template.isFavorite}
            />
        </FlexBox>
    );

    // режим перегляду публічного
    if (template.companyId === null) {
        return (
            <EditorModal
                key="view-publick"
                hideMenu
                collaboraQuery={collaboraQuery}
                renderHeader={() => (
                    <>
                        <FlexBox
                            className={css.titleBox}
                            align="center"
                            grow={1}
                        >
                            <Title ellipsis level={4}>
                                {template.title}
                            </Title>
                        </FlexBox>
                        <FlexBox align="center" justify="end">
                            <FlexBox
                                className={css.actions}
                                gap={0}
                                align="center"
                            >
                                {favoriteAction}
                            </FlexBox>

                            <Button
                                disabled={!hasEditPermission}
                                theme="secondary"
                                type="button"
                                onClick={
                                    duplicateTemplateActionConfig(template)
                                        .onClick
                                }
                            >
                                <span className={css.desktopText}>
                                    {t`Додати до шаблонів Компанії`}
                                </span>
                                <span
                                    style={{ fontSize: 24 }}
                                    className={css.mobileAltText}
                                >
                                    +
                                </span>
                            </Button>
                            <CloseButton onClose={onClose} position="static" />
                        </FlexBox>
                    </>
                )}
            />
        );
    }

    // режим перегляду шаблону компанії
    return (
        <EditorModal
            key="view"
            hideMenu
            collaboraQuery={collaboraQuery}
            renderHeader={() => (
                <>
                    <FlexBox className={css.titleBox} align="center" grow={1}>
                        <Title ellipsis level={4}>
                            {template.title}
                        </Title>
                    </FlexBox>
                    <FlexBox align="center" justify="end">
                        <FlexBox className={css.actions} gap={0} align="center">
                            {favoriteAction}
                            <SingleActionIcon
                                disabled={!hasEditPermission}
                                className={css.actionIcon}
                                item={deleteTemplateActionConfig(template)}
                            />
                            <SingleActionIcon
                                disabled={!hasEditPermission}
                                className={css.actionIcon}
                                item={duplicateTemplateActionConfig(template)}
                            />
                        </FlexBox>
                        <Button
                            disabled={!hasEditPermission}
                            theme="secondary"
                            type="button"
                            onClick={() => {
                                setEditMode(true);
                                eventTracking.sendToGTMV4({
                                    event: 'ec_template_edit_page',
                                });
                            }}
                        >
                            <span className={css.desktopText}>
                                {t`Редагувати шаблон`}
                            </span>
                            <span className={css.mobileAltText}>
                                <Icon glyph={EditSVG} />
                            </span>
                        </Button>
                        <CloseButton
                            withHover
                            onClose={onClose}
                            position="static"
                        />
                    </FlexBox>
                </>
            )}
        />
    );
};

export default TemplateEditor;
