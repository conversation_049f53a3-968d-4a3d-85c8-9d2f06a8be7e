.form {
    display: flex;
    align-items: center;
    gap: 10px;
}

.checkIcon {
    display: inline-flex;
    width: 12px;
    height: 12px;
}

.actionIcon {
    position: static;
    display: inline-flex;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    padding: 0;
    border-radius: 50%;
    cursor: pointer;
    opacity: 1;
    transition: background-color 0.3s;
}

.actionIcon:hover {
    background-color: var(--grey-bg);
}

button.actionIcon > svg {
    color: var(--grey-color);
}

.mobileAltText {
    display: none;
}

.mobileAltText svg {
    width: 20px;
    height: 20px;
}

@media screen and (max-width: 768px) {
    .form,
    .actions {
        display: none;
    }

    .titleBox {
        max-width: 50%;
    }

    .desktopText {
        display: none;
    }

    .mobileAltText {
        display: flex;
    }
}
