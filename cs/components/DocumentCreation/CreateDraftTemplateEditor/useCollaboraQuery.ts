import { useEffect } from 'react';

import { useQuery } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import { GET_DRAFT_TEMPLATE_COLLABORA_DATA } from 'lib/queriesConstants';
import { getDocumentEditData } from 'services/documents/ts/api';

export const useCollaboraQuery = (draftId: string) => {
    const collaboraQuery = useQuery({
        queryFn: () =>
            getDocumentEditData({ type: 'draft', entityId: draftId }),
        queryKey: [GET_DRAFT_TEMPLATE_COLLABORA_DATA, { draftId }],
        staleTime: Infinity, // need to change when token will be expired
        select: (data) => ({
            url: data.wopi_url,
            token: data.access_token,
        }),
    });

    useEffect(() => {
        if (collaboraQuery.isError && collaboraQuery.error) {
            snackbarToast.error('Проблема з отриманням даних для редактора');
        }
    }, [collaboraQuery.isError]);

    return collaboraQuery;
};
