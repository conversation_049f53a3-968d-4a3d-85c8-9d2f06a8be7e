import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { getCurrentUser } from 'selectors/app.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { diiaRequestForCompanyVerification } from 'services/diia/api';
import { t } from 'ttag';

import actionCreators from '../checkKeyPopup/checkKeyPopupActionCreators';

import DiiaSigner from '../DiiaSigner/DiiaSigner';

const generateSubtitle = (mobile: boolean) =>
    mobile
        ? t`Натисніть на кнопку "Відкрити застосунок" для реєстрації`
        : t`Зчитайте QR-код сканером у застосунку Дія`;

const logAddRoleError = (label: string) =>
    eventTracking.sendEvent('diia', 'error_ecp_confirmation', label);

const AddRoleDiia: React.FC = () => {
    const dispatch = useDispatch();

    const currentUser = useSelector(getCurrentUser);

    return (
        <DiiaSigner
            retrieveLink={diiaRequestForCompanyVerification}
            onSuccess={() => {
                if (!currentUser.registrationCompleted) {
                    eventTracking.sendToGTM({
                        event: 'funnel_reg_step_4',
                        action: 'success',
                        category: '',
                        label: 'diya',
                    });
                }
                dispatch(actionCreators.onDiiaSignSuccess());
            }}
            title={t`Реєстрація через застосунок Дія`}
            generateSubtitle={generateSubtitle}
            logError={(msg) => {
                if (!currentUser.registrationCompleted) {
                    eventTracking.sendToGTM({
                        event: 'funnel_reg_step_4',
                        action: 'fail',
                        category: '',
                        label: 'diya',
                    });
                }
                logAddRoleError(msg);
            }}
        />
    );
};

export default AddRoleDiia;
