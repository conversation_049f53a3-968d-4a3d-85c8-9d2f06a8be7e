import { yupResolver } from '@hookform/resolvers/yup';

import { t } from 'ttag';
import * as yup from 'yup';

import { ExportDataPopupFormFields } from './types';

import { isPeriodMoreThenYear } from './utils';

const exportAutocompleteFormValidationSchema: yup.ObjectSchema<ExportDataPopupFormFields> = yup
    .object()
    .shape({
        fromDate: yup
            .date()
            .max(
                yup.ref('endDate'),
                t`Початкова дата повинна бути раніше кінцевої дати`,
            )
            .required(t`Початкова дата обов'язкова`),
        endDate: yup
            .date()
            .min(
                yup.ref('fromDate'),
                t`Кінцева дата повинна бути пізніше початкової дати`,
            )
            .required(t`Кінцева дата обов'язкова`),
        format: yup
            .string()
            .oneOf(['csv', 'xlsx'], t`Формат має бути csv або xlsx`)
            .required(t`Формат обов'язковий`),
    });

export const exportAutocompleteFormResolver = yupResolver(
    exportAutocompleteFormValidationSchema,
);
