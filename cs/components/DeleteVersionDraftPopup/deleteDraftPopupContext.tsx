import React from 'react';

import { Draft } from 'services/documents/ts/types';

import { useDeleteDraftPopupContextHandler } from './useDeleteDraftPopupContextHandler';

interface PopupContext {
    isActive: boolean;
    onOpen: (chosenDraft: Draft) => void;
    onClose: VoidFunction;
    draft: Nullable<Draft>;
    handleDeleteDraft: VoidFunction;
}

const DeleteVersionDraftPopupContext = React.createContext({} as PopupContext);

export const DeleteVersionDraftPopupProvider: React.FC = (props) => {
    const { value } = useDeleteDraftPopupContextHandler();

    return (
        <DeleteVersionDraftPopupContext.Provider value={value}>
            {props.children}
        </DeleteVersionDraftPopupContext.Provider>
    );
};

export function useDeleteVersionDraftPopupContext() {
    const context = React.useContext(DeleteVersionDraftPopupContext);

    if (Object.keys(context).length === 0) {
        throw new Error(
            'useDeleteVersionDraftPopupContext must be inside DeleteVersionDraftPopupProvider',
        );
    }

    return context;
}
