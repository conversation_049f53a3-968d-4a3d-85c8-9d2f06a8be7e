.container {
    position: relative;
    height: 306px;
    padding: 20px 12px 12px;
    background-color: #f8f9f8;
    background-image: url('./images/backgroundDetectedIn.png');
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: 100% auto;
    color: #252d3d;
}

.title {
    padding: 7px;
    margin-bottom: 12px;
    background-color: #fff;
    border-radius: 7px;
    font-size: 18px;
    font-weight: 600;
    line-height: 25px;
}

.buttonWrapper {
    display: block;
    margin-top: auto;
}

.backgroundTechnique {
    position: absolute;
    z-index: 1;
    bottom: 12px;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('./images/technique.png');
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: auto;
}
