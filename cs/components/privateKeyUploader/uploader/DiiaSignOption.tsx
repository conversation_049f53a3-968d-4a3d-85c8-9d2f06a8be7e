import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { signWithKepFlowSelectors } from 'components/SignWithKepFlow/signWithKepFlowSlice';
import documentActionCreators from 'components/document/documentActionCreators';
import Button from 'components/ui/button/button';
import { isTovCompany } from 'lib/helpers';
import { getCurrentCompanyEdrpou } from 'selectors/app.selectors';
import { t } from 'ttag';
import { StoreState } from 'types/store';

import css from './uploader.css';

export const DiiaSignOption = () => {
    const dispatch = useDispatch();
    const currentCompanyEdrpou = useSelector(getCurrentCompanyEdrpou);
    const isTov = isTovCompany(currentCompanyEdrpou);
    const docsToSign = useSelector((state: StoreState) => state.signPopup.docs);
    const isMulti = docsToSign.length > 1;
    const isSignWithKepFlowEnabled = useSelector(
        signWithKepFlowSelectors.selectIsFlowEnabled,
    );

    if (isTov || isMulti || !isSignWithKepFlowEnabled) {
        return null;
    }

    const handleClick = async () => {
        await dispatch(
            documentActionCreators.onLoadDocument(docsToSign[0].id, false),
        );
        await dispatch(documentActionCreators.onSign('', '', false, true));
    };

    return (
        <div className={css.button}>
            <Button theme="darkGray" width="full" onClick={handleClick}>
                {t`Дія.Підпис`}
            </Button>
        </div>
    );
};
