import React from 'react';

import cn from 'classnames';
import PropTypes from 'prop-types';
import { t } from 'ttag';

import CircleSpinner from '../ui/CircleSpinner/CircleSpinner';
import Icon from '../ui/icon/icon';

import { trackForm } from '../../services/analytics/formTracking';
import Uploader from './uploader/uploader';

import KeySvg from './images/key.svg';

import css from './privateKeyUploader.css';

class PrivateKeyUploader extends React.Component {
    static propTypes = {
        autoSubmitKey: PropTypes.bool,
        isMultiSign: PropTypes.bool,
        isSignerServiceLoaded: PropTypes.bool,
        headerBig: PropTypes.bool,
        formTrackingName: PropTypes.string,
        formTrackingLabel: PropTypes.string,
        title: PropTypes.string,
        theme: PropTypes.string,
        errorMessage: PropTypes.string,
        onClick: PropTypes.func.isRequired,
        onUSBClick: PropTypes.func,
        onSubmitKeysFromStorage: PropTypes.func,
        onIitWidgetClick: PropTypes.func,
    };

    static defaultProps = {
        autoSubmitKey: true,
        formTrackingName: 'private_key_uploader',
    };

    componentDidMount() {
        this.formTracker = trackForm(
            this.form,
            this.props.formTrackingName,
            this.props.formTrackingLabel,
        );
    }

    componentWillUnmount() {
        this.formTracker?.unbindEvents();
    }

    handleSubmit = (evt) => {
        evt.preventDefault();
        this.formTracker.sendEvent('file_sign_click');
        this.props.onClick();
    };

    onUSBClick = () => {
        this.formTracker.sendEvent('token_sign_click');
        this.props.onUSBClick();
    };

    onIitWidgetClick = (url) => {
        const actions = {
            [config.SIGN_WIDGET_IIT_FOZZY_URL]: 'fozzy_sign_click',
            [config.SIGN_WIDGET_IIT_URL]: 'id.gov.ua_sign_click',
        };
        this.formTracker.sendEvent(actions[url] || '');
        this.props.onIitWidgetClick(url);
    };

    render() {
        const { errorMessage } = this.props;
        if (errorMessage) {
            this.formTracker.submitTry();
            this.formTracker.validationError('key', errorMessage);
        }

        // hide usb button on mobile
        const onUsbClick = this.props.onUSBClick ? this.onUSBClick : null;

        return (
            <form
                className={cn(css.root, {
                    [css.rootLoading]: !this.props.isSignerServiceLoaded,
                })}
                onSubmit={this.handleSubmit}
                ref={(el) => {
                    this.form = el;
                }}
            >
                {this.props.isSignerServiceLoaded ? (
                    <Uploader
                        autoSubmitKey={this.props.autoSubmitKey}
                        headerBig={this.props.headerBig}
                        title={this.props.title}
                        theme={this.props.theme}
                        errorMessage={this.props.errorMessage}
                        onUSBClick={onUsbClick}
                        onIitWidgetClick={this.onIitWidgetClick}
                        onSubmitKeysFromStorage={
                            this.props.onSubmitKeysFromStorage
                        }
                    />
                ) : (
                    <div className={css.alert}>
                        {!errorMessage && (
                            <>
                                <div className={css.icon}>
                                    <Icon glyph={KeySvg} />
                                </div>
                                <CircleSpinner />
                            </>
                        )}
                        {errorMessage ? (
                            <div className={cn(css.error, css.message)}>
                                <div>{errorMessage}</div>
                            </div>
                        ) : (
                            <div className={css.message}>
                                <div>
                                    {t`Завантажується бібліотека для роботи з КЕП/ЕЦП`}
                                    .
                                </div>
                                <div>{t`Це може тривати деякий час`}.</div>
                            </div>
                        )}
                    </div>
                )}
            </form>
        );
    }
}

export default PrivateKeyUploader;
