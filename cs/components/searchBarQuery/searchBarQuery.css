.root,
.rootActive {
    position: relative;
    display: table;
    width: 100%;
}

.inputHolder,
.buttonHolder {
    display: table-cell;
    vertical-align: middle;
}

.inputHolder {
    width: 100%;
}

.input,
.button {
    display: block;
    box-sizing: border-box;
    padding: 10px 15px;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    color: var(--content-color);
    font-family:
        'Roboto',
        Arial,
        Tahoma,
        Helvetica,
        'Liberation Sans',
        sans-serif;
    font-size: 13px;
    line-height: 18px;
    vertical-align: middle;
}

.input {
    width: 100%;
    min-height: 40px;
    -moz-appearance: none;
    -webkit-appearance: none;
    -webkit-border-radius: 8px 0 0 8px;
    border-radius: 8px 0 0 8px;
}

.input::-webkit-search-cancel-button {
    -webkit-appearance: none;
}

.input::-webkit-search-decoration {
    -webkit-appearance: none;
}

.input::-ms-clear {
    display: none;
}

.clearIconHolder {
    position: absolute;
    top: 14px;
    right: 108px;
}

.buttonHolder {
    width: 1px;
}

.button {
    padding: 10px 25px;
    margin-left: -1px;
    border-radius: 0 8px 8px 0;
    user-select: none;
}

.button:active {
    outline: none;
}

.rootActive .input {
    border-color: var(--primary-cta-color);
}

.rootActive .button {
    border-color: var(--primary-cta-color);
    background: var(--primary-cta-color);
    box-shadow: inset 0 -2px 0 0 rgba(0, 0, 0, 0.1);
    color: #fff;
    cursor: pointer;
}

.rootActive .button:hover {
    border-color: $orange-hover-bg;
    background: $orange-hover-bg;
}

.rootActive .button:active {
    box-shadow: inset 0 2px 0 0 rgba(0, 0, 0, 0.1);
}

.rootActive .button:active .buttonText {
    margin-bottom: -2px;
}
