import { IUser } from '../../../types/user';

import {
    PRO_TRIALS_SET,
    UNLIMITED_RATES_SET,
} from '../../../services/billing/constants';

const RATES_FOR_REQUIRED_FIELDS = new Set([
    ...PRO_TRIALS_SET,
    ...UNLIMITED_RATES_SET,
]);

export const isRequiredFieldsAvailable = (currentUser: IUser) => {
    if (
        !currentUser.currentCompany ||
        !currentUser.currentCompany.activeRates
    ) {
        return false;
    }
    return currentUser.currentCompany.activeRates.some((r) =>
        RATES_FOR_REQUIRED_FIELDS.has(r),
    );
};
