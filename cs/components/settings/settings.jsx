import React from 'react';
import { connect } from 'react-redux';

import cn from 'classnames';

import { mapStateToCurrentUser } from '../../store/utils';

import InfoBanner from '../infoBanner/infoBanner';

// styles
import css from './settings.css';

const Settings = (props) => {
    const { currentUser } = props;

    return (
        <div className={cn(css.root, css.sidebar2022)}>
            <div className={css.content}>
                <InfoBanner currentUser={currentUser} />
                <div className={css.wrapper}>{props.children}</div>
            </div>
        </div>
    );
};

export default connect(mapStateToCurrentUser)(Settings);
