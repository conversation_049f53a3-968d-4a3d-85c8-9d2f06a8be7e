import React from 'react';

import cn from 'classnames';
import { t } from 'ttag';

import { CheckResult } from './types';

import CustomScrollBar from '../../../ui/CustomScrollBar';

import css from './ExistingCompanyTableList.css';

interface ExistingCompanyTableListProps {
    list: CheckResult[];
}

const ExistingCompanyTableList: React.FC<
    React.PropsWithChildren<ExistingCompanyTableListProps>
> = ({ list }) => {
    const tableComponents = (
        <>
            <tbody>
                {list.map((res) => (
                    <tr key={res.edrpou} className={css.resultsTableRow}>
                        <td className={css.resultsName}>{res.name}</td>
                        <td className={css.resultsEdrpou}>{res.edrpou}</td>
                    </tr>
                ))}
            </tbody>
            <thead>
                <tr className={css.resultsTableHeader}>
                    <th className={css.resultsName}>
                        <b>{t`Назва компанії`}</b>
                    </th>
                    <th className={css.resultsEdrpou}>
                        <b>{t`ЄДРПОУ/ІПН`}</b>
                    </th>
                </tr>
            </thead>
        </>
    );

    return (
        <>
            <table className={cn(css.resultsTable, css.resultsHeadOnly)}>
                {tableComponents}
            </table>
            <CustomScrollBar maxHeight="200px">
                <table className={cn(css.resultsTable, css.resultsBodyOnly)}>
                    {tableComponents}
                </table>
            </CustomScrollBar>
        </>
    );
};

export default React.memo(ExistingCompanyTableList);
