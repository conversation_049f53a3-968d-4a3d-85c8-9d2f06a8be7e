.resultsTable {
    width: 100%;
    margin-top: 20px;
    font-size: 13px;
    table-layout: fixed;
    text-align: left;
}

.resultsHeadOnly tbody {
    display: none;
}

.resultsBodyOnly thead {
    display: none;
}

.resultsTableHeader {
    border-bottom: 1px solid var(--default-border);
}

.resultsTableRow:hover {
    background-color: var(--grey-bg);
}

.resultsName,
.resultsEdrpou {
    padding: 12px 20px;
}

@media (min-width: 720px) {
    .resultsName,
    .resultsEdrpou {
        padding: 10px;
    }
}

.resultsName {
    width: 75%;
    border-bottom-left-radius: 8px;
    border-top-left-radius: 8px;
}

.resultsEdrpou {
    width: 25%;
    border-bottom-right-radius: 8px;
    border-top-right-radius: 8px;
}
