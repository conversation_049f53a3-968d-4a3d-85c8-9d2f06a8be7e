.root {
    width: 460px;
    box-sizing: border-box;
    padding: 40px;
    margin: 18px auto;
    background-color: var(--white-bg);
    border-radius: 10px;
    box-shadow: 0 2px 20px rgba(182, 202, 219, 0.5);
}

@media screen and (max-width: 768px) {
    .root {
        width: 90%;
    }
}

@media screen and (max-width: 480px) {
    .root {
        width: 100%;
        margin: 0 0 2px 0;
    }
}

.form {
    width: 100%;
    border-collapse: collapse;
}

.row {
    display: block;
    margin-top: 15px;
}

.priceRow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 15px 0;
}

.label {
    display: block;
    margin-bottom: 3px;
    color: var(--slate-grey-color);
    white-space: nowrap;
}

.priceLabel {
    margin-bottom: 0;
    color: var(--content-color);
    font-weight: bold;
}

.cell {
    display: block;
    width: 100%;
}

.priceCell {
    width: auto;
}

.hint {
    margin-top: 15px;
}

.link {
    color: var(--link-color);
    text-decoration: none;
}

.checkbox {
    margin-bottom: 15px;
}

.fieldWrap {
    max-width: 230px;
}

.price {
    font-size: 24px;
    font-weight: bold;
}

.buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}
