import React, { FC } from 'react';
import { connect } from 'react-redux';

import { t } from 'ttag';

import createDocumentPopupActionCreators from '../createDocumentPopup/createDocumentPopupActionCreators';

import Button from '../ui/button/button';

interface Props {
    onClick: () => void;
}

const CreateDocumentButton: FC<React.PropsWithChildren<Props>> = ({
    onClick,
}) => (
    <Button typeContour theme="blue" width="full" onClick={onClick}>
        {t`Створити е-ТТН`}
    </Button>
);

const mapDispatchToProps: Props = {
    onClick: createDocumentPopupActionCreators.onOpenPopup,
};

const connector = connect(null, mapDispatchToProps);

export default connector(CreateDocumentButton);
