import React, { useLayoutEffect } from 'react';
import { useSelector } from 'react-redux';

import { BodyPortal } from '@vchasno/ui-kit';

import { getCurrentUser } from 'selectors/app.selectors';

const TypeFormSurvey: React.FC = () => {
    const currentUser = useSelector(getCurrentUser);
    const isVisible = currentUser.email && config.TYPEFORM_FORM_ID;

    useLayoutEffect(() => {
        if (!isVisible) return;

        const script = document.createElement('script');
        script.src = '//embed.typeform.com/next/embed.js';
        document.body.appendChild(script);

        return () => {
            if (script) {
                document.body.removeChild(script);
            }
        };
    }, [isVisible]);

    if (!isVisible) {
        return null;
    }

    return (
        <BodyPortal>
            <div
                data-tf-hidden={`email=${currentUser.email},edrpou=${currentUser.currentCompany.edrpou}`}
                data-tf-live={config.TYPEFORM_FORM_ID}
            />
        </BodyPortal>
    );
};

export default TypeFormSurvey;
