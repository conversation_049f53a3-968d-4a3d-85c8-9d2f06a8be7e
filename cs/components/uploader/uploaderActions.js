const actions = {
    UPLOADER__SET_IS_DISABLE: 'UPLOADER__SET_IS_DISABLE',
    UPLOADER__SET_SIGN_TYPE: 'UPLOADER__SET_SIGN_TYPE',
    UPLOADER__SET_DOCUMENT_KIND: 'UPLOADER__SET_DOCUMENT_KIND',
    UPLOADER__SET_DOCUMENT_TYPE: 'UPLOADER__SET_DOCUMENT_TYPE',
    UPLOADER__CHANGE_IS_VERSIONED: 'UPLOADER__CHANGE_IS_VERSIONED',
    UPLOADER__SET_DOCUMENT_TYPE_BY_NAME: 'UPLOADER__SET_DOCUMENT_TYPE_BY_NAME',
    UPLOADER__SET_UPLOAD_SOURCE: 'UPLOADER__SET_UPLOAD_SOURCE',
    UPLOADER__START_CHOOSE_FILES_FROM_GOOGLE_DRIVE:
        'UPLOADER__START_CHOOSE_FILES_FROM_GOOGLE_DRIVE',
    UPLOADER__FINISH_CHOOSE_FILES_FROM_GOOGLE_DRIVE:
        'UPLOADER__FINISH_CHOOSE_FILES_FROM_GOOGLE_DRIVE',
    UPLOADER__START_GET_FILES_FROM_GOOGLE_DRIVE:
        'UPLOADER__START_GET_FILES_FROM_GOOGLE_DRIVE',
    UPLOADER__FINISH_GET_FILES_FROM_GOOGLE_DRIVE:
        'UPLOADER__FINISH_GET_FILES_FROM_GOOGLE_DRIVE',
    UPLOADER__SHOW_POPUP: 'UPLOADER__SHOW_POPUP',
    UPLOADER__CLOSE_POPUP: 'UPLOADER__CLOSE_POPUP',
    UPLOADER__SHOW_ERROR: 'UPLOADER__SHOW_ERROR',
    UPLOADER__CHANGE_EXPECTED_OWNER_SIGNATURES:
        'UPLOADER__CHANGE_EXPECTED_OWNER_SIGNATURES',
    UPLOADER__CHANGE_EXPECTED_RECIPIENT_SIGNATURES:
        'UPLOADER__CHANGE_EXPECTED_RECIPIENT_SIGNATURES',

    // selected files for upload
    UPLOADER__SET_SELECTED_FILES: 'UPLOADER__SET_SELECTED_FILES',
    // add document info
    UPLOADER__INFO_CHANGE_FIELD: 'UPLOADER__INFO_CHANGE_FIELD',
    UPLOADER__INFO_CHANGE_DATE: 'UPLOADER__INFO_CHANGE_DATE',

    // add document access actions
    UPLOADER__CHANGE_USER_VALUE: 'UPLOADER__CHANGE_USER_VALUE',
    UPLOADER__CHANGE_SUGGESTION_USERS: 'UPLOADER__CHANGE_SUGGESTION_USERS',
    UPLOADER__TOGGLE_SHOW_VALUE_SUGGESTIONS_USERS:
        'UPLOADER__TOGGLE_SHOW_VALUE_SUGGESTIONS_USERS',
    UPLOADER__CHANGE_USERS_LIST: 'UPLOADER__CHANGE_USERS_LIST',

    // add document signer actions
    UPLOADER__CHANGE_SIGNER_VALUE: 'UPLOADER__CHANGE_SIGNER_VALUE',
    UPLOADER__CHANGE_SUGGESTION_SIGNERS: 'UPLOADER__CHANGE_SUGGESTION_SIGNERS',
    UPLOADER__TOGGLE_SHOW_VALUE_SUGGESTIONS_SIGNERS:
        'UPLOADER__TOGGLE_SHOW_VALUE_SUGGESTIONS_SIGNERS',
    UPLOADER__CHANGE_SIGNERS_LIST: 'UPLOADER__CHANGE_SIGNERS_LIST',
    UPLOADER__TOGGLE_SIGN_PROCESS_TYPE: 'UPLOADER__TOGGLE_SIGN_PROCESS_TYPE',

    // add document tags actions
    UPLOADER__TAGS_CHANGE_NEW: 'UPLOADER__TAGS_CHANGE_NEW',
    UPLOADER__TAGS_CHANGE_SELECTED: 'UPLOADER__TAGS_CHANGE_SELECTED',
    UPLOADER__TAGS_CHANGE_SUGGESTION: 'UPLOADER__TAGS_CHANGE_SUGGESTION',

    // add document reviewers
    UPLOADER__TOGGLE_REVIEW_PROCESS_TYPE:
        'UPLOADER__TOGGLE_REVIEW_PROCESS_TYPE',
    UPLOADER__REVIEWERS_CHANGE_LIST: 'UPLOADER__REVIEWERS_CHANGE_LIST',
    UPLOADER__REVIEWERS_CHANGE_VALUE: 'UPLOADER__REVIEWERS_CHANGE_VALUE',
    UPLOADER__REVIEWERS_CHANGE_SUGGESTION:
        'UPLOADER__REVIEWERS_CHANGE_SUGGESTION',
    UPLOADER__REVIEWERS_TOGGLE_SHOW_VALUE:
        'UPLOADER__REVIEWERS_TOGGLE_SHOW_VALUE',
    UPLOADER__TOGGLE_SIGN_AFTER_REVIEW: 'UPLOADER__TOGGLE_SIGN_AFTER_REVIEW',
    UPLOADER__CHANGE_REVIEWERS_LIST: 'UPLOADER__CHANGE_REVIEWERS_LIST',

    // add document templates
    UPLOADER__SET_DOCUMENT_TEMPLATES: 'UPLOADER__SET_DOCUMENT_TEMPLATES',
    UPLOADER__SET_ACTIVE_DOCUMENT_TEMPLATE:
        'UPLOADER__SET_ACTIVE_DOCUMENT_TEMPLATE',

    // Document fields
    UPLOADER__SET_DOCUMENT_PARAMETERS: 'UPLOADER__SET_DOCUMENT_PARAMETERS',

    // reсipient
    UPLOADER__SET_RECIPIENT: 'UPLOADER__SET_RECIPIENT',

    // document signe after upload id
    UPLOADER__SET_DOCUMENT_IDS_FOR_SIGN: 'UPLOADER__SET_DOCUMENT_IDS_FOR_SIGN',
};

export default actions;
