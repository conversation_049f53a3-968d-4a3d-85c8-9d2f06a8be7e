import { useSelector } from 'react-redux';

import { useDocsForActions } from 'hooks/useDocsForActions';
import { useIsEdi } from 'hooks/useIsEdi';
import {
    getCurrentCompanyEdrpou,
    getCurrentUser,
} from 'selectors/app.selectors';
import { getIsDirectoriesSelected } from 'selectors/documentList.selectors';
import {
    canCreateDeleteRequest,
    getIsDocumentHasRejectedDeleteRequest,
    isDeleteRequestInitiator,
    isDeleteRequestReceiver,
} from 'services/documents/utils';

export const useIsShowCreateDeleteRequest = () => {
    const isEdi = useIsEdi();
    const docsForAction = useDocsForActions();

    const currentUser = useSelector(getCurrentUser);
    const isDirectoriesSelected = useSelector(getIsDirectoriesSelected);
    const currentCompanyEdrpou = useSelector(getCurrentCompanyEdrpou);

    const isDeleteRequestAllowed = canCreateDeleteRequest(
        docsForAction,
        currentUser,
    );
    const isCurrentCompanyDeleteRequestReceiver = isDeleteRequestReceiver(
        docsForAction,
        currentCompanyEdrpou,
    );
    const isCurrentCompanyDeleteRequestInitiator = isDeleteRequestInitiator(
        docsForAction,
        currentCompanyEdrpou,
    );

    return (
        !isEdi &&
        !isDirectoriesSelected &&
        (isDeleteRequestAllowed ||
            ((isCurrentCompanyDeleteRequestReceiver ||
                isCurrentCompanyDeleteRequestInitiator) &&
                !getIsDocumentHasRejectedDeleteRequest(docsForAction[0])))
    );
};
