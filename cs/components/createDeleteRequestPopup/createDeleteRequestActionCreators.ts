import * as React from 'react';

import { Thunk } from '../../types';

import documentActionCreators from '../document/documentActionCreators';
import documentListActionCreators from '../documentList/documentListActionCreators';
import actions from './createDeleteRequestPopupActions';

import { createDeleteRequest } from '../../services/documents/api';
import { State } from './createDeleteRequestPopupTypes';

function onShow({ documents, docPage = false }: State): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.CREATE_DELETE_REQUEST_POPUP__SHOW,
            documents,
            docPage,
        });
    };
}

function onClose(): Thunk {
    return async (dispatch, getState) => {
        const {
            createDeleteRequestPopup: { showOk, docPage, documents },
        } = getState();
        if (showOk) {
            if (!docPage) {
                dispatch(documentListActionCreators.onLoadDocuments());
            } else {
                dispatch(
                    documentActionCreators.onLoadDocument(
                        documents[0].id,
                        false,
                    ),
                );
            }
        }
        dispatch({ type: actions.CREATE_DELETE_REQUEST_POPUP__CLOSE });
    };
}

function onToggleDocList(evt: React.SyntheticEvent): Thunk {
    evt.preventDefault();
    return (dispatch) => {
        dispatch({
            type: actions.CREATE_DELETE_REQUEST_POPUP__TOGGLE_DOC_LIST,
        });
    };
}

function onChangeMessage(evt: React.ChangeEvent<HTMLTextAreaElement>): Thunk {
    return (dispatch) => {
        const target = evt.target as HTMLTextAreaElement;
        dispatch({
            type: actions.CREATE_DELETE_REQUEST_POPUP__CHANGE_MESSAGE,
            message: target.value,
        });
    };
}

function onSubmit(evt: React.ChangeEvent<HTMLInputElement>): Thunk {
    return async (dispatch, getState) => {
        evt.preventDefault();
        dispatch({ type: actions.CREATE_DELETE_REQUEST_POPUP__UPLOADING });
        const {
            createDeleteRequestPopup: { documents, message },
        } = getState();
        try {
            await createDeleteRequest(
                documents.map((item: any) => item.id),
                message,
            );
            dispatch({ type: actions.CREATE_DELETE_REQUEST_POPUP__OK });
        } catch (err) {
            dispatch({
                type: actions.CREATE_DELETE_REQUEST_POPUP__ERRORS,
                error: err.reason || err.message,
            });
        }
    };
}

export default {
    onShow,
    onClose,
    onToggleDocList,
    onChangeMessage,
    onSubmit,
};
