import React from 'react';

import cn from 'classnames';
import { t } from 'ttag';

import { Nullable } from '../../../types/general';

import { makeReactKeyFromFile } from '../utils';

import Button from '../../ui/button/button';
import Icon from '../../ui/icon/icon';

import { ACCEPT_EXTENSIONS } from '../../../lib/constants';
import FlexBox from '../../FlexBox';
import FileBadge from '../FileBadge';
import { useFilesSelectionContext } from '../context/filesSelectionContext';

import DownloadSvg from '../../../icons/download.svg';

import css from './UploaderFileSelection.css';

export interface UploaderFileSelectionProps {
    onSubmit: (file: File[]) => void;
}

/**
 * @deprecated
 */
const UploaderFileSelection: React.FC<
    React.PropsWithChildren<UploaderFileSelectionProps>
> = ({ onSubmit }) => {
    const { files, addFiles } = useFilesSelectionContext();
    const fileInputRef = React.useRef<Nullable<HTMLInputElement>>(null);

    const handleInputChange = (
        event:
            | React.DragEvent<HTMLInputElement>
            | React.ChangeEvent<HTMLInputElement>,
    ) => {
        addFiles(Array.from(event.currentTarget?.files || []));
    };

    return (
        <FlexBox direction="column" gap={10} className={css.root}>
            {files.length > 0 && (
                <div>
                    <div className={css.countTitle}>{t`Uploaded:`}</div>
                    <div className={css.countValue}>
                        {files.length} {files.length === 1 && t`document`}
                        {files.length > 1 && 'documents'}
                    </div>
                </div>
            )}
            <label
                className={cn(css.label, {
                    [css.hidden]: files.length > 0,
                })}
            >
                <FlexBox
                    direction="column"
                    gap={10}
                    align="center"
                    justify="center"
                    className={css.labelContent}
                >
                    <div className={css.text}>{t`Drag & Drop files`}</div>
                    <Icon glyph={DownloadSvg} className={css.uploadIcon} />
                </FlexBox>
                <input
                    ref={fileInputRef}
                    onDragEnter={(event) =>
                        event.currentTarget.parentElement?.setAttribute(
                            'data-drop-active',
                            'true',
                        )
                    }
                    onDragLeave={(event) =>
                        event.currentTarget.parentElement?.removeAttribute(
                            'data-drop-active',
                        )
                    }
                    value=""
                    onDrop={handleInputChange}
                    onChange={handleInputChange}
                    multiple
                    type="file"
                    accept={ACCEPT_EXTENSIONS}
                    className={css.file}
                />
            </label>
            {files.length > 0 && (
                <div className={css.badgeList}>
                    {files.map((item) => (
                        <FileBadge
                            file={item}
                            key={makeReactKeyFromFile(item)}
                        />
                    ))}
                </div>
            )}
            <footer>
                <FlexBox gap={10} justify="flex-end">
                    <Button
                        typeContour
                        theme="blue"
                        onClick={() => fileInputRef.current?.click()}
                    >
                        {files.length === 0 ? t`Choose files` : t`Upload more`}
                    </Button>
                    <Button
                        onClick={() => onSubmit(files)}
                        theme="cta"
                        className={css.nextBtn}
                        disabled={files.length === 0}
                    >{t`Next`}</Button>
                </FlexBox>
            </footer>
        </FlexBox>
    );
};

export default UploaderFileSelection;
