import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { t } from 'ttag';

import { getCurrentCompanyPermissionMap } from '../../../selectors/app.selectors';
import {
    getIsReviewParallelProcessType,
    getIsReviewersSuggestionsShown,
    getReviewerValue,
    getReviewersList,
    getSuggestionReviewers,
} from '../../../selectors/uploader.selectors';
import { useUploadActions } from '../../uploader/useUploadActions';

import {
    getChoosenRoleGroupList,
    renderRoleAndGroupSuggestion,
} from './helpers';

import AutosuggestionList from '../../autosuggestionList/autosuggestionList';
import { PermissionCategory } from '../../proRateInfoPopup/proRateInfoPopupTypes';

interface ReviewersAutosuggestionListProps {
    dataQa?: string;
}

const ReviewersAutosuggestionList: React.FC<
    React.PropsWithChildren<ReviewersAutosuggestionListProps>
> = ({ dataQa }) => {
    const actions = useUploadActions();
    const companyPermissionMap = useSelector(getCurrentCompanyPermissionMap);
    const isReviewParallelProcessType = useSelector(
        getIsReviewParallelProcessType,
    );
    const reviewerValue = useSelector(getReviewerValue);
    const isReviewersSuggestionsShown = useSelector(
        getIsReviewersSuggestionsShown,
    );
    const reviewersSuggestions = useSelector(getSuggestionReviewers);
    const reviewersList = useSelector(getReviewersList);

    const isAvailableEditReviewers =
        companyPermissionMap[PermissionCategory.REVIEWS];

    const chosenReviewersList = useMemo(() => {
        return getChoosenRoleGroupList(reviewersList);
    }, [reviewersList]);

    return (
        <AutosuggestionList
            dataQa={dataQa}
            label={t`Очікує погодження від:`}
            shouldSearchOnEmptyValue
            disabled={!isAvailableEditReviewers}
            isOrdered={!isReviewParallelProcessType}
            value={reviewerValue}
            placeholder={t`Вкажіть email або ім’я співробітника`}
            showSuggestions={isReviewersSuggestionsShown}
            suggestionsData={reviewersSuggestions}
            onSuggestionClick={actions.onReviewerSuggestionClick}
            onCloseSuggestions={actions.onCloseReviewerSuggestions}
            chosenItems={chosenReviewersList}
            onRearrangeItem={actions.onRearrangeReviewer}
            onRemoveItem={actions.onRemoveReviewer}
            onDataChange={actions.onReviewerDataChange}
            onAutosuggest={actions.onAutosuggestReviewers}
            renderSuggestion={renderRoleAndGroupSuggestion}
        />
    );
};

export default ReviewersAutosuggestionList;
