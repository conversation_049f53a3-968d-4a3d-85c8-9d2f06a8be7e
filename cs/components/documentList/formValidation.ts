import { yupResolver } from '@hookform/resolvers/yup';

import { directoryFormFields } from 'components/Archive/types';
import { t } from 'ttag';
import * as yup from 'yup';

import { DirectoryFormFields } from './types';

const directoryValidationSchema: yup.ObjectSchema<DirectoryFormFields> = yup
    .object({
        directoryName: yup
            .string()
            .trim()
            .max(100, t`Назва папки не може перевищувати 100 символів`)
            .required(),
    })
    .required();

export const directoryFormResolver = yupResolver(directoryValidationSchema);
