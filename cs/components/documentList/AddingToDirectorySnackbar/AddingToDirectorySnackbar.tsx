import React from 'react';
import { useSelector } from 'react-redux';

import { Snackbar } from '@vchasno/ui-kit';

import { useBackToDirectoryMutation } from 'hooks/useBackToDirectoryMutation';
import { useDirectoryQuery } from 'hooks/useDirectoryQuery';
import { getDocumentListDirectoryId } from 'selectors/router.selectors';
import { t } from 'ttag';

interface AddingToDirectorySnackbarProps {
    prevParentDirectoryId?: Nullable<number>;
    directoryIds?: number[];
    documentIds?: string[];
}

const AddingToDirectorySnackbar: React.FC<AddingToDirectorySnackbarProps> = (
    props,
) => {
    const directoryId = useSelector(getDocumentListDirectoryId);
    const backToDirectoryMutation = useBackToDirectoryMutation();
    const { data: prevDirectory } = useDirectoryQuery(
        props.prevParentDirectoryId,
        true,
    );

    const onCancelMoving = async () => {
        await backToDirectoryMutation.mutateAsync([
            {
                parentId: directoryId ? Number(directoryId) : null,
                directoryIds: props.directoryIds,
                documentIds: props.documentIds,
            },
        ]);
    };

    const mainText = prevDirectory?.name
        ? `${t`Успішно переміщено в папку`} "${prevDirectory?.name}"`
        : t`Успішно переміщено`;

    return (
        <Snackbar.WithAction>
            {mainText}
            <Snackbar.ActionButton onClick={onCancelMoving}>
                {t`Скасувати переміщення`}
            </Snackbar.ActionButton>
        </Snackbar.WithAction>
    );
};

export default AddingToDirectorySnackbar;
