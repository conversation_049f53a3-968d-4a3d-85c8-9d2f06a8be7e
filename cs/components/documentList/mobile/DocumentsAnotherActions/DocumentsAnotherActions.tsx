import React, { useState } from 'react';

import { Button } from '@vchasno/ui-kit';

import DocumentsActionsSheet from 'components/documentList/mobile/DocumentsActionsSheet';
import { t } from 'ttag';

const DocumentsAnotherActions: React.FC = () => {
    const [isOpenDocsActionsSheet, setIsOpenDocsActionsSheet] = useState(false);

    const onCloseDocsActionsSheet = () => {
        setIsOpenDocsActionsSheet(false);
    };

    const onOpenDocsActionsSheet = () => {
        setIsOpenDocsActionsSheet(true);
    };

    return (
        <>
            <Button wide theme="secondary" onClick={onOpenDocsActionsSheet}>
                {t`Інші дії`}
            </Button>
            <DocumentsActionsSheet
                isOpen={isOpenDocsActionsSheet}
                onClose={onCloseDocsActionsSheet}
            />
        </>
    );
};

export default DocumentsAnotherActions;
