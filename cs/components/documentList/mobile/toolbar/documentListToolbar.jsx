import React, { Fragment, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Button, FlexBox } from '@vchasno/ui-kit';

import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import { signWithKepFlowSelectors } from 'components/SignWithKepFlow/signWithKepFlowSlice';
import AddListToDirectoryButton from 'components/documentList/AddListToDirectoryButton';
import DocumentsAnotherActions from 'components/documentList/mobile/DocumentsAnotherActions';
import PropTypes from 'prop-types';
import { getAppFlags, getCurrentCompanyEdrpou } from 'selectors/app.selectors';
import { getIsArchivePage } from 'selectors/router.selectors';
import eventTracking from 'services/analytics/eventTracking';
import { SignatureFormat } from 'services/enums';
import { t } from 'ttag';

import { getIsReviewNeededBeforeAction } from '../../helpers';

import PopupMobile from '../../../ui/popup/mobile/popup';

import KepSignButton from '../../../KepSigner/KepSignButton';
import { MAX_KEP_MULTI_SIGN_DOCUMENTS_COUNT } from '../../../KepSigner/constants';
import SendPopup from '../../../sendPopup/sendPopup';

// styles
import css from './documentListToolbar.css';

const Toolbar = (props) => {
    const [showSignPopup, setShowPopup] = React.useState(false);
    const featureFlags = useSelector(getAppFlags);
    const currentCompanyEdrpou = useSelector(getCurrentCompanyEdrpou);
    const isNewKepSignFlowEnabled = useSelector(
        signWithKepFlowSelectors.selectIsFlowEnabled,
    );
    const isArchivePage = useSelector(getIsArchivePage);

    const handleSendDocument = (email, edrpou, isEmailHidden) => {
        props.onSendDocuments(
            props.selectedDocuments,
            email,
            edrpou,
            isEmailHidden,
        );
    };
    const isReviewNeededBeforeAction = getIsReviewNeededBeforeAction(
        props.selectedDocuments,
        currentCompanyEdrpou,
    );

    const isKepDisallowToSignOverflowMaxLimit =
        props.isSelectedAllByFilter &&
        props.totalDocumentsCount > MAX_KEP_MULTI_SIGN_DOCUMENTS_COUNT;

    const { EXTERNAL_SEPARATED, INTERNAL_SEPARATED } = SignatureFormat;
    const isKepDisabled = props.selectedDocuments.some(
        (doc) =>
            ![
                EXTERNAL_SEPARATED,
                ...(featureFlags.ENABLE_KEP_SIGN_FOR_INTERNAL_SEPARATED_SIGNATURE_FORMAT
                    ? [INTERNAL_SEPARATED]
                    : []),
            ].includes(doc.expectedSignatureFormat),
    );

    const signButton = useMemo(() => {
        if (isNewKepSignFlowEnabled) {
            return (
                <KepSignButton
                    width="full"
                    buttonTitle={t`Підписати`}
                    size="sm"
                    onSign={() => {
                        eventTracking.sendToGTMV4({
                            event:
                                KEP_SIGN_FLOW_ANALYTICS_EVENT.SIGN_DOC_LIST_PAGE,
                        });
                        props.onSignDocuments(props.selectedDocuments, {
                            isKepNew: !isKepDisabled,
                        });
                    }}
                />
            );
        }

        return (
            <Button
                wide
                disabled={!props.documentActions.sign}
                size="sm"
                onClick={() => setShowPopup(true)}
            >
                {t`Підписати`}
            </Button>
        );
    }, [isNewKepSignFlowEnabled]);

    return (
        <FlexBox gap={10} align="center">
            <div style={{ flex: 1 }}>
                <DocumentsAnotherActions />
            </div>
            <div style={{ flex: 1 }}>
                {isArchivePage && (
                    <AddListToDirectoryButton
                        theme="primary"
                        size="sm"
                        selectedDocuments={props.selectedDocuments}
                    />
                )}
                {!isArchivePage && (
                    <>
                        {props.documentActions.send ? (
                            <Fragment>
                                <Button
                                    wide
                                    disabled={isReviewNeededBeforeAction}
                                    size="sm"
                                    onClick={() => props.onOpenPopup('send')}
                                >
                                    {t`Надіслати`}
                                </Button>
                                {props.isSendPopupOpened && (
                                    <SendPopup
                                        currentRoleId={props.currentRoleId}
                                        isActive={props.isSendPopupOpened}
                                        isMultiAction={
                                            props.selectedDocuments.length > 1
                                        }
                                        isOverlimit={props.isOverlimit}
                                        documents={props.selectedDocuments}
                                        sentResults={props.sentResults}
                                        status={props.sendPopupStatus}
                                        errorMessage={props.errorMessage}
                                        sentDocCount={props.sentDocCount}
                                        onSend={handleSendDocument}
                                        onClose={() =>
                                            props.onClosePopup('send')
                                        }
                                    />
                                )}
                            </Fragment>
                        ) : (
                            <>
                                {signButton}
                                <PopupMobile
                                    inPortal
                                    active={showSignPopup}
                                    onClose={() => setShowPopup(false)}
                                >
                                    <h4 className={css.mobilePopupTitle}>
                                        {t`Підписати`}
                                    </h4>
                                    <div
                                        className={css.mobilePopupSubTitle}
                                    >{`${t`Вибрано`} ${
                                        props.selectedDocuments.length
                                    } ${
                                        props.selectedDocuments.length === 1
                                            ? t`документ`
                                            : t`документи`
                                    } ${t`до підписання`}`}</div>
                                    <div
                                        className={css.mobilePopupBtnContainer}
                                    >
                                        <Button
                                            disabled={
                                                !props.documentActions.sign
                                            }
                                            wide
                                            size="sm"
                                            onClick={() => {
                                                setShowPopup(false);
                                                props.onSignDocuments(
                                                    props.selectedDocuments,
                                                );
                                            }}
                                        >
                                            {t`Підписати ключем КЕП/ЕЦП`}
                                        </Button>
                                        {!isKepDisallowToSignOverflowMaxLimit &&
                                            !isKepDisabled && (
                                                <KepSignButton
                                                    disabled={isKepDisabled}
                                                    width="full"
                                                    onSign={() => {
                                                        setShowPopup(false);
                                                        props.onSignDocuments(
                                                            props.selectedDocuments,
                                                            {
                                                                isKep: true,
                                                            },
                                                        );
                                                    }}
                                                    buttonTitle={t`Підписати Вчасно.КЕП`}
                                                />
                                            )}
                                    </div>
                                </PopupMobile>
                            </>
                        )}
                    </>
                )}
            </div>
        </FlexBox>
    );
};

Toolbar.propTypes = {
    isSendPopupOpened: PropTypes.bool,
    isOverlimit: PropTypes.bool,
    documentActions: PropTypes.object.isRequired,
    sendPopupStatus: PropTypes.string,
    errorMessage: PropTypes.string,
    sentDocCount: PropTypes.number,
    sentResults: PropTypes.array,
    selectedDocuments: PropTypes.array.isRequired,
    onSignDocuments: PropTypes.func.isRequired,
    onOpenPopup: PropTypes.func.isRequired,
    onClosePopup: PropTypes.func.isRequired,
};

export default Toolbar;
