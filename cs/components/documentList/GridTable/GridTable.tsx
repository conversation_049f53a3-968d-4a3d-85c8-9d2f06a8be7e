import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import {
    ColDef,
    ColumnState,
    DragStoppedEvent,
    GridApi,
    IRowNode,
    ModuleRegistry,
} from '@ag-grid-community/core';
import { AgGridReact, AgGridReactProps } from '@ag-grid-community/react';

import cn from 'classnames';
import { useHandleAgGridRowClicked } from 'components/documentList/useHandleAgGridRowClicked';
import { useUrlQueryParam } from 'hooks/useUrlQueryParam';
import { getAppFlags } from 'selectors/app.selectors';
import { Document } from 'services/documents/ts/types';

import { CommonDocumentColumnKey } from '../types';

import actionCreators from '../documentListActionCreators';
import {
    getActualRightPinnedColumnWidth,
    getDocumentListActiveTableSettings,
    getDocumentListDocuments,
} from '../selectors';

import {
    defineGridColumnPropsBySettingItem,
    getContainerStyles,
    getGridColumnStateFromLocalStorage,
    getGridTypeByFolder,
    getRowClass,
    getRowId,
    getSortedColumnList,
    handleAgGridCellClicked,
    onBodyScrollEnd,
    saveGridColumnStateToLocalStorage,
} from './utils';

import '../../../lib/ag-grid';
import ConfigHeaderRenderer from './ConfigHeaderRenderer/ConfigHeaderRenderer';
import HorizontalScrollControls from './HorizontalScrollControls';
import NoRowsOverlayComponent from './NoRowsOverlayComponent';
import columnConfigProps from './columnConfig';
import { frameworkComponents } from './frameworkComponents';
import { rowSelection, selectionColumnDef } from './staticGridProps';

ModuleRegistry.registerModules([ClientSideRowModelModule]);

export interface GridTableProps {
    className?: string;
}

const defaultColDef: ColDef<Document> = {
    resizable: true,
    suppressMovable: false,
};

const GridTable: React.FC<React.PropsWithChildren<GridTableProps>> = () => {
    const flags = useSelector(getAppFlags);
    const folderId = useUrlQueryParam('folder_id');
    const gridType = getGridTypeByFolder(folderId);
    const isDocumentViewLockActive = flags.DOCUMENT_VIEW_LOCK_ON;

    const dispatch = useDispatch();
    const [gridApi, setGridApi] = useState<Nullable<GridApi>>(null);
    const [savedGridColumnsState, setSavedGridColumnsState] = useState(() =>
        getGridColumnStateFromLocalStorage(gridType),
    );
    const documents = useSelector(getDocumentListDocuments);
    const activeTableSettings = useSelector(getDocumentListActiveTableSettings);
    const isPerformMode = documents.length > 25;
    const rightColumnWidth = useSelector(getActualRightPinnedColumnWidth);

    const handleAgGridRowClicked = useHandleAgGridRowClicked();

    useEffect(() => {
        setSavedGridColumnsState(() =>
            getGridColumnStateFromLocalStorage(gridType),
        );
    }, [gridType]);

    const sortedColumnList = useMemo(
        () =>
            getSortedColumnList({
                savedGridColumnsState,
                activeTableSettings,
            }),
        [activeTableSettings, savedGridColumnsState],
    );

    const columnDefs = useMemo(() => {
        const columnMap = savedGridColumnsState.reduce((acc, item) => {
            acc[item.colId as string] = item;

            return acc;
        }, {} as Record<string, ColumnState>);

        return [
            {
                keyCreator: () => 'config',
                colId: 'config',
                cellRenderer: 'rightCellRenderer',
                resizable: false,
                headerName: ' ',
                headerComponent: ConfigHeaderRenderer,
                pinned: 'right',
                lockPinned: true,
                lockPosition: true,
                width: rightColumnWidth,
                cellStyle: { overflow: 'visible' },
            },
            ...sortedColumnList.map((item) => ({
                colId: item.name,
                ...defineGridColumnPropsBySettingItem(item),
                ...columnConfigProps[item.name as CommonDocumentColumnKey],
                pinned: columnMap[item.name]?.pinned,
                width: columnMap[item.name]?.width,
            })),
        ] as ColDef<Document>[];
    }, [sortedColumnList, savedGridColumnsState]);

    const handleDragStopped = (event: DragStoppedEvent) => {
        setSavedGridColumnsState(() => event.api.getColumnState());
        saveGridColumnStateToLocalStorage(event.api.getColumnState(), gridType);
    };

    useEffect(() => {
        if (gridApi) {
            setSavedGridColumnsState(() => gridApi.getColumnState());
            saveGridColumnStateToLocalStorage(
                gridApi.getColumnState(),
                gridType,
            );
        }
    }, [rightColumnWidth]);

    const handleGridReady: AgGridReactProps['onGridReady'] = (event) => {
        setGridApi(event.api);

        event.api.forEachLeafNode((node) => {
            node.setSelected(node.data.selected, false);
        });

        if (savedGridColumnsState) {
            event.api.applyColumnState({
                state: savedGridColumnsState,
            });
        }
    };

    const onSelectionChanged: AgGridReactProps['onSelectionChanged'] = (
        event,
    ) => {
        const selectedRows = event.api.getSelectedRows();

        // table => redux
        dispatch(
            actionCreators.batchSelection(selectedRows?.map((item) => item.id)),
        );
    };

    useEffect(() => {
        // синхронізація стану redux та таблиці  redux => table
        const selectedRowsInTable = gridApi?.getSelectedRows() || [];
        const reduxSelectedRows = documents.filter((item) => item.selected);

        // запобігаємо виклику синхронізації обраних елементів через апі таблиці якщо всі елементи співпадають
        if (
            reduxSelectedRows.length === selectedRowsInTable.length &&
            reduxSelectedRows.every((item) =>
                selectedRowsInTable?.includes(item),
            )
        ) {
            return;
        }

        const nodes = gridApi
            ?.getRenderedNodes()
            .filter((node) =>
                reduxSelectedRows.some((item) => item.id === node.data.id),
            ) as IRowNode[];

        // одразу всі елементи скидаємо
        gridApi?.deselectAll();
        // масово вибираємо елементи за допомогою нового апі таблиці
        gridApi?.setNodesSelected({ nodes, newValue: true });
    }, [documents]);

    return (
        <div
            className={cn('ag-theme-alpine', {
                'perform-mode': isPerformMode,
            })}
            style={getContainerStyles(documents.length)}
        >
            {gridApi && <HorizontalScrollControls api={gridApi} />}
            <AgGridReact
                noRowsOverlayComponent={NoRowsOverlayComponent}
                alwaysShowHorizontalScroll
                selectionColumnDef={selectionColumnDef}
                scrollbarWidth={isPerformMode ? undefined : 0}
                onBodyScrollEnd={onBodyScrollEnd}
                rowDragManaged
                getRowClass={isDocumentViewLockActive ? getRowClass : undefined}
                getRowId={getRowId}
                onSelectionChanged={onSelectionChanged}
                suppressScrollOnNewData
                suppressColumnMoveAnimation
                onRowClicked={handleAgGridRowClicked}
                onDragStopped={handleDragStopped}
                onGridReady={handleGridReady}
                rowHeight={36}
                onCellClicked={handleAgGridCellClicked}
                components={frameworkComponents}
                domLayout={isPerformMode ? 'normal' : 'autoHeight'}
                defaultColDef={defaultColDef}
                popupParent={document.body}
                suppressDragLeaveHidesColumns
                rowData={documents}
                rowSelection={rowSelection}
                columnDefs={columnDefs}
            />
        </div>
    );
};

export default GridTable;
