import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import documentListActions from '../../documentListActionCreators';
import { getDocumentListTableSettings } from '../../selectors';

import DocumentListSettings from '../../../documentListSettings/documentListSettings';

import css from './ConfigHeaderRenderer.css';

const ConfigHeaderRenderer = () => {
    const dispatch = useDispatch();
    const tableSettings = useSelector(getDocumentListTableSettings);

    const onChangeTableSettings = (
        settingName: string,
        isPinnedIcon: boolean,
    ) => {
        if (isPinnedIcon) {
            dispatch(
                documentListActions.onChangePinnedIconsSettings(settingName),
            );
        } else {
            dispatch(documentListActions.onChangeTableSettings(settingName));
        }
    };

    return (
        <div className={css.root}>
            <DocumentListSettings
                suppressColumnLimits
                tableSettings={tableSettings}
                onChangeTableSettings={onChangeTableSettings}
            />
        </div>
    );
};

export default ConfigHeaderRenderer;
