import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { useSelector } from 'react-redux';

import { DocumentCellRendererParams } from 'components/documentList/GridTable/types';
import constants from 'components/documentList/constants';
import { useGridTableContext } from 'components/documentList/gridTableContext';
import { DirectoryFormFields } from 'components/documentList/types';
import { getDocumentListDirectoryId } from 'selectors/router.selectors';
import { t } from 'ttag';

import { useAddDirectoryMutation } from './useAddDirectoryMutation';
import { useChangeDirectoryMutation } from './useChangeDirectoryMutation';

export const useDirectoryFormSubmit = (params: DocumentCellRendererParams) => {
    const { reset, getFieldState, setError } = useFormContext();
    const { gridApi } = useGridTableContext();
    const directoryId = useSelector(getDocumentListDirectoryId);
    const addDirectoryMutation = useAddDirectoryMutation();
    const changeDirectoryMutation = useChangeDirectoryMutation();

    const directoryNameErrorMsg =
        changeDirectoryMutation.error?.message ||
        addDirectoryMutation.error?.message;

    useEffect(() => {
        if (directoryNameErrorMsg) {
            setError('directoryName', {
                message: directoryNameErrorMsg,
            });
        }
    }, [directoryNameErrorMsg]);

    const onSubmit = async (data: DirectoryFormFields) => {
        if (!getFieldState('directoryName').isDirty) {
            reset();

            gridApi?.applyTransaction({
                update: [
                    {
                        ...params.data,
                        isEditRow: false,
                    },
                ],
            });

            gridApi?.refreshCells({
                force: true,
                rowNodes: [params.node],
            });

            return;
        }
        // створення папки
        if (params.data.id === constants.CREATE_DIRECTORY_ROW_ID) {
            const directory = await addDirectoryMutation.mutateAsync([
                {
                    name: data.directoryName,
                    parentId: directoryId,
                },
            ]);

            reset();

            gridApi?.applyTransaction({
                remove: [params.data],
            });
            gridApi?.applyTransaction({
                addIndex: 0,
                add: [
                    {
                        ...directory,
                        dateUpdated: directory.date_created,
                        category: t`Папка`,
                        isDirectory: true,
                    },
                ],
            });

            return;
        }

        const directory = await changeDirectoryMutation
            .mutateAsync([
                {
                    id: params.data.id,
                    name: data.directoryName,
                },
            ])
            .then((response) => response.json());

        reset();

        gridApi?.applyTransaction({
            update: [
                {
                    ...params.data,
                    ...directory,
                    date: directory.date_created,
                    parentId: directory.parent_id,
                    isEditRow: false,
                },
            ],
        });

        gridApi?.refreshCells({
            force: true,
            rowNodes: [params.node],
        });
    };

    return onSubmit;
};
