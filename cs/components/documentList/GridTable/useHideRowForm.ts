import { useGridTableContext } from 'components/documentList/gridTableContext';

export const useHideRowForm = () => {
    const { gridApi } = useGridTableContext();

    const hideRowForm = () => {
        const rowsNode: any[] = [];
        gridApi?.forEachNode((node) => rowsNode.push(node));

        const folderFormRowNode = rowsNode.find((node) => node.data.isEditRow);

        if (!folderFormRowNode) {
            return;
        }
        const folderFormRowData = folderFormRowNode?.data;

        const updatedDirectoryFormRowData = {
            ...folderFormRowData,
            isEditRow: false,
        };

        // Якщо відкрита форма створення/редагування папки, то видаляємо її
        if (!updatedDirectoryFormRowData.id) {
            gridApi?.applyTransaction({
                remove: [updatedDirectoryFormRowData],
            });

            return;
        }

        gridApi?.applyTransaction({
            update: [updatedDirectoryFormRowData],
        });

        gridApi?.refreshCells({
            force: true,
            rowNodes: [folderFormRowNode],
        });
    };

    return hideRowForm;
};
