import React from 'react';

import { FlexBox } from '@vchasno/ui-kit';

import { DocumentCellRendererParams } from 'components/documentList/GridTable/types';
import Icon from 'ui/icon';

import DirectorySvg from './images/directory.svg';

import css from './DirectoryTitleRenderer.css';

const DirectoryTitleRenderer: React.FC<DocumentCellRendererParams> = (
    params,
) => (
    <FlexBox
        gap={8}
        align="center"
        justify="flex-start"
        className={css.container}
    >
        <div className={css.icon}>
            <Icon glyph={DirectorySvg} />
        </div>
        {params.data.name}
    </FlexBox>
);

export default DirectoryTitleRenderer;
