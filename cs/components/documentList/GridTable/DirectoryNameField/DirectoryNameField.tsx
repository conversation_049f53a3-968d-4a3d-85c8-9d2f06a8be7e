import React, { KeyboardEvent, useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

import { TextInput } from '@vchasno/ui-kit';

import { DocumentCellRendererParams } from '../types';

import { makeSuppressRowClickEffectHandler } from '../utils';

import { useDirectoryFormSubmit } from '../useDirectoryFormSubmit';

import css from './DirectoryNameField.css';

interface DirectoryNameFieldProps {
    cellParams: DocumentCellRendererParams;
}

const DirectoryNameField: React.FC<DirectoryNameFieldProps> = ({
    cellParams,
}) => {
    const { setFocus, handleSubmit, control } = useFormContext();
    const onSubmit = useDirectoryFormSubmit(cellParams);

    const onKeyUp = (event: KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            handleSubmit(onSubmit)();
        }
    };

    useEffect(() => {
        setFocus('directoryName', {
            shouldSelect: true,
        });
    }, []);

    return (
        <div className={css.container}>
            <Controller
                name="directoryName"
                control={control}
                render={({ field, fieldState }) => (
                    <TextInput
                        {...field}
                        onClick={makeSuppressRowClickEffectHandler()}
                        className={css.input}
                        onKeyUp={onKeyUp}
                        error={fieldState.error?.message}
                    />
                )}
            />
        </div>
    );
};

export default DirectoryNameField;
