.root {
    position: relative;
    display: inline-block;
    line-height: inherit;
}

.root .mainIcon {
    width: 16px;
    height: 16px;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.root:hover .mainIcon {
    opacity: 1;
}

.root.justCopied .mainIcon {
    color: #1cb800;
}

.containerRoot {
    position: relative;
    right: 0;
    left: 3px;
    display: flex;
    max-width: 100%;
    align-items: center;
    padding: 0;
}

.text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.copyIcon {
    position: absolute;
    top: 4px;
    right: 4px;
    display: flex;
    width: 25px;
    height: 25px;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border-radius: var(--border-radius);
    color: rgb(51, 51, 51);
    cursor: pointer;
    opacity: 0;
    transform: translateX(3px);
    transition: opacity 0.3s, color 0.3s, background-color 0.3s, transform 0.3s;
}

.copyIcon > svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    color: var(--content-color);
}

.containerRoot:hover .copyIcon {
    background-color: var(--white-bg);
    opacity: 1;
    transform: translateX(0);
}

.copyIcon:hover svg {
    color: var(--primary-cta-color);
}
