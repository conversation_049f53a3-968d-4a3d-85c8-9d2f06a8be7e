import React from 'react';

import cn from 'classnames';
import { useStateWithTimeoutReset } from 'hooks/useStateWithTimeoutReset';
import { copyTextToClipboard } from 'services/clipboard';
import Icon, { Glyph } from 'ui/icon/icon';

import { makeSuppressRowClickEffectHandler } from '../utils';

import CopySvg from './images/copy.svg';

import css from './CopyActionIcon.css';

type CopyActionIconVariant = 'copy';

export interface CopyActionIconProps
    extends React.HTMLAttributes<HTMLDivElement> {
    variant?: CopyActionIconVariant;
    children?: string;
    onCopied?: () => void;
    onCopyFailed?: () => void;
}

const iconVariantMap: Record<CopyActionIconVariant, Glyph> = {
    copy: CopySvg,
};

const CopyActionIcon: React.FC<
    React.PropsWithChildren<CopyActionIconProps>
> = ({
    variant = 'copy',
    onClick,
    onCopied,
    onCopyFailed,
    className,
    children,
}) => {
    const [justCopied, setJustCopied] = useStateWithTimeoutReset(3000);

    const handleClick = makeSuppressRowClickEffectHandler((event) => {
        if (children) {
            copyTextToClipboard(children)
                .then(() => {
                    if (!justCopied) {
                        setJustCopied(() => true);
                    }

                    if (typeof onCopied === 'function') {
                        onCopied();
                    }
                })
                .catch(onCopyFailed);
        }

        if (onClick) {
            onClick(event as React.MouseEvent<HTMLDivElement, MouseEvent>);
        }
    });

    return (
        <div
            className={cn(css.root, className, {
                [css.justCopied]: justCopied,
            })}
            onClick={handleClick}
        >
            <Icon glyph={iconVariantMap[variant]} className={css.mainIcon} />
        </div>
    );
};

export default CopyActionIcon;
