import React, { useEffect, useRef } from 'react';
import { useHistory } from 'react-router-dom';

import { RowDropZoneParams } from '@ag-grid-community/core';
import { Text } from '@vchasno/ui-kit';

import cn from 'classnames';
import { useGridTableContext } from 'components/documentList/gridTableContext';
import { useAddToDirectoryMutation } from 'hooks/useAddToDirectoryMutation';

import css from './DirectoryBreadcrumbs.css';

interface BreadcrumbItemProps {
    directoryId?: number;
}

export const BreadcrumbsItem: React.FC<BreadcrumbItemProps> = (props) => {
    const history = useHistory();
    const { gridApi } = useGridTableContext();
    const addToDirectoryMutation = useAddToDirectoryMutation({
        isShowAddingToDirectorySnackbar: true,
    });

    const componentId = useRef(`breadcrumbItem${props.directoryId || ''}`); // '' use for Archive root folder

    const onChangeDirectory = () => {
        history.push({
            pathname: `/app/archive${
                props.directoryId ? `/${props.directoryId}` : ''
            }`,
        });
    };

    useEffect(() => {
        if (!gridApi) {
            return;
        }

        const targetContainer: Nullable<HTMLElement> = document.getElementById(
            componentId.current,
        );

        if (!targetContainer) {
            return;
        }

        const dropZoneParams: RowDropZoneParams = {
            getContainer: () => targetContainer,
            onDragStop: (params) => {
                const directoryIdsForMoving = params.nodes
                    .filter((node) => node.data.isDirectory)
                    .map((directory) => Number(directory.id));
                const documentIdsForMoving = params.nodes
                    .filter((node) => !node.data.isDirectory)
                    .map((document) => document.id!);

                addToDirectoryMutation.mutateAsync([
                    {
                        parentId: props.directoryId || null,
                        directoryIds: directoryIdsForMoving,
                        documentIds: documentIdsForMoving,
                    },
                ]);
                targetContainer.classList.remove(css.breadcrumbsItemHover);
            },
            onDragEnter: () => {
                targetContainer.classList.add(css.breadcrumbsItemHover);
            },
            onDragLeave: () => {
                targetContainer.classList.remove(css.breadcrumbsItemHover);
            },
        };

        gridApi?.addRowDropZone(dropZoneParams);
    }, [gridApi]);

    return (
        <Text
            onClick={onChangeDirectory}
            id={componentId.current}
            className={cn(css.breadcrumbsItem, css.breadcrumbsActiveItem)}
        >
            {props.children}
        </Text>
    );
};

export default BreadcrumbsItem;
