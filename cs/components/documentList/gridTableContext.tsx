import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useMediaQuery } from 'react-responsive';

import { GridApi } from '@ag-grid-community/core';

import { DocumentListDocumentItem } from 'components/documentList/types';
import { DocumentDirectory } from 'gql-types';
import { MEDIA_WIDTH } from 'lib/constants';
import {
    getDocumentListDirectories,
    getDocumentListItems,
} from 'selectors/documentList.selectors';

interface IContext {
    items: (DocumentListDocumentItem | DocumentDirectory)[];
    documents: DocumentListDocumentItem[];
    directories: DocumentDirectory[];
    gridApi: Nullable<GridApi>;
    setGridApi: (gridApi: Nullable<GridApi>) => void;
}

const GridTableContext = React.createContext({} as IContext);

export const GridTableProvider: React.FC = (props) => {
    const documents = useSelector(getDocumentListItems);
    const directories = useSelector(getDocumentListDirectories);
    const isMobile = useMediaQuery({ maxWidth: MEDIA_WIDTH.tablet });

    const [gridApi, setGridApi] = useState<Nullable<GridApi>>(null);

    const value = {
        directories: directories || [],
        documents: documents || [],
        items: [...(directories || []), ...(documents || [])],
        gridApi,
        setGridApi,
    };

    useEffect(() => {
        // Цей провайдер використовується для декількох таблиць
        // і при переключені з мобільної таблиці на десктопну або навпаки це прибире конфлікти у функціоналі
        setGridApi(null);
    }, [isMobile]);

    return (
        <GridTableContext.Provider value={value}>
            {props.children}
        </GridTableContext.Provider>
    );
};

export function useGridTableContext() {
    const context = React.useContext(GridTableContext);

    if (Object.keys(context).length === 0) {
        throw new Error('useGridTableContext must be inside GridTableProvider');
    }

    return context;
}
