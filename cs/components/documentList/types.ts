import { Document } from 'services/documents/ts/types';

export enum DocumentsRequestStatus {
    UNCALLED,
    PENDING,
    SUCCESS,
    FAILURE,
}

export type CommonDocumentColumnKey =
    | 'date'
    | 'category'
    | 'title'
    | 'company'
    | 'number'
    | 'email'
    | 'status'
    | 'reviews'
    | 'requisites'
    | 'currentExecutor'
    | 'signers'
    | 'sender'
    | 'amount'
    | 'recipient';

export type CustomCompanyField = string;

export interface DocumentListTableSettingItem {
    name: CommonDocumentColumnKey | CustomCompanyField;
    title: string;
    text: string;
    isActive: boolean;
    sort: string;
    filter?: string;
    field?: Record<string, unknown>;
}

export interface IDocumentListPinnedIconsSettings {
    name: string;
    isActive: boolean;
    isIcon: boolean;
    side: 'left' | 'right';
}

export type TDocumentListPinnedIconsSettingsMap = Record<
    string,
    IDocumentListPinnedIconsSettings
>;

export interface DocumentListDocumentItem extends Document {
    selected: boolean;
}

export interface DirectoryFormFields {
    directoryName: string;
}
