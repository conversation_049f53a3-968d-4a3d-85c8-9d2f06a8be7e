.iconContainer {
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
}

:global(.vchasno-dark-theme) .icon svg {
    color: var(--content-color);
}

.icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.icon svg {
    color: var(--grey-color);
}

.commentIcon {
    position: relative;
    margin-top: 1px;
    color: var(--dark-pigeon-color);
}

.commentIcon .counterBadge {
    position: absolute;
    top: -5px;
    right: -8px;
    display: flex;
    min-width: 12px;
    height: 14px;
    align-items: center;
    justify-content: center;
    padding: 0 1px;
    background-color: var(--dark-pigeon-color);
    border-radius: 7px;
    color: #fff;
    font-size: 9px;
    font-weight: 600;
}

.commentIconDisabled {
    opacity: 0.1;
    transition: opacity 0.3s;
}

.commentIconDisabled:hover {
    opacity: 1;
}
