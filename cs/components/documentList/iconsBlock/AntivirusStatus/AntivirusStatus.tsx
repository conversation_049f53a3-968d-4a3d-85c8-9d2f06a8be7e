import React, { FC } from 'react';

import { BlackTooltip } from '@vchasno/ui-kit';

import { AntivirusStatusTypes } from 'services/antivirus/enums';
import { t } from 'ttag';

import { Nullable } from '../../../../types/general';

import Icon from '../../../ui/icon/icon';

import { ANTIVIRUS_STATUS_ICONS } from '../../../document/toolbar/AntivirusStatus/constants';

interface Props {
    status?: Nullable<AntivirusStatusTypes> | undefined;
    suppressPortal?: boolean;
}

const ANTIVIRUS_STATUS_TEXT = {
    [AntivirusStatusTypes.encrypted]: t`Документ не перевірений`,
    [AntivirusStatusTypes.checking]: t`Документ в процесі перевірки антивірусом`,
    [AntivirusStatusTypes.clean]: t`Документ пройшов перевірку антивірусом`,
    [AntivirusStatusTypes.infected]: t`Документ помічений як небезпечний`,
    default: t`Документ не перевірений`,
};

const AntivirusStatus: FC<React.PropsWithChildren<Props>> = (props) => {
    const antivirusIcon = props.status
        ? ANTIVIRUS_STATUS_ICONS[props.status]
        : ANTIVIRUS_STATUS_ICONS.default;

    const antivirusText = props.status
        ? ANTIVIRUS_STATUS_TEXT[props.status]
        : ANTIVIRUS_STATUS_TEXT.default;

    return (
        <BlackTooltip
            placement="right"
            disableInteractive
            title={antivirusText}
        >
            <span>
                <Icon glyph={antivirusIcon} />
            </span>
        </BlackTooltip>
    );
};

export default AntivirusStatus;
