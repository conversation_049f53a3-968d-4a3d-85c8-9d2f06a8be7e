import React, { FC, useState } from 'react';

import { t } from 'ttag';

import { SignDocError } from '../../services/documents/ts/types';

import Message from '../ui/message/message';
import PseudoLink from '../ui/pseudolink/pseudolink';

import DocumentsSignSummaryBlock from './documentsSignSummaryBlock';

interface Props {
    results: SignDocError[];
}

const DocumentsSignSummaryErrors: FC<React.PropsWithChildren<Props>> = ({
    results,
}) => {
    const [expanded, setExpanded] = useState(false);

    return (
        <>
            <Message type="error" sizeSmall>
                {t`Інші помилки:`}
            </Message>
            {!expanded ? (
                <PseudoLink onClick={() => setExpanded(true)}>
                    {t`Більше`}
                </PseudoLink>
            ) : (
                <ul>
                    {results.map(({ doc, error }) => {
                        return (
                            <li key={doc.id}>
                                <DocumentsSignSummaryBlock
                                    label={error}
                                    docs={[doc]}
                                />
                            </li>
                        );
                    })}
                </ul>
            )}
        </>
    );
};

export default DocumentsSignSummaryErrors;
