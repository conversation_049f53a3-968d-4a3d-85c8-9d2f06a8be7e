import actions from './notificationCenterActions';

const initState = {
    notifications: [],
};

const notificationCenterReducer = (state = initState, action) => {
    switch (action.type) {
        case actions.NOTIFICATION_CENTER__ADD_NOTIFICATION:
            return {
                ...state,
                notifications: [...state.notifications, action.notification],
            };
        case actions.NOTIFICATION_CENTER__UPDATE_NOTIFICATION:
            return {
                ...state,
                notifications: state.notifications.map((item) => {
                    if (item.id === action.id)
                        return { ...item, ...action.updateData };
                    return item;
                }),
            };
        case actions.NOTIFICATION_CENTER__REMOVE_NOTIFICATION:
            return {
                ...state,
                notifications: state.notifications.filter(
                    (item) => item.id !== action.id,
                ),
            };
        default:
            return state;
    }
};

export default notificationCenterReducer;
