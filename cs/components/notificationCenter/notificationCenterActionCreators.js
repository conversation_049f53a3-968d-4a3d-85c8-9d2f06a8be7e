import actions from './notificationCenterActions';

import uuid from '../../services/uuid';

function removeNotification(id) {
    return (dispatch) => {
        dispatch({
            type: actions.NOTIFICATION_CENTER__REMOVE_NOTIFICATION,
            id,
        });
    };
}

function updateNotification(id, updateData) {
    return (dispatch) => {
        dispatch({
            type: actions.NOTIFICATION_CENTER__UPDATE_NOTIFICATION,
            id,
            updateData,
        });
        if (updateData.autoClose)
            setTimeout(
                () => dispatch(removeNotification(id)),
                updateData.autoClose,
            );
    };
}

/**
 * @typedef {object} NotificationOptions
 * @property {string} [title]
 * @property {string} [text]
 * @property {'error'|'info'|'success'} [textType]
 * @property {'text'|'progress'} [type]
 * @property {number} [progressTotalCount]
 * @property {number} [progressCurrentCount]
 * @property {number} [autoClose]
 * @property {string} [borderColor] - 'green'
 * @property {string} [progressBarColor] - 'green'
 * @property {boolean} [showCloseButton] - 'green'
 */
/**
 * @param {NotificationOptions} notification
 * @returns {[function(*): void, function(): void]}
 */
function addNotification(notification) {
    return (dispatch) => {
        const id = uuid();
        const onRemove = () => dispatch(removeNotification(id));
        dispatch({
            type: actions.NOTIFICATION_CENTER__ADD_NOTIFICATION,
            notification: { ...notification, id, onClose: onRemove },
        });

        if (notification.autoClose)
            setTimeout(onRemove, notification.autoClose);

        return [
            (updateData) => dispatch(updateNotification(id, updateData)),
            onRemove,
        ];
    };
}

export default {
    addNotification,
    removeNotification,
    updateNotification,
};
