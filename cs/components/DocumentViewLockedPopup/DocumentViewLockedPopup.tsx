import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';

import { Button, FlexBox } from '@vchasno/ui-kit';

import {
    getCompanyConfigSettingsMaxDocumentsForView,
    getCurrentCompany,
    getIsMaxArchive,
} from 'selectors/app.selectors';
import { AccountRate } from 'services/enums';
import { jt, t } from 'ttag';

import documentListActionCreators from '../documentList/documentListActionCreators';
import { getIsDocumentViewLockedPopupShow } from '../documentList/selectors';

import Icon from '../ui/icon/icon';
import Popup from '../ui/popup/popup';

import eventTracking from '../../services/analytics/eventTracking';

import LockedDocumentSvg from './images/lockedDocument.svg';

import css from './DocumentViewLockedPopup.css';

const DOCUMENTS_PATHNAME = '/app/documents';

const DocumentViewLockedPopup: React.FC<
    React.PropsWithChildren<unknown>
> = () => {
    const location = useLocation();
    const history = useHistory();
    const dispatch = useDispatch();
    const isShow = useSelector(getIsDocumentViewLockedPopupShow);
    const currentCompany = useSelector(getCurrentCompany);
    const isMaxArchive = useSelector(getIsMaxArchive);
    const maxDocumentsForView = useSelector(
        getCompanyConfigSettingsMaxDocumentsForView,
    );
    const isDocumentsPage = location.pathname === DOCUMENTS_PATHNAME;

    const onClose = () => {
        if (!isDocumentsPage) {
            history.replace(DOCUMENTS_PATHNAME);
        }

        dispatch(documentListActionCreators.showDocumentViewLockedPopup(false));
    };

    const handleShowTariffsBtnClick = () => {
        dispatch(documentListActionCreators.showDocumentViewLockedPopup(false));

        if (isMaxArchive) {
            eventTracking.sendToGTM({
                event: 'click_choose_tariff',
            });
            history.push(`/app/checkout-rates/web`);
            return;
        }

        eventTracking.sendToGTM({
            event: isDocumentsPage
                ? 'click_increase_limit_table'
                : 'click_increase_limit_document',
        });

        eventTracking.sendToGTM({
            category: 'restrictions_pop-up',
            action: 'click_purchase_button',
        });

        history.push(`/app/checkout?rate=${AccountRate.ARCHIVE_BIG}`);
    };

    const documentLimit = (
        <strong
            key={maxDocumentsForView}
        >{t`останні ${maxDocumentsForView} документів`}</strong>
    );

    const descriptionText = isMaxArchive
        ? jt`Ми зберігаємо всі ваші документи без обмежень. Проте ваш тарифний план дозволяє переглянути лише ${documentLimit}. Щоб переглянути цей та всі інші документи, перейдіть на більший тарифний план`
        : jt`Ми зберігаємо всі ваші документи без обмежень. Проте ваш тарифний план дозволяє переглянути лише ${documentLimit}. Щоб переглянути цей та всі інші документи, розширте можливості перегляду документів`;

    useEffect(() => {
        if (isShow) {
            eventTracking.sendToGTM({
                category: 'restrictions_pop-up',
                action: 'show_pop-up',
                label: [...currentCompany?.activeRates].sort().join('|'),
            });
        }
    }, [isShow, currentCompany]);

    return (
        <Popup onClose={onClose} active={isShow}>
            <div className={css.root}>
                <div className={css.iconContainer}>
                    <Icon className={css.icon} glyph={LockedDocumentSvg} />
                </div>
                <div className={css.marginTop}>
                    <h3
                        className={css.title}
                    >{t`Ми не можемо відобразити цей документ`}</h3>
                    <div className={css.text}>{descriptionText}</div>
                </div>
                <FlexBox direction="column" className={css.marginTop}>
                    <Button
                        theme="primary"
                        wide
                        onClick={handleShowTariffsBtnClick}
                    >
                        {isMaxArchive ? t`Обрати тариф` : t`Збільшити ліміт`}
                    </Button>

                    {!isDocumentsPage && (
                        <Button
                            onClick={onClose}
                            theme="secondary"
                            wide
                        >{t`Повернутися до таблиці`}</Button>
                    )}
                </FlexBox>
            </div>
        </Popup>
    );
};

export default DocumentViewLockedPopup;
