.list {
    padding: 0 30px;
    margin: 10px 0 0 0;
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    line-height: 1.2;
    word-wrap: break-word;
}

.item + .item {
    border-top: 1px solid var(--default-border);
}

.header {
    margin-bottom: 10px;
}

.counter {
    text-align: right;
}

.footer {
    display: table;
    width: 100%;
    margin-top: 10px;
    table-layout: fixed;
}

.pagination {
    display: table-cell;
    vertical-align: middle;
}

.itemsPerPage {
    display: table-cell;
    text-align: right;
    vertical-align: middle;
}

.itemsPerPageTitle {
    display: inline-block;
    margin-right: 5px;
}

.itemsPerPageDropdown {
    display: inline-block;
    width: 75px;
}

.filters {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.buttons {
    display: flex;
    flex-wrap: wrap;
    padding: 10px 20px;
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    white-space: nowrap;
}

@media all and (max-width: 768px) {
    .buttons {
        padding: 0;
        margin: 10px 0;
        background-color: transparent;
    }
}

.buttonWrapper {
    position: relative;
}

.buttonContainer:global(.vchasno-ui-button.--secondary) {
    min-width: 40px;
    padding: 0 36px 0 10px;
}

.button {
    display: inline-flex;
    align-items: center;
    vertical-align: top;
}

.button + .button {
    margin-left: 10px;
}

@media all and (max-width: 768px) {
    .button {
        display: flex;
    }

    .button + .button {
        margin: 10px 0 0 0;
    }
}

.icon {
    width: 10px;
    height: 10px;
    margin: auto;
    color: var(--pigeon-color);
}

.proLabel {
    position: absolute;
    top: 0;
    right: 25px;
    bottom: 0;
    display: flex;
    align-items: center;
    margin: auto;
    cursor: pointer;
}
