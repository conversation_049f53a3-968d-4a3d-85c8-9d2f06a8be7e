import { FORM_ERROR } from 'final-form';
import { redirect } from 'lib/navigation';
import { changePhone, update2FAState, verifyPhone } from 'services/user';

import { Thunk } from '../../types';

import appActionCreators from '../app/appActionCreators';
import headerActionCreators from '../header/headerActionCreators';

const ADD_2FA_POPUP__SHOW = 'ADD_2FA_POPUP__SHOW';
const ADD_2FA_POPUP__CLOSE = 'ADD_2FA_POPUP__CLOSE';
const ADD_2FA_POPUP__SET_STEP = 'ADD_2FA_POPUP__SET_STEP';

export type Add2FAStep = 'SUBMIT_PHONE' | 'SUBMIT_CODE' | 'SUCCESS';

const initialState = {
    isActive: false,
    roleId: '',
    step: 'SUBMIT_PHONE' as Add2FAStep,
};

export type Add2FAPopupState = typeof initialState;

export const onOpen2FAPopup = (roleId: string) =>
    ({
        type: ADD_2FA_POPUP__SHOW,
        roleId,
    } as const);

export const onClose2FAPopup = () =>
    ({
        type: ADD_2FA_POPUP__CLOSE,
    } as const);

export const set2FAPopupStep = (step: Add2FAStep) =>
    ({
        type: ADD_2FA_POPUP__SET_STEP,
        step,
    } as const);

export const onSubmitPhone = (values: Record<string, string>): Thunk => async (
    dispatch,
) => {
    const { phone, password } = values;
    try {
        await verifyPhone({ phone, password });
        dispatch(set2FAPopupStep('SUBMIT_CODE'));
    } catch (err) {
        return {
            [FORM_ERROR]: err.message,
        };
    }

    return {};
};

export const onSubmitCode = (values: Record<string, string>): Thunk => async (
    dispatch,
) => {
    const { phone, password, code } = values;
    try {
        await changePhone({ phone, password, code });
        await update2FAState(true);
        dispatch(appActionCreators.onRefreshCurrentUser());
        dispatch(set2FAPopupStep('SUCCESS'));
    } catch (err) {
        return {
            [FORM_ERROR]: err.message,
        };
    }

    return {};
};

export const handle2FASubmit = (
    values: Record<string, string>,
): Thunk => async (dispatch, getState) => {
    const { step, roleId } = getState().add2FAPopup;
    if (step === 'SUBMIT_PHONE') {
        return dispatch(onSubmitPhone(values));
    } else if (step === 'SUBMIT_CODE') {
        return dispatch(onSubmitCode(values));
    } else if (step === 'SUCCESS') {
        await dispatch(headerActionCreators.onChangeRole(roleId, ''));
        redirect(`/app/settings/companies/${roleId}`);
        return dispatch(onClose2FAPopup());
    }

    return null;
};

export const onResendCode = async (values: Record<string, string>) => {
    const { phone, password } = values;
    try {
        await verifyPhone({ phone, password });
    } catch (err) {
        // Empty
    }
};

export const onChangePhone = (): Thunk => async (dispatch) => {
    dispatch(set2FAPopupStep('SUBMIT_PHONE'));
};

type ActionTypes = ReturnType<
    typeof onOpen2FAPopup | typeof onClose2FAPopup | typeof set2FAPopupStep
>;

const add2FAPopupReducer = (state = initialState, action: ActionTypes) => {
    switch (action.type) {
        case ADD_2FA_POPUP__SHOW:
            return {
                ...state,
                isActive: true,
                roleId: action.roleId,
            };
        case ADD_2FA_POPUP__CLOSE:
            return {
                ...initialState,
                isActive: false,
            };
        case ADD_2FA_POPUP__SET_STEP:
            return {
                ...state,
                step: action.step,
            };
        default:
            return state;
    }
};

export default add2FAPopupReducer;
