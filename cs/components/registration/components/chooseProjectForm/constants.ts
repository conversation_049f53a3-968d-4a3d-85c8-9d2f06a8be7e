import { t } from 'ttag';

import { LandingProjectSource } from '../../../../services/enums';
import { ProjectsOption } from './types';

import css from '../../../../hooks/authBranding/button.css';

export const PROJECTS_OPTIONS: ProjectsOption[] = [
    {
        value: 'vchasno',
        logo: `${config.STATIC_HOST}/images/registration/vchasno-logo.svg`,
        logoLarge: `${config.STATIC_HOST}/images/registration/vchasno-logo-large.svg`,
        picture: `${config.STATIC_HOST}/images/registration/vchasno-image.svg`,
        alt: t`Вчасно`,
        label: t`Миттєвий обмін документами онлайн`,
        redirect: '/app',
        source: LandingProjectSource.MAIN,
        themeClassName: '',
    },
    {
        value: 'edi',
        logo: `${config.STATIC_HOST}/images/registration/edi-logo.svg`,
        logoLarge: `${config.STATIC_HOST}/images/registration/edi-logo-large.svg`,
        picture: `${config.STATIC_HOST}/images/registration/edi-image.svg`,
        alt: t`Вчасно.EDI`,
        label: t`Обмін EDI-документами для постачальників та торгових мереж`,
        redirect: config.EDI_HOST,
        source: LandingProjectSource.EDI,
        themeClassName: css.edi,
    },
    {
        value: 'kasa',
        logo: `${config.STATIC_HOST}/images/registration/kassa-logo.svg`,
        logoLarge: `${config.STATIC_HOST}/images/registration/kassa-logo-large.svg`,
        picture: `${config.STATIC_HOST}/images/registration/kassa-image.svg`,
        alt: t`Вчасно.Каса`,
        label: t`Реєстрація РРО, фіскалізація та зберігання чеків`,
        redirect: `${config.KASSA_HOST}/api/internal/auth`,
        source: LandingProjectSource.KASA,
        themeClassName: css.kasa,
    },
    {
        value: 'kep',
        logo: `${config.STATIC_HOST}/images/registration/kep-logo.svg`,
        logoLarge: `${config.STATIC_HOST}/images/registration/kep-logo-large.svg`,
        picture: `${config.STATIC_HOST}/images/registration/kep-image.svg`,
        alt: t`Вчасно.КЕП`,
        label: t`Видача, зберігання та управління електронними ключами`,
        redirect: `${config.KEP_HOST}/api/internal/auth`,
        source: LandingProjectSource.KEP,
        themeClassName: css.kep,
    },
    {
        value: 'ttn',
        logo: `${config.STATIC_HOST}/images/registration/ettn-logo.svg`,
        logoLarge: `${config.STATIC_HOST}/images/registration/ettn-logo.svg`,
        picture: `${config.STATIC_HOST}/images/registration/edi-image.svg`,
        alt: t`Вчасно.ТТН`,
        label: t`Створення, обмін електронними товарно-транспортними накладними`,
        redirect: `${config.TTN_HOST}/api/internal/auth`,
        source: LandingProjectSource.TTN,
        themeClassName: css.ttn,
    },
];

export const SOURCE_LABELS = {
    main: 'Vchasno',
    edi: 'Vchasno.EDI',
    kasa: 'Vchasno.KASA',
    kep: 'Vchasno.KEP',
    ttn: 'Vchasno.TTN',
};

export const GTMEventsMap = {
    [LandingProjectSource.MAIN]: 'choose_project_edo',
    [LandingProjectSource.EDI]: 'choose_project_edi',
    [LandingProjectSource.KASA]: 'choose_project_kasa',
    [LandingProjectSource.KEP]: 'choose_project_cap',
    [LandingProjectSource.TTN]: 'choose_project_ttn',
};
