import React, { FC, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import PartnerLogos from 'components/AuthLayout/PartnerLogos';
import FlexBox from 'components/FlexBox/FlexBox';
import appActionCreators from 'components/app/appActionCreators';
import { loadAuthFormStorageItem } from 'components/auth/utils';
import PageTitle from 'components/pageTitle/pageTitle';
import { LS_KEY_IS_TTN_MOBILE_APP } from 'components/registration/constants';
import { redirect } from 'lib/navigation';
import { getLocalStorageItem } from 'lib/webStorage';
import { getCurrentUser } from 'selectors/app.selectors';
import auth from 'services/auth';
import { c, t } from 'ttag';
import BackButton from 'ui/BackButton/BackButton';
import PseudoLink from 'ui/pseudolink/pseudolink';

import RegistrationChangeEmail from './components/RegistrationChangeEmail';
import ResendEmailLink from './components/ResendEmailLink';

import css from './EmailConfirmation.css';

const EmailConfirmation: FC = () => {
    const [isEditMode, setIsEditMode] = useState<boolean>(false);
    const [isEmailSent, setIsEmailSent] = useState<boolean>(false);

    const initialFormData = loadAuthFormStorageItem();

    const dispatch = useDispatch();
    const currentUser = useSelector(getCurrentUser);

    useEffect(() => {
        dispatch(appActionCreators.onMount());
    }, []);

    useEffect(() => {
        if (currentUser?.emailConfirmed) {
            const isTTNMobileWebViewApp = getLocalStorageItem(
                LS_KEY_IS_TTN_MOBILE_APP,
            );
            if (isTTNMobileWebViewApp) {
                // INFO: for TTN mobile webview app, we need to redirect to TTN host
                redirect(config.TTN_HOST);
            } else {
                redirect('/app');
            }
        }
    }, [currentUser]);

    useEffect(() => {
        if (!initialFormData?.login) {
            auth.logout({ redirectUrl: '/auth' });
        }
    }, [initialFormData]);

    const onToggleEditMode = () => setIsEditMode((prevState) => !prevState);

    const boldEmailString = (
        <span className={css.email} key={initialFormData?.login}>
            {initialFormData?.login}
        </span>
    );

    const handleBackBtnClick = async () => {
        await auth.logout({ redirectUrl: '/auth' });
    };

    return (
        <>
            <PageTitle>{t`Підтвердіть ваш Email`}</PageTitle>
            <FlexBox className={css.container} justify="center" gap={0}>
                <div className={css.content}>
                    {isEditMode ? (
                        <RegistrationChangeEmail
                            onBack={onToggleEditMode}
                            onSubmitCallback={() => {
                                onToggleEditMode();
                                setIsEmailSent(true);
                            }}
                        />
                    ) : (
                        <FlexBox direction="column" gap={30}>
                            <PartnerLogos className={css.partnersLogos} />
                            <BackButton
                                className={css.iconContainer}
                                onClick={handleBackBtnClick}
                            />
                            <FlexBox direction="column" gap={20}>
                                <h1 className={css.title}>
                                    {t`Підтвердіть ваш Email`}
                                </h1>
                                <h4 className={css.description}>
                                    {c('new email')
                                        .jt`На ваш email ${boldEmailString} було надіслано посилання для підтвердження вашої поштової скриньки. Перейдіть по ньому та продовжуйте реєстрацію`}
                                </h4>
                            </FlexBox>
                            <FlexBox direction="column" gap={16}>
                                <ResendEmailLink
                                    isEmailSent={isEmailSent}
                                    setIsEmailSent={setIsEmailSent}
                                />
                                <div className={css.setNewEmailLink}>
                                    {t`Не маєте доступу до цього email?`}{' '}
                                    <PseudoLink onClick={onToggleEditMode}>
                                        {t`Вкажіть нову електронну адресу`}
                                    </PseudoLink>
                                </div>
                            </FlexBox>
                        </FlexBox>
                    )}
                </div>
            </FlexBox>
        </>
    );
};

export default EmailConfirmation;
