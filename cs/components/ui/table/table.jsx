import React from 'react';
import MediaQuery from 'react-responsive';
import { Link } from 'react-router-dom';

import cn from 'classnames';
import { MEDIA_WIDTH } from 'lib/constants';
import PropTypes from 'prop-types';
import uuid from 'services/uuid';
import Checkbox from 'ui/checkbox/checkbox';

import css from './table.css';

const Table = (props) => {
    const onSelectedChange = (item) => {
        if (item.selected) {
            props.onDeselect(item.id);
        } else {
            props.onSelect(item.id);
        }
    };

    const onSelectedChangeAll = () => {
        if (props.isSelectedAll) {
            props.onDeselectAll();
        } else {
            props.onSelectAll();
        }
    };

    const onRowClick = (id) => {
        if (typeof props.onRowClick === 'function') {
            props.onRowClick(id);
        }
    };

    return (
        <div ref={props.refFn}>
            {(props.headerItems || props.headerBlock) && (
                <div className={css.root}>
                    <div
                        className={cn(css.head, {
                            [css.headFixed]: props.isHeaderFixed,
                        })}
                    >
                        <div className={css.headerContent}>
                            {props.isSelectable && (
                                <div className={css.checkbox}>
                                    <Checkbox
                                        partialChecked={
                                            props.selectedCount > 0 &&
                                            !props.isSelectedAll
                                        }
                                        onChange={onSelectedChangeAll}
                                        checked={props.isSelectedAll}
                                    />
                                </div>
                            )}
                            {props.headerItems && (
                                <div className={css.titles}>
                                    {props.headerItems.map((title, index) => (
                                        <div
                                            className={css.title}
                                            title={
                                                typeof title === 'string'
                                                    ? title
                                                    : ''
                                            }
                                            key={uuid()}
                                            style={{
                                                width: props.sizes
                                                    ? props.sizes[index]
                                                    : '',
                                            }}
                                        >
                                            {title}
                                        </div>
                                    ))}
                                </div>
                            )}
                            {props.headerBlock && (
                                <div className={css.tools}>
                                    {props.headerBlock}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
            {props.selectedCount > 0 && (
                <div className={css.counter}>
                    Обрано документів: <b>{props.selectedCount}</b>
                </div>
            )}
            {!!props.data?.length && (
                <ul
                    className={cn(css.list, {
                        [css.fullWidth]: props.isFullWidth,
                    })}
                >
                    {props.data.map((row) => {
                        const renderRow = Object.keys(row.items).map(
                            (key, keyIndex) => {
                                if (
                                    props.isLastCellAppearedOnHover &&
                                    keyIndex ===
                                        Object.keys(row.items).length - 1
                                ) {
                                    return (
                                        <MediaQuery
                                            minWidth={MEDIA_WIDTH.tablet + 1}
                                            key={uuid()}
                                        >
                                            <div
                                                className={css.cell}
                                                title={
                                                    typeof row.items[key] ===
                                                    'string'
                                                        ? row.items[key]
                                                        : ''
                                                }
                                                style={{
                                                    width: props.sizes
                                                        ? props.sizes[keyIndex]
                                                        : '',
                                                }}
                                            >
                                                {row.items[key] || ''}
                                            </div>
                                        </MediaQuery>
                                    );
                                }
                                return (
                                    <div
                                        className={css.cell}
                                        key={uuid()}
                                        title={
                                            typeof row.items[key] === 'string'
                                                ? row.items[key]
                                                : ''
                                        }
                                        style={{
                                            width: props.sizes
                                                ? props.sizes[keyIndex]
                                                : '',
                                        }}
                                        data-qa={`qa_contact_${key}`}
                                    >
                                        {row.items[key] || ''}
                                    </div>
                                );
                            },
                        );
                        return (
                            <li
                                key={uuid()}
                                className={cn(css.item, {
                                    [css.itemClickable]:
                                        typeof props.onRowClick === 'function',
                                    [css.itemWithHiddenChild]:
                                        props.isLastCellAppearedOnHover,
                                    [css.itemAlignTop]: props.align === 'top',
                                    [css.fullWidth]: props.isFullWidth,
                                })}
                                onClick={() => onRowClick(row.id)}
                            >
                                {props.isSelectable && (
                                    <div className={css.checkbox}>
                                        <Checkbox
                                            onChange={() =>
                                                onSelectedChange(row)
                                            }
                                            checked={row.selected}
                                        />
                                    </div>
                                )}
                                {row.link ? (
                                    <Link
                                        to={(location) => ({
                                            ...location,
                                            pathname: row.link,
                                        })}
                                        className={cn(css.row, {
                                            [css.fullWidth]: props.isFullWidth,
                                        })}
                                    >
                                        {renderRow}
                                    </Link>
                                ) : (
                                    <div
                                        className={cn(css.row, {
                                            [css.fullWidth]: props.isFullWidth,
                                        })}
                                    >
                                        {renderRow}
                                    </div>
                                )}
                            </li>
                        );
                    })}
                </ul>
            )}
            {!props.data?.length && props.EmptyList}
        </div>
    );
};

Table.propTypes = {
    isSelectedAll: PropTypes.bool,
    isSelectable: PropTypes.bool,
    isHeaderFixed: PropTypes.bool,
    isLastCellAppearedOnHover: PropTypes.bool,
    isFullWidth: PropTypes.bool,
    headerBlock: PropTypes.element,
    data: PropTypes.array.isRequired,
    headerItems: PropTypes.array,
    sizes: PropTypes.array,
    selectedCount: PropTypes.number,
    align: PropTypes.string,
    refFn: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),
    onSelect: PropTypes.func,
    onSelectAll: PropTypes.func,
    onDeselect: PropTypes.func,
    onDeselectAll: PropTypes.func,
    onRowClick: PropTypes.func,
    EmptyList: PropTypes.element,
};

export default Table;
