.root {
    width: 20px;
    height: 20px;
    color: var(--dark-pigeon-color);
    cursor: pointer;
    transition: color 0.3s ease;
}

.root:hover {
    color: var(--pigeon-color);
}

.rootSmall {
    width: 10px;
    height: 10px;
    padding: 5px;
}

.rootDisabled {
    cursor: not-allowed;
    filter: brightness(0) saturate(100%) invert(94%) sepia(21%) saturate(281%)
        hue-rotate(173deg) brightness(94%) contrast(94%);
    opacity: 0.5;
}

:global(.vchasno-dark-theme) .root,
.rootDisabled {
    color: var(--slate-grey-color);
}

:global(.vchasno-dark-theme) .root:hover {
    color: var(--white-color);
}
