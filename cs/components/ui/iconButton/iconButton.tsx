import React from 'react';

import cn from 'classnames';

import Icon, { Glyph } from '../icon/icon';

// styles
import css from './iconButton.css';

interface IconButtonProps {
    disabled?: boolean;
    onClick?: (evt: React.MouseEvent<HTMLDivElement>) => void;
    title?: string;
    svg: Glyph;
    dataQa?: string;
    sizeSmall?: boolean;
    className?: string;
}

const IconButton = React.forwardRef<HTMLDivElement, IconButtonProps>(
    (props, ref) => {
        const handleClick: IconButtonProps['onClick'] = (evt) => {
            if (!props.disabled && typeof props.onClick === 'function') {
                props.onClick(evt);
            }
        };
        const totalClasses = cn(css.root, props.className, {
            [css.rootDisabled]: props.disabled,
            [css.rootSmall]: props.sizeSmall,
        });

        return (
            <div
                ref={ref}
                className={totalClasses}
                onClick={handleClick}
                title={props.title}
                data-qa={`qa_icon_${props.svg.id}`}
            >
                <Icon glyph={props.svg} />
            </div>
        );
    },
);

export default IconButton;
