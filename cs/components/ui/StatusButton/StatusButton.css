.root {
    height: 40px;
    box-sizing: border-box;
    padding: 0 20px;
    border: 1px solid var(--default-border);
    border-radius: var(--border-radius);
    color: var(--green-color);
    font-size: 13px;
    font-weight: bold;
    text-align: center;
    white-space: nowrap;
}

.root::before {
    display: inline-block;
    height: 100%;
    content: '';
    vertical-align: middle;
}

.root.rootColored {
    border-color: var(--green-color);
}

button.root {
    width: 100%;
    cursor: pointer;
}

.rootSmall {
    height: 30px;
}

.rootError {
    color: var(--red-color);
}

.rootError.rootColored {
    border-color: var(--red-color);
}

.icon {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 3px;
    vertical-align: middle;
}

.text {
    display: inline-block;
    vertical-align: middle;
}
