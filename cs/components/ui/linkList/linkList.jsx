import React from 'react';
import { Link } from 'react-router-dom';

import cn from 'classnames';
import PropTypes from 'prop-types';

import uuid from '../../../services/uuid';

import css from './linkList.css';

function LinkList({ sizeSmall, items, getItemContent, containerClassName }) {
    const getItemContentByDefault = (item) => {
        return (
            <div className={css.table} data-qa="qa_companies_lists">
                {item.icon && <div className={css.icon}>{item.icon}</div>}
                {item.data &&
                    item.data.map((text, idx) => (
                        <div
                            key={uuid()}
                            className={sizeSmall ? css.cellSmall : css.cell}
                            title={text}
                        >
                            {idx === 0 ? (
                                <span className={css.mainItem}>{text}</span>
                            ) : (
                                text
                            )}
                        </div>
                    ))}
                {item.additionalData &&
                    item.additionalData.map((text) => (
                        <div
                            key={uuid()}
                            className={
                                sizeSmall ? css.cellRightSmall : css.cellRight
                            }
                            title={typeof text === 'string' ? text : null}
                        >
                            {text}
                        </div>
                    ))}
            </div>
        );
    };

    const getItemContentHandler = getItemContent || getItemContentByDefault;

    return (
        <ul className={cn(css.root, containerClassName)}>
            {items.map((item) => (
                <li key={uuid()} className={css.item}>
                    {item.url ? (
                        <Link
                            className={css.link}
                            to={(location) => ({
                                ...location,
                                pathname: item.url,
                                search: '',
                            })}
                        >
                            {getItemContentHandler(item)}
                        </Link>
                    ) : (
                        <div className={css.disabled}>
                            {getItemContentHandler(item)}
                        </div>
                    )}
                </li>
            ))}
        </ul>
    );
}

LinkList.propTypes = {
    sizeSmall: PropTypes.bool,
    items: PropTypes.arrayOf(
        PropTypes.shape({
            data: PropTypes.oneOfType([PropTypes.object, PropTypes.array])
                .isRequired,
            additionalData: PropTypes.array,
            icon: PropTypes.element,
            url: PropTypes.string,
        }),
    ).isRequired,
    getItemContent: PropTypes.func,
    containerClassName: PropTypes.string,
};

export default LinkList;
