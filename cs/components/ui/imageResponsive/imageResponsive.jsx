import React from 'react';

import PropTypes from 'prop-types';

const ImageResponsive = (props) => {
    const { imageName, alt } = props;
    return (
        <img
            srcSet={`
                ${config.STATIC_HOST}/images/${imageName}_1x.png 1x,
                ${config.STATIC_HOST}/images/${imageName}_2x.png 2x,
                ${config.STATIC_HOST}/images/${imageName}_3x.png 3x,
            `}
            src={`${config.STATIC_HOST}/images/${imageName}_1x.png`}
            alt={alt}
        />
    );
};

ImageResponsive.propTypes = {
    imageName: PropTypes.string.isRequired,
    alt: PropTypes.string,
};

export default ImageResponsive;
