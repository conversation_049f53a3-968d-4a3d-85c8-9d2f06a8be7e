import React from 'react';

import { Spinner } from '@vchasno/ui-kit';

import { QRCodeSVG } from 'qrcode.react';
import { t } from 'ttag';

import css from './QRCode.css';

interface QRCodeProps {
    value: string | null | undefined;
    isLoading?: boolean;
    isError?: boolean;
    isStale?: boolean;
    size?: number;
    bgColor?: string | undefined;
    fgColor?: string | undefined;
    gap?: number;
    onClick?: () => void;
    onRetry?: () => void;
}

const QRCode: React.FC<QRCodeProps> = ({
    value,
    isLoading = false,
    isError = false,
    isStale = false,
    size = 144,
    bgColor,
    fgColor,
    gap = 4,
    onClick,
    onRetry,
}) => {
    const qrSize = size - gap * 2;
    const containerStyle = { width: size, height: size };

    if (isError) {
        return (
            <button
                className={css.errorContainer}
                style={containerStyle}
                onClick={onRetry}
            >
                <span className={css.errorText}>
                    {t`Упс, щось пішло не так...`}
                </span>
                <span className={css.retryButton}>{t`Спробувати ще раз`}</span>
            </button>
        );
    }

    if (isLoading || !value) {
        return (
            <div className={css.container} style={containerStyle}>
                <Spinner color="#ffb200" />
                <div className={css.loadingText}>{t`Генеруємо QR-код...`}</div>
            </div>
        );
    }

    return (
        <div className={css.qrContainer} style={{ padding: `${gap}px` }}>
            <QRCodeSVG
                onClick={onClick}
                value={value}
                size={qrSize}
                bgColor={bgColor}
                fgColor={fgColor}
            />

            {isStale && (
                <button className={css.staleOverlay} onClick={onRetry}>
                    <span className={css.retryButton}>
                        {t`Отримати новий QR-код`}
                    </span>
                </button>
            )}
        </div>
    );
};

export default QRCode;
