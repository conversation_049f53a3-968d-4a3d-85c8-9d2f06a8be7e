import React from 'react';

import cn from 'classnames';

import css from './icon.css';

export interface Glyph {
    id: string;
    viewBox: string;
}

export interface IconProps {
    glyph: Glyph;
    isInline?: boolean;
    className?: string;
    style?: React.CSSProperties;
}

const Icon = React.forwardRef<SVGSVGElement, IconProps>(
    ({ glyph: { id, viewBox }, isInline, className, style }, ref) => {
        return (
            <svg
                ref={ref}
                className={cn(
                    css.root,
                    {
                        [css.rootInline]: isInline,
                    },
                    className,
                )}
                style={style}
                viewBox={viewBox}
            >
                <use xlinkHref={`#${id}`} />
            </svg>
        );
    },
);

export default Icon;
