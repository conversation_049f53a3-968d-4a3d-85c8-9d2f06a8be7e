import React from 'react';

import PropTypes from 'prop-types';
import { t } from 'ttag';

import Button from '../../ui/button/button';
import Card from '../../ui/card/card';
import Dropdown from '../../ui/dropdown/dropdown';
import Input from '../../ui/input/input';
import Message from '../../ui/message/message';

import css from './companyCancellationCard.css';

const CompanyCancellationCard = (props) => (
    <Card title="Компанії відмовники">
        <div className={css.help}>
            {t`Додати компанію у список відмовників, ця компанія не буде більше
            отримувати будь-які листи.`}
        </div>
        <div className={css.input}>
            <Input
                id="edrpou"
                type="number"
                maxLength="10"
                value={props.refuseEdrpou}
                placeholder={t`Вкажіть ЄДРПОУ компанії`}
                onChange={props.onRefuseEdrpouChange}
            />
        </div>
        <div className={css.input}>
            <Dropdown
                options={[
                    {
                        value: 'legal',
                        label: t`юридична`,
                    },
                    {
                        value: 'natural',
                        label: t`фізична`,
                    },
                ]}
                value={props.refuseCompanyType}
                onChange={props.onRefuseCompanyTypeChange}
            />
        </div>
        <div className={css.block}>
            <div className={css.button}>
                <Button
                    isLoading={props.inviteNewUsersInProcess}
                    theme="cta"
                    onClick={props.onRefuse}
                >
                    {t`Додати до відмовників`}
                </Button>
            </div>
            {props.refuseSuccessMessage && (
                <div className={css.block}>
                    <Message sizeSmall type="success">
                        {props.refuseSuccessMessage}
                    </Message>
                </div>
            )}
            {props.refuseErrorMessage && (
                <div className={css.block}>
                    <Message sizeSmall type="error">
                        {props.refuseErrorMessage}
                    </Message>
                </div>
            )}
        </div>
    </Card>
);

CompanyCancellationCard.propTypes = {
    inviteNewUsersInProcess: PropTypes.bool,
    refuseCompanyType: PropTypes.string,
    refuseEdrpou: PropTypes.string,
    refuseErrorMessage: PropTypes.string,
    refuseSuccessMessage: PropTypes.string,
    onRefuse: PropTypes.func.isRequired,
    onRefuseCompanyTypeChange: PropTypes.func.isRequired,
    onRefuseEdrpouChange: PropTypes.func.isRequired,
};

export default CompanyCancellationCard;
