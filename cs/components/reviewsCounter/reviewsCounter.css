.root,
.counter,
.item {
    display: flex;
    max-width: 300px;
}

.baseItem,
.counter,
.item {
    display: flex;
    max-width: 275px;
    align-items: center;
}

.baseItem + .baseItem {
    margin-left: 16px;
}

.notation {
    display: flex;
    margin-top: 10px;
}

.icon,
.pending,
.approved,
.rejected {
    width: 10px;
    height: 10px;
    flex: 10px 0 0;
    margin-right: 6px;
}

.pending {
    color: var(--pigeon-color);
}

.approved {
    color: var(--green-color);
}

.rejected {
    color: var(--red-color);
}

.item {
    padding-top: 15px;
}
