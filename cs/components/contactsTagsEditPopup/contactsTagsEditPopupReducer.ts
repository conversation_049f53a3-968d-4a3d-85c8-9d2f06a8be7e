import { AnyAction } from 'redux';

import actions from './contactsTagsEditPopupActions';

const initState = {
    isActive: false,
    isLoading: false,
    isContactsSuggestionsShown: false,
    isSuggestionsShown: false,
    isDocumentPage: false,
    errorMessage: '',
    accessValue: '',
    title: '',
    allTags: [],
    docIds: [],
    newTags: [],
    selectedTags: [],
    suggestedTags: [],
    tagsToDelete: [],
    suggestionContacts: [],
    tags: [],
    contactsList: [],
};

const tagsAccessPopupReducer = (state = initState, action: AnyAction) => {
    switch (action.type) {
        case actions.CONTACTS_TAGS_EDIT_POPUP__OPEN:
            return {
                ...state,
                isActive: true,
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__START_SUBMIT:
            return {
                ...state,
                isLoading: true,
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__SHOW_ERROR_MESSAGE:
            return {
                ...state,
                errorMessage: action.errorMessage,
                isLoading: false,
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__SHOW_SUGGESTIONS_TAGS:
            return {
                ...state,
                isSuggestionsShown: true,
                suggestedTags: action.suggestedTags,
                allTags: action.allTags || state.allTags,
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__CLOSE_SUGGESTIONS_TAGS:
            return {
                ...state,
                isSuggestionsShown: false,
                suggestedTags: [],
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__CHANGE_SELECTED_LIST:
            return {
                ...state,
                selectedTags: action.selectedTags,
                errorMessage: '',
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__CHANGE_NEW_TAGS:
            return {
                ...state,
                newTags: action.newTags,
                errorMessage: '',
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__CHANGE_TAGS_TO_DELETE:
            return {
                ...state,
                tagsToDelete: action.tagsToDelete,
                selectedTags: action.selectedTags,
                tags: action.tags,
                errorMessage: '',
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__CLOSE:
            return {
                ...initState,
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__CHANGE_CONTACTS_VALUE:
            return {
                ...state,
                accessValue: action.accessValue,
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__CHANGE_SUGGESTION_CONTACTS:
            return {
                ...state,
                suggestionContacts: action.suggestionContacts,
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__CHANGE_CONTACTS_LIST:
            return {
                ...state,
                contactsList: action.contactsList,
                isContactsSuggestionsShown: false,
                accessValue: '',
                errorMessage: '',
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__CLOSE_CONTACTS_SUGGESTIONS:
            return {
                ...state,
                isContactsSuggestionsShown: false,
                suggestionContacts:
                    action.suggestionContacts || state.suggestionContacts,
            };
        case actions.CONTACTS_TAGS_EDIT_POPUP__SHOW_SUGGESTIONS_CONTACTS:
            return {
                ...state,
                isContactsSuggestionsShown: true,
                suggestionContacts:
                    action.suggestionContacts || state.suggestionContacts,
            };
        default:
            return state;
    }
};

export default tagsAccessPopupReducer;
