.container {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 20px;
}

.content {
    display: flex;
    width: 100%;
    max-width: 400px;
    flex-direction: column;
    gap: 32px;
}

.backBtn {
    position: absolute;
    top: 20px;
    left: 20px;
    background-color: var(--grey-bg);
}

.backBtn svg {
    color: var(--slate-grey-color);
}

.partnersLogos {
    display: none;
}

.header h1 {
    color: var(--content-color);
    font-size: 32px;
    font-weight: 700;
    line-height: normal;
}

.header h4 {
    color: var(--slate-grey-color);
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
}

.authMethods {
    width: 100%;
}

.horizontalLineSeparator {
    width: 100%;
    margin: 10px 0;
}

.horizontalLineSeparator span {
    color: var(--grey-color);
}

.horizontalLineSeparator::before,
.horizontalLineSeparator::after {
    background-color: var(--default-border);
}

.submitBtn {
    width: 180px;
    height: 50px;
    padding: 12px 30px;
    border-radius: var(--border-radius);
}

@media all and (max-width: 768px) {
    .container {
        padding: 0;
    }

    .content {
        margin-top: 0;
        gap: 24px;
    }

    .header {
        position: relative;
    }

    .backBtn {
        top: 0;
        left: 0;
    }

    .partnersLogos {
        display: flex;
        height: 36vh;
        align-self: center;
        margin-top: 22px;
        object-fit: contain;
    }

    .header h1 {
        font-size: 24px;
        font-weight: 500;
    }

    .header h4 {
        font-size: 14px;
        font-weight: 400;
    }

    .submitBtn {
        width: 100%;
    }
}
