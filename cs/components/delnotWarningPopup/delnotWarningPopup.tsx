import React, { FC } from 'react';

import { t } from 'ttag';

import Button from '../ui/button/button';
import Icon from '../ui/icon/icon';
import Popup from '../ui/popup/popup';

import SvgDocumentWarning from './images/documentWarning.svg';

import css from './delnotWarningPopup.css';

interface Props {
    onAction: () => void;
    onClose: () => void;
}

const DelnotWarningPopup: FC<React.PropsWithChildren<Props>> = (props) => (
    <Popup active isCloseByButtonOnly onClose={props.onClose}>
        <div className={css.root}>
            <div className={css.icon}>
                <Icon glyph={SvgDocumentWarning} />
            </div>
            <div className={css.content}>
                <div className={css.title}>{t`Зверніть увагу`}</div>
                <div>
                    {t`Видаткова накладна для поставки в Розетку`}
                    <br />
                    {t`формується тільки в сервісі Вчасно EDI.`}
                </div>
                <div className={css.buttonsBlock}>
                    <div className={css.button}>
                        <a
                            className={css.link}
                            href={config.EDI_LANDING}
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            {t`Перейти на Вчасно EDI`}
                        </a>
                    </div>
                    <div className={css.button}>
                        <Button theme="cta" onClick={props.onAction}>
                            {t`Продовжити відправлення`}
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </Popup>
);

export default DelnotWarningPopup;
