.input {
    position: relative;
    display: flex;
    overflow: hidden;
    flex: 1;
    border: 1px solid var(--default-border);
    margin-right: 4px;
    background: var(--white-bg);
    border-radius: var(--border-radius);
    color: var(--content-color);
    cursor: pointer;
    font-size: 14px;
    line-height: 18px;
    outline: 2px solid transparent;
    transition: border 0.3s, outline 0.3s;
    user-select: none;
}

:global(.vchasno-autumn-theme) .input {
    --white-bg: var(--autumn-4-color);
    --orange-border: var(--autumn-2-color);
    --content-color: var(--autumn-2-color);
}

:global(.vchasno-spring-theme) .input {
    --white-bg: var(--spring-4-color);
    --orange-border: var(--spring-2-color);
    --content-color: var(--spring-2-color);
}

:global(.vchasno-dark-theme) .input {
    --white-bg: var(--dark-1-color);
}

.input:hover,
.input:focus-within {
    border-color: var(--orange-border);
    outline: 2px solid var(--light-orange-bg);
}

.input:hover .icon,
.input:focus-within .icon {
    color: var(--orange-border);
}

.iconArrow:hover {
    background-color: var(--hover-bg);
}

.icon,
.removeIcon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    padding: 9px 10px;
    color: #BFCEDE;
    transition: color 0.3s;
}

.removeIcon {
    position: absolute;
    right: 40px;
    padding-left: 60px;
    background: linear-gradient(
        270deg,
        #fff 52.3%,
        rgba(255, 255, 255, 0) 116.1%
    );
    float: right;
}

:global(.vchasno-dark-theme) .removeIcon {
    background: transparent;
}

:global(.vchasno-autumn-theme) .removeIcon {
    background: linear-gradient(
        270deg,
        var(--autumn-4-color) 52.3%,
        rgba(255, 255, 255, 0) 116.1%
    );
}

.removeIcon.hidden {
    display: none;
}

.searchOptions {
    display: flex;
    overflow: hidden;
    flex-grow: 1;
    align-items: center;
    padding: 4px 5px;
    margin-left: -10px;
}

.searchOptions.expanded {
    flex-wrap: wrap;
}

.searchOption {
    margin: 5px;
}

.emptySearchInput {
    display: flex;
    overflow: hidden;
    width: 100%;
    align-items: center;
    user-select: none;
    white-space: nowrap;
}

.emptySearchInputPlaceholder {
    width: 100%;
    color: var(--content-color);
    font-size: 14px;
}
