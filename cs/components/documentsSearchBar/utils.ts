import { Nullable } from '../../types/general';
import { DocumentParameter, SearchOptions } from './types';

type LocationQuery = Nullable<string | string[] | undefined>;

const convertLocationOption = (option: LocationQuery): string[] => {
    if (!option) {
        return [];
    }
    return Array.isArray(option) ? option : [option];
};

const splitParameter = (value: string): Nullable<DocumentParameter> => {
    const parts = value.split(/_/, 2);

    if (parts.length !== 2) {
        return null;
    }

    const isNotSet = parts[1] === '';

    return {
        fieldId: parts[0],
        value: isNotSet ? '' : parts[1],
        isNotSet,
    };
};

const convertLocationParameterOption = (
    option: LocationQuery,
): DocumentParameter[] => {
    return convertLocationOption(option)
        .map(splitParameter)
        .filter((item): item is DocumentParameter => item !== null);
};

export const getOptionsFromLocationQuery = (
    query: Record<string, LocationQuery>,
): SearchOptions => {
    return {
        search: convertLocationOption(query.q_search),
        titles: convertLocationOption(query.q_title),
        numbers: convertLocationOption(query.q_number),
        tags: convertLocationOption(query.q_tag),
        companyNames: convertLocationOption(query.q_company_name),
        companyEdrpous: convertLocationOption(query.q_company_edrpou),
        userEmails: convertLocationOption(query.q_user_email),
        parameters: convertLocationParameterOption(query.q_parameter),
    };
};

const convertParameter = (
    parameter: DocumentParameter,
): `${string}_${string}` =>
    `${parameter.fieldId}_${
        // якщо isNotSet тоді повертаємо пустий рядок
        parameter.isNotSet ? '' : parameter.value
    }` as const;

export const getLocationQueryFromOptions = (
    options: SearchOptions,
): Record<string, string[]> => {
    return {
        q_search: options.search,
        q_title: options.titles,
        q_number: options.numbers,
        q_tag: options.tags,
        q_company_name: options.companyNames,
        q_company_edrpou: options.companyEdrpous,
        q_user_email: options.userEmails,
        q_parameter: options.parameters.map(convertParameter),
    };
};
