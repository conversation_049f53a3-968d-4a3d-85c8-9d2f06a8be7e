import { useSelector } from 'react-redux';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { snackbarToast } from '@vchasno/ui-kit';

import { getSessionsListWithoutRemoved } from 'components/employee/EmployeeSessionsConfig/EmployeeSession/utils';
import { GET_EMPLOYEE_SESSIONS_LIST } from 'lib/queriesConstants';
import { getCurrentEmployeeRole } from 'selectors/employee.selectors';
import { logoutSession } from 'services/auth';
import { t } from 'ttag';

import { Session } from './types';

export const useDeleteSessionMutation = () => {
    const queryClient = useQueryClient();
    const { id: employeeRoleId } = useSelector(getCurrentEmployeeRole);

    return useMutation({
        mutationFn: (params: Parameters<typeof logoutSession>) =>
            logoutSession(...params),
        onSuccess: (_data, variables) => {
            snackbarToast.success(t`Сесію успішно завершено`);
            queryClient.setQueryData(
                [GET_EMPLOYEE_SESSIONS_LIST, employeeRoleId],
                (prevData: { user: { sessions: Session[] } }) => ({
                    ...prevData,
                    user: {
                        ...prevData.user,
                        sessions: getSessionsListWithoutRemoved(
                            prevData.user.sessions,
                            variables[0].sessionId,
                        ),
                    },
                }),
            );
        },
        onError: () => {
            snackbarToast.error(t`Сталася помилка при завершенні сесії`);
        },
    });
};
