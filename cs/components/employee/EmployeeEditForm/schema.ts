import { MAX_DOC_CATEGORY_TITLE_SYMBOLS } from 'components/InternalDocCategoriesConfig/InternalDocCategoryForm/constants';
import * as yup from 'yup';

import { EmployeeEditForm } from './types';

export const employeeProfileFromSchema: yup.ObjectSchema<EmployeeEditForm> = yup
    .object()
    .shape({
        position: yup
            .string()
            .default(null)
            .nullable()
            .optional()
            .max(MAX_DOC_CATEGORY_TITLE_SYMBOLS),
        lastName: yup.string().trim().default('').required().max(255),
        firstName: yup.string().trim().default('').required().max(255),
        secondName: yup.string().trim().default('').optional().max(255),
    })
    .required();
