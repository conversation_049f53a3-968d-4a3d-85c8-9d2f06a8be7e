import React from 'react';
import { connect } from 'react-redux';

import PropTypes from 'prop-types';

import appActionCreators from '../app/appActionCreators';
import fieldActionCreators from './additionalFieldsInputActionCreators';
import actionCreators from './employeeActionCreators';
import tagActionCreators from './tagsInputActionCreators';

import { Company, EmployeeRole } from '../../records/user';
import Employee from './employee';

function mapStateToProps(state) {
    return {
        isAdmin: state.app.currentUser.currentRole.isAdmin,
        company: state.app.currentUser.currentCompany,
        roleId: state.companyCard.roleId,
        ...state.employee,
    };
}

const mapDispatchToProps = {
    ...actionCreators,
    ...tagActionCreators,
    ...fieldActionCreators,
    onConfirmPopupShow: appActionCreators.onConfirmPopupShow,
};

class EmployeeContainer extends React.Component {
    static propTypes = {
        isAdmin: PropTypes.bool.isRequired,
        isLabelSavedShown: PropTypes.bool.isRequired,
        isSuggestionsShown: PropTypes.bool,
        company: PropTypes.instanceOf(Company).isRequired,
        currentEmployeeRole: PropTypes.instanceOf(EmployeeRole).isRequired,
        match: PropTypes.object.isRequired,
        errorMessage: PropTypes.string,
        roleId: PropTypes.string,
        currentUserId: PropTypes.string,
        newIps: PropTypes.string,
        ipErrorMessage: PropTypes.string,
        allTags: PropTypes.array,
        tags: PropTypes.array,
        suggestedTags: PropTypes.array,
        loadEmployeeRoleData: PropTypes.func,
        onNewIpsChange: PropTypes.func.isRequired,
        onAddIps: PropTypes.func.isRequired,
        onDeleteIp: PropTypes.func.isRequired,
        onCreateToken: PropTypes.func.isRequired,
        onDeleteToken: PropTypes.func.isRequired,
        tokenExpireDays: PropTypes.number,
        onChangeTokenExpireDays: PropTypes.func.isRequired,
        onAddNewTag: PropTypes.func.isRequired,
        onAutosuggestTags: PropTypes.func.isRequired,
        onCloseSuggestions: PropTypes.func.isRequired,
        onDeleteTagFromList: PropTypes.func.isRequired,
        onSuggestionClick: PropTypes.func.isRequired,
        editedSection: PropTypes.string,
    };

    componentDidMount() {
        const { employeeRoleId } = this.props.match.params;
        this.props.loadEmployeeRoleData(employeeRoleId);
    }

    render() {
        if (!this.props.currentEmployeeRole?.user) {
            return null;
        }
        return <Employee {...this.props} />;
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(EmployeeContainer);
