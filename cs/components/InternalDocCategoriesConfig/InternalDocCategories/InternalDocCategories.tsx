import React from 'react';

import { FlexBox, Pagination, Paragraph } from '@vchasno/ui-kit';

import { useInternalDocCategoriesContext } from 'components/InternalDocCategoriesConfig/context';
import SearchBar from 'components/searchBar/searchBar';
import { DocumentCategory } from 'gql-types';
import { t } from 'ttag';

import InternalDocCategory from './InternalDocCategory';

import css from './InternalDocCategories.css';

const InternalDocCategories: React.FC = () => {
    const {
        docCategories,
        page,
        onChangePage,
        totalPages,
        title,
        onChangeTitle,
        onClearTitle,
        isShowSearchBar,
    } = useInternalDocCategoriesContext();

    return (
        <FlexBox direction="column" gap={24}>
            {isShowSearchBar && (
                <div className={css.searchWrapper}>
                    <SearchBar
                        value={title}
                        placeholder={t`Вкажіть назву типу`}
                        onChange={onChangeTitle}
                        onClear={onClearTitle}
                    />
                </div>
            )}
            <FlexBox wrap="wrap" gap={12}>
                {!docCategories.length && (
                    <Paragraph>{t`Внутрішні типи не знайдено`}</Paragraph>
                )}
                {docCategories.map((category: DocumentCategory) => (
                    <InternalDocCategory
                        key={category.id}
                        category={category}
                    />
                ))}
            </FlexBox>
            <Pagination
                hideOnSinglePage
                scrollOnChange={false}
                current={page}
                onChange={onChangePage}
                total={totalPages}
            />
        </FlexBox>
    );
};

export default InternalDocCategories;
