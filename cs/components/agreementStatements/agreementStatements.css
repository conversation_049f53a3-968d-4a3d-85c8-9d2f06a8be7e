.root {
    margin-top: 30px;
}

.headline {
    font-size: 18px;
    font-weight: bold;
}

.list {
    counter-reset: item;
}

.item,
.itemAdditional {
    position: relative;
    padding: 20px 80px;
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
    counter-increment: item;
}

.item::before {
    position: absolute;
    top: 20px;
    left: 40px;
    color: var(--dark-pigeon-color);
    content: counter(item);
    font-size: 24px;
    font-weight: bold;
    line-height: 1;
}

.item + .item {
    margin-top: 10px;
}

@media screen and (max-width: 1200px) {
    .item {
        padding: 20px 34px 20px 62px;
        word-break: break-word;
    }

    .item::before {
        left: 26px;
    }
}

.itemAdditional {
    margin-bottom: 10px;
}

.itemAdditional::before {
    content: none;
}

.sublist {
    counter-reset: subitem;
}

.subitem {
    position: relative;
    padding-left: 50px;
    margin-top: 15px;
    counter-increment: subitem;
}

@media screen and (max-width: 480px) {
    .subitem {
        padding-left: 0;
    }
}

.subitem::before {
    position: absolute;
    left: 0;
    color: var(--dark-pigeon-color);
    content: counter(item) '.' counter(subitem);
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
    white-space: nowrap;
}

@media screen and (max-width: 480px) {
    .subitem::before {
        right: 100%;
        left: auto;
        margin-right: 15px;
    }
}

.sublistSecond {
    counter-reset: subitemSecond;
}

.sublistSecond .subitem {
    counter-increment: subitemSecond;
}

.sublistSecond .subitem::before {
    top: 2px;
    content: counter(item) '.' counter(subitem) '.' counter(subitemSecond);
    font-size: inherit;
}
