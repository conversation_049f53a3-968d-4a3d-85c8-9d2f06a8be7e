import React, { ChangeEvent, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import SearchSvg from 'icons/search.svg';
import { getCurrentCompanyPermissionMap } from 'selectors/app.selectors';
import { DocumentField } from 'services/documentFields/types';
import { t } from 'ttag';
import Autosuggest from 'ui/autosuggest/autosuggest';
import TextShorten from 'ui/textShorten/textShorten';

import { PermissionCategory } from '../../proRateInfoPopup/proRateInfoPopupTypes';
import { useUploadDocumentsFormContext } from '../useUploadDocumentsFormContext';
import useDocumentParametersQuery from './useDocumentParametersQuery';

interface SearchDocumentFieldProps {
    onSelect: (item: DocumentField) => void;
    className?: string;
}

const SearchDocumentFieldsAutosuggest: React.FC<SearchDocumentFieldProps> = ({
    onSelect,
    className,
}) => {
    const [isShow, setShow] = useState<boolean>(false);
    const [searchField, setSearchField] = useState<string>('');
    const companyPermissionMap = useSelector(getCurrentCompanyPermissionMap);
    const methods = useUploadDocumentsFormContext();
    const selectedDocumentParameters = methods.watch('documentParameters');

    const { data = [], isLoading } = useDocumentParametersQuery({
        enabled: isShow,
    });

    const isAvailableExtraFields =
        companyPermissionMap[PermissionCategory.ADDITIONAL_FIELDS];

    const suggestionFields = useMemo(
        () =>
            data
                .filter(
                    (suggestedField: DocumentField) =>
                        !selectedDocumentParameters?.some(
                            (selectedParameter) =>
                                selectedParameter.id === suggestedField.id,
                        ),
                )
                .filter((field) =>
                    field.name
                        .toLowerCase()
                        .includes(searchField.toLowerCase()),
                ),
        [data, selectedDocumentParameters, searchField],
    );

    const handleSelectSuggestion = (item: DocumentField) => {
        onSelect(item);
        setSearchField('');
        setShow(false);
    };

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        setSearchField(event.target.value);
        setShow(true);
    };

    const renderSuggestion = (field: DocumentField) => {
        return <TextShorten>{field.name}</TextShorten>;
    };

    return (
        <Autosuggest
            className={className}
            disabled={!isAvailableExtraFields || methods.formState.disabled}
            isLoading={isLoading}
            name="extra-fields"
            value={searchField}
            placeholder={t`Знайдіть необхідний параметр`}
            leadingIcon={SearchSvg}
            showSuggestions={isShow}
            suggestionsData={suggestionFields}
            renderSuggestion={renderSuggestion}
            onSuggestionClick={handleSelectSuggestion}
            onCloseSuggestion={() => setShow(false)}
            onFocus={() => setShow(true)}
            onChange={handleChange}
        />
    );
};

export default SearchDocumentFieldsAutosuggest;
