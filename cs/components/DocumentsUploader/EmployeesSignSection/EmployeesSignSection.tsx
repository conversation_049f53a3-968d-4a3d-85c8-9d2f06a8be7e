import React from 'react';
import { useFieldArray } from 'react-hook-form';

import { FlexBox, Title } from '@vchasno/ui-kit';

import { t } from 'ttag';
import Checkbox from 'ui/checkbox/checkbox';

import { TabComponent } from '../EmployeesSection/types';

import ActorAutoSuggest from '../ActorAutoSuggest';
import EmptyEmployeePlaceholder from '../EmptyEmployeePlaceholder';
import FormController from '../FormController';
import SelectedActorList from '../SelectedActorList';
import { useUploadDocumentsFormContext } from '../useUploadDocumentsFormContext';

import css from './EmployeesSignSection.css';

interface EmployeesSignSectionProps extends TabComponent {}

const EmployeesSignSection: React.FC<EmployeesSignSectionProps> = ({
    disabled,
    hideEmptyPlaceholder,
}) => {
    const methods = useUploadDocumentsFormContext();
    const actorSigners = useFieldArray({
        control: methods.control,
        name: 'employeesSigners',
        shouldUnregister: false,
    });
    const isOrdered = methods.watch('employeesSignIsOrdered');
    const employeesSigners = methods.watch('employeesSigners');

    return (
        <FlexBox direction="column" gap={15}>
            <Title
                className={css.actorSectionTitle}
                level={4}
            >{t`Хто має підписати?`}</Title>
            <ActorAutoSuggest
                onlyCanSignAndRejectDocument
                disabled={methods.formState.disabled || disabled}
                onSelect={(item) => {
                    actorSigners.append(item);
                    methods.trigger('counterparties');
                }}
                filterOptions={({ id }) =>
                    !employeesSigners.some((item) => item.id === id)
                }
            />

            <FormController
                name="employeesSignIsOrdered"
                render={({ field }) => (
                    <Checkbox
                        disabled={field.disabled || disabled}
                        className={css.employeesSignOrderedCheckbox}
                        checked={field.value}
                        onChange={field.onChange}
                        text={t`Налаштувати порядок підписання`}
                    />
                )}
            />

            <SelectedActorList
                disabled={disabled}
                isOrdered={isOrdered}
                actorFieldArray={actorSigners}
            />

            {employeesSigners.length === 0 && !hideEmptyPlaceholder && (
                <EmptyEmployeePlaceholder />
            )}
        </FlexBox>
    );
};

export default EmployeesSignSection;
