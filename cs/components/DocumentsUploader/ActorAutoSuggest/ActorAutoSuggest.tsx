import React, { useState } from 'react';

import AutosuggestionGroupItem from 'components/uploaderPopup/SignersReviewersAutosuggestionList/AutosuggestionItems/AutosuggestionGroupItem';
import AutosuggestionRoleItem from 'components/uploaderPopup/SignersReviewersAutosuggestionList/AutosuggestionItems/AutosuggestionRoleItem';
import { t } from 'ttag';
import Autosuggest from 'ui/autosuggest/autosuggest';

import { Actor } from '../types';

import { filterBySearch } from './utils';

import { useCompanyActorsList } from '../useCompanyActorsList';

import SearchSvg from '../../../icons/search.svg';

interface SearchCounterpartyAutoSuggestProps {
    onSelect: (item: Actor) => void;
    filterOptions?: (item: Actor) => boolean;
    placeholder?: string;
    disabled?: boolean;
    onlyCanSignAndRejectDocument?: boolean;
}

const ActorAutoSuggest: React.FC<SearchCounterpartyAutoSuggestProps> = ({
    onSelect,
    filterOptions = () => true,
    disabled,
    placeholder = t`Вкажіть email, імʼя співробітника або назву команди`,
    onlyCanSignAndRejectDocument,
}) => {
    const [search, setSearch] = useState('');
    const [isShow, setShow] = useState(false);

    const { list, isFetching } = useCompanyActorsList('', {
        onlyCanSignAndRejectDocument,
    });

    const renderSuggestion = (item: Actor) => {
        if (item.type === 'role') {
            return <AutosuggestionRoleItem suggestion={item.source} />;
        }

        return <AutosuggestionGroupItem suggestion={item.source} />;
    };

    const handleChange: React.ChangeEventHandler<HTMLInputElement> = (
        event,
    ) => {
        setSearch(event.target.value.trim());
        setShow(true);
    };

    const handleSelectSuggestion = (item: Actor) => {
        onSelect(item);
        setSearch('');
        setShow(false);
    };

    return (
        <Autosuggest
            disabled={disabled}
            leadingIcon={SearchSvg}
            isLoading={isFetching}
            showSuggestions={isShow}
            value={search}
            onFocus={() => setShow(true)}
            placeholder={placeholder}
            suggestionsData={list
                .filter(filterOptions)
                .filter(filterBySearch(search))}
            renderSuggestion={renderSuggestion}
            onSuggestionClick={handleSelectSuggestion}
            onCloseSuggestion={() => setShow(false)}
            onChange={handleChange}
        />
    );
};

export default ActorAutoSuggest;
