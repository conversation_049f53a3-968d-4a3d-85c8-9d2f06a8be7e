import React from 'react';
import { List } from 'react-movable';
import { useSelector } from 'react-redux';

import { FlexBox } from '@vchasno/ui-kit';

import { useCounterpartyContext } from 'components/DocumentsUploader/CounterpartySection/CounterpartyContext';
import { useToast } from 'hooks/useToast';
import { getCurrentCompany } from 'selectors/app.selectors';
import { t } from 'ttag';
import Alert from 'ui/Alert/Alert';

import { useUploadDocumentsFormContext } from '../useUploadDocumentsFormContext';
import CounterpartyListItem from './CounterpartyListItem';
import { useCounterpartyFieldArray } from './useCounerpartyFieldArray';

interface CounterpartyListProps {
    counterpartiesFieldArray: ReturnType<typeof useCounterpartyFieldArray>;
    disabled?: boolean;
}

const CounterpartyList: React.FC<CounterpartyListProps> = ({
    counterpartiesFieldArray,
    disabled,
}) => {
    const toast = useToast();
    const {
        disabledActions,
        suppressAutoRemoveOwner,
        doc,
    } = useCounterpartyContext();

    const methods = useUploadDocumentsFormContext();
    const currentCompany = useSelector(getCurrentCompany);
    const externalCounterparties = counterpartiesFieldArray.fields.filter(
        (item) => item.edrpou !== currentCompany.edrpou,
    );

    const internalItemCount =
        counterpartiesFieldArray.fields.length - externalCounterparties.length;

    if (!currentCompany) {
        return null;
    }

    const getDisabledActions = (index: number) =>
        disabledActions(counterpartiesFieldArray.fields[index]);

    return (
        <FlexBox direction="column">
            {externalCounterparties.length === 0 &&
                counterpartiesFieldArray.fields.length > 0 && (
                    <Alert>{t`Якщо не зазначити контрагентів, то такий документ буде збережено як внутрішній`}</Alert>
                )}
            <List
                lockVertically
                removableByMove
                onChange={({ oldIndex, newIndex }) => {
                    if (
                        getDisabledActions(oldIndex).order ||
                        getDisabledActions(newIndex).order
                    ) {
                        toast({
                            textType: 'info',
                            message: t`Неможливо перемістити контрагента`,
                            showCloseButton: true,
                            autoClose: 5_000,
                        });
                        return;
                    }
                    counterpartiesFieldArray.move(oldIndex, newIndex);
                    methods.trigger('counterparties');
                }}
                transitionDuration={300}
                renderList={({ children, props }) => (
                    <ul
                        {...props}
                        style={{
                            padding: 0,
                            margin: 0,
                            display: 'flex',
                            flexDirection: 'column',
                            marginBottom: -10,
                        }}
                    >
                        {children}
                    </ul>
                )}
                renderItem={({
                    isOutOfBounds,
                    value,
                    props,
                    isDragged,
                    index = 0,
                }) => {
                    if (!value) {
                        return null;
                    }
                    return (
                        <li
                            {...props}
                            key={value.id}
                            style={{
                                ...props.style,
                                listStyleType: 'none',
                                cursor: isDragged ? 'grabbing' : 'inherit',
                                marginBottom: 10,
                                zIndex: isDragged ? 1000 : 'inherit',
                                opacity: isOutOfBounds ? 0.5 : 'inherit',
                                borderRadius: 8,
                            }}
                        >
                            <CounterpartyListItem
                                disabled={disabled}
                                item={value}
                                index={index}
                                counterpartiesFieldArray={
                                    counterpartiesFieldArray
                                }
                                isSender={
                                    doc
                                        ? doc.edrpouOwner === value.edrpou
                                        : value.edrpou === currentCompany.edrpou
                                }
                                canClose={
                                    // показуємо кнопку видалення, для контрагентів
                                    value.edrpou !== currentCompany.edrpou ||
                                    // якщо в списку 2 і більше позицій власної компанії
                                    internalItemCount >= 2 ||
                                    // якщо є тільки своя компанія та вимкнуто авто видалення власника
                                    (internalItemCount === 1 &&
                                        externalCounterparties.length === 0 &&
                                        !!suppressAutoRemoveOwner)
                                }
                            />
                        </li>
                    );
                }}
                values={counterpartiesFieldArray.fields}
            />
        </FlexBox>
    );
};

export default CounterpartyList;
