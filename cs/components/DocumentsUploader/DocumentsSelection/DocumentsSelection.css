.root {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.dropBox {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 10px;
    border-radius: var(--border-radius);
    gap: 5px;
    inset: 70px 10px 10px 10px;
    transition: border 0.3s ease-in-out, background-color 0.3s ease-in-out;
}

@media (max-width: 768px) {
    .dropBox {
        display: none;
    }
}

.dropBoxHint {
    position: absolute;
    right: 0;
    bottom: 10px;
    left: 0;
    padding-top: 10px;
    pointer-events: none;
}

.dropBoxHintScrollable {
    bottom: 0;
    max-width: var(--document-uploader-container-width);
    box-sizing: border-box;
    padding-top: 25px;
    padding-bottom: 25px;
    margin: 0 auto;
    overflow-y: clip;
    padding-inline: var(--document-uploader-hor-padding);
}

.uploadIcon {
    position: relative;
    display: flex;
    width: 40px;
    height: 40px;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--default-border);
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
}

.uploadIcon svg {
    width: 20px;
    height: 20px;
    color: var(--content-color);
}

.dropBoxActive .uploadIcon svg {
    animation: dropActive 1s infinite;
    color: var(--link-color);
}

.fullWidthContainer {
    width: calc(100vw - 20px);
    box-sizing: border-box;
    margin-left: calc(
        (100vw - var(--document-uploader-container-width) + 10px) / 2 * -1
    );
}

@media (max-width: 768px) {
    .fullWidthContainer {
        width: 100%;
        margin-left: 0;
    }
}

.fileContainer {
    position: relative;
    display: flex;
    max-width: calc(var(--document-uploader-container-width) - 30px);
    flex-wrap: wrap;
    margin: 0 auto;
    gap: 10px;
}

.title {
    color: var(--content-color);
    font-size: 24px;
    font-weight: 500;
    line-height: 28px;
}

.linkText {
    color: var(--link-color);
    cursor: pointer;
    text-decoration: underline;
}

.subText {
    margin-bottom: 10px;
    color: var(--grey-color);
    font-size: 12px;
}

.textBg {
    background-color: var(--white-bg);
    border-radius: var(--border-radius);
}

.dropBoxActive {
    z-index: 3;
    background-color: rgba(175, 201, 224, 0.2);
}

.icon {
    width: 50px;
}

@keyframes dropActive {
    0% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-5px);
    }

    100% {
        transform: translateY(0);
    }
}
