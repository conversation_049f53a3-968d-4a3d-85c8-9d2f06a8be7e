import React from 'react';

import PropTypes from 'prop-types';

import { formatTotalPrice } from '../utils';

import SignLine from '../ui/signLine/signLine';

import { formatDate } from '../../../lib/date';
import { XmlDocument } from '../../../records/document';
import FooterRenderer from '../../footerRenderer/footerRenderer';
import { DATE_FORMAT_FULL, NUMBER_REGEXP } from '../zk/constants';
import { CompanyAgromat } from './components/agromatCompany/agromatCompany';
import ProductsTableAgromat from './components/agromatProductsTable/agromatProductsTable';
import { AgromatXmlData } from './parser';

import css from './deedRtu.css';

const DeedRtu = ({ data, doc, renderSignatures, renderReviews }) => {
    const { date, number, owner, partner, services } = data;
    const shortNumber = number.replace(NUMBER_REGEXP, '$3');
    const totalServicesSum = services.totalDocumentSum
        ? formatTotalPrice(services, 'totalDocumentSum', true)
        : formatTotalPrice(services, 'totalSum', true);

    return (
        <div>
            <h1 className={css.documentTitle}>
                Видаткова накладна № {shortNumber} від{' '}
                {formatDate(date, DATE_FORMAT_FULL)} р.
            </h1>

            <table className={css.headerTable}>
                <tbody>
                    <tr>
                        <td>Постачальник:</td>
                        <td>
                            <CompanyAgromat data={owner} />
                        </td>
                    </tr>
                    <tr>
                        <td>Покупець:</td>
                        <td>
                            <CompanyAgromat data={partner} />
                        </td>
                    </tr>
                    {data.agreementName && (
                        <tr>
                            <td>Договір:</td>
                            <td>{data.agreementName}</td>
                        </tr>
                    )}
                    <tr>
                        <td>Рах.-фактура:</td>
                        <td>{data.dealName}</td>
                    </tr>
                    <tr>
                        <td>Доставка:</td>
                        <td>{data.delivery}</td>
                    </tr>
                    <tr>
                        <td>Місце відпуску товару:</td>
                        <td>{data.deliveryPlace}</td>
                    </tr>
                </tbody>
            </table>

            <ProductsTableAgromat isFullNameDisplay data={services} />
            <div className={css.documentTrivia}>
                Всього найменувань {services.items.size}, на суму{' '}
                {totalServicesSum}
            </div>
            {services.totalSumStr && (
                <div className={css.documentTriviaBold}>
                    {services.totalSumStr}
                </div>
            )}
            {services.totalTaxesStr && (
                <div className={css.documentTriviaBold}>
                    В т.ч. ПДВ: {services.totalTaxesStr}
                </div>
            )}
            {data.agreementPlace && (
                <div className={css.documentTriviaMargin}>
                    Місце складання: {data.agreementPlace}
                </div>
            )}

            <p className={css.warning}>
                <sub>{data.warning}</sub>
            </p>
            <div className={css.documentTriviaBorder} />

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Від Постачальника*
                            <br />
                        </th>
                        <th>
                            Отримав*
                            <br />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <SignLine />
                        </td>
                        <td>
                            <SignLine />
                        </td>
                    </tr>
                    <tr className={css.representativeRow}>
                        <td>
                            <b>{owner.representative}</b>
                        </td>
                        <td>
                            <b>{partner.representative}</b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            * Відповідальний за здійснення господарської
                            операції і правильність її оформлення
                        </td>
                        <td>
                            За дорученням № {data.letterCode} від{' '}
                            {data.letterDate}
                            <br />* Товар отримав, претензій до кількості і
                            якості товару не маю.
                        </td>
                    </tr>
                </tbody>
            </table>
            {(renderSignatures || renderReviews) && (
                <div className={css.digitalSignaturesWrapper}>
                    <FooterRenderer
                        renderSignatures={renderSignatures}
                        renderReviews={renderReviews}
                        doc={doc}
                    />
                </div>
            )}
        </div>
    );
};

DeedRtu.propTypes = {
    data: PropTypes.instanceOf(AgromatXmlData).isRequired,
    doc: PropTypes.instanceOf(XmlDocument).isRequired,
    renderSignatures: PropTypes.bool,
    renderReviews: PropTypes.bool,
};

export default DeedRtu;
