.headerTable {
    width: 100%;
    font-size: 90%;
}

.headerTable td {
    padding-bottom: 1em;
    vertical-align: top;
}

.headerTable td:nth-child(1) {
    width: 3.5cm;
}

.documentTitle {
    padding-bottom: 0.05cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-bottom: 0.4cm;
    font-size: 110%;
    font-weight: bold;
}

.documentTrivia,
.documentTriviaBold,
.documentTriviaBorder,
.documentTriviaMargin {
    width: 85%;
    font-size: 85%;
    line-height: 110%;
}

.documentTriviaBold {
    font-weight: bold;
}

.documentTriviaBorder {
    width: 100%;
    padding-bottom: 0.2cm;
    border-bottom: 0.075cm solid var(--content-color);
    margin-top: 0.4cm;
}

.documentTriviaMargin {
    margin: 0.4cm 0;
}

.footerTable {
    width: 100%;
    margin-top: 0.4cm;
    font-size: 90%;
}

.footerTable thead th {
    width: 50%;
    font-weight: bold;
    text-align: left;
    vertical-align: top;
}

.footerTable tbody {
    font-size: 80%;
}

.footerTable tbody td {
    padding-right: 0.25cm;
    vertical-align: top;
}

@media (min-width: 720px) {
    .footerTable tbody td {
        padding-right: 1.25cm;
    }
}

.representativeRow {
    font-size: 120%;
}

.blankPlace {
    margin-left: 1.5cm;
}

.goodsTable {
    width: 100%;
    border: 0.075cm solid var(--content-color);
    margin-top: 0.1cm;
    margin-bottom: 0.1cm;
    border-collapse: collapse;
}

.goodsTable thead th {
    padding: 0.1cm 0;
    border: 0.05cm solid var(--content-color);
    background-color: #fcf9ea;
    font-size: 90%;
    font-weight: bold;
    text-align: center;
}

.goodsTable tbody td {
    padding: 0.1cm;
    border: 0.05cm solid var(--content-color);
    font-size: 75%;
    vertical-align: top;
}

.number {
    width: 5%;
}

.name {
    width: 45%;
}

.code,
.quantity,
.item,
.price,
.sum {
    width: 10%;
}

.totalTable {
    width: 100%;
    margin-top: 0.2cm;
    margin-bottom: 0.2cm;
}

.totalTable td {
    padding-left: 10px;
    font-size: 90%;
    font-weight: bold;
    text-align: right;
}

.totalName {
    width: 90%;
}

.textCenter {
    text-align: center;
}

.textRight {
    text-align: right;
}
