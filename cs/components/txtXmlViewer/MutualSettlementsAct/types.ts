import { Moment } from 'moment';

import {
    XmlData as XmlDataDefault,
    XmlDocument,
} from '../../../types/xmlDocuments';

export interface XmlCompanyProduct {
    date?: Date | Moment | string;
    number?: number | string;
    name?: string;
    debit?: number | string;
    credit?: number | string;
}

export interface XmlProduct {
    owner?: XmlCompanyProduct;
    partner?: XmlCompanyProduct;
}

export interface XmlProducts {
    items?: XmlProduct[];
    ownerInitialDebitBalance?: number | string;
    ownerInitialCreditBalance?: number | string;
    partnerInitialDebitBalance?: number | string;
    partnerInitialCreditBalance?: number | string;
    ownerTurnoverPeriodDebit?: number | string;
    ownerTurnoverPeriodCredit?: number | string;
    partnerTurnoverPeriodDebit?: number | string;
    partnerTurnoverPeriodCredit?: number | string;
    ownerFinalBalanceDebit?: number | string;
    ownerFinalBalanceCredit?: number | string;
    partnerFinalBalanceDebit?: number | string;
    partnerFinalBalanceCredit?: number | string;
    totalSum?: number | string;
    totalDocumentSum?: number | string;
    totalTaxes?: number | string;
}

export interface XmlData extends XmlDataDefault {
    servicesAct?: XmlProducts;
    startServicesProvisionDate?: Date | Moment | string;
    endServicesProvisionDate?: Date | Moment | string;
}

export interface Props {
    doc: XmlDocument;
    data: XmlData;
    notAcceptedQuantity?: boolean;
    quantityColumnName?: string;
}
