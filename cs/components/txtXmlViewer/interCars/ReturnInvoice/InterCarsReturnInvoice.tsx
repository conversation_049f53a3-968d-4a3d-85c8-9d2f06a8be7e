import React from 'react';

import { DATE_FORMAT } from 'lib/constants';
import { formatDate } from 'lib/date';
import { formatPrice, sanitizeAndFormatPrice } from 'lib/numbers';

import { XmlTemplateProps } from './types';

import css from './InterCarsReturnInvoice.css';

const InterCarsReturnInvoice: React.FC<XmlTemplateProps> = ({ data }) => {
    const {
        date,
        number,
        seller,
        buyer,
        parameters,
        items,
        pdv,
        sumWithoutPdv,
        sum,
    } = data;

    return (
        <div>
            <div className={css.header}>Накладна на повернення {number}</div>
            <table>
                <thead>
                    <tr>
                        <td>
                            <div className={css.pageHeaderSpace} />
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div>
                                <table className={css.sellerTable}>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div>
                                                    <b>Постачальник:</b>
                                                </div>
                                                <div>
                                                    <p>{seller.name}</p>
                                                </div>
                                            </td>
                                            <td>
                                                <img
                                                    width="182"
                                                    height="67"
                                                    src={`${config.STATIC_HOST}/images/txt_xml_viewer/inter_cars/inter_cars_logo.png`}
                                                    alt="logo"
                                                />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                <p>{seller.address}</p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                <b>Тел.:</b> {seller.phone};{' '}
                                                <b>Факс:</b> {seller.fax};{' '}
                                                <b>Web-сайт:</b> {seller.web};{' '}
                                                <b>E-mail:</b> {seller.email}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                IBAN: {seller.iban}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                Ідентифікаційний код:{' '}
                                                {seller.edrpou}; ІПН:{' '}
                                                {seller.ipn};
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                <b>Відпустив:</b>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                Філія:{' '}
                                                {parameters.affiliateName}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colSpan={3}>
                                                {parameters.affiliateAddress}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center" colSpan={3}>
                                                {parameters.textNds}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div className={css.title}>
                                    Накладна на повернення {number} від{' '}
                                    {formatDate(date, DATE_FORMAT)}
                                </div>
                                <table className={css.buyerTable}>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <b>Код постачальника:</b>
                                            </td>
                                            <td>{buyer.code}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Постачальник:</b>
                                            </td>
                                            <td>{buyer.name}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Ідентифікаційний код:</b>
                                            </td>
                                            <td>{buyer.taxCode}</td>
                                        </tr>

                                        <tr>
                                            <td>
                                                <b>Адреса:</b>
                                            </td>
                                            <td>{buyer.address}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Тел.:</b>
                                            </td>
                                            <td>{buyer.phone}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Метод оплати:</b>
                                            </td>
                                            <td>{parameters.paymentForm}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Початкова накладна:</b>
                                            </td>
                                            <td>{parameters.initialInvoice}</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table className={css.goodsTable}>
                                    <thead>
                                        <tr>
                                            <th
                                                className={
                                                    css.goodsTableItemNumber
                                                }
                                            >
                                                №
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemCode
                                                }
                                            >
                                                Код товару
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemName
                                                }
                                            >
                                                Назва товару
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemUnit
                                                }
                                            >
                                                Од. вим.
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemAmount
                                                }
                                            >
                                                Кіл-ть
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemPriceWithoutPdv
                                                }
                                            >
                                                Ціна без ПДВ
                                            </th>
                                            <th
                                                className={
                                                    css.goodsTableItemDiscount
                                                }
                                            >
                                                Знижка без ПДВ
                                            </th>

                                            <th
                                                className={
                                                    css.goodsTableItemSum
                                                }
                                            >
                                                Разом без ПДВ
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {items?.map((item, index) => (
                                            <tr key={item.article}>
                                                <td>{index + 1}</td>
                                                <td>{item.article}</td>
                                                <td>{item.name}</td>
                                                <td>{item.unit}</td>
                                                <td>{item.amount}</td>
                                                <td>
                                                    {sanitizeAndFormatPrice(
                                                        item.basePrice,
                                                    )}
                                                </td>
                                                <td>
                                                    {sanitizeAndFormatPrice(
                                                        item.discount,
                                                    )}
                                                </td>

                                                <td>
                                                    {sanitizeAndFormatPrice(
                                                        item.sum,
                                                    )}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>

                                <table className={css.goodsTableFooter}>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <b>Всього без ПДВ, грн.:</b>
                                            </td>
                                            <td>
                                                {sanitizeAndFormatPrice(
                                                    sumWithoutPdv,
                                                )}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>ПДВ, грн.:</td>
                                            <td>
                                                {sanitizeAndFormatPrice(pdv)}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <b>Всього Вкл. ПДВ, грн.:</b>
                                            </td>
                                            <td>
                                                {sanitizeAndFormatPrice(sum)}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                                <table className={css.totalSumTable}>
                                    <tbody>
                                        <tr>
                                            <td width="20%">Всього на суму:</td>
                                            <td>{parameters.amountWords}</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <tr className={css.signatureRow}>
                                    <td>
                                        <b>Склав:</b>
                                        {'  '}
                                    </td>
                                    <td>{parameters.invoiceCompiler}</td>
                                </tr>
                                <tr className={css.signatureRow}>
                                    <td>
                                        <b>Відповідальний:</b> {'  '}
                                    </td>
                                    <td>{parameters.responsible}</td>
                                </tr>

                                <tr className={css.signatureRow}>
                                    <td width="20%">
                                        <b>Товар прийняв завскладом:</b>
                                    </td>
                                    <td width="30%">
                                        {parameters.productReceived}
                                    </td>
                                    <td width="15%">
                                        <b>Товар здав:</b>
                                    </td>
                                    <td width="35%">
                                        {parameters.productSubmitted}
                                    </td>
                                </tr>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
};

export default InterCarsReturnInvoice;
