import { XmlParser } from '../../classes/XmlParser';
import {
    ADDITION_ARRAY_ITEM_FIELD_NAME,
    FIELDS,
    PRODUCT_ARRAY_ITEM_FIELD_NAME,
    ROOT_TAG,
} from './constants';

type ParsedSingleData = Record<string, string>;
type ParsedArrayData = ParsedSingleData[];

interface ParsedData {
    parsedSingleFields: ParsedSingleData;
    parsedArrayFields: {
        productArray: ParsedArrayData;
        additionArray: ParsedArrayData;
    };
}

const parseXml = (content: string) => {
    const { PRODUCTS, ...SINGLE_FIELDS } = FIELDS;

    const xmlParser = new XmlParser(content);

    const parsedSingleFields = xmlParser.parseTagsContents(
        Object.values(SINGLE_FIELDS),
    );

    const productArray = xmlParser.parseTagsContentsListFromContainerTag(
        PRODUCTS,
        PRODUCT_ARRAY_ITEM_FIELD_NAME,
    );
    const additionArray = xmlParser.parseTagsContentsListFromContainerTag(
        ROOT_TAG,
        ADDITION_ARRAY_ITEM_FIELD_NAME,
    );

    return {
        parsedSingleFields,
        parsedArrayFields: {
            productArray,
            additionArray,
        },
    } as ParsedData;
};

export type AcceptanceTransferActData = ReturnType<typeof parseXml>;

export default (data: { content: string }) => parseXml(data.content);
