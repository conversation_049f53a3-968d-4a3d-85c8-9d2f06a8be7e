import I from 'immutable';

import { getJsonValue } from '../utils';

import { XmlCompany, XmlData } from '../../../records/xml';
import constants from './constants';

const XmlExtra = new I.Record({
    senderAddress: null,
    senderName: null,
    senderPerson: null,
    sentAt: null,

    recipientAddress: null,
    recipientName: null,
    recipientPerson: null,
    receivedAt: null,

    cargoName: null,
    cargoWeight: null,
    cargoLength: null,
    cargoMode: null,
    cargoNote: null,

    vehicleType: null,
    vehicleWeight: null,
    vehicleLength: null,
    vehicleNote: null,
    vehiclePlate: null,
    trailerPlate: null,
    vehicleModel: null,

    paymentAmount: null,
    paymentType: 'Згідно договору',

    driverName: null,
    driverPhone: null,

    driverPassportNumber: null,
    driverPassportInfo: null,

    driverLicenseNumber: null,
    driverLicenseInfo: null,
});

export const parseJsonOwner = (json) => {
    return new XmlCompany({
        fullName: getJsonValue(json, constants.OWNER_FULL_NAME_PATH),
    });
};

export const parseJsonPartner = (json) => {
    return new XmlCompany({
        fullName: getJsonValue(json, constants.PARTNER_FULL_NAME_PATH),
    });
};

export const parseJsonExtra = (json) => {
    return new XmlExtra({
        senderAddress: getJsonValue(json, constants.EXTRA_SENDER_ADDRESS_PATH),
        senderName: getJsonValue(json, constants.EXTRA_SENDER_NAME_PATH),
        senderPerson: getJsonValue(json, constants.EXTRA_SENDER_PERSON_PATH),
        sentAt: getJsonValue(json, constants.EXTRA_SENT_AT_PATH),

        recipientAddress: getJsonValue(
            json,
            constants.EXTRA_RECIPIENT_ADDRESS_PATH,
        ),
        recipientName: getJsonValue(json, constants.EXTRA_RECIPIENT_NAME_PATH),
        recipientPerson: getJsonValue(
            json,
            constants.EXTRA_RECIPIENT_PERSON_PATH,
        ),
        receivedAt: getJsonValue(json, constants.EXTRA_RECEIVED_AT_PATH),

        cargoName: getJsonValue(json, constants.EXTRA_CARGO_NAME_PATH),
        cargoWeight: getJsonValue(json, constants.EXTRA_CARGO_WEIGHT_PATH),
        cargoLength: getJsonValue(json, constants.EXTRA_CARGO_LENGTH_PATH),
        cargoMode: getJsonValue(json, constants.EXTRA_CARGO_MODE_PATH),

        vehicleType: getJsonValue(json, constants.EXTRA_VEHICLE_TYPE_PATH),
        vehicleWeight: getJsonValue(json, constants.EXTRA_VEHICLE_WEIGHT_PATH),
        vehicleLength: getJsonValue(json, constants.EXTRA_VEHICLE_LENGTH_PATH),
        vehiclePlate: getJsonValue(json, constants.EXTRA_VEHICLE_PLATE_PATH),
        trailerPlate: getJsonValue(json, constants.EXTRA_TRAILER_PLATE_PATH),

        driverName: getJsonValue(json, constants.EXTRA_DRIVER_NAME_PATH),
        driverPhone: getJsonValue(json, constants.EXTRA_DRIVER_PHONE_PATH),

        driverPassportNumber: getJsonValue(
            json,
            constants.EXTRA_DRIVER_PASSPORT_NUMBER_PATH,
        ),
        driverPassportInfo: getJsonValue(
            json,
            constants.EXTRA_DRIVER_PASSPORT_INFO_PATH,
        ),

        driverLicenseNumber: getJsonValue(
            json,
            constants.EXTRA_DRIVER_LICENSE_NUMBER_PATH,
        ),
        driverLicenseInfo: getJsonValue(
            json,
            constants.EXTRA_DRIVER_LICENSE_INFO_PATH,
        ),
    });
};

export const parseJson = (json) => {
    return new XmlData({
        date: getJsonValue(json, constants.DATE_PATH),
        number: getJsonValue(json, constants.NUMBER_PATH),
        type: Object.keys(json)[0],

        agreementDate: getJsonValue(json, constants.AGREEMENT_DATE_PATH),
        agreementName: getJsonValue(json, constants.AGREEMENT_NAME_PATH),

        owner: parseJsonOwner(json),
        partner: parseJsonPartner(json),

        extra: parseJsonExtra(json),
    });
};

export default (data) => parseJson(data.jsonList.first());
