import { XmlParser } from '../../classes/XmlParser';
import {
    ADDITIONAL_PRODUCT_INFO_FIELD_NAME,
    FIELDS,
    PRODUCT_ARRAY_ITEM_FIELD_NAME,
} from './constants';

type ParsedSingleData = Record<string, string>;
type ParsedArrayData = ParsedSingleData[];

interface ParsedData {
    parsedSingleFields: ParsedSingleData;
    parsedArrayFields: {
        productArray: ParsedArrayData;
        additionProductInfoArray: ParsedArrayData;
    };
}

const parseXml = (content: string) => {
    const { PRODUCTS, ...SINGLE_FIELDS } = FIELDS;

    const xmlParser = new XmlParser(content);

    const parsedSingleFields = xmlParser.parseTagsContents(
        Object.values(SINGLE_FIELDS),
    );

    const productArray = xmlParser.parseTagsContentsListFromContainerTag(
        PRODUCTS,
        PRODUCT_ARRAY_ITEM_FIELD_NAME,
    );

    const additionProductInfoArray = xmlParser.parseTagsContentsListFromContainerTag(
        ADDITIONAL_PRODUCT_INFO_FIELD_NAME,
        PRODUCT_ARRAY_ITEM_FIELD_NAME,
    );

    return {
        parsedSingleFields,
        parsedArrayFields: {
            productArray,
            additionProductInfoArray,
        },
    } as ParsedData;
};

export type SalesInvoiceParserData = ReturnType<typeof parseXml>;

export default (data: { content: string }) => parseXml(data.content);
