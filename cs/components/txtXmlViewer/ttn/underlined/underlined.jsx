import React from 'react';

import PropTypes from 'prop-types';

import css from './underlined.css';

const Underlined = ({ subtext, children }) => (
    <div className={css.root}>
        <div className={css.text}>{children}</div>
        <div className={css.subtext}>{subtext && `(${subtext})`}</div>
    </div>
);

Underlined.propTypes = {
    subtext: PropTypes.string,
};

export default Underlined;
