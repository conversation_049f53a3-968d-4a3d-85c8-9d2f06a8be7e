import { parseTableData, textContent } from '../../utils';

import { parseXml as domParseXml } from '../../../../services/xml';
import { FIELDS, GOODS_COL_NAME, TAX_INVOICE_ROOT } from './constants';

const parseJson = (content: string) => {
    const xml = domParseXml(content);
    const getRootValue = (tag: string): string =>
        textContent(xml, `${TAX_INVOICE_ROOT} > ${tag}`).trim();

    const GOODS_ITEMS: Record<keyof typeof GOODS_COL_NAME, string>[] = (
        parseTableData(xml, FIELDS.TABLE, FIELDS.ROW) || []
    )
        .map((item: any) => {
            const rowNode = item?.querySelector('ROW');

            return {
                line: rowNode?.getAttribute('LINE'),
                name: rowNode?.getAttribute('NAME').trim(),
                value: textContent(item, 'VALUE').trim(),
            };
        })
        .reduce(
            (
                acc: Record<keyof typeof GOODS_COL_NAME, string>[],
                item: {
                    line: string;
                    name: keyof typeof GOODS_COL_NAME;
                    value: string;
                },
            ) => {
                if (!item.line || !item.name) {
                    return acc;
                }

                const index = Number(item.line);

                acc[index] = acc[index] || {};

                acc[index][item.name] = item.value;

                return acc;
            },
            [],
        )
        .filter(Boolean);

    return {
        NUM: getRootValue(FIELDS.NUM),
        DOCDATE: getRootValue(FIELDS.DOCDATE),
        FIRM_NAME: getRootValue(FIELDS.FIRM_NAME),
        FIRM_EDRPOU: getRootValue(FIELDS.FIRM_EDRPOU),
        FIRM_ADR: getRootValue(FIELDS.FIRM_ADR),
        FIRM_TELEFON: getRootValue(FIELDS.FIRM_TELEFON),
        FIRM_INN: getRootValue(FIELDS.FIRM_INN),
        FIRM_CODENDS: getRootValue(FIELDS.FIRM_CODENDS),
        FIRM_RS: getRootValue(FIELDS.FIRM_RS),
        FIRM_NMBANK: getRootValue(FIELDS.FIRM_NMBANK),
        FIRM_CBANK: getRootValue(FIELDS.FIRM_CBANK),
        CITY: getRootValue(FIELDS.CITY),
        SIDE_CD_K: getRootValue(FIELDS.SIDE_CD_K),
        SIDE_EDRPOU_K: getRootValue(FIELDS.SIDE_EDRPOU_K),
        SIDE_CDADR_K: getRootValue(FIELDS.SIDE_CDADR_K),
        SIDE_CDINDTAXNUM_K: getRootValue(FIELDS.SIDE_CDINDTAXNUM_K),
        SIDE_CODENDS_K: getRootValue(FIELDS.SIDE_CODENDS_K),
        FIELD2: getRootValue(FIELDS.FIELD2),
        FIELD3: getRootValue(FIELDS.FIELD3),
        DOG_NUM: getRootValue(FIELDS.DOG_NUM),
        DOG_DATE: getRootValue(FIELDS.DOG_DATE),
        FIELD1: getRootValue(FIELDS.FIELD1),
        FIELD4: getRootValue(FIELDS.FIELD4),
        SUMWITHOUTPDV: getRootValue(FIELDS.SUMWITHOUTPDV),
        PDV: getRootValue(FIELDS.PDV),
        DOCSUM: getRootValue(FIELDS.DOCSUM),
        DOCSUM_TEXT: getRootValue(FIELDS.DOCSUM_TEXT),
        GOODS_ITEMS,
    };
};

export type TaxInvoiceData = ReturnType<typeof parseJson>;

export default (data: { content: string }) => parseJson(data.content);
