import I from 'immutable';
import { DATE_FORMAT } from 'lib/constants';
import { formatDate } from 'lib/date';
import { formatPrice } from 'lib/numbers';
import { t } from 'ttag';
import * as writtenNumber from 'written-number';

import jsonPath from '../../lib/vendor/jsonpath-0.8.0';
import loggerService from '../../services/logger';

export const boolContent = (xml, query) => {
    const selected = xml.querySelector(query);
    if (selected) {
        return selected.textContent === 'true';
    }
    return false;
};

export const createXmlFromArray = (array) => {
    const xml = document.implementation.createDocument(
        null,
        'XmlDocument',
        null,
    );
    array.forEach((node) => xml.firstChild.appendChild(node));
    return xml;
};

export const formatAgreementName = (date, number) => {
    if (!number) return '';
    return `№ ${number} ${t`від`} ${formatDate(date, DATE_FORMAT)}`;
};

export const formatCertificate = (certificate) => {
    return certificate && `${t`№ свід.`} ${certificate}`;
};

export const formatIpn = (ipn) => {
    return ipn && `${t`ІПН`} ${ipn}`;
};

export const formatPhone = (phone) => {
    return phone && `${t`тел.`} ${phone}`;
};

export const formatMfo = (mfo) => {
    return mfo && `${t`МФО`} ${mfo}`;
};

export const formatEdrpou = (edrpou) => {
    return edrpou && `${t`ЄДРПОУ`} ${edrpou}`;
};

export const formatTotalPrice = (data, key, doNotUseStrValue = false) => {
    const strValue = data.get(`${key}Str`);
    return doNotUseStrValue
        ? `${formatPrice(data.get(key))} ${data.currency}`
        : strValue;
};

export const generateUATextMoneyRepresentation = (value) => {
    const grivnyaValue = Math.floor(value);
    const copiyckaValue = (value - grivnyaValue) * 100;

    const lastGrivnyaDigit = grivnyaValue % 10;
    const lastCopiyckaDigit = copiyckaValue % 10;

    const grivnyaSingularWord = t`гривня`;
    const grivnyaPluralWord = t`гривні`;
    const grivnyaGeneralWord = t`гривень`;

    const copiyckaPluralWord = t`копійки`;
    const copiyckaGeneralWord = t`копійок`;

    const pluralLastDigit = new Set([2, 3, 4]);

    let grivnyaWord = grivnyaGeneralWord;
    if (lastGrivnyaDigit == 1) {
        grivnyaWord = grivnyaSingularWord;
    } else if (pluralLastDigit.has(lastGrivnyaDigit)) {
        grivnyaWord = grivnyaPluralWord;
    }

    let copiyckaWord = copiyckaGeneralWord;
    if (lastCopiyckaDigit == 1) {
        grivnyaWord = grivnyaSingularWord;
    } else if (pluralLastDigit.has(lastCopiyckaDigit)) {
        grivnyaWord = copiyckaPluralWord;
    }

    const grivnyaNumberText = writtenNumber(grivnyaValue, {
        lang: 'uk',
        alternativeBase: 'feminine',
    });
    const copiyckaNumberText = writtenNumber(copiyckaValue, {
        lang: 'uk',
        alternativeBase: 'feminine',
    });

    return `${grivnyaNumberText} ${grivnyaWord}, ${copiyckaNumberText} ${copiyckaWord}`;
};

export const getCoordinateToSign = (container, signPlaceholder) => {
    const signContainerRect = container.getBoundingClientRect();
    const placeholderRect = signPlaceholder.getBoundingClientRect();
    return {
        top: placeholderRect.top - signContainerRect.top,
        left: placeholderRect.left - signContainerRect.left,
    };
};

export const getSignatures = (signatures) => {
    return {
        ownerSignatures: signatures.filter(
            ({ isSignature, isOwner }) => isSignature && isOwner,
        ),
        ownerStamps: signatures.filter(
            ({ isSignature, isOwner }) => !isSignature && isOwner,
        ),
        recipientSignatures: signatures.filter(
            ({ isSignature, isOwner }) => isSignature && !isOwner,
        ),
        recipientStamps: signatures.filter(
            ({ isSignature, isOwner }) => !isSignature && !isOwner,
        ),
    };
};

export const getJsonValue = (json, path) => {
    if (!path) {
        return '';
    }

    const values = jsonPath(json, path);
    if (values && values.length) {
        return values[0] || '';
    }

    return '';
};

export const isExpandable = (data) => {
    const { jsonList } = data;
    if (!jsonList || !jsonList.count()) {
        return false;
    }

    const json = jsonList.last();
    const rootKey = Object.keys(json)[0];
    return Object.keys(json[rootKey]).some((key) => {
        return json[rootKey][key]['@СледующаяСтрока'] === 'true';
    });
};

export const isXmlDocument = (data) => {
    return data.ext === '.xml';
};

export const parseJsonTableData = (json) => {
    return Object.keys(json || {})
        .map((key) => {
            if (key[0] === '@' || key === '$') return null;
            return json[key];
        })
        .filter((obj) => Boolean(obj));
};

export const cloneXml = (xml) => {
    return xml.implementation.createDocument(
        xml.namespaceURI, // namespace to use
        null, // name of the root element (or for empty document)
        null, // doctype (null for XML)
    );
};

export const parseTableData = (
    xml,
    tableQuery,
    tableRowQuery,
    attributeName = '',
    attributeValue = '',
) => {
    const newXml = cloneXml(xml);
    const newNode = newXml.importNode(
        xml.documentElement, // node to import
        true, // clone its descendants
    );
    newXml.appendChild(newNode);
    const selector = newXml.querySelector(tableQuery);

    const tableData = [];
    let counter = -1;

    if (selector) {
        new I.List(selector.childNodes)
            .filter((node) => node.nodeType !== Node.TEXT_NODE)
            .forEach((node) => {
                if (
                    node.nodeName === tableRowQuery ||
                    (attributeName &&
                        node.attributes[attributeName].nodeValue ===
                            attributeValue)
                ) {
                    counter++;
                    tableData[counter] = [];
                }
                if (counter >= 0) tableData[counter].push(node);
            });

        return tableData.map(createXmlFromArray);
    }

    loggerService.log(
        'No such query for table rendering in XML. Fallback to empty list',
        { table_query: tableQuery },
    );
    return new I.List();
};

export const textContent = (xml, query) => {
    if (!query) {
        return '';
    }

    const selected = xml.querySelector(query);
    if (selected) {
        return selected.textContent;
    }

    return '';
};

export const splitStringByLength = (str, length = 18) => {
    let result = '';

    for (let i = 0; i < str.length; i += length) {
        result += str.slice(i, i + length) + '\n';
    }
    return result.trim();
};

export default {
    boolContent,
    createXmlFromArray,
    formatAgreementName,
    formatCertificate,
    formatPhone,
    formatTotalPrice,
    getCoordinateToSign,
    getJsonValue,
    getSignatures,
    isExpandable,
    isXmlDocument,
    parseJsonTableData,
    parseTableData,
    textContent,
    splitStringByLength,
};
