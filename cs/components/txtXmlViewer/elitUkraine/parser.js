import I from 'immutable';
import moment from 'moment';

import { parseTableData, textContent } from '../utils';

import {
    XmlBank,
    XmlCompany,
    XmlData,
    XmlProduct,
    XmlProducts,
} from '../../../records/xml';
import { parseXml as domParseXml } from '../../../services/xml';
import constants from './constants';

const XmlExtra = new I.Record({
    barcode: null,
    paymentMethod: null,
    storage: null,
    location: null,
    message: null,
    footernote: null,
    info: null,
    payer: null,
    clientCode: null,
});

export const parseXmlOwnerBank = (xml) => {
    const account = textContent(xml, constants.OWNER_BANK_ACCOUNT_QUERY, false);
    const mfo = textContent(xml, constants.OWNER_BANK_MFO_QUERY, false);
    const name = textContent(xml, constants.OWNER_BANK_NAME_QUERY, false);

    return new XmlBank({ account, mfo, name });
};

export const parseXmlOwner = (xml) => {
    return new XmlCompany({
        address: textContent(xml, constants.OWNER_ADDRESS_QUERY),
        bank: parseXmlOwnerBank(xml),
        certificate: textContent(xml, constants.OWNER_CERTIFICATE_QUERY),
        email: textContent(xml, constants.OWNER_EMAIL_QUERY),
        edrpou: textContent(xml, constants.OWNER_EDRPOU_QUERY),
        fullName: textContent(xml, constants.OWNER_FULL_NAME_QUERY),
        ipn: textContent(xml, constants.OWNER_IPN_QUERY),
        phone: textContent(xml, constants.OWNER_PHONE_QUERY),
        representative: textContent(
            xml,
            constants.OWNER_REPRESENTATIVE_QUERY,
            false,
        ),
    });
};

export const parseXmlPartner = (xml) => {
    return new XmlCompany({
        address: textContent(xml, constants.PARTNER_ADDRESS_QUERY),
        email: textContent(xml, constants.PARTNER_EMAIL_QUERY),
        edrpou: textContent(xml, constants.PARTNER_EDRPOU_QUERY),
        certificate: textContent(xml, constants.PARTNER_CERTIFICATE_QUERY),
        fullName: textContent(xml, constants.PARTNER_FULL_NAME_QUERY),
        ipn: textContent(xml, constants.PARTNER_IPN_QUERY),
        phone: textContent(xml, constants.PARTNER_PHONE_QUERY),
    });
};

export const parseXmlService = (serviceXml) => {
    return new XmlProduct({
        item: textContent(serviceXml, constants.SERVICE_ITEM_QUERY),
        name: textContent(serviceXml, constants.SERVICE_NAME_QUERY),
        price: parseFloat(
            textContent(serviceXml, constants.SERVICE_PRICE_QUERY),
        ),
        quantity: textContent(serviceXml, constants.SERVICE_QUANTITY_QUERY),
        sum: parseFloat(textContent(serviceXml, constants.SERVICE_SUM_QUERY)),
        supplierCode: textContent(serviceXml, constants.SERVICE_CODE_QUERY),
        externalCode: textContent(
            serviceXml,
            constants.SERVICE_CODE_ADDITIONAL_QUERY,
        ),
        importedOption: textContent(
            serviceXml,
            constants.SERVICE_IMPORTED_OPTION_QUERY,
        ),
        unit: textContent(serviceXml, constants.SERVICE_UNIT_QUERY),
    });
};

export const parseXmlServices = (xml) => {
    const serviceXmls = xml.querySelector(constants.SERVICES_QUERY)
        ? parseTableData(
              xml,
              constants.SERVICES_QUERY,
              constants.SERVICE_ITEM_QUERY,
          )
        : [];
    const items = new I.List(serviceXmls.map(parseXmlService));

    return new XmlProducts({
        items,
        totalSum: parseFloat(textContent(xml, constants.TOTAL_SUM_QUERY)),
        totalDocumentSum: parseFloat(
            textContent(xml, constants.TOTAL_DOCUMENT_SUM_QUERY),
        ),
        totalSumStr: textContent(xml, constants.TOTAL_SUM_STR_QUERY, false),
        totalTaxes: parseFloat(textContent(xml, constants.TOTAL_TAXES_QUERY)),
    });
};

export const parseXmlExtra = (xml) => {
    return new XmlExtra({
        barcode: textContent(xml, constants.EXTRA_BARCODE_QUERY),
        paymentMethod: textContent(xml, constants.EXTRA_PAYMENT_METHOD_QUERY),
        storage: textContent(xml, constants.EXTRA_STORAGE_QUERY),
        location: textContent(xml, constants.EXTRA_LOCATION_QUERY),
        message: textContent(xml, constants.EXTRA_MESSAGE_QUERY),
        footernote: textContent(xml, constants.EXTRA_FOOTERNOTE_QUERY),
        info: textContent(xml, constants.EXTRA_INFO_QUERY),
        payer: textContent(xml, constants.EXTRA_PAYER_QUERY),
        clientCode: textContent(xml, constants.EXTRA_CLIENT_CODE_QUERY),
    });
};

export const parseXml = (content) => {
    const xml = domParseXml(content);
    const dateTo = textContent(xml, constants.DATE_TO_QUERY).trim();
    return new XmlData({
        date: textContent(xml, constants.DATE_QUERY),
        dateTo: dateTo && moment(dateTo),

        number: textContent(xml, constants.NUMBER_QUERY),
        agreementName: textContent(xml, constants.AGREEMENT_NAME_QUERY),
        documentType: textContent(xml, constants.DOCUMENT_NAME_QUERY),
        dealName: textContent(xml, constants.DEAL_NAME_QUERY, false),

        parentNumber: textContent(xml, constants.PARENT_DOCUMENT_NUMBER),
        parentType: textContent(xml, constants.PARENT_DOCUMENT_NAME),
        parentDate: textContent(xml, constants.PARENT_DOCUMENT_DATE),

        barcode: textContent(xml, constants.BARCODE_QUERY, false),

        owner: parseXmlOwner(xml),
        partner: parseXmlPartner(xml),
        services: parseXmlServices(xml),
        extra: parseXmlExtra(xml),
    });
};

export default (data) => parseXml(data.content);
