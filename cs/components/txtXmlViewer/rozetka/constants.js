export const DATE_FORMAT_FULL = 'DD MMMM YYYY';

export const DATE_QUERY = 'Invoice-Header > InvoiceDate';
export const DATE_DELIVERY_QUERY =
    'Invoice-Reference > ReceivingAdvice > DeliveryDate';
export const BUYER_ORDER_QUERY = 'Invoice-Reference > Order > BuyerOrderNumber';
export const BUYER_ORDER_DATE_QUERY =
    'Invoice-Reference > Order > BuyerOrderDate';

export const BUYER_ORDER_OVERWRITE_QUERY =
    'Invoice-Reference > Order > BuyerOrderOverwrite';

export const MANAGER_ORDER_QUERY = 'Invoice-Reference > Order > ManagerOrder';
export const PAYMENT_TERN_ORDER_QUERY =
    'Invoice-Reference > Order > PaymentTernOrder';

export const NOTE_NUMBER_QUERY = 'Invoice-Header > InvoiceNumber';
export const EXTRA_ADD_DOC_QUERY = 'Invoice-Header > AddDoc';
export const EXTRA_ADD_DOC_SIGNER_QUERY = 'Invoice-Header > AddDocSigner';
export const EXTRA_COMPOSITION_CHECKED_QUERY =
    'Invoice-Header > CompositionChecked';
export const EXTRA_SHIPPED_QUERY = 'Invoice-Header > Shipped';
export const NOTE_AGREEMENT_NUMBER_QUERY = 'Invoice-Header > ContractNumber';
export const NOTE_AGREEMENT_DATE_QUERY = 'Invoice-Header > ContractDate';

export const PLACE_QUERY = 'Invoice-Header > Place';

export const PAYMENT_TERM = 'Invoice-Header > PAYMENT_TERM';
export const DELIVERY_ADDRESS = 'DeliveryAddress';

export const BUYER_ADDRESS_STREET_QUERY =
    'Invoice-Parties > Buyer > StreetAndNumber';
export const BUYER_ADDRESS_CITY_QUERY = 'Invoice-Parties > Buyer > CityName';
export const BUYER_ADDRESS_POSTAL_CODE_QUERY =
    'Invoice-Parties > Buyer > PostalCode';
export const BUYER_NAME_QUERY = 'Invoice-Parties > Buyer > Name';
export const BUYER_EDRPOU_QUERY =
    'Invoice-Parties > Buyer > UtilizationRegisterNumber';

export const SELLER_ADDRESS_STREET_QUERY =
    'Invoice-Parties > Seller > StreetAndNumber';
export const SELLER_ADDRESS_CITY_QUERY = 'Invoice-Parties > Seller > CityName';
export const SELLER_ADDRESS_POSTAL_CODE_QUERY =
    'Invoice-Parties > Seller > PostalCode';
export const SELLER_EDRPOU_QUERY =
    'Invoice-Parties > Seller > UtilizationRegisterNumber';
export const SELLER_NAME_QUERY = 'Invoice-Parties > Seller > Name';

export const GOODS_QUERY = 'Invoice-Lines';
export const GOODS_ITEM_QUERY = 'Line';
export const GOODS_UNIT_QUERY = 'UnitOfMeasure';
export const GOODS_ITEM_NAME_QUERY = 'ItemDescription';
export const GOODS_ITEM_CODE_QUERY = 'BuyerItemCode';
export const GOODS_ITEM_CODE_SELF_QUERY = 'BuyerItemCodeSelf';
export const GOODS_ITEM_NUMBER_QUERY = 'LineNumber';
export const GOODS_ITEM_PRICE_QUERY = 'InvoiceUnitNetPrice';
export const GOODS_ITEM_SUM_WITH_TAX_PRICE_QUERY = 'NetAmount';
export const GOODS_ITEM_QUANTITY_QUERY = 'InvoiceQuantity';
export const GOODS_ITEM_EXTERNAL_CODE_QUERY = 'ExternalItemCode';
export const GOODS_ITEM_SUPPLIER_CODE_QUERY = 'SupplierItemCode';
export const GOODS_ITEM_ADDITIONAL_INVOICE_QUANTITY_QUERY =
    'AdditionalInvoiceQuantity';
export const GOODS_ITEM_ADDITIONAL_UNIT_OF_MEASURE_QUERY =
    'AdditionalUnitOfMeasure';
export const GOODS_ITEM_ADDITIONAL_NET_AMOUNT_QUERY = 'AdditionalNetAmount';

export const GOODS_SUM_QUERY = 'Invoice-Summary > TotalNetAmount';
export const GOODS_SUM_TAXES_QUERY = 'Invoice-Summary > TotalTaxAmount';
export const GOODS_SUM_WITH_TAX_QUERY = 'Invoice-Summary > TotalGrossAmount';
export const GOODS_SUM_WITH_TAX_TEXT_QUERY =
    'Invoice-Summary > TotalGrossAmountText';

export const EXTRA_BUYER_DELIVERY_POINT_QUERY =
    'Invoice-Parties > DeliveryPoint > ILN';
export const EXTRA_SELLER_GLN_QUERY = 'Invoice-Parties > Seller > ILN';
