import React, { FC } from 'react';

import { DATE_FORMAT_FULL } from 'components/txtXmlViewer/Comdoc/constants';
import { coalesceJoin } from 'components/txtXmlViewer/Comdoc/helper';
import SignLine from 'components/txtXmlViewer/ui/signLine/signLine';
import { generateUATextMoneyRepresentation } from 'components/txtXmlViewer/utils';
import { formatDate } from 'lib/date';
import { capitalizeFirstLetter } from 'lib/helpers';
import { formatPrice } from 'lib/numbers';
import uuid from 'services/uuid';

import { Props } from './types';

import css from './ReturnToSupplier.css';

const ReturnToSupplier: FC<React.PropsWithChildren<Props>> = (props) => {
    const {
        date,
        number,
        owner,
        partner,
        services,
        agreementName,
        agreementDate,
        documentBasis,
        documentBasisDate,
        agreementPlace,
        parentType,
        parentNumber,
        parentDate,
        parentOrderNumber,
        parentOrderDate,
        parentOrderDatePayment,
        documentType,
        compositionChecked,
        shipped,
        fromPerformer,
        deliveryAddress,
    } = props.data;

    const documentName = props.disagreementName
        ? 'Акт розбіжностей'
        : documentType;
    const totalAmountString = !services?.totalDocumentSum
        ? undefined
        : capitalizeFirstLetter(
              generateUATextMoneyRepresentation(services?.totalDocumentSum),
          );
    const totalAmountContent = totalAmountString
        ? `(${totalAmountString})`
        : '';
    return (
        <div>
            <h1 className={css.documentTitle}>
                {documentName} № {number} від{' '}
                {formatDate(date, DATE_FORMAT_FULL)} р.
            </h1>
            <table className={css.headerTable}>
                <tbody>
                    <tr>
                        <td>{partner?.status}:</td>
                        <td>
                            {coalesceJoin(
                                <b>{partner?.fullName}</b>,
                                partner?.edrpou && (
                                    <>код ЄДРПОУ {partner?.edrpou}</>
                                ),
                                partner?.ipn && <>IПН {partner?.ipn}</>,
                                partner?.bank?.account && (
                                    <>П/р {partner?.bank?.account}</>
                                ),
                                partner?.bank?.mfo && (
                                    <>МФО {partner?.bank?.mfo}</>
                                ),
                            )}
                        </td>
                    </tr>
                    <tr>
                        <td>{owner?.status}:</td>
                        <td>
                            {coalesceJoin(
                                <b>{owner?.fullName}</b>,
                                owner?.edrpou && (
                                    <>код ЄДРПОУ {owner?.edrpou}</>
                                ),
                                owner?.ipn && <>IПН {owner?.ipn}</>,
                                owner?.bank?.account && (
                                    <>П/р {owner?.bank?.account}</>
                                ),
                                owner?.bank?.mfo && <>МФО {owner?.bank?.mfo}</>,
                            )}
                        </td>
                    </tr>
                    <tr>
                        <td>Договір:</td>
                        <td>
                            {agreementName || documentBasis} від{' '}
                            {formatDate(agreementDate || documentBasisDate)}
                        </td>
                    </tr>
                    {parentOrderNumber && (
                        <tr>
                            <td>Замовлення:</td>
                            <td>
                                Замовлення покупця № {parentOrderNumber}{' '}
                                {parentOrderDate && `від ${parentOrderDate}`}
                            </td>
                        </tr>
                    )}
                    {deliveryAddress && (
                        <tr>
                            <td>Адреса доставки:</td>
                            <td>{deliveryAddress}</td>
                        </tr>
                    )}
                    <tr>
                        <td>Документ підстава:</td>
                        <td>
                            {parentType} № {parentNumber} від{' '}
                            {formatDate(parentDate)}
                        </td>
                    </tr>
                    {parentOrderDatePayment && (
                        <tr>
                            <td>Дата оплати:</td>
                            <td>{parentOrderDatePayment}</td>
                        </tr>
                    )}
                    {agreementPlace && (
                        <tr>
                            <td>Місце складання:</td>
                            <td>{agreementPlace}</td>
                        </tr>
                    )}
                </tbody>
            </table>
            <table className={css.goodsTable}>
                <thead>
                    <tr>
                        <th className={css.number}>№</th>
                        <th className={css.name}>Товар</th>
                        {props.inconsistencyAct && props.disagreementName && (
                            <th className={css.number}>
                                Кількість по накладній
                            </th>
                        )}
                        <th className={css.quantity}>К-сть</th>
                        {props.notAcceptedQuantity && (
                            <th className={css.quantity}>
                                Не прийнята кількість
                            </th>
                        )}
                        {props.inconsistencyAct && (
                            <th className={css.quantity}>Розбіжність</th>
                        )}
                        {props.inconsistencyAct && (
                            <th className={css.item}>Причина</th>
                        )}
                        <th className={css.item}>Од.</th>
                        <th className={css.price}>Ціна без ПДВ</th>
                        <th className={css.sum}>Сума без ПДВ</th>
                    </tr>
                </thead>
                <tbody>
                    {services?.items?.map((service, index) => (
                        <tr key={`product-${uuid()}`}>
                            <td className={css.textCenter}>{index + 1}</td>
                            <td>{service.name}</td>
                            {props.inconsistencyAct &&
                                props.disagreementName && (
                                    <td>{service.declaredQuantity}</td>
                                )}
                            <td className={css.textRight}>
                                {service.quantity}
                            </td>
                            {props.notAcceptedQuantity && (
                                <td className={css.textRight}>
                                    {service.notAcceptedQuantity}
                                </td>
                            )}
                            {props.inconsistencyAct && (
                                <td className={css.textRight}>
                                    {service.notAcceptedQuantity}
                                </td>
                            )}
                            {props.inconsistencyAct && (
                                <td className={css.textRight}>
                                    {service.reason}
                                </td>
                            )}
                            <td className={css.textRight}>{service.unit}</td>
                            <td className={css.textRight}>
                                {formatPrice(service.price)}
                            </td>
                            <td className={css.textRight}>
                                {formatPrice(service.sum)}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
            <table className={css.totalTable}>
                <tbody>
                    <tr>
                        <td className={css.totalName}>Разом:</td>
                        <td>{formatPrice(services?.totalSum)}</td>
                    </tr>
                    <tr>
                        <td className={css.totalName}>Сума ПДВ:</td>
                        <td>{formatPrice(services?.totalTaxes)}</td>
                    </tr>
                    <tr>
                        <td className={css.totalName}>Усього з ПДВ:</td>
                        <td>{formatPrice(services?.totalDocumentSum)}</td>
                    </tr>
                </tbody>
            </table>
            <div className={css.amountText}>
                Всього на суму {formatPrice(services?.totalDocumentSum)} грн{' '}
                {totalAmountContent}
            </div>
            {services?.totalTaxes && (
                <div className={css.amountText}>
                    У т.ч. ПДВ:{' '}
                    {capitalizeFirstLetter(
                        generateUATextMoneyRepresentation(services.totalTaxes),
                    )}
                </div>
            )}
            {props.inconsistencyAct && props.disagreementName && (
                <div className={css.place}>
                    Акт є невід'ємною частиною накладної № {parentNumber} від{' '}
                    {formatDate(parentDate)}
                </div>
            )}
            <SignLine full className={css.signLine} />
            <table className={css.footerTable}>
                <tbody>
                    {props.inconsistencyAct && (
                        <>
                            <tr>
                                <td>{owner?.fullName}</td>
                                <td>{partner?.fullName}</td>
                            </tr>
                            <tr>
                                <td>
                                    {owner?.edrpou && (
                                        <>код ЄДРПОУ {owner?.edrpou}</>
                                    )}
                                </td>
                                <td>
                                    {partner?.edrpou && (
                                        <>код ЄДРПОУ {partner?.edrpou}</>
                                    )}
                                </td>
                            </tr>
                        </>
                    )}
                    <tr>
                        <td>
                            * Відповідальний за здійснення господарської
                            операції і правильність її оформлення
                        </td>
                        <td />
                    </tr>
                    {fromPerformer && (
                        <>
                            <tr>
                                <br />
                            </tr>
                            <tr>
                                <td>Від виконавця: {fromPerformer}</td>
                                <td />
                            </tr>
                        </>
                    )}
                </tbody>
            </table>
            {compositionChecked && (
                <table className={css.footerTable}>
                    <thead>
                        <tr>
                            <th>Перевірено склад: {compositionChecked}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <SignLine />
                            </td>
                        </tr>
                    </tbody>
                </table>
            )}
            {shipped && (
                <table className={css.footerTable}>
                    <thead>
                        <tr>
                            <th>Відвантажено: {shipped}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <SignLine />
                            </td>
                        </tr>
                    </tbody>
                </table>
            )}

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Отримав(ла)
                            <br />
                        </th>
                        <th>
                            Від Покупця*
                            <br />
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    );
};

export default ReturnToSupplier;
