import React, { FC } from 'react';

import { generateUATextMoneyRepresentation } from 'components/txtXmlViewer/utils';
import { formatDate } from 'lib/date';
import { capitalizeFirstLetter } from 'lib/helpers';

import { XmlTemplateProps } from '../../ServiceDeed/types';

import SignLine from '../../ui/signLine/signLine';

import Company from '../../company/company';
import ServicesTable from '../../ServiceDeed/ServicesTable/ServicesTable';
import { DATE_FORMAT, DATE_FORMAT_FULL } from '../../ServiceDeed/constants';

import css from './ServiceDeed.css';

const ServiceDeed: FC<React.PropsWithChildren<XmlTemplateProps>> = ({
    data,
}) => {
    const { date, number, owner, partner, services, compilationPlace } = data;
    const shortDate = formatDate(date);
    const agreementDate = data.agreementDate && formatDate(data.agreementDate);
    const scoreDate =
        data.agreementDate && formatDate(data.scoreDate, DATE_FORMAT);
    const scoreData =
        data.scoreName && data.scoreDate && data.scoreType
            ? `що діє на підставі ${data.scoreType.toLowerCase()} № ${
                  data.scoreName
              } від ${scoreDate},`
            : '';

    const totalAmountString = !services?.total?.totalSum
        ? undefined
        : capitalizeFirstLetter(
              generateUATextMoneyRepresentation(services?.total?.totalSum),
          );
    const totalTaxString = !services?.total?.totalTaxes
        ? undefined
        : capitalizeFirstLetter(
              generateUATextMoneyRepresentation(services?.total?.totalTaxes),
          );
    const totalAmountWithTaxString = !services?.total?.totalDocumentSum
        ? undefined
        : capitalizeFirstLetter(
              generateUATextMoneyRepresentation(
                  services?.total?.totalDocumentSum,
              ),
          );

    return (
        <div>
            <h1 className={css.documentTitle}>АКТ надання послуг</h1>

            <table className={css.documentNumberTable}>
                <tbody>
                    <tr>
                        <td className={css.documentNumberDetailsTable}>
                            <h2 className={css.documentNumber}>
                                № {number} від{' '}
                                {formatDate(date, DATE_FORMAT_FULL)} р.
                            </h2>
                        </td>
                        <td>{compilationPlace}</td>
                    </tr>
                </tbody>
            </table>

            <div className={css.documentTriviaIndent}>
                Ми, що нижче підписалися, представник Замовника{' '}
                {partner?.fullName} {partner?.secondRepresentative}, з одного
                боку, і представник Виконавця {owner?.fullName}{' '}
                {owner?.secondRepresentative}, {scoreData} з іншого боку, склали
                цей акт про те, що на підставі наведених документів:
            </div>

            <table className={css.documentDetailsTable}>
                <tbody>
                    <tr>
                        <td className={css.detailName}>Договір:</td>
                        <td>
                            № {data.agreementName} від {agreementDate}
                        </td>
                    </tr>
                </tbody>
            </table>

            <div className={css.documentTrivia}>
                Виконавцем були виконані наступні роботи (надані такі послуги):
            </div>

            {services && <ServicesTable data={services} />}

            <div className={css.amountText}>
                Загальна вартість робіт (послуг) склала без ПДВ{' '}
                {totalAmountString && totalAmountString}
                , <br />
                ПДВ {totalTaxString && ` ${totalTaxString}`}
                , <br />
                загальна вартість робіт (послуг) склала з ПДВ{' '}
                {totalAmountWithTaxString && totalAmountWithTaxString}
            </div>

            <div className={css.documentTrivia}>
                Замовник претензій по об'єму, якості та строкам виконання робіт
                (надання послуг) не має.
            </div>

            <div className={css.documentTriviaBorder} />

            <table className={css.footerTable}>
                <tbody>
                    <tr className={css.representativeRow}>
                        <td>
                            <b>{owner?.representative}</b>
                        </td>
                        <td>
                            <b>{partner?.representative}</b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            * Відповідальний за здійснення господарської
                            операції і правильність її оформлення
                        </td>
                        <td />
                    </tr>
                    <tr>
                        <td className={css.footerDate}>
                            <b>{shortDate}</b>
                        </td>
                        <td className={css.footerDate}>
                            <b>{shortDate}</b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <Company data={owner} />
                        </td>
                        <td>
                            <Company data={partner} />
                        </td>
                    </tr>
                </tbody>
            </table>

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Від Виконавця*
                            <br />
                        </th>
                        <th>
                            Від Замовника
                            <br />
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    );
};

export default ServiceDeed;
