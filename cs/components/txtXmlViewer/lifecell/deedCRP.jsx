import React from 'react';

import PropTypes from 'prop-types';

import { XmlDocument } from '../../../records/document';
import { XmlData } from '../../../records/xml';
import BaseDeedCRP from './baseDeedCRP';

import css from './deed.css';

const DeedCRP = (props) => {
    const { agreement, serviceAccordingTo } = props.data.extra;
    return (
        <BaseDeedCRP
            {...props}
            paymentPurposeContent={
                <b>
                    Оплата за телекомунікаційні послуги по договору{' '}
                    <span className={css.highlight}>{agreement}</span> згідно з
                    місячним рахунком від{' '}
                    <span className={css.highlight}>{serviceAccordingTo}</span>
                </b>
            }
        />
    );
};

DeedCRP.propTypes = {
    printMode: PropTypes.bool,
    doc: PropTypes.instanceOf(XmlDocument).isRequired,
    data: PropTypes.instanceOf(XmlData).isRequired,
};

export default DeedCRP;
