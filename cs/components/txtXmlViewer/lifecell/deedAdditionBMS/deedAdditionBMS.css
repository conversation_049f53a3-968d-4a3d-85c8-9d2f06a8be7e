.root {
    padding-top: 1cm;
}

.name {
    text-align: right;
}

.title {
    margin: 1.5cm 0 1cm;
    color: #f00;
    font-size: 110%;
    font-weight: bold;
    text-align: center;
}

.phone {
    font-weight: bold;
}

.main {
    width: 100%;
    border-bottom: 0.05cm solid var(--content-color);
    margin: 0 0 2cm;
    font-size: 80%;
}

.main th:last-child {
    border-bottom: 0.05cm solid var(--content-color);
    text-align: right;
}

.main td {
    padding: 0.1cm 0;
}

.main td:last-child {
    text-align: right;
}

.bold {
    font-weight: bold;
}

.padded td {
    padding: 0.5cm 0;
}
