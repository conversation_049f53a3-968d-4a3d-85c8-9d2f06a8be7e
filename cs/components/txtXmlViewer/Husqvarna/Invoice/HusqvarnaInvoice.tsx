import React from 'react';

import { XmlTemplateProps } from './types';

import { formatDate } from '../../../../lib/date';
import { formatPrice } from '../../../../lib/numbers';
import FooterRenderer from '../../../footerRenderer/footerRenderer';

import css from './HusqvarnaInvoice.css';

const HusqvarnaDeliveryNote: React.FC<XmlTemplateProps> = ({
    data,
    renderSignatures,
    renderReviews,
    doc,
}) => {
    const {
        customerName,
        customerPhone,
        customerAddress,
        orderDate,
        orderNumber,
        warehouse,
        lines,
        vat,
        totalWithoutVat,
        totalVat,
        totalWithVat,
    } = data;

    return (
        <div>
            <div className={css.header}>
                <div className={css.headerImage}>
                    <img
                        height="80"
                        src={`${config.STATIC_HOST}/images/txt_xml_viewer/husqvarna/husqvarna_logo.png`}
                        alt="Husqvarna logo"
                    />
                </div>
                <div className={css.headerText}>
                    <b>
                        Рахунок на оплату по замовленню №{orderNumber} від{' '}
                        {formatDate(orderDate, 'LL')}
                    </b>
                </div>
            </div>
            <div className={css.buyerSellerTables}>
                <table className={css.sellerTable}>
                    <colgroup>
                        <col width="20%" />
                        <col width="80%" />
                    </colgroup>
                    <tbody>
                        <tr>
                            <td>Постачальник:</td>
                            <td>
                                Товариство з обмеженою відповідальністю
                                "Хускварна Україна"
                            </td>
                        </tr>
                        <tr>
                            <td />
                            <td>
                                IBAN: ***************************** В АТ «СЕБ
                                КОРПОРАТИВНИЙ БАНК» (МФО 380797)
                            </td>
                        </tr>
                        <tr>
                            <td />
                            <td>
                                03022, Київ, вул. Васильківська, 34, офіс 204-Г,
                                тел.: (044) 498-39-02,
                            </td>
                        </tr>
                        <tr>
                            <td />
                            <td>
                                код за ЄДРПОУ 35039974, ІПН 350399710135, No
                                свід.100270023,
                            </td>
                        </tr>
                        <tr>
                            <td />
                            <td>
                                Є платником податку на прибуток на загальних
                                підставах
                            </td>
                        </tr>
                    </tbody>
                </table>
                <table className={css.buyerTable}>
                    <colgroup>
                        <col width="20%" />
                        <col width="80%" />
                    </colgroup>
                    <tbody>
                        <tr>
                            <td>Покупець:</td>
                            <td>
                                <b>{customerName.toUpperCase()}</b>
                            </td>
                        </tr>
                        <tr>
                            <td />
                            <td>
                                Адреса: {customerAddress} тел. {customerPhone}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div className={css.buyersOrder}>
                    <p>
                        Замовлення покупця: {orderNumber} від{' '}
                        {formatDate(orderDate)}р.
                    </p>
                    <p>Замовлення на склад: {warehouse}</p>
                </div>
            </div>
            <table className={css.goodsTable}>
                <colgroup>
                    <col width="5%" />
                    <col width="12%" />
                    <col width="25%" />
                    <col width="5%" />
                    <col width="5%" />
                    <col width="12%" />
                    <col width="12%" />
                    <col width="12%" />
                    <col width="12%" />
                </colgroup>
                <thead>
                    <tr>
                        <th>№</th>
                        <th>Артикул</th>
                        <th>Назва товару</th>
                        <th>Од. вим.</th>
                        <th>К-сть</th>
                        <th>Ціна без ПДВ</th>
                        <th>Знижка</th>
                        <th>Сума без ПДВ</th>
                        <th>ПДВ %</th>
                    </tr>
                </thead>
                <tbody>
                    {lines?.map((item) => (
                        <tr key={item.productCode}>
                            <td>{item.number}</td>
                            <td>{item.productCode}</td>
                            <td className={css.cellTextLeft}>
                                {item.productDescription}
                            </td>
                            <td>{item.units}</td>
                            <td>{item.qty}</td>
                            <td>{formatPrice(item.price)}</td>
                            <td>{formatPrice(item.discount)}</td>
                            <td>{formatPrice(item.lineTotal)}</td>
                            <td>{formatPrice(vat, 0)}%</td>
                        </tr>
                    ))}
                </tbody>
            </table>

            <div className={css.goodsTableFooterWrapper}>
                <table className={css.goodsTableFooter}>
                    <tbody>
                        <tr>
                            <td>
                                <b>Разом без ПДВ</b>
                            </td>
                            <td>
                                <b>{formatPrice(totalWithoutVat)}</b>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <b>ПДВ 20%</b>
                            </td>
                            <td>
                                <b>{formatPrice(totalVat)}</b>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <b>Всього з ПДВ</b>
                            </td>
                            <td>
                                <b>{formatPrice(totalWithVat)}</b>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div className={css.attentionBlock}>
                <p className={css.attentionBlockText}>
                    Увага! Оплата цього рахунку означає погодження з умовами
                    поставки товарів.Повідомлення про оплату є обов'язковим, в
                    іншому випадку не гарантується наявність товарів на складі.
                    Товар відпускається за фактом надходження коштів на р/р
                    Постачальника, самовивозом, за наявності довіреності та
                    паспорта.
                </p>
                <p className={css.attentionBlockText}>
                    Адреса 03022, Київ, вул. Васильківська, 34, офіс 204-Г{' '}
                    <br />
                    Телефон (044) 498-39-02
                </p>
            </div>
            {(renderSignatures || renderReviews) && (
                <div className={css.digitalSignaturesWrapper}>
                    <FooterRenderer
                        renderSignatures={renderSignatures}
                        renderReviews={renderReviews}
                        doc={doc}
                    />
                </div>
            )}
        </div>
    );
};

export default HusqvarnaDeliveryNote;
