import React, { Fragment } from 'react';

import PropTypes from 'prop-types';

import { formatPrice } from '../../../../lib/numbers';
import { XmlProducts } from '../../../../records/xml';
import uuid from '../../../../services/uuid';

import css from './transportationTable.css';

const TransportationTable = ({ extended, withVat, data }) => (
    <div className={css.root}>
        <table className={css.table}>
            <thead>
                <tr>
                    <th className={css.headerCellLarge}>Послуга</th>
                    {extended && (
                        <th className={css.headerCellLarge}>Маршрут</th>
                    )}
                    <th className={css.headerCellSmall}>Од. виміру</th>
                    <th className={css.headerCellSmall}>Кількість</th>
                    {extended && (
                        <th className={css.headerCell}>Дата відправки</th>
                    )}
                    {extended && (
                        <th className={css.headerCell}>Дата прибуття</th>
                    )}
                    <th className={css.headerCell}>Вартість, грн</th>
                    {withVat && (
                        <th className={css.headerCell}>Сума без ПДВ, грн</th>
                    )}
                    {withVat && (
                        <th className={css.headerCell}>Сума ПДВ, грн</th>
                    )}
                    <th className={css.headerCell}>Сума, грн</th>
                </tr>
            </thead>
            <tbody>
                {data.items.map((service) => (
                    <tr key={`services-${service.number || uuid()}`}>
                        <td className={css.cell}>{service.name}</td>
                        {extended && (
                            <td className={css.cell}>{service.route}</td>
                        )}
                        <td className={css.cellCenter}>{service.unit}</td>
                        <td className={css.cellRight}>{service.quantity}</td>
                        {extended && (
                            <td className={css.cellCenter}>
                                {service.departureDate}
                            </td>
                        )}
                        {extended && (
                            <td className={css.cellCenter}>
                                {service.arrivalDate}
                            </td>
                        )}
                        <td className={css.cellRight}>
                            {formatPrice(service.price)}
                        </td>
                        {withVat && (
                            <td className={css.cellRight}>
                                {formatPrice(service.sumWOVat)}
                            </td>
                        )}
                        {withVat && (
                            <td className={css.cellRight}>
                                {formatPrice(service.taxes)}
                            </td>
                        )}
                        <td className={css.cellRight}>
                            {formatPrice(service.sum)}
                        </td>
                    </tr>
                ))}
                {extended && (
                    <tr>
                        <td colSpan={3} className={css.cellRight}>
                            <b>Всього:</b>
                        </td>
                        <td className={css.cellRight}>{data.totalQuantity}</td>
                    </tr>
                )}
            </tbody>
        </table>
        <table className={css.totalTable}>
            <tbody>
                {withVat && (
                    <Fragment>
                        <tr>
                            <td className={css.totalName}>Сума без ПДВ:</td>
                            <td className={css.totalSum}>
                                {formatPrice(data.totalPrice)}
                            </td>
                        </tr>
                        <tr>
                            <td className={css.totalName}>Сума ПДВ:</td>
                            <td className={css.totalSum}>
                                {formatPrice(data.totalTaxes)}
                            </td>
                        </tr>
                    </Fragment>
                )}
                <tr>
                    <td className={css.totalName}>Сума всього:</td>
                    <td className={css.totalSum}>
                        {formatPrice(data.totalSum)}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
);

TransportationTable.propTypes = {
    extended: PropTypes.bool,
    withVat: PropTypes.bool,
    data: PropTypes.instanceOf(XmlProducts).isRequired,
};

export default TransportationTable;
