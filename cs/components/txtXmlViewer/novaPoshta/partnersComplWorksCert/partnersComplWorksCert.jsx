import React, { Fragment } from 'react';

import PropTypes from 'prop-types';

import { formatDate } from '../../../../lib/date';
import { XmlDocument } from '../../../../records/document';
import { XmlData } from '../../../../records/xml';
import FooterRenderer from '../../../footerRenderer/footerRenderer';
import { DATE_FORMAT_FULL } from '../constants';
import ServicesTable from '../servicesTable/servicesTable';

import css from './partnersComplWorksCert.css';

const PartnersComplWorksCert = ({
    data,
    doc,
    renderSignatures,
    renderReviews,
    withVat,
}) => (
    <div className={css.root}>
        <div className={css.title}>
            Акт приймання-передачі
            <br />
            наданих послуг
        </div>

        <div className={css.date}>
            {formatDate(data.date, DATE_FORMAT_FULL)} р.
        </div>

        <div className={css.paragraph}>
            <b>Замовник: {data.owner.name}</b>, в особі директора Бульби
            Олександра Миколайовича, який діє на підставі статуту, з одного боку
            та
        </div>
        <div className={css.paragraph}>
            <b>Партнер: {data.partner.fullName}</b>,
        </div>
        <div className={css.paragraph}>
            який діє на підставі державної реєстрації, з іншого боку, разом
            іменуються Сторони, а окремо – Сторона, склали і підписали цей акт
            приймання-передачі наданих послуг до Договору надання послуг №{' '}
            {data.agreementName} від {data.agreementDate} р. (далі – Договір)
            про таке:
        </div>
        <div className={css.paragraph}>
            Замовник зобов’язаний виплатити Партнеру винагороду за{' '}
            {data.extra.period} р. в розмірі {data.services.totalSum} грн.
        </div>

        <ServicesTable withVat={withVat} data={data.services} />

        <div className={css.totalString}>
            {withVat ? (
                <Fragment>
                    Загальна вартість робіт (послуг) склала без ПДВ{' '}
                    {data.services.totalPriceStr}, ПДВ{' '}
                    {data.services.totalTaxesStr}, загальна вартість робіт
                    (послуг) із ПДВ {data.services.totalSumStr}
                </Fragment>
            ) : (
                `Загальна вартість робіт (послуг) склала ${data.services.totalSumStr}`
            )}
        </div>
        <div className={css.paragraph}>
            Замовник до Партнера за якістю та обсягом наданих послуг претензій
            не має.
        </div>

        <div className={css.partiesRow}>
            <div className={css.partiesCell}>
                <div className={css.partyTitle}>ПАРТНЕР</div>
                <div>
                    <b>{data.partner.name}</b>
                </div>
                <div className={css.adressRow}>
                    <div className={css.adressCell}>Адреса:</div>
                    <div className={css.adressCell}>{data.partner.address}</div>
                </div>
                {data.partner.phone && <div>Тел: {data.partner.phone}</div>}
                <div>
                    {data.partner.edrpouType}: {data.partner.edrpou}
                </div>
                <div>Р/Р: {data.partner.bank.iban}</div>
                <div>
                    {data.partner.bank.name}, МФО: {data.partner.bank.mfo}
                </div>
            </div>
            <div className={css.partiesCell}>
                <div className={css.partyTitle}>ЗАМОВНИК</div>
                <div>
                    <b>{data.owner.name}</b>
                </div>
                <div className={css.adressRow}>
                    <div className={css.adressCell}>Адреса:</div>
                    <div className={css.adressCell}>{data.owner.address}</div>
                </div>
                <div>ІПН: {data.owner.ipn}</div>
                <div>ЄДРПОУ: {data.owner.edrpou}</div>
                <div>№ свід. {data.owner.certificate}</div>
                <div>Р/Р: {data.owner.bank.iban}</div>
                <div>
                    {data.owner.bank.name}, МФО: {data.owner.bank.mfo}
                </div>
                {data.owner.phone && <div>Тел: {data.owner.phone}</div>}
            </div>
        </div>

        <div className={css.partiesRow}>
            <div className={css.partiesSignCell}>
                <div className={css.signLine}>{data.extra.partnerSigner}</div>
                <div className={css.seal}>М.П.</div>
            </div>
            <div className={css.partiesSignCell}>
                {data.extra.signerPost}
                <div className={css.signLineRight}>{data.extra.signer}</div>
                <div className={css.seal}>М.П.</div>
            </div>
        </div>

        {(renderSignatures || renderReviews) && (
            <FooterRenderer
                renderSignatures={renderSignatures}
                renderReviews={renderReviews}
                doc={doc}
            />
        )}
    </div>
);

PartnersComplWorksCert.propTypes = {
    data: PropTypes.instanceOf(XmlData).isRequired,
    doc: PropTypes.instanceOf(XmlDocument).isRequired,
    renderSignatures: PropTypes.bool,
    renderReviews: PropTypes.bool,
    withVat: PropTypes.bool,
};

export default PartnersComplWorksCert;
