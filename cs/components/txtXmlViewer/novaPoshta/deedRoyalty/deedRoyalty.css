.table {
    width: 100%;
    margin: 1cm 0;
    font-size: 85%;
    font-weight: bold;
}

.table td:last-child {
    text-align: right;
}

.documentTitle {
    font-size: 110%;
    font-weight: bold;
    text-align: center;
}

.documentTrivia {
    font-size: 85%;
    line-height: 110%;
    text-align: justify;
    text-indent: 0.5cm;
}

.footerTable {
    width: 100%;
    margin-top: 1cm;
    font-size: 90%;
}

.footerTable thead th {
    width: 50%;
    padding: 0 0.25cm 0.25cm 0;
    font-weight: bold;
    text-align: left;
    vertical-align: top;
}

@media (min-width: 720px) {
    .footerTable thead th {
        padding-right: 1.25cm;
    }
}

.footerTable tbody {
    font-size: 80%;
}

.footerTable tbody td {
    padding: 0 0.25cm 0.25cm 0;
    vertical-align: top;
}

@media (min-width: 720px) {
    .footerTable tbody td {
        padding-right: 1.25cm;
    }
}

.signature {
    position: absolute;
    width: 20vw;
    height: 20vw;
    font-size: 80%;
    text-align: center;
}

@media (min-width: 21cm) {
    .signature {
        width: 5.25cm;
        height: 5.25cm;
    }
}

.place {
    margin-top: 0.5cm;
    font-size: 85%;
    line-height: 110%;
}

.list {
    margin-top: 0.5cm;
    counter-reset: list-item;
    font-size: 85%;
}

.list li {
    counter-increment: list-item;
}

.list li::before {
    content: counter(list-item) '. ';
}
