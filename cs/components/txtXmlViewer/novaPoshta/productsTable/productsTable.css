.table {
    width: 100%;
    margin-top: 0.1cm;
    margin-bottom: 0.1cm;
    border-collapse: collapse;
}

.table th {
    padding: 0.1cm 0;
    border: 0.075cm solid var(--content-color);
    background-color: #c2c2c1;
    font-size: 90%;
    font-weight: bold;
    text-align: center;
}

.table td {
    padding: 0.1cm;
    border: 0.075cm solid var(--content-color);
    font-size: 75%;
    vertical-align: top;
}

.number {
    width: 5%;
}

.name {
    width: 55%;
}

.quantity {
    width: 10%;
}

.item {
    width: 10%;
}

.price {
    width: 10%;
}

.sum {
    width: 10%;
}

.total {
    width: 100%;
    margin-top: 0.2cm;
    margin-bottom: 0.2cm;
    border-collapse: collapse;
}

.total td {
    padding-left: 10px;
    border: 0;
    font-size: 90%;
    font-weight: bold;
    text-align: right;
}

.total td:last-child {
    width: 2cm;
    border: 0.075cm solid var(--content-color);
}

.totalName {
    width: 90%;
}

.textCenter {
    text-align: center;
}

.textRight {
    text-align: right;
}
