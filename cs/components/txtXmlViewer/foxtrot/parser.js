import I from 'immutable';
import moment from 'moment';

import { parseTableData, textContent } from '../utils';

import {
    XmlBank,
    XmlCompany,
    XmlData,
    XmlProduct,
    XmlProducts,
} from '../../../records/xml';

const BaseDocument = new I.Record({
    type: null,
    number: null,
    date: null,
});

const XmlExtra = new I.Record({
    baseDocument: null,
    parameters: null,
});

export const parseXmlService = (serviceXml) => {
    const $ = (query, logEmptyText) =>
        textContent(serviceXml, query, logEmptyText);
    return new XmlProduct({
        number: $('НомПоз'),
        item: $('Штрихкод'),
        name: $('Найменування'),
        unit: $('ОдиницяВиміру'),
        quantity: $('ПрийнятаКількість'),
        claimedQuantity: $('ЗаявленаКількість'),
        priceWOVat: parseFloat($('БазоваЦіна')),
        price: parseFloat($('Ціна')),
        sumWOVat: parseFloat($('ВсьогоПоРядку > СумаБезПДВ')),
        sum: parseFloat($('ВсьогоПоРядку > Сума')),
        taxes: parseFloat($('ВсьогоПоРядку > СумаПДВ')),
    });
};

export const parseXmlServices = (xml, $) => {
    const serviceXmls = parseTableData(xml, 'Таблиця', 'Рядок');
    const items = new I.List(serviceXmls.map(parseXmlService));

    return new XmlProducts({
        items,
        totalSum: parseFloat($('ВсьогоПоДокументу > СумаБезПДВ')),
        totalDocumentSum: parseFloat($('ВсьогоПоДокументу > Сума')),
        totalTaxes: parseFloat($('ВсьогоПоДокументу > ПДВ')),
        totalQuantity: items.size,
    });
};

export const parseXmlCompany = (companyXml) => {
    const $ = (query, logEmptyText) =>
        textContent(companyXml, query, logEmptyText);
    const index = $('ЮрАдреса > Індекс');
    const city = $('ЮрАдреса > Місто');
    const street = $('ЮрАдреса > Вулиця');

    const addressFields = [index, city, street].filter((field) => field);

    return new XmlCompany({
        status: $('СтатусКонтрагента'),
        legalType: $('ВидОсоби'),
        name: $('НазваКонтрагента'),
        edrpou: $('КодКонтрагента'),
        ipn: $('ІПН'),
        phone: $('Телефон'),
        address: addressFields.join(', '),
        bank: new XmlBank({
            account: $('ПоточРах'),
            mfo: $('МФО'),
        }),
    });
};

export const parseXmlParties = (xml) => {
    const partiesXmls = parseTableData(xml, 'Сторони', 'Контрагент');
    return new I.List(partiesXmls.map(parseXmlCompany));
};

export const parseXmlExtra = (xml, $) => {
    const parameters = [];
    xml.querySelectorAll('Параметри > Параметр').forEach((param) =>
        parameters.push({
            name: param.attributes['назва'].value,
            value: param.textContent,
        }),
    );
    return new XmlExtra({
        baseDocument: new BaseDocument({
            number: $('Заголовок > ДокПідстава > НомерДокументу'),
            type: $('Заголовок > ДокПідстава > ТипДокументу'),
            subtype: $('Заголовок > ДокПідстава > КодТипуДокументу'),
            date: $('Заголовок > ДокПідстава > ДатаДокументу'),
        }),
        parameters,
    });
};

export const parseXml = (xml) => {
    const $ = (query, logEmptyText) => textContent(xml, query, logEmptyText);
    const date = $('Заголовок > ДатаДокументу').trim();

    return new XmlData({
        date: date && moment(date),
        number: $('Заголовок > НомерДокументу'),
        documentType: $('Заголовок > ТипДокументу'),
        kind: $('Заголовок > КодТипуДокументу'),
        parties: parseXmlParties(xml),
        services: parseXmlServices(xml, $),
        extra: parseXmlExtra(xml, $),
    });
};

export default parseXml;
