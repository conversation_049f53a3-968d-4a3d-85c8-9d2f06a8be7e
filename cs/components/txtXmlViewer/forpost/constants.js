export const DATE_FORMAT_FULL = 'DD MMMM YYYY';
export const NUMBER_REGEXP = new RegExp(/^(ЗК-)?(0+)(\d+)$/);

export const AGREEMENT_NAME_QUERY =
    'РеквизитыШапки > ДоговорКонтрагента > НаименованиеДляПечати';
export const CURRENCY_QUERY = 'РеквизитыШапки > ВалютаДокумента > Наименование';
export const DATE_QUERY = 'Дата';
export const DEAL_NAME_QUERY = 'РеквизитыШапки > Сделка';
export const NUMBER_QUERY = 'Номер';

export const OWNER_ADDRESS_QUERY = 'РеквизитыШапки > Организация > Адрес';
export const OWNER_EDRPOU_QUERY = 'РеквизитыШапки > Организация > КодПоЕДРПОУ';
export const OWNER_FULL_NAME_QUERY =
    'РеквизитыШапки > Организация > НаименованиеПолное';
export const OWNER_IPN_QUERY = 'РеквизитыШапки > Организация > ИНН';
export const OWNER_PHONE_QUERY = 'РеквизитыШапки > Организация > Телефон';
export const OWNER_REPRESENTATIVE_QUERY =
    'РеквизитыШапки > ПредставительОрганизации';
export const OWNER_REPRESENTATIVE_FALLBACK_QUERY =
    'РеквизитыШапки > ФИОРуководителяОрганизации';
export const OWNER_REPRESENTATIVE_POSITION_QUERY =
    'РеквизитыШапки > ДолжностьРуководителяОрганизации';

export const OWNER_BANK_ACCOUNT_QUERY =
    'РеквизитыШапки > БанковскийСчетОрганизации > НомерСчета';
export const OWNER_BANK_ACCOUNT_FALLBACK_QUERY =
    'РеквизитыШапки > БанковскийСчетОрганизацииНомерСчета';
export const OWNER_BANK_MFO_QUERY =
    'РеквизитыШапки > БанковскийСчетОрганизации > Банк > Код';
export const OWNER_BANK_MFO_FALLBACK_QUERY =
    'РеквизитыШапки > БанковскийСчетОрганизацииБанкМФО';
export const OWNER_BANK_NAME_QUERY =
    'РеквизитыШапки > БанковскийСчетОрганизации > Банк > Наименование';
export const OWNER_BANK_NAME_FALLBACK_QUERY =
    'РеквизитыШапки > БанковскийСчетОрганизацииБанкНаименование';

export const PARTNER_BANK_ACCOUNT_QUERY =
    'РеквизитыШапки > СчетКонтрагента > НомерСчета';
export const PARTNER_BANK_ACCOUNT_FALLBACK_QUERY =
    'РеквизитыШапки > СтруктурнаяЕдиница[Вид=БанковскиеСчета] > НомерСчета';
export const PARTNER_BANK_MFO_QUERY =
    'РеквизитыШапки > СчетКонтрагента > Банк > Код';
export const PARTNER_BANK_MFO_FALLBACK_QUERY =
    'РеквизитыШапки > СтруктурнаяЕдиница[Вид=БанковскиеСчета] > Банк > Код';
export const PARTNER_BANK_NAME_QUERY =
    'РеквизитыШапки > СчетКонтрагента > Банк > Наименование';
export const PARTNER_BANK_NAME_FALLBACK_QUERY =
    'РеквизитыШапки > СтруктурнаяЕдиница[Вид=БанковскиеСчета] > Банк > Наименование';

export const PARTNER_ADDRESS_QUERY = 'РеквизитыШапки > Контрагент > Адрес';
export const PARTNER_EDRPOU_QUERY = 'РеквизитыШапки > Контрагент > КодПоЕДРПОУ';
export const PARTNER_FULL_NAME_QUERY =
    'РеквизитыШапки > Контрагент > НаименованиеПолное';
export const PARTNER_IPN_QUERY = 'РеквизитыШапки > Контрагент > ИНН';
export const PARTNER_PHONE_QUERY = 'РеквизитыШапки > Контрагент > Телефон';
export const PARTNER_REPRESENTATIVE_QUERY = 'РеквизитыШапки > Получил';
export const PARTNER_REPRESENTATIVE_FALLBACK_QUERY =
    'РеквизитыШапки > ФИОРуководителяКонтрагента';
export const PARTNER_REPRESENTATIVE_POSITION_QUERY =
    'РеквизитыШапки > ДолжностьРуководителяКонтрагента';

export const IS_TAXES_INCLUDED_QUERY = 'РеквизитыШапки > СуммаВключаетНДС';
export const TOTAL_DOCUMENT_SUM_QUERY = 'РеквизитыШапки > СуммаДокумента';
export const TOTAL_PRICE_STR_QUERY = 'РеквизитыШапки > СуммаБезНДСПрописью';
export const TOTAL_SUM_STR_QUERY = 'РеквизитыШапки > СуммаСНДСПрописью';
export const TOTAL_SUM_STR_FALLBACK_QUERY =
    'РеквизитыШапки > СуммаДокументаПрописью';
export const TOTAL_TAXES_STR_QUERY = 'РеквизитыШапки > СуммаНДСПрописью';
export const TOTAL_FEE_SUM_QUERY = 'РеквизитыШапки > СуммаВознаграждения';
export const TOTAL_OUTPUT_SUM_QUERY =
    'РеквизитыШапки > СуммаПеречисленныхСредств';

export const SERVICES_QUERY = 'РеквизитыТабличнойЧасти_Услуги';
export const SERVICE_ITEM_QUERY =
    'Номенклатура > ТекстДляПечатиВКолонкеКоличествоНалоговойНакладной';
export const SERVICE_NAME_QUERY = 'Содержание';
export const SERVICE_NUMBER_QUERY = 'НомерСтроки';
export const SERVICE_PRICE_QUERY = 'Цена';
export const SERVICE_QUANTITY_QUERY = 'Количество';
export const SERVICE_SUM_QUERY = 'Сумма';
export const SERVICE_TAXES_QUERY = 'СуммаНДС';

export const GOODS_QUERY = 'РеквизитыТабличнойЧасти_Товары';
export const GOODS_ITEM_QUERY =
    'Номенклатура > БазоваяЕдиницаИзмерения > Наименование';
export const GOODS_NAME_QUERY = 'Номенклатура > Наименование';
export const GOODS_NUMBER_QUERY = 'НомерСтроки';
export const GOODS_PRICE_QUERY = 'Цена';
export const GOODS_QUANTITY_QUERY = 'Количество';
export const GOODS_SUM_QUERY = 'Сумма';
export const GOODS_TAXES_QUERY = 'СуммаНДС';

export const TRANSFER_QUERY = 'РеквизитыТабличнойЧасти_ADD_ТЧРеестрыРПКСводный';
export const TRANSFER_DATE_QUERY = 'ДатаПеречисленияСредств';
export const TRANSFER_NUMBER_QUERY = 'ДатаПеречисленияСредств';
export const TRANSFER_INPUT_SUM_QUERY = 'СуммаПринятыхСредств';
export const TRANSFER_FEE_QUERY = 'СуммаВознаграждения';
export const TRANSFER_SUM_QUERY = 'СуммаПеречисленныхСредств';
export const TRANSFER_SERVICE_QUERY = 'Сервис';
export const TRANSFER_BUYER_QUERY = 'ФИОПокупателя';
export const TRANSFER_INVOICE_QUERY = 'НомерЕН_НП';
export const TRANSFER_ORDER_QUERY = 'НомерЗаказа';

export default {
    DATE_FORMAT_FULL,
    NUMBER_REGEXP,

    AGREEMENT_NAME_QUERY,
    CURRENCY_QUERY,
    DATE_QUERY,
    DEAL_NAME_QUERY,
    NUMBER_QUERY,

    OWNER_ADDRESS_QUERY,
    OWNER_EDRPOU_QUERY,
    OWNER_FULL_NAME_QUERY,
    OWNER_IPN_QUERY,
    OWNER_PHONE_QUERY,
    OWNER_REPRESENTATIVE_QUERY,
    OWNER_REPRESENTATIVE_FALLBACK_QUERY,
    OWNER_REPRESENTATIVE_POSITION_QUERY,

    OWNER_BANK_ACCOUNT_QUERY,
    OWNER_BANK_ACCOUNT_FALLBACK_QUERY,
    OWNER_BANK_MFO_QUERY,
    OWNER_BANK_MFO_FALLBACK_QUERY,
    OWNER_BANK_NAME_QUERY,
    OWNER_BANK_NAME_FALLBACK_QUERY,

    PARTNER_BANK_ACCOUNT_QUERY,
    PARTNER_BANK_ACCOUNT_FALLBACK_QUERY,
    PARTNER_BANK_MFO_QUERY,
    PARTNER_BANK_MFO_FALLBACK_QUERY,
    PARTNER_BANK_NAME_QUERY,
    PARTNER_BANK_NAME_FALLBACK_QUERY,

    PARTNER_ADDRESS_QUERY,
    PARTNER_EDRPOU_QUERY,
    PARTNER_FULL_NAME_QUERY,
    PARTNER_IPN_QUERY,
    PARTNER_PHONE_QUERY,
    PARTNER_REPRESENTATIVE_QUERY,
    PARTNER_REPRESENTATIVE_FALLBACK_QUERY,
    PARTNER_REPRESENTATIVE_POSITION_QUERY,

    IS_TAXES_INCLUDED_QUERY,
    SERVICES_QUERY,
    GOODS_QUERY,
    TRANSFER_QUERY,
    TOTAL_DOCUMENT_SUM_QUERY,
    TOTAL_PRICE_STR_QUERY,
    TOTAL_SUM_STR_QUERY,
    TOTAL_SUM_STR_FALLBACK_QUERY,
    TOTAL_TAXES_STR_QUERY,
    TOTAL_FEE_SUM_QUERY,
    TOTAL_OUTPUT_SUM_QUERY,

    SERVICE_ITEM_QUERY,
    SERVICE_NAME_QUERY,
    SERVICE_NUMBER_QUERY,
    SERVICE_PRICE_QUERY,
    SERVICE_QUANTITY_QUERY,
    SERVICE_SUM_QUERY,
    SERVICE_TAXES_QUERY,

    GOODS_ITEM_QUERY,
    GOODS_NAME_QUERY,
    GOODS_NUMBER_QUERY,
    GOODS_PRICE_QUERY,
    GOODS_QUANTITY_QUERY,
    GOODS_SUM_QUERY,
    GOODS_TAXES_QUERY,

    TRANSFER_DATE_QUERY,
    TRANSFER_NUMBER_QUERY,
    TRANSFER_INPUT_SUM_QUERY,
    TRANSFER_FEE_QUERY,
    TRANSFER_SUM_QUERY,
    TRANSFER_SERVICE_QUERY,
    TRANSFER_BUYER_QUERY,
    TRANSFER_INVOICE_QUERY,
    TRANSFER_ORDER_QUERY,
};
