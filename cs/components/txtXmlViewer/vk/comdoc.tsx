import React, { FC, PropsWithChildren } from 'react';

import { Props } from '../Comdoc/types';

import { capitalizeFirstLetter } from '../../../lib/helpers';
import { generateUATextMoneyRepresentation } from '../utils';

import SignLine from '../ui/signLine/signLine';

import { formatDate } from '../../../lib/date';
import { formatPrice } from '../../../lib/numbers';
import uuid from '../../../services/uuid';
import { DATE_FORMAT_FULL } from '../Comdoc/constants';
import { coalesceJoin } from '../Comdoc/helper';

import css from '../Comdoc/Comdoc.css';

// eslint-disable-next-line complexity
const VKComdoc: FC<PropsWithChildren<Props>> = (props) => {
    const {
        date,
        number,
        owner,
        partner,
        services,
        agreementName,
        agreementDate,
        agreementPlace,
        parentType,
        parentNumber,
        parentDate,
        parentOrderNumber,
        parentOrderDate,
        parentOrderDatePayment,
        documentType,
        fromPerformer,
        deliveryAddress,
    } = props.data;

    const totalAmountString = !services?.totalDocumentSum
        ? undefined
        : capitalizeFirstLetter(
              generateUATextMoneyRepresentation(services?.totalDocumentSum),
          );

    return (
        <div>
            <h1 className={css.documentTitle}>
                {documentType} № {number} від{' '}
                {formatDate(date, DATE_FORMAT_FULL)} р.
            </h1>
            <table className={css.headerTable}>
                <tbody>
                    <tr>
                        <td>{partner?.status}:</td>
                        <td>
                            {coalesceJoin(
                                <b>{partner?.fullName}</b>,
                                partner?.edrpou && (
                                    <>код ЄДРПОУ {partner?.edrpou}</>
                                ),
                                partner?.ipn && <>IПН {partner?.ipn}</>,
                                partner?.iban && <>IBAN {partner?.iban}</>,
                                partner?.bank?.account && (
                                    <>П/р {partner?.bank?.account}</>
                                ),
                                partner?.bank?.mfo && (
                                    <>МФО {partner?.bank?.mfo}</>
                                ),
                            )}
                        </td>
                    </tr>
                    <tr>
                        <td>{owner?.status}:</td>
                        <td>
                            {coalesceJoin(
                                <b>{owner?.fullName}</b>,
                                owner?.edrpou && (
                                    <>код ЄДРПОУ {owner?.edrpou}</>
                                ),
                                owner?.ipn && <>IПН {owner?.ipn}</>,
                                owner?.iban && <>IBAN {owner?.iban}</>,
                                owner?.bank?.account && (
                                    <>П/р {owner?.bank?.account}</>
                                ),
                                owner?.bank?.mfo && <>МФО {owner?.bank?.mfo}</>,
                            )}
                        </td>
                    </tr>
                    <tr>
                        <td>Договір:</td>
                        <td>
                            {agreementName} від {formatDate(agreementDate)}
                        </td>
                    </tr>
                    {parentOrderNumber && (
                        <tr>
                            <td>Замовлення:</td>
                            <td>
                                Замовлення покупця № {parentOrderNumber}{' '}
                                {parentOrderDate && `від ${parentOrderDate}`}
                            </td>
                        </tr>
                    )}
                    {deliveryAddress && (
                        <tr>
                            <td>Адреса доставки:</td>
                            <td>{deliveryAddress}</td>
                        </tr>
                    )}
                    <tr>
                        <td>Документ підстава:</td>
                        <td>
                            {parentType} № {parentNumber} від{' '}
                            {formatDate(parentDate)}
                        </td>
                    </tr>
                    {parentOrderDatePayment && (
                        <tr>
                            <td>Дата оплати:</td>
                            <td>{parentOrderDatePayment}</td>
                        </tr>
                    )}
                    {agreementPlace && (
                        <tr>
                            <td>Місце складання:</td>
                            <td>{agreementPlace}</td>
                        </tr>
                    )}
                </tbody>
            </table>
            <table className={css.goodsTable}>
                <thead>
                    <tr>
                        <th className={css.number}>№</th>
                        <th>Артикул</th>
                        <th className={css.name}>Товар</th>
                        {props.inconsistencyAct && (
                            <th className={css.number}>
                                Кількість по накладній
                            </th>
                        )}
                        <th className={css.quantity}>
                            {props.quantityColumnName || 'К-сть'}
                        </th>
                        {props.notAcceptedQuantity && (
                            <th className={css.quantity}>
                                Не прийнята кількість
                            </th>
                        )}
                        {props.inconsistencyAct && (
                            <th className={css.quantity}>Розбіжність</th>
                        )}
                        {props.inconsistencyAct && (
                            <th className={css.item}>Причина</th>
                        )}
                        <th className={css.item}>Од.</th>
                        <th className={css.price}>Ціна без ПДВ</th>
                        <th className={css.sum}>Сума без ПДВ</th>
                    </tr>
                </thead>
                <tbody>
                    {services?.items?.map((service, index) => (
                        <tr key={`product-${uuid()}`}>
                            <td className={css.textCenter}>{index + 1}</td>
                            <td>{service.code}</td>
                            <td>{service.name}</td>
                            {props.inconsistencyAct && (
                                <td>{service.declaredQuantity}</td>
                            )}
                            <td className={css.textRight}>
                                {service.declaredQuantity
                                    ? service.declaredQuantity
                                    : service.quantity}
                            </td>
                            {props.notAcceptedQuantity && (
                                <td className={css.textRight}>
                                    {service.notAcceptedQuantity}
                                </td>
                            )}
                            {props.inconsistencyAct && (
                                <td className={css.textRight}>
                                    {service.notAcceptedQuantity}
                                </td>
                            )}
                            {props.inconsistencyAct && (
                                <td className={css.textRight}>
                                    {service.reason}
                                </td>
                            )}
                            <td className={css.textRight}>{service.unit}</td>
                            <td className={css.textRight}>
                                {formatPrice(service.price)}
                            </td>
                            <td className={css.textRight}>
                                {formatPrice(service.sum)}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
            <table className={css.totalTable}>
                <tbody>
                    <tr>
                        <td className={css.totalName}>Разом:</td>
                        <td>{formatPrice(services?.totalSum)}</td>
                    </tr>
                    <tr>
                        <td className={css.totalName}>Сума ПДВ:</td>
                        <td>{formatPrice(services?.totalTaxes)}</td>
                    </tr>
                    <tr>
                        <td className={css.totalName}>Усього з ПДВ:</td>
                        <td>{formatPrice(services?.totalDocumentSum)}</td>
                    </tr>
                </tbody>
            </table>
            <div className={css.amountText}>
                Всього на суму {formatPrice(services?.totalDocumentSum)} грн{' '}
                {totalAmountString ? `(${totalAmountString})` : ''}
            </div>

            {props.inconsistencyAct && (
                <div className={css.place}>
                    Акт є невід'ємною частиною накладної № {parentNumber} від{' '}
                    {formatDate(parentDate)}
                </div>
            )}

            <SignLine full />
            <table className={css.footerTable}>
                <tbody>
                    {props.inconsistencyAct && (
                        <>
                            <tr>
                                <td>{partner?.fullName}</td>
                                <td>{owner?.fullName}</td>
                            </tr>
                            <tr>
                                {partner?.edrpou && (
                                    <td>код ЄДРПОУ {partner?.edrpou}</td>
                                )}
                                {owner?.edrpou && (
                                    <td>код ЄДРПОУ {owner?.edrpou}</td>
                                )}
                            </tr>
                        </>
                    )}
                    <tr>
                        <td>
                            * Відповідальний за здійснення господарської
                            операції і правильність її оформлення
                        </td>
                        <td />
                    </tr>
                    {fromPerformer && (
                        <>
                            <tr>
                                <br />
                            </tr>
                            <tr>
                                <td>Від виконавця: {fromPerformer}</td>
                                <td />
                            </tr>
                        </>
                    )}
                </tbody>
            </table>

            <table className={css.footerTable}>
                <thead>
                    <tr>
                        <th>
                            Від Продавця*
                            <br />
                        </th>
                        <th>
                            Отримав(ла)
                            <br />
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    );
};

export default VKComdoc;
