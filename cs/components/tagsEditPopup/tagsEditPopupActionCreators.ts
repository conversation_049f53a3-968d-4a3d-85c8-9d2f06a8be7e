import { Thunk } from '../../types';

import documentActionCreators from '../document/documentActionCreators';
import documentListActionCreators from '../documentList/documentListActionCreators';
import filtersActionCreators from '../filters/filtersActionCreators';
import actions from './tagsEditPopupActions';

import {
    isTagAlreadyInList,
    isTagNameAlreadyInList,
    splitArrayIntoChunks,
} from '../../lib/helpers';

import { ITag } from '../ui/tags/tagsTypes';

import eventTracking from '../../services/analytics/eventTracking';
import {
    connectDocumentsAndTags,
    createDocumentTags,
    disconnectDocumentsAndTags,
} from '../../services/documents/api';
import { getCurrentCompanyTags } from '../../services/user';
import { chunkSize } from './constants';

export function onOpenPopup(
    docIds: string[],
    tags: ITag[],
    title = '',
    isDocumentPage = false,
): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.TAGS_EDIT_POPUP__OPEN,
            isDocumentPage,
            tags,
            title,
            docIds,
        });
    };
}

export function onAutosuggestTags(search = ''): Thunk {
    return async (dispatch, getState) => {
        const {
            tagsEditPopup: { allTags },
        } = getState();
        // not to repeat request for all tags with search = ''
        if (!search && allTags.length) {
            dispatch({
                type: actions.TAGS_EDIT_POPUP__CHANGE_SUGGESTION_TAGS,
                isSuggestionsShown: true,
                suggestedTags: allTags,
            });
            return;
        }

        const suggestedTags: ITag[] = await getCurrentCompanyTags({ search });
        dispatch({
            type: actions.TAGS_EDIT_POPUP__CHANGE_SUGGESTION_TAGS,
            isSuggestionsShown: true,
            suggestedTags,
            allTags: !search && suggestedTags,
        });
    };
}

export function onCloseSuggestions(): Thunk {
    return (dispatch) => {
        dispatch({
            type: actions.TAGS_EDIT_POPUP__CHANGE_SUGGESTION_TAGS,
            suggestedTags: [],
            isSuggestionsShown: false,
        });
    };
}

export function onSuggestionClick(suggestion: ITag): Thunk {
    return (dispatch, getState) => {
        const {
            tagsEditPopup: { selectedTags = [], tags = [] },
        } = getState();
        const updatedSelectedTags:
            | ITag[]
            | undefined = isTagAlreadyInList(suggestion.id, [
            ...selectedTags,
            ...tags,
        ])
            ? selectedTags
            : [...selectedTags, suggestion];

        dispatch({
            type: actions.TAGS_EDIT_POPUP__CHANGE_SELECTED_LIST,
            selectedTags: updatedSelectedTags,
        });
        dispatch(onCloseSuggestions());
    };
}

export function onDeleteTagFromList({ id, name }: ITag): Thunk {
    return (dispatch, getState) => {
        const {
            tagsEditPopup: { newTags, selectedTags, tagsToDelete = [], tags },
        } = getState();
        const isSelectedTag: ITag | undefined =
            selectedTags && selectedTags.find((item: ITag) => item.id === id);
        if (isSelectedTag) {
            const updatedSelectedTags: ITag[] | undefined =
                selectedTags &&
                selectedTags.filter((item: ITag) => item.id !== id);

            dispatch({
                type: actions.TAGS_EDIT_POPUP__CHANGE_SELECTED_LIST,
                selectedTags: updatedSelectedTags,
            });
        } else if (!id && newTags) {
            dispatch({
                type: actions.TAGS_EDIT_POPUP__CHANGE_NEW_TAGS,
                newTags:
                    newTags && newTags.filter((item) => item.name !== name),
            });
        } else if (tags) {
            dispatch({
                type: actions.TAGS_EDIT_POPUP__CHANGE_TAGS_TO_DELETE,
                tagsToDelete: [...tagsToDelete, { id, name }],
                selectedTags:
                    selectedTags &&
                    selectedTags.filter((item: ITag) => item.id !== id),
                tags: tags.filter((item: ITag) => item.id !== id),
            });
        }
    };
}

export function onClose(): Thunk {
    return (dispatch) => {
        dispatch({ type: actions.TAGS_EDIT_POPUP__CLOSE });
    };
}

export function onAddNewTag(value: string): Thunk {
    return (dispatch, getState) => {
        const {
            tagsEditPopup: { newTags = [], selectedTags = [], tags = [] },
        } = getState();
        if (value) {
            dispatch({
                type: actions.TAGS_EDIT_POPUP__CHANGE_NEW_TAGS,
                newTags: isTagNameAlreadyInList(value, [
                    ...selectedTags,
                    ...tags,
                    ...newTags,
                ])
                    ? newTags
                    : [...newTags, { id: '', name: value, canAssign: true }],
            });
        }
        dispatch(onCloseSuggestions());
    };
}

export function onSubmit(): Thunk {
    return async (dispatch, getState) => {
        const {
            tagsEditPopup: {
                isDocumentPage,
                newTags,
                selectedTags,
                tagsToDelete,
                docIds,
            },
        } = getState();
        let needToReloadTags = false;

        dispatch({ type: actions.TAGS_EDIT_POPUP__START_SUBMIT });

        const docIdsChunks = splitArrayIntoChunks(docIds, chunkSize);

        try {
            if (newTags && newTags.length) {
                const createRequests = [];
                for (const chunk of docIdsChunks) {
                    createRequests.push(
                        createDocumentTags({
                            names: newTags.map((t) => t.name),
                            documents_ids: chunk,
                        }),
                    );
                }

                await Promise.all(createRequests);

                needToReloadTags = true;
            }

            if (selectedTags && selectedTags.length) {
                const connectRequests = [];
                for (const chunk of docIdsChunks) {
                    connectRequests.push(
                        connectDocumentsAndTags({
                            documents_ids: chunk,
                            tags_ids: selectedTags.map((t) => t.id),
                        }),
                    );
                }

                await Promise.all(connectRequests);

                const newTagsLength = newTags ? newTags.length : 0;
                eventTracking.sendEvent(
                    'tag',
                    'add_tag',
                    `${newTagsLength + selectedTags.length}`,
                );
            }
            if (tagsToDelete && tagsToDelete.length) {
                const disconnectRequests = [];
                for (const chunk of docIdsChunks) {
                    disconnectRequests.push(
                        disconnectDocumentsAndTags({
                            documents_ids: chunk,
                            tags_ids: tagsToDelete.map((t) => t.id),
                        }),
                    );
                }

                await Promise.all(disconnectRequests);

                needToReloadTags = true;
                eventTracking.sendEvent(
                    'tag',
                    'delete',
                    `${tagsToDelete.length}`,
                );
            }

            // Reload tags in the filters
            if (needToReloadTags) {
                await filtersActionCreators.onReloadTags(true)(
                    dispatch,
                    getState,
                );
            }
        } catch (err) {
            dispatch({
                type: actions.TAGS_EDIT_POPUP__SHOW_ERROR_MESSAGE,
                errorMessage: err.reason,
            });
            return;
        } finally {
            if (isDocumentPage) {
                documentActionCreators.onLoadDocument(docIds[0], false)(
                    dispatch,
                    getState,
                );
            } else {
                documentListActionCreators.onLoadDocuments()(
                    dispatch,
                    getState,
                );
            }
        }
        dispatch(onClose());
    };
}
