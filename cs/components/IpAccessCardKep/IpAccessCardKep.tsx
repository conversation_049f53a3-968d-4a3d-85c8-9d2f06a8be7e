import React, { ChangeEvent, FormEvent } from 'react';

import { Button } from '@vchasno/ui-kit';

import { t } from 'ttag';

import Input from '../ui/input/input';
import List from '../ui/list/list';
import Message from '../ui/message/message';

// styles
import css from './IpAccessCardKep.css';

interface Props {
    onAddApiIps: () => void;
    newApiIps?: string;
    apiIpErrorMessage?: string;
    onNewApiIpsChange: (value: string) => void;
    onDeleteApiIp: (apiIpToDelete: string) => void;
    apiIps: Set<string>;
}

const IpAccessCardKep: React.FC<React.PropsWithChildren<Props>> = (props) => {
    const handleSubmit = (evt: FormEvent) => {
        evt.preventDefault();
        props.onAddApiIps();
    };
    return (
        <div className={css.root}>
            <form className={css.cell} onSubmit={handleSubmit}>
                <div className={css.field}>
                    <div className={css.input}>
                        <Input
                            type="text"
                            placeholder={t`Вкажіть IP адреси, розділивши їх комами`}
                            value={props.newApiIps}
                            onChange={(evt: ChangeEvent<HTMLInputElement>) =>
                                props.onNewApiIpsChange(evt.target.value)
                            }
                        />
                    </div>
                    <div className={css.cell}>
                        <Button type="submit" theme="secondary" size="lg">
                            {t`Зберегти`}
                        </Button>
                    </div>
                </div>
                {props.apiIpErrorMessage && (
                    <div className={css.error}>
                        <Message sizeSmall type="error">
                            {props.apiIpErrorMessage}
                        </Message>
                    </div>
                )}
                <div className={css.list}>
                    {props.apiIps.size > 0 && (
                        <div>
                            <List
                                items={Array.from(props.apiIps).map((item) => ({
                                    label: item,
                                }))}
                                onDelete={props.onDeleteApiIp}
                            />
                        </div>
                    )}
                </div>
            </form>
            <div className={css.cell}>
                <div className={css.info}>
                    <div className={css.infoTitle}>
                        {t`Вкажіть ті IP адреси, з котрих ваша компанія зможе направляти запити на авторизацію та
                         підписання документів хмарним підписом "Вчасно.КЕП".
                         Ви можете вказати декілька IP адрес, розділивши їх комами`}
                        .
                    </div>
                    <div className={css.infoParagraph}>
                        {t`IP адреса може бути постійною або змінною`}.
                    </div>
                    <div className={css.infoParagraph}>
                        -{' '}
                        {t`Якщо IP адреса постійна - просто вкажіть її у полі`}
                    </div>
                    <div className={css.infoParagraph}>
                        -{' '}
                        {t`Якщо IP адреса змінна – вкажіть ту частину адреси, що буде повторюватись у форматі XXX*`}
                        <div className={css.hint}>
                            {t`Наприклад, якщо ви вкажете 111* - ви дозволите доступ тільки з тих IP адрес,
                            що починаються на 111`}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default IpAccessCardKep;
