import { useEffect, useRef, useState } from 'react';

import { getSocketInstanceFromCollaboraIframe } from 'components/document/edit/DocumentEditor/utils';

export const useSocketHandler = (handler: (event: MessageEvent) => void) => {
    const [socket, setSocket] = useState<WebSocket | null>(null);

    const onReadyState = (iframeRef: React.RefObject<HTMLIFrameElement>) => {
        if (!iframeRef.current) {
            return;
        }
        setSocket(getSocketInstanceFromCollaboraIframe(iframeRef.current));
    };

    const handlerRef = useRef(handler);

    useEffect(() => {
        handlerRef.current = handler;
    }, [handler]);

    useEffect(() => {
        if (!socket) return;

        const handleEvent = (event: MessageEvent) => {
            handlerRef.current(event);
        };

        socket.addEventListener('message', handleEvent);

        return function cleanup() {
            socket.removeEventListener('message', handleEvent);
        };
    }, [socket]);

    return { setSocket, onReadyState } as const;
};
