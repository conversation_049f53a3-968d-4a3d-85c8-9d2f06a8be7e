import { useInfiniteQuery } from '@tanstack/react-query';

import {
    CACHE_TIME_MS,
    GET_ARCHIVE_DIRECTORY_LIST_INFINITY_QUERY,
} from 'lib/queriesConstants';
import { getAllDirectories } from 'services/directories';

export const useAllDirectoriesInfinityQuery = (
    args: {
        parentDirectoryId?: Nullable<number>;
        search?: string;
        limit?: number;
        offset?: number;
    },
    isEnabled = false,
) => {
    return useInfiniteQuery({
        queryFn: ({ pageParam = args }) => getAllDirectories(pageParam),
        enabled: isEnabled,
        queryKey: [GET_ARCHIVE_DIRECTORY_LIST_INFINITY_QUERY, args],
        staleTime: CACHE_TIME_MS,
        getNextPageParam: (lastPage, allPages) => {
            const requestedCount = allPages.reduce(
                (acc, page) => acc + page.directories.length,
                0,
            );

            if (requestedCount >= lastPage.count) {
                return undefined;
            }

            return {
                offset: requestedCount,
            };
        },
    });
};
