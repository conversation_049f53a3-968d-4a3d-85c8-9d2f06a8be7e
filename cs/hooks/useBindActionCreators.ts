import { useMemo } from 'react';

import { bindActionCreators } from 'redux';

import { useThunkDispatch } from '../store/hooks';

export const useBindActionCreators = <
    T extends Parameters<typeof bindActionCreators>[0]
>(
    actionsCreators: T,
) => {
    const dispatch = useThunkDispatch();

    const actions = useMemo(
        () => bindActionCreators(actionsCreators, dispatch),
        [dispatch],
    );

    return actions;
};
