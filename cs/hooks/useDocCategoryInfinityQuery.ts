import { useInfiniteQuery } from '@tanstack/react-query';

import {
    CACHE_TIME_MS,
    GET_DOC_CATEGORY_LIST_INFINITY_QUERY,
} from 'lib/queriesConstants';
import { getDocCategories } from 'services/documentCategories/api';
import { GetDocumentCategoriesArgs } from 'services/documentCategories/types';

type DocCategoryInfinityQuery = Pick<
    GetDocumentCategoriesArgs,
    'onlyPublic' | 'onlyInternal'
>;

export const useDocCategoryInfinityQuery = (
    options: DocCategoryInfinityQuery = {},
) => {
    const args: Partial<GetDocumentCategoriesArgs> = {
        onlyInternal: options.onlyInternal || false,
        onlyPublic: options.onlyPublic || false,
    };

    // @see <https://tanstack.com/query/latest/docs/framework/react/guides/infinite-queries>
    return useInfiniteQuery({
        queryFn: ({ pageParam = args }) => getDocCategories(pageParam),
        staleTime: CACHE_TIME_MS,
        queryKey: [GET_DOC_CATEGORY_LIST_INFINITY_QUERY, args],
        getNextPageParam: ({ count }, allPages) => {
            const requestedCount = allPages.reduce(
                (acc, page) => acc + page.documentCategories.length,
                0,
            );

            if (requestedCount >= count) {
                return undefined;
            }

            return {
                offset: requestedCount,
            };
        },
    });
};
