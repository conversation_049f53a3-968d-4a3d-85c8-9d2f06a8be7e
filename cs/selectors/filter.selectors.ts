import { getCurrentSignFolders } from 'components/filters/utils';
import { QUERY_DATE_FORMAT } from 'lib/constants';
import { toDate } from 'lib/date';
import { createSelector } from 'reselect';

import { Nullable } from '../types/general';
import { StoreState } from '../types/store';

import { getLocationQueryParams } from './router.selectors';

export const getSlice = (state: StoreState) => state.filters;

export const getFiltersIsLoadingStatus = (state: StoreState) =>
    state.filters.isLoading;

export const getFiltersAmountErrors = (state: StoreState) => ({
    from: state.filters.amountGteError,
    to: state.filters.amountLteError,
});
export const getFiltersAmountGte = (state: StoreState) =>
    state.filters.amountGte || '';

export const getFiltersAmountLte = (state: StoreState) =>
    state.filters.amountLte || '';

export const getFiltersQueryDocAmountGte = createSelector(
    getLocationQueryParams,
    (query) => query.amount_gte || '',
);

export const getFiltersQueryDocAmountLte = createSelector(
    getLocationQueryParams,
    (query) => query.amount_lte || '',
);

export const getFiltersFromDate = createSelector(
    getLocationQueryParams,
    (query) =>
        query.date_from ? toDate(query.date_from, QUERY_DATE_FORMAT) : null,
);

export const getFiltersToDate = createSelector(
    getLocationQueryParams,
    (query) =>
        query.date_to ? toDate(query.date_to, QUERY_DATE_FORMAT) : null,
);

export const getFiltersDocumentCategories = createSelector(
    getLocationQueryParams,
    (query) => query.categories || null,
);

export const getIsWaitMySignFilter = createSelector(
    getLocationQueryParams,
    (query) => query.wait_my_sign === 'true',
);

export const getIsMyInvalidSigned = createSelector(
    getLocationQueryParams,
    (query) => query.is_my_invalid_signed === 'true',
);

export const getIsPrivateAccess = createSelector(
    getLocationQueryParams,
    (query) => query.is_private_access === 'true',
);

export const getIsPartnerInvalidSigned = createSelector(
    getLocationQueryParams,
    (query) => query.is_partner_invalid_signed === 'true',
);

export const getHasCommentsFilter = createSelector(
    getLocationQueryParams,
    (query) => query.has_comments === 'true',
);

export const getFiltersSignFolder = createSelector(
    getLocationQueryParams,
    getCurrentSignFolders,
);

export const getFiltersReviewFolder = createSelector(
    getLocationQueryParams,
    (query) => query.review_folder || null,
);

export const getFiltersTags = (state: StoreState) => getSlice(state).tags;

export const getFiltersIsLoading = (state: StoreState) =>
    getSlice(state).isLoading;

export const getFiltersSelectedTags = createSelector(
    getLocationQueryParams,
    (query) => (query.tag ? [query.tag].flat() : []),
);

export const getFiltersIsWithoutTagsChecked = createSelector(
    getLocationQueryParams,
    (query) => query.without_tags === 'true' && !query.tag,
);

export const isFilterSelectValue = (value: Nullable<string>): boolean => {
    return Boolean(value && value !== 'all');
};

export const getActiveFilterCounts = createSelector(
    getFiltersFromDate,
    getFiltersToDate,
    getFiltersDocumentCategories,
    getFiltersSignFolder,
    getFiltersReviewFolder,
    getFiltersSelectedTags,
    getFiltersIsWithoutTagsChecked,
    getIsWaitMySignFilter,
    getIsMyInvalidSigned,
    getIsPartnerInvalidSigned,
    getHasCommentsFilter,
    getFiltersQueryDocAmountGte,
    getFiltersQueryDocAmountLte,
    getIsPrivateAccess,
    (
        dateFrom,
        dateTo,
        documentCategory,
        signFolder,
        reviewFolder,
        selectedTags,
        isWithoutTagsChecked,
        isWaitMySignFilter,
        isMyInvalidSigned,
        isParentInvalidSigned,
        hasCommentsFilter,
        docAmountFrom,
        docAmountTo,
        isPrivateAccess,
    ) => {
        let activeFilters = 0;

        if (dateFrom || dateTo) {
            activeFilters++;
        }

        if (docAmountFrom || docAmountTo) {
            activeFilters++;
        }

        if (isFilterSelectValue(documentCategory)) {
            activeFilters++;
        }

        if (signFolder.find((value) => isFilterSelectValue(value))) {
            activeFilters++;
        }

        if (isFilterSelectValue(reviewFolder)) {
            activeFilters++;
        }

        if (selectedTags && selectedTags.length > 0) {
            activeFilters++;
        } else if (isWithoutTagsChecked) {
            activeFilters++;
        }

        if (isWaitMySignFilter) {
            activeFilters++;
        }

        if (isMyInvalidSigned) {
            activeFilters++;
        }

        if (isParentInvalidSigned) {
            activeFilters++;
        }

        if (hasCommentsFilter) {
            activeFilters++;
        }

        if (isPrivateAccess) {
            activeFilters++;
        }

        return activeFilters;
    },
);
