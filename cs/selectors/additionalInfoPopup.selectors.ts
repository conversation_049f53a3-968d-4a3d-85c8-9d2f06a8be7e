import { createSelector } from 'reselect';

import { StoreState } from '../types/store';

import {
    getCurrentCompany,
    getCurrentUser,
    getCurrentUserRole,
} from './app.selectors';

import { isFopCompany } from '../lib/company';

export const getSlice = (state: StoreState) => state.additionalInfoPopup;

export const getDisplayStatus = (state: StoreState) =>
    getSlice(state).displayStatus;

export const getIsAdditionalInfoAlreadyExist = createSelector(
    getCurrentCompany,
    getCurrentUser,
    getCurrentUserRole,
    (currentCompany, currentUser, currentRole) => {
        const isCurrentRolePositionAlreadyExist =
            isFopCompany({ edrpou: currentCompany.edrpou }) ||
            currentRole.position;
        return (
            currentUser.firstName &&
            currentUser.phone &&
            isCurrentRolePositionAlreadyExist
        );
    },
);
