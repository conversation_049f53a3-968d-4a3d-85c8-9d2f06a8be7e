import { createSelector } from 'reselect';

import { DocumentReviewProcess } from '../services/enums';
import { StoreState } from '../types/store';

export const getChangeDocumentPopupSlice = (state: StoreState) =>
    state.changeDocumentPopup;

export const getReviewProcessEditable = (state: StoreState) =>
    getChangeDocumentPopupSlice(state).reviewProcessEditable;

export const getSelectedItemIndex = (state: StoreState) =>
    getChangeDocumentPopupSlice(state).selectedItemIndex;

export const getDocumentsForEdit = (state: StoreState) =>
    getChangeDocumentPopupSlice(state).documents;

export const getIsActive = (state: StoreState) =>
    getChangeDocumentPopupSlice(state).isActive;

export const getReviewerValue = (state: StoreState) =>
    getChangeDocumentPopupSlice(state).reviewerValue;

export const getReviewProcess = (state: StoreState) =>
    getChangeDocumentPopupSlice(state).reviewProcess;

export const getIsReviewersSuggestionsShown = (state: StoreState) =>
    getChangeDocumentPopupSlice(state).isReviewersSuggestionsShown;

export const getReviewersSuggestions = (state: StoreState) =>
    getChangeDocumentPopupSlice(state).reviewersSuggestions;

export const getIsReviewRequired = (state: StoreState) =>
    getChangeDocumentPopupSlice(state).isReviewRequired;

export const getIsOrderedReviewProcess = createSelector(
    getReviewProcess,
    (reviewProcess) =>
        reviewProcess === DocumentReviewProcess.ORDERED_PROCESS_TYPE,
);
