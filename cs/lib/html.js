function renderCanonicalUrl(canonicalUrl, config) {
    if (!canonicalUrl) {
        return '';
    }

    const url = `https://${config.DOMAIN_NAME}${canonicalUrl}`;
    return `
        <meta property="og:url" content="${url}" />
        <link rel="canonical" href="${url}" />
    `;
}

function renderColbert(config) {
    if (!config.COLBERT_URL) return '';

    return `
        <script>
            !function(c,o,l,b,e,r,t){
                // Check for initialized Colbert script
                if (window.ColbertObject) return;

                c['ColbertObject']=e;
                c[e]=c[e]||function(){(c[e].q=c[e].q||[]).push(arguments)};c[e].l=1*new Date();
                r=o.createElement(l);
                r.async=1;r.crossOrigin='anonymous';r.src=b;
                o.body.appendChild(r);
            }(window,document,'script','${config.COLBERT_URL}','colbert')
        </script>
    `;
}

function renderDescription(description) {
    if (!description) {
        return '';
    }
    return `<meta name="description" content="${description}">`;
}

function renderFacebookDomainVerificationMeta(config) {
    const { ANALYTICS_FB_DOMAIN_VERIFICATION_CONTENT_ID: contentId } = config;

    if (!contentId) {
        return '';
    }

    return `<meta name="facebook-domain-verification" content="${contentId}" />`;
}

function renderFacebookPixel(config) {
    const { ANALYTICS_FB_PIXEL_ID: pixelId } = config;
    if (!pixelId) {
        return '';
    }
    return `
        <script>
            (function() {
                // check for IE
                var ua = window.navigator.userAgent;
                var isIE = /MSIE|Trident/.test(ua);
                if (!isIE) {
                    !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function()
                    {n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)}
                    ;if(!f._fbq)f._fbq=n;
                    n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
                    t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
                    document,'script','https://connect.facebook.net/en_US/fbevents.js');
                    fbq('init', '${pixelId}');
                    fbq('track', 'PageView');
                }
            }());
        </script>
        <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1"
        /></noscript>
    `;
}

function renderFavicons(config) {
    if (config.DEMO) {
        return `<link href="${config.STATIC_PREFIX}/favicons/favicon-dev.ico" rel="icon">`;
    }
    if (config.DEBUG) {
        return `<link href="${config.STATIC_PREFIX}/favicons/favicon-trunk1.ico" rel="icon">`;
    }
    return `
        <link rel="manifest" href="${config.STATIC_PREFIX}/manifest.json">
        <link rel="apple-touch-icon" sizes="180x180" href="${config.STATIC_PREFIX}/favicons/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="${config.STATIC_PREFIX}/favicons/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="${config.STATIC_PREFIX}/favicons/favicon-16x16.png">
        <link rel="mask-icon" href="${config.STATIC_PREFIX}/favicons/safari-pinned-tab.svg" color="#fff200">
        <link rel="shortcut icon" href="${config.STATIC_PREFIX}/favicons/favicon.ico">
        <meta name="msapplication-TileColor" content="#fff200">
        <meta name="msapplication-config" content="${config.STATIC_PREFIX}/favicons/browserconfig.xml">
        <meta name="theme-color" content="#fff200">
    `;
}

function renderGoogleAnalytics(config) {
    const { ANALYTICS_GA_TRACKING_ID: trackingId } = config;
    if (!trackingId) {
        return '';
    }
    return `<script type="text/javascript">
        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function()
        { (i[r].q=i[r].q||[]).push(arguments)}
        ,i[r].l=1*new Date();a=s.createElement(o),
        m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
        })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
        ga('create', '${trackingId}', 'auto');
        ga('send', 'pageview');
    </script>`;
}

function renderGoogleAnalyticsLinker(config) {
    const { ANALYTICS_GA_TRACKING_ID: trackingId } = config;
    if (!trackingId) {
        return '';
    }
    return `<script type="text/javascript">
        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function()
        { (i[r].q=i[r].q||[]).push(arguments)}
        ,i[r].l=1*new Date();a=s.createElement(o),
        m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
        })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');
        ga('create', '${trackingId}', 'auto', {'allowLinker': true});
        ga('require', 'linker');
        ga('linker:autoLink', ['${config.DOMAIN_NAME}']);
    </script>`;
}

function renderGoogleTagManager(config) {
    const {
        ANALYTICS_GTM_TRACKING_ID: trackingId,
        ANALYTICS_GA_KASA_TRACKING_ID: trackingGAKasaId,
        ANALYTICS_GTM_KASA_TRACKING_ID: trackingKasaId,
    } = config;
    if (!trackingId) {
        return '';
    }

    return `
        <!-- Google Tag Manager -->
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','${trackingId}');</script>
        <!-- End Google Tag Manager -->

        <!-- Google Tag Manager Kasa -->
        ${
            trackingKasaId &&
            trackingGAKasaId &&
            `
            <script async src="https://www.googletagmanager.com/gtag/js?id=${trackingKasaId}"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtagKasa() {
                    dataLayer.push(arguments);
                }
                window['ga-disable-${trackingKasaId}'] = true;
                window['ga-disable-${trackingGAKasaId}'] = true;
                gtagKasa('js', new Date());
                gtagKasa('config', '${trackingKasaId}');
                gtagKasa('config', '${trackingGAKasaId}');
            </script>
        `
        }
        <!-- End Google Tag Manager Kasa -->
    `;
}

function renderCommonGoogleTagManager(config) {
    const { ANALYTICS_GTM_COMMON_TRACKING_ID: trackingId } = config;

    if (!trackingId) {
        return '';
    }

    return `
        <!-- Google Tag Manager -->
        <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','${trackingId}');</script>
        <!-- End Google Tag Manager -->
    `;
}

function renderGoogleTagManagerFrame(config) {
    const { ANALYTICS_GTM_COMMON_TRACKING_ID: trackingId } = config;

    if (!trackingId) {
        return '';
    }

    return `
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=${trackingId}"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
    `;
}

function renderCommonGoogleTagManagerFrame(config) {
    const { ANALYTICS_GTM_TRACKING_ID: trackingId } = config;
    if (!trackingId) {
        return '';
    }
    return `
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=${trackingId}"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
    `;
}

function renderOpenGraphTags(data, config) {
    const title =
        data.title ||
        `${config.BRAND_NAME} - безкоштовний обмін документами онлайн`;
    const description =
        data.description ||
        'Простий та швидкий спосіб підписання та обміну документами в електронному вигляді.';
    const image = `${config.STATIC_HOST}/images/vchasno.png`;
    return `
        <meta property="og:title" content="${title}">
        <meta property="og:description" content="${description}">
        <meta property="og:image" content="${image}">
    `;
}

function renderScripts(scripts) {
    return scripts
        .map((script) => {
            if (typeof script === 'string') {
                return `<script crossorigin type="text/javascript" src="${script}"></script>`;
            }
            return `
            <script
                type="text/javascript"
                src="${script.url}"
                ${
                    script.crossOrigin
                        ? `crossorigin="${script.crossOrigin}"`
                        : ''
                }
                ${script.integrity ? `integrity="${script.integrity}"` : ''}
            ></script>
        `;
        })
        .join('');
}

function renderStyles(styles) {
    return styles
        .map((style) => {
            if (typeof style === 'string') {
                return `<link rel="stylesheet" href="${style}">`;
            }
            return `
            <link
                rel="stylesheet"
                href="${style.url}"
                ${style.crossOrigin ? `crossorigin="${style.crossOrigin}"` : ''}
                ${style.integrity ? `integrity="${style.integrity}"` : ''}
            >
        `;
        })
        .join('');
}

function renderViewportMetatag(viewportMetatag) {
    if (!viewportMetatag) {
        return '';
    }
    return (
        '<meta name="viewport" ' +
        'content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">'
    );
}

function renderNoIndex(noIndex) {
    if (!noIndex) {
        return '';
    }
    return '<meta name="robots" content="noindex, follow">';
}

module.exports.baseHtml = (options) => {
    const config = options.config;
    const {
        scripts = [],
        styles = [],
        title = '',
        analytics = true,
        viewportMetatag = false,
        noIndex = false,
        canonicalUrl = '',
        description = '',
        useLinker = false,
    } = options.htmlConfig;

    const analyticsCode = useLinker
        ? renderGoogleAnalyticsLinker(config)
        : renderGoogleAnalytics(config);
    return `
      <!DOCTYPE html>
      <html lang="uk">
      <head>
        <title>${title}</title>
        <meta charset="utf-8">
        ${renderNoIndex(noIndex)}
        <meta name="google-site-verification" content="GARFPI2E94PHAHMDwyipH1ZjfVrYPtXD4vVajhZZ-QA" />
        ${renderFacebookDomainVerificationMeta(config)}
        ${renderFavicons(config)}
        ${renderOpenGraphTags({ title, description }, config)}
        ${renderCanonicalUrl(canonicalUrl, config)}
        ${renderDescription(description)}
        ${renderViewportMetatag(viewportMetatag)}
        ${renderStyles(styles)}
        ${analytics ? analyticsCode : ''}
        ${analytics ? renderGoogleTagManager(config) : ''}
        ${analytics ? renderCommonGoogleTagManager(config) : ''}
        ${analytics ? renderFacebookPixel(config) : ''}
      </head>
      <body>
        ${analytics ? renderGoogleTagManagerFrame(config) : ''}
        ${analytics ? renderCommonGoogleTagManagerFrame(config) : ''}
        <div id="content"></div>
        <div id="root-portal"></div>

        ${renderScripts(scripts)}
        ${renderColbert(config)}
      </body>
      </html>
    `;
};
