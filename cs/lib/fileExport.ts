import { saveBlobAs } from './helpers';

type ExportFile = (filename: string, data: any[], fields: string[]) => void;
type ExportFormat = 'csv' | 'xlsx';

export const exportXLSXFile: ExportFile = async (
    filename = 'export',
    data,
    fields,
) => {
    const { default: XLSX } = await import(
        /* webpackChunkName: "xlsx" */ 'xlsx'
    );
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet([fields, ...data]);
    XLSX.utils.book_append_sheet(wb, ws, 'Документи');
    const wbout = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: false,
        type: 'array',
    });
    const blob = new Blob([wbout], { type: 'application/octet-stream' });
    saveBlobAs(blob, `${filename}.xlsx`);
};

export const exportCsvFile: ExportFile = async (
    filename = 'export',
    data,
    fields,
) => {
    const { default: Papa } = await import(
        /* webpackChunkName: "papaparse" */ 'papaparse'
    );
    const csv = Papa.unparse(
        { fields, data },
        {
            // Default separator for Excel in UA region is ';'
            delimiter: ';',
            // If true, field values that begin with =, +, -, @, \t, or \r, will be prepended
            // with a ' to defend against injection attacks, because Excel and LibreOffice
            // will automatically parse such cells as formulae
            escapeFormulae: true,
        },
    );
    const blob = new Blob([`\uFEFF${csv}`]); // Added UTF-8 BOM for Excel
    saveBlobAs(blob, `${filename}.csv`);
};

export const getExportFunction = (format: ExportFormat) => {
    switch (format) {
        case 'csv':
            return exportCsvFile;
        case 'xlsx':
            return exportXLSXFile;
        default:
            throw Error('Unknown format');
    }
};
