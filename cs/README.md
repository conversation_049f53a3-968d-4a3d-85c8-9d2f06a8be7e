## Загальна інформація

Це React SPA додаток, який взаємодіє з API бекенд Python.

Вчасно.ЕДО розміщений на [https://edo.vchasno.ua/app](https://edo.vchasno.ua/app). Також в
репозиторії є окремий додаток авторизації (використовується для реєстрації всіх продуктів Вчасно),
який розміщений на [https://edo.vchasno.ua/auth](https://edo.vchasno.ua/auth).

Адаптовано для роботи на мобільних пристроях, але не весь функціонал. Розробляються нативні мобільні
додатки для iOS та Android.

## Керування станом

Для керування глобальним станом використовується Redux. Для обробки асинхронних подій — redux-thunk.
Стан форм зберігається в react-hook-form (старий підхід — в redux). Стан даних з API зберігається в
react-query (старий підхід — в redux).

## TypeScript

Новий функціонал розробляється на TypeScript. Велика частка кодової бази написана на JavaScript. Не
маємо на меті переписати всі 100% коду на TypeScript, але новий функціонал пишемо на ньому.

## Стилі та шрифти

Для написання стилів використовується підхід модульного CSS. Не використовуємо ніяких CSS
framework-ів. Шрифти сайту на всіх суміжних продуктах — Roboto 700/500/400.

## UI-KIT

Використовуємо власний [UI-KIT](https://web-ui-kit.vchasno.com.ua/), який розробляється в процесі
роботи над проєктом. [@vchasno/ui-kit](https://www.npmjs.com/package/@vchasno/ui-kit) інтегрований
також в інші продукти Вчасно. Це дозволяє швидко розробляти новий функціонал та відповідати дизайну.

## Svg, зображення

Для SVG іконок використовується підхід — SVG sprite. SVG sprite додається в тіло HTML файлу. SVG
імпортується як glyph, який вставляється у компонент Icon. Зображення імпортуються як шлях до
ресурсу статики в `assets/images`.

## Локалізація

Продукт здебільшого орієнтований на ринок України, тому нових мов поки не планується додавати.
Формати часу та дати визначаються відповідно до дизайну і не залежать від контексту обраної мови.

## Тестування

Для тестування використовуються **mocha** та **chai**. **Cypress** пробували використовувати, але
він виявився важким у підтримці, а також тести на ньому проходили досить довго. Тестами покривають
базові функції utils, сервісів та компонентів.

## Процес оновлення додатка

Для безперервного розгортання використовується **CI/CD** механізм, розгорнутий через **GitLab CI**
на власних **AWS середовищах**. Релізи можуть бути відкочені на більш стабільні версії, якщо
останній реліз містить баги.

Перед релізом проводиться **manual QA** тестування. Спочатку тестування проходить локально на гілці,
а потім у **дев середовищі**.

## Безпека

В додатку використовуються токени для сесії, які можна налаштовувати за часом дії від 1 години до
пів року. Для захисту даних використовується **HTTPS (http only)**. Також є можливість відображення
списку активних сесій з прив'язкою до **IP-адреси**. На рівні компаній можна налаштовувати діапазон
IP-адрес, з яких користувачі зможуть потрапити в застосунок.

Для захисту від атак типу **CSRF** використовується відповідний механізм з порівнюванням токенів.

## Технічні вимоги для роботи додатка

Додаток побудовано на **React 17**, оскільки певна частина користувачів використовує браузери,
несумісні з React 18. Міграція на 18 версію може відбутися в майбутньому, коли частка таких
користувачів зменшиться до мінімуму.

## ШІ функціонал

В додатку використовується ШІ функціонал для розпізнавання параметрів завантажених документів. Цей
функціонал дозволяє автоматизувати процес аналізу і витягування важливих даних з документів.

## Розкриті теми

- [Генерацію статики](./docs/static.md). Як згенерувати фронтенд.
- [Підходи до написання стилів](./docs/css.md)
- [EDO user permission checks](./docs/permissions.md)

## TODO

1. [ ] Загальна структура фронтенду і сервіси
   - Опис архітектури фронтенду: як організована структура директорій, які сервіси використовуються,
     як взаємодіють фронтенд і бекенд.
   - Інтерфейс між фронтендом і бекендом: REST API, WebSocket або інші технології.
   - Частини коду, які є спільними для всіх продуктів Вчасно.
2. [ ] Як користувач авторизується
   - Процес аутентифікації: як користувач проходить реєстрацію через додаток Вчасно.ЕДО, а потім
     через Вчасно.ЕДО Auth.
   - Типи аутентифікації: cookie аутентифікація, токени.
   - Процес управління сесією: налаштування часу життя токенів, відображення активних сесій.
3. [x] [Підходи до написання стилів](./docs/css.md)
   - Модульний CSS: принципи та підхід.
   - Шаблони для компонентів: чи є рекомендації щодо структурування стилів у компонентах.
   - Немає використання CSS фреймворків.
4. [ ] Про UI-KIT
   - Що таке UI-KIT: його роль в проєкті.
   - Як використовувати UI-KIT: рекомендації щодо інтеграції та використання компонентів з UI-KIT.
   - Інтеграція з іншими продуктами Вчасно: як UI-KIT забезпечує єдину дизайн-систему для всіх
     продуктів.
5. [ ] Написання форм
   - Підходи до написання форм:
   - Використання react-hook-form для збереження стану форм.
   - Раніше використовувався redux для збереження стану форм.
   - Кращі практики для нових форм: які бібліотеки використовувати, як організувати обробку даних.
6. [ ] Redux та useQuery
   - Коли використовувати Redux: глобальний стан, збереження даних користувачів, даних сесій.
   - Коли використовувати useQuery: для запитів до API, кешування, автоматичний рефетчинг даних.
   - Кращі практики використання: як ефективно поєднувати ці технології.
7. [ ] Динамічні модулі, code splitting, module federation
   - Що таке динамічні модулі: як використовувати lazy loading для компонентів.
   - Code splitting: як розділити код на модулі для оптимізації завантаження.
   - Module Federation: як використовувати для інтеграції різних частин застосунку, створених
     незалежними командами.
8. [ ] Написання unit тестів
   - Інструменти для тестування: Mocha, Chai.
   - Рекомендації щодо написання unit тестів: які компоненти та функції повинні бути покриті
     тестами, як організувати тести.
   - Чи є плани щодо покращення процесу тестування: використання інших бібліотек або автоматизація
     тестів.
9. [ ] Перегляд документів PDF, TXT, XML, Images, Microsoft
   - Підтримка різних форматів документів: PDF, TXT, XML, Microsoft Office.
10. [ ] Використання Collabora
    - Що таке Collabora: інтеграція з редактором для документів DOCX/XLSX.
    - Як використовувати Collabora в додатку: як забезпечити взаємодію користувачів з документами
      через цей редактор.
