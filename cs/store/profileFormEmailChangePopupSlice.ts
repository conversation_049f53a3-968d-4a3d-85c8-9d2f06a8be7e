import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export type ProfileFormEmailChangePopupState = {
    isEmailChangeConfirmPopupOpen: boolean;
    isEmailChangeSuccessPopupOpen: boolean;
};

const initialState: ProfileFormEmailChangePopupState = {
    isEmailChangeConfirmPopupOpen: false,
    isEmailChangeSuccessPopupOpen: false,
};

export const profileFormEmailChangePopupSlice = createSlice({
    name: 'profileFormEmailChangePopup',
    initialState,
    reducers: {
        setEmailChangeConfirmPopupOpen: (
            state,
            action: PayloadAction<{ isEmailChangeConfirmPopupOpen: boolean }>,
        ) => {
            state.isEmailChangeConfirmPopupOpen =
                action.payload.isEmailChangeConfirmPopupOpen;
        },
        setEmailChangeSuccessPopupOpen: (
            state,
            action: PayloadAction<{ isEmailChangeSuccessPopupOpen: boolean }>,
        ) => {
            state.isEmailChangeSuccessPopupOpen =
                action.payload.isEmailChangeSuccessPopupOpen;
        },
    },
});

export const {
    setEmailChangeConfirmPopupOpen,
    setEmailChangeSuccessPopupOpen,
} = profileFormEmailChangePopupSlice.actions;

export default profileFormEmailChangePopupSlice.reducer;
