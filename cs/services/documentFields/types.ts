import { Nullable } from '../../types/general';
import { DocumentFieldType } from './enums';

export type DocumentFieldKeys =
    | 'id'
    | 'name'
    | 'type'
    | 'isRequired'
    | 'enumOptions';

type RoleItem = {
    id: string;
    user: {
        id: string;
        firstName: string;
        lastName: string;
        secondName: string;
    };
};

export interface DocumentField {
    id: string;
    name: string;
    type: DocumentFieldType;
    isRequired: boolean;
    enumOptions: Nullable<string[]>;
    canEdit: boolean;
    roles: RoleItem[];
}

export interface DocumentParameter {
    fieldId: string;
    value: Nullable<string>;
    isRequired?: boolean;
}

export interface DocumentSettingParameter {
    field_id: string;
    is_required?: boolean;
    value: Nullable<string>;
}

export interface ParametersSettings {
    parameters: DocumentSettingParameter[];
}
