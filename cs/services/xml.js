import { decodeTxt } from './txt';

export const decodeXml = async (buffer, encoding) => {
    const decoded = await decodeTxt(buffer, encoding);
    if (decoded.slice(0, 5) === '<?xml') {
        return decoded.slice(decoded.indexOf('?>') + 2);
    }
    return decoded;
};

export const parseXml = (content) => {
    const xml = new DOMParser().parseFromString(content, 'text/xml');
    if (['html', 'parseerror'].indexOf(xml.documentElement.nodeName) !== -1) {
        const errorXml = new XMLSerializer().serializeToString(xml);
        throw new Error(`Unable to parse mallformed XML document\n${errorXml}`);
    }
    return xml;
};

export const parseXmlRoot = async (content) => {
    const { default: sax } = await import('sax');
    const parser = sax.parser(true, { trim: true });
    let root;
    parser.onopentag = (tag) => {
        root = tag;
    };
    parser.write(content.slice(0, content.indexOf('>') + 1)).close();
    return root;
};

export default {
    decodeXml,
    parseXml,
    parseXmlRoot,
};
