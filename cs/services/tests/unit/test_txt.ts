/* global describe, it */
import { expect } from 'chai';

import { arrToString } from '../../txt';

const repeat = (original: any, counter: number) => {
    const result: Array<any> = [];
    for (let i = 0; i < counter; i++) {
        original.forEach((item: any) => result.push(item));
    }
    return result;
};

const TEST_BYTES = [
    72,
    101,
    108,
    108,
    111,
    44,
    32,
    119,
    111,
    114,
    108,
    100,
    33,
];

const TEST_UINT8ARRAY_SMALL = new Uint8Array(TEST_BYTES);
const TEST_UINT8ARRAY_LARGE = new Uint8Array(repeat(TEST_BYTES, 100));

const TEST_TEXT_SMALL = 'Hello, world!';
const TEST_TEXT_LARGE = 'Hello, world!'.repeat(100);

describe('services/txt', () => {
    describe('arrToString', () => {
        it('default chunk size small uint8array', () => {
            expect(arrToString(TEST_UINT8ARRAY_SMALL)).to.equal(
                TEST_TEXT_SMALL,
            );
        });

        it('default chunk size large uint8array', () => {
            expect(arrToString(TEST_UINT8ARRAY_LARGE)).to.equal(
                TEST_TEXT_LARGE,
            );
        });

        it('custom chunk size', () => {
            expect(arrToString(TEST_UINT8ARRAY_SMALL, 2)).to.equal(
                TEST_TEXT_SMALL,
            );
        });
    });
});
