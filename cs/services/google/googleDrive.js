/* global google */
import {
    ACCEPT_EXTENSIONS,
    ACCEPT_GOOGLE_DRIVE_MIMETYPES,
} from '../../lib/constants';
import io from '../io';
import googleApi from './googleApi';

const ERROR_TEXT_WRONG_FILE_FORMAT = 'Формат файлу не підтримується';

function isGoogleTypeFile(file) {
    return (
        ACCEPT_GOOGLE_DRIVE_MIMETYPES.indexOf(
            file[google.picker.Document.MIME_TYPE],
        ) >= 0
    );
}

function checkFileTypes(files = []) {
    files.forEach((file) => {
        if (isGoogleTypeFile(file)) {
            const fileMimetype = file[google.picker.Document.MIME_TYPE];
            if (ACCEPT_GOOGLE_DRIVE_MIMETYPES.indexOf(fileMimetype) === -1) {
                throw new Error(ERROR_TEXT_WRONG_FILE_FORMAT);
            }
        } else {
            const fileFormat = file[google.picker.Document.NAME]
                .split('.')
                .pop()
                .toLowerCase();
            if (ACCEPT_EXTENSIONS.indexOf(fileFormat) === -1) {
                throw new Error(ERROR_TEXT_WRONG_FILE_FORMAT);
            }
        }
    });
}

function getDownloadUrl(file) {
    let url;
    const fileId = file[google.picker.Document.ID];
    if (isGoogleTypeFile(file)) {
        url = `https://www.googleapis.com/drive/v3/files/${fileId}/export?mimeType=application%2Fpdf`;
    } else {
        url = `https://www.googleapis.com/drive/v3/files/${fileId}?alt=media`;
    }
    return url;
}

function getFileName(file) {
    let name;
    if (isGoogleTypeFile(file)) {
        name = `${file[google.picker.Document.NAME]}.pdf`;
    } else {
        name = file[google.picker.Document.NAME];
    }
    return name;
}

function getFileMimetype(file) {
    let mimetype;
    if (isGoogleTypeFile(file)) {
        mimetype = 'application/pdf';
    } else {
        mimetype = file[google.picker.Document.MIME_TYPE];
    }
    return mimetype;
}

function getFileInfo(file) {
    return {
        downloadUrl: getDownloadUrl(file),
        fileName: getFileName(file),
        fileMimetype: getFileMimetype(file),
    };
}

async function getFilesAsBlob(files = []) {
    const accessToken = googleApi.getAccessToken();

    if (!accessToken) {
        throw new Error('No access token');
    }

    checkFileTypes(files);

    const filesAsBlob = [];

    for (const file of files) {
        const { downloadUrl, fileName, fileMimetype } = getFileInfo(file);
        const fileAsArrayBuffer = await io.getAsArrayBuffer(downloadUrl, {
            headers: {
                Authorization: `Bearer ${accessToken}`,
            },
        });

        filesAsBlob.push({
            name: fileName,
            data: new Blob([fileAsArrayBuffer], { type: fileMimetype }),
        });
    }

    return filesAsBlob;
}

function createPicker(accessToken, onChoose, onClose) {
    if (accessToken) {
        const picker = new google.picker.PickerBuilder()
            .addView(new google.picker.DocsView().setIncludeFolders(true))
            .setLocale('uk')
            .enableFeature(google.picker.Feature.MULTISELECT_ENABLED)
            .setOAuthToken(accessToken)
            .setCallback(async (data) => {
                const action = data[google.picker.Response.ACTION];
                if (action === google.picker.Action.PICKED) {
                    onChoose(data[google.picker.Response.DOCUMENTS]);
                } else if (action === google.picker.Action.CANCEL) {
                    onClose();
                }
            })
            .build();
        picker.setVisible(true);
    }
}

async function showPicker(onChoose, onClose) {
    const accessToken = await googleApi.authorize();
    await googleApi.loadPickerApi();
    // Keep this method close to user action to avoid browser pop-up blocking
    createPicker(accessToken, onChoose, onClose);
}

export default {
    getFilesAsBlob,
    showPicker,
};
