import { Nullable } from '../../types/general';
import { AntivirusStatusTypes } from './enums';

export interface AntivirusChecks {
    dateCreated: string;
    dateUpdated: string;
    id: string;
    provider: string;
    status?: Nullable<AntivirusStatusTypes>;
}

export interface DraftAntivirusCheck {
    provider: string;
    status: AntivirusStatusTypes;
    dateCreated: string;
    dateUpdated: string;
    draftId: string;
}
