import { schemaAiDocumentSettings } from 'services/ai/schemas';
import io from 'services/io';

import { AIDocumentSettings } from './types';

/**
 * @description - передаємо або файл якщо документ не завантажений або його id - якщо файл вже був завантажений
 */
type FileOrDocId = File | string;

export const getAiDocumentSettings = async (
    fileOrDocId: FileOrDocId,
): Promise<AIDocumentSettings> => {
    // для локального тестування безпосередньо підставляєм необхідний варіант респонсу
    // await new Promise((resolve) => setTimeout(resolve, 1000));
    // return {
    //     title: null,
    //     category: 2,
    //     date: '2025-05-21',
    //     number: 'ВЧ-00000004628',
    //     amount: '5000',
    //     companyEdrpou: '77777777',
    //     companyEmail: '<EMAIL>',
    //     companyName: null,
    // } as AIDocumentSettings;

    const responseData = (typeof fileOrDocId === 'string'
        ? await io.post(
              `/internal-api/documents/${fileOrDocId}/suggest`,
              undefined,
              true,
          )
        : await io.postFile(
              '/internal-api/documents/suggest',
              fileOrDocId,
          )) as AIDocumentSettings;

    if (config.DEBUG) {
        console.log('fileOrDocId', fileOrDocId);
        console.log('getAiDocumentSettings', responseData);
    }

    // з бекенду може приходити як стрінг, так і number
    if (typeof responseData.category === 'number') {
        // якщо category прийшов як number, то конвертуємо його в string
        responseData.category = String(responseData.category);
    }

    return schemaAiDocumentSettings.cast(responseData);
};

export const getAIDocumentSurvey = async (): Promise<AISurveyAPIPayload> => {
    // return Promise.resolve({
    //     phone: '',
    //     data: [
    //         {
    //             name: 'Кастомний тип тип',
    //             customer: [],
    //             executor: [],
    //             other: ['Перше поле'],
    //             product: [],
    //             requisites: [],
    //             isCustomType: true,
    //         },
    //         {
    //             name: 'Видаткові накладні',
    //             customer: [],
    //             executor: [],
    //             other: [],
    //             product: [],
    //             requisites: ['Інше', 'одне інше'],
    //             isCustomType: false,
    //         },
    //     ],
    //     isCompleted: false,
    //     isClosed: false,
    // });

    const response = await io.get(
        '/internal-api/surveys/document_ai',
        null,
        true,
    );

    return {
        data: response.data || [],
        isCompleted: response.isCompleted || false,
        phone: response.phone || '',
        isClosed: response.isClosed || false,
    };
};

export const putAIDocumentSurvey = (payload: Partial<AISurveyAPIPayload>) => {
    return io.post('/internal-api/surveys/document_ai', payload);
};
