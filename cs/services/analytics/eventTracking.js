/* global ga, gtagKasa, fbq */
import { sendToGTMV4 } from './gtm';

export const isGAEnabled =
    config.ANALYTICS_GA_TRACKING_ID !== undefined &&
    config.ANALYTICS_GA_TRACKING_ID !== null &&
    typeof ga === 'function';

const isGAKasaEnabled =
    config.ANALYTICS_GA_KASA_TRACKING_ID !== undefined &&
    config.ANALYTICS_GA_KASA_TRACKING_ID !== null &&
    typeof gtagKasa === 'function';

const isFBPEnabled =
    config.ANALYTICS_FB_PIXEL_ID &&
    config.ANALYTICS_FB_PIXEL_ID !== null &&
    typeof fbq === 'function';

function composeLabels(labels) {
    let result = '';
    for (const key of Object.keys(labels)) {
        result += `${key}:${labels[key]}, `;
    }
    return result.slice(0, -2);
}

function createFunctionWithTimeout(callback, timeout = 1000) {
    let called = false;

    function fn() {
        if (!called) {
            called = true;
            callback();
        }
    }

    setTimeout(fn, timeout);
    return fn;
}

function pageview(location, page) {
    if (isGAEnabled) {
        ga('send', { hitType: 'pageview', location, page });
    }
}

function sendToGA(option) {
    if (isGAEnabled) {
        ga('send', {
            hitType: option.type,
            eventCategory: option.category,
            eventAction: option.action,
            eventLabel: option.label,
            eventValue: option.value,
            hitCallback: option.callback,
            nonInteraction: option.nonInteraction,
        });
    }
}

/**
 * @deprecated - use sendToGTMV4 instead
 * Variant to send events with GTM
 * @param {object} options
 * @param {string} [options.event]
 * @param {string | string[]} [options.category]
 * @param {string | string[]} [options.action]
 * @param {string | null | string[]} [options.label]
 * @param {string} [options.param]
 * @param {string} [options.param1]
 */
export function sendToGTM({
    event = 'ga_event',
    category,
    action,
    label,
    param,
    param1,
}) {
    if (config.DEBUG) {
        console.log('>'.repeat(10), ' GTM: SEND EVENT ', '<'.repeat(10));
        console.table({ event, category, action, label, param, param1 });
    }
    window.dataLayer = window.dataLayer || [];

    const data = {
        event,
        eventAction: action || '',
        eventCategory: category || '',
        eventLabel: label || '',
    };

    if (param) {
        data.eventParam = param;
    }

    if (param1) {
        data.eventParam1 = param1;
    }

    window.dataLayer.push(data);
}

function sendToGAKasa(option) {
    if (isGAKasaEnabled) {
        const {
            ANALYTICS_GA_KASA_TRACKING_ID: trackingGAKasaId,
            ANALYTICS_GTM_KASA_TRACKING_ID: trackingKasaId,
        } = config;
        window[`ga-disable-${trackingKasaId}`] = false;
        window[`ga-disable-${trackingGAKasaId}`] = false;
        gtagKasa('event', option.action, {
            event_category: option.category,
            event_action: option.action,
            event_label: option.label,
            value: option.value,
            event_callback: option.callback,
        });
        window[`ga-disable-${trackingKasaId}`] = true;
        window[`ga-disable-${trackingGAKasaId}`] = true;
    }
}

function setCustomDimensions(dimensions, sendNewDimensions = false) {
    if (isGAEnabled) {
        ga('set', dimensions);
    }
    if (sendNewDimensions) {
        sendToGA({
            type: 'event',
            category: 'custom_dimension',
            action: 'set',
            nonInteraction: true,
        });
        sendToGTM({ category: 'custom_dimension', action: 'set' });
    }
}

/**
 * @deprecated
 * @use sendToGTM
 */
function sendEvent(category, action, label, callback) {
    if (config.DEBUG) {
        console.log('>'.repeat(10), ' GA: SEND EVENT ', '<'.repeat(10));
        console.table({ category, action, label });
    }
    try {
        if (label && typeof label === 'object') label = composeLabels(label);
        sendToGA({
            type: 'event',
            category,
            action,
            label,
            callback: callback && createFunctionWithTimeout(callback),
        });
        sendToGTM({ category, action, label });
    } catch (error) {
        // it's ok
    }
}

function sendEventKasa(category, action, label, callback) {
    if (label && typeof label === 'object') {
        label = composeLabels(label);
    }
    sendToGAKasa({
        category,
        action,
        label,
        callback: callback && createFunctionWithTimeout(callback),
    });
}

function sendEventFBP(eventName, customData = {}) {
    if (isFBPEnabled) {
        fbq('track', eventName, customData);
    }
}

export default {
    pageview: pageview,
    sendToGTM: sendToGTM,
    sendToGTMV4,
    sendEvent,
    sendEventKasa,
    setCustomDimensions: setCustomDimensions,
    sendEventFBP: sendEventFBP,
};
