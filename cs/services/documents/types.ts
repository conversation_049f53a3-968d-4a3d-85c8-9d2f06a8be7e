import { DocumentAccessesArgs } from 'gql-types';

export interface AddRecipientsRequestPayload {
    emails: string[];
    edrpou: string;
    sign_num: 1;
    order?: number;
}

/**
 * Frontend copy of app/documents/enums.py class AccessSource(Enum)
 */
export const AccessSource: Record<string, DocumentAccessesArgs['source']> = {
    // for owner or for recipient
    default: 0,
    // for document signers
    signer: 1,
    // for viewer
    viewer: 4,
    // for reviewer
    reviewer: 8,
    // for mentioned role in comment
    comment: 16,
    // for roles with same tag as in document
    tag: 32,
    // for company admins or user who can see all document in company
    admin: 64,
} as const;
