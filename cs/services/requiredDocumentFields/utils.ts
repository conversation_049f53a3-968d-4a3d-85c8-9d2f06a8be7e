import { DocumentRequiredFields } from './types';

export function mapRequiredFieldToApi(field: Partial<DocumentRequiredFields>) {
    return {
        id: field.id,
        document_category: field.documentCategory,
        is_name_required: field.isNameRequired,
        is_type_required: field.isTypeRequired,
        is_number_required: field.isNumberRequired,
        is_date_required: field.isDateRequired,
        is_amount_required: field.isAmountRequired,
    };
}
