/**
 * Result of the function `getKeys` that extracts keys from the state.
 */
export interface KeysStateInfo {
    pk: {
        type: 'pk';
        keyInfo: unknown;
        keyData: Uint8Array;
        password: string;
        certificates: unknown[];
        caServerIdx: number;
    };
    stamp: {
        type: 'stamp';
        keyInfo: unknown;
        keyData: Uint8Array;
        password: string;
        certificates: unknown[];
        caServerIdx: number;
    };
}
