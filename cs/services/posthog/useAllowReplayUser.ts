import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { getPeriod } from 'lib/date';
import { getCurrentUser } from 'selectors/app.selectors';

const USER_REGISTRATION_DAYS_TO_ALLOW_REPLAY = 30; // днів після реєстрації

export const useAllowReplayUser = () => {
    const user = useSelector(getCurrentUser);

    const daysFromRegistration = useMemo(() => {
        return user
            ? getPeriod(new Date(user.dateCreated), new Date(), 'days')
            : null;
    }, [user.dateCreated]);

    if (
        !user.registrationCompleted ||
        typeof daysFromRegistration !== 'number'
    ) {
        return false;
    }

    // Перші 30 днів після реєстрації записуємо всіх користувачів незалежно від дня в місяці
    if (daysFromRegistration < USER_REGISTRATION_DAYS_TO_ALLOW_REPLAY) {
        return true;
    }
};
