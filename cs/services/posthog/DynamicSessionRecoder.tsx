import React from 'react';

import { FeatureShow } from 'components/FeatureDisplay';

import SessionRecoder from './SessionRecorder';
import { useAllowReplayUser } from './useAllowReplayUser';

const DynamicSessionRecoder: React.FC = () => {
    const isPosthogInjected = Boolean(config.POSTHOG_API_KEY);
    const userAllowedRecording = useAllowReplayUser();

    if (!isPosthogInjected) {
        return null;
    }

    if (!userAllowedRecording) {
        return null;
    }

    return (
        <FeatureShow feature="POSTHOG_SR_ENABLED">
            <SessionRecoder />
        </FeatureShow>
    );
};

export default DynamicSessionRecoder;
