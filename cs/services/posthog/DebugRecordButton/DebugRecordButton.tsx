import React, { useEffect } from 'react';

import {
    BlackTooltip,
    BodyPortal,
    Button,
    snackbarToast,
} from '@vchasno/ui-kit';

import cn from 'classnames';
import { usePostHog } from 'posthog-js/react';

import css from './DebugRecordButton.css';

export interface DebugRecordButtonProps {
    className?: string;
}

const DebugRecordButton: React.FC<DebugRecordButtonProps> = ({ className }) => {
    const posthog = usePostHog();
    const [keyCombination, setIsKeyCombination] = React.useState(false);
    const [isRecording, setIsRecording] = React.useState(() =>
        posthog.sessionRecordingStarted(),
    );

    const isKeyCombination = (event: KeyboardEvent) => {
        return event.shiftKey && event.key === 'Z';
    };

    useEffect(() => {
        const handler = (event: KeyboardEvent) => {
            if (isKeyCombination(event)) {
                setIsKeyCombination((prev) => !prev);
            }
        };
        window.addEventListener('keydown', handler);

        return () => {
            window.removeEventListener('keydown', handler);
        };
    }, []);

    if (!config.DEBUG || !keyCombination) {
        return null;
    }

    return (
        <BodyPortal>
            <div className={cn(css.root, className)}>
                <BlackTooltip
                    title={
                        isRecording
                            ? 'Зупинити запис сесії'
                            : 'Почати запис сесії'
                    }
                >
                    <Button
                        theme="pink"
                        className={css.btn}
                        pulse={isRecording}
                        suppressPadding
                        onClick={() => {
                            if (posthog.sessionRecordingStarted()) {
                                snackbarToast.info('Зупинка запису сесії');
                                posthog.stopSessionRecording();
                                setIsRecording(false);
                            } else {
                                snackbarToast.info('Початок запису сесії');
                                posthog.startSessionRecording();
                                setIsRecording(true);
                            }
                        }}
                    />
                </BlackTooltip>
            </div>
        </BodyPortal>
    );
};

export default DebugRecordButton;
