html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video,
input {
    padding: 0;
    border: 0;
    margin: 0;
    font-style: inherit;
    font-weight: inherit;
}

html:focus,
body:focus,
div:focus,
span:focus,
applet:focus,
object:focus,
iframe:focus,
h1:focus,
h2:focus,
h3:focus,
h4:focus,
h5:focus,
h6:focus,
p:focus,
blockquote:focus,
pre:focus,
a:focus,
abbr:focus,
acronym:focus,
address:focus,
big:focus,
cite:focus,
code:focus,
del:focus,
dfn:focus,
em:focus,
img:focus,
ins:focus,
kbd:focus,
q:focus,
s:focus,
samp:focus,
small:focus,
strike:focus,
strong:focus,
sub:focus,
sup:focus,
tt:focus,
var:focus,
b:focus,
u:focus,
i:focus,
center:focus,
dl:focus,
dt:focus,
dd:focus,
ol:focus,
ul:focus,
li:focus,
fieldset:focus,
form:focus,
label:focus,
legend:focus,
table:focus,
caption:focus,
tbody:focus,
tfoot:focus,
thead:focus,
tr:focus,
th:focus,
td:focus,
article:focus,
aside:focus,
canvas:focus,
details:focus,
embed:focus,
figure:focus,
figcaption:focus,
footer:focus,
header:focus,
hgroup:focus,
menu:focus,
nav:focus,
output:focus,
ruby:focus,
section:focus,
summary:focus,
time:focus,
mark:focus,
audio:focus,
video:focus,
input:focus {
    outline: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

ol,
ul {
    list-style: none;
}

blockquote,
q {
    quotes: none;
}

blockquote::before,
blockquote::after,
q::before,
q::after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}
