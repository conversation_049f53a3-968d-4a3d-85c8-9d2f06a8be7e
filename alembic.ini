# A generic, single database configuration.

[DEFAULT]
# path to migration scripts
script_location = app/models/migrations
file_template = %%(year)d-%%(month).2d-%%(day).2d_%%(slug)s_%%(rev)s

# template used to generate migration files
# file_template = %%(rev)s_%%(slug)s

# max length of characters to apply to the
# "slug" field
#truncate_slug_length = 40

# set to 'true' to run the environment during
# the 'revision' command, regardless of autogenerate
# revision_environment = false

# set to 'true' to allow .pyc and .pyo files without
# a source .py file to be detected as revisions in the
# versions/ directory
# sourceless = false

# version location specification; this defaults
# to ./migrations/versions.  When using multiple version
# directories, initial revisions must be specified with --version-path
# version_locations = %(here)s/bar %(here)s/bat ./migrations/versions

# the output encoding used when revision files
# are written from script.py.mako
# output_encoding = utf-8

[alembic:vchasno-test]
config_file = /work/config/vchasno/test/config.yaml

[alembic:vchasno-local]
config_file = /work/config/vchasno/local/config.yaml

[alembic:vchasno-docker]
config_file = /work/config/vchasno/local/docker/config.yaml

[alembic:vchasno-docker-test]
config_file = /work/config/vchasno/test/docker/config.yaml

[alembic:vchasno-dev]
config_file = /work/config/vchasno/dev/config.yaml

[alembic:vchasno-bluebird]
config_file = /work/config/vchasno/bluebird/config.yaml

[alembic:vchasno-aws]
config_file = /work/config/vchasno/aws/config.yaml

[alembic:vchasno-prod]
config_file = /work/config/vchasno/prod/config.yaml

# Logging configuration
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S

[post_write_hooks]
hooks=ruff
ruff.type=exec
ruff.executable=ruff
ruff.options=format REVISION_SCRIPT_FILENAME
