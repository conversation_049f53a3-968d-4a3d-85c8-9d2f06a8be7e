localhost {
    # load mkcert certificate
    tls /etc/caddy/certs/localhost.pem /etc/caddy/certs/localhost-key.pem
    log {
        output stdout
    }

    ## Reverse proxy for /browser, /hosting/discovery, /hosting/capabilities, and /cool/adminws
    reverse_proxy /collabora/browser/* collabora:9980
    reverse_proxy /collabora/hosting/discovery collabora:9980
    reverse_proxy /collabora/hosting/capabilities collabora:9980
    reverse_proxy /collabora/cool/adminws collabora:9980

    # Reverse proxy for /cool/*/ws - WebSocket
    reverse_proxy /collabora/cool/*/ws collabora:9980

    # Reverse proxy for download, presentation, and image upload
    reverse_proxy /collabora/c* collabora:9980
    reverse_proxy /collabora/l* collabora:9980

    # Reverse proxy for index
    reverse_proxy * web:8000
}
