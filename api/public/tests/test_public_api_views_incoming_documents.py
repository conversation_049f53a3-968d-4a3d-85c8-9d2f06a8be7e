import datetime
import unittest
from http import HTTPStatus

import pytest
from multidict import MultiDict

from api.public.tests.common import (
    TEST_RECIPIENT_EDRPOU,
    TEST_RECIPIENT_EDRPOU_2,
    TEST_RECIPIENT_EMAIL,
)
from app.archive.tests.utils import archive_documents_request
from app.documents.enums import DocumentReviewState
from app.documents.tables import document_link_table
from app.documents.tests.utils import prepare_private_document
from app.documents_fields.enums import DocumentFieldType
from app.documents_fields.tests.common import (
    create_document_parameter,
    create_documents_field,
)
from app.flags import FeatureFlags
from app.lib.datetime_utils import to_local_datetime
from app.lib.enums import DocumentStatus, UserRole
from app.reviews.enums import ReviewType
from app.tags.tests.common import create_document_tags_with_access
from app.tags.utils import create_new_tags_for_documents
from app.tests.common import (
    API_V1_INCOMING_DOCUMENTS_URL,
    API_V2_INCOMING_DOCUMENTS_URL,
    insert_values,
    prepare_app_client,
    prepare_auth_headers,
    prepare_client,
    prepare_company_data,
    prepare_document_data,
    prepare_public_document_categories,
    prepare_review,
    prepare_review_db,
    prepare_review_requests,
    prepare_user_data,
    with_elastic,
)

COMPANY_ID_1 = '89887743-c4d9-4373-ade3-dfd3ad481c90'
COMPANY_ID_2 = '94014d05-2891-478c-856b-5a59bc110895'

COMPANY_EDRPOU_1 = '00112233'
COMPANY_EDRPOU_2 = '11223344'


DOCUMENT_ID_1 = '3000000-0000-0000-0000-000000000001'
DOCUMENT_ID_2 = '3000000-0000-0000-0000-000000000002'
DOCUMENT_ID_3 = '3000000-0000-0000-0000-000000000003'
DOCUMENT_ID_4 = '3000000-0000-0000-0000-000000000004'


@pytest.mark.parametrize('api', [API_V1_INCOMING_DOCUMENTS_URL, API_V2_INCOMING_DOCUMENTS_URL])
@pytest.mark.parametrize(
    'params,ids_indexes',
    [
        (None, [0, 1]),
        ({'date_sent_from': '2017-01-01', 'date_sent_to': '2100-01-01'}, [0, 1]),
        ({'extension': '.xml'}, [0]),
        ({'date_document_from': '2017-01-01'}, [0]),
        ({'date_document_from': '2017-01-02'}, []),
        ({'date_document_to': '2017-01-02'}, [0]),
        ({'date_document_to': '2016-01-01'}, []),
        ({'date_finished_from': '2017-01-01', 'date_finished_to': '2100-01-01'}, [0, 1]),
        ({'date_finished_from': '2017-01-02'}, [1]),
        ({'date_finished_from': '2017-01-03'}, []),
        ({'date_finished_to': '2017-01-02'}, [0, 1]),
        ({'date_finished_to': '2016-01-01'}, []),
    ],
)
async def test_list_incoming_documents_m(
    aiohttp_client,
    api,
    params,
    ids_indexes,
):
    app, client, recipient = await prepare_client(aiohttp_client)

    async def get_ids(params=None):
        response = await client.get(api, params=params, headers=prepare_auth_headers(recipient))
        documents = (await response.json())['documents']
        return {item['id'] for item in documents}

    owner = await prepare_user_data(app, company_edrpou='12331212', email='<EMAIL>')

    # Received by target user
    d1 = await prepare_document_data(
        app,
        owner,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': None}],
        extension='.xml',
        date_document=to_local_datetime(datetime.datetime(2017, 1, 1)),
        date_finished=datetime.datetime(2017, 1, 1),
    )
    # Received by target user
    d2 = await prepare_document_data(
        app,
        owner,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': None}],
        date_listing_recipient=datetime.datetime(2017, 1, 2),
        date_finished=datetime.datetime(2017, 1, 2),
        extension='.pdf',
    )
    # Not received by target user
    d3 = await prepare_document_data(app, recipient)
    # Not received by target user
    d4 = await prepare_document_data(
        app, recipient, document_recipients=[{'edrpou': '11111111', 'emails': None}]
    )
    ds = [d1.id, d2.id, d3.id, d4.id]

    async with with_elastic(app, ds):
        assert await get_ids(params) == {ds[i] for i in ids_indexes}


@pytest.mark.parametrize('api', [API_V1_INCOMING_DOCUMENTS_URL, API_V2_INCOMING_DOCUMENTS_URL])
async def test_list_incoming_documents_by_ids(aiohttp_client, api):
    app, client, recipient = await prepare_client(aiohttp_client)

    owner = await prepare_user_data(app, company_edrpou='12331212', email='<EMAIL>')

    d1 = await prepare_document_data(
        app,
        owner,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': None}],
    )
    d2 = await prepare_document_data(
        app,
        owner,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': None}],
    )
    d3 = await prepare_document_data(
        app,
        owner,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': None}],
    )
    async with with_elastic(app, [d1.id, d2.id, d3.id]):
        response = await client.get(
            api,
            params=MultiDict([('ids', d1.id), ('ids', d3.id)]),
            headers=prepare_auth_headers(recipient),
        )
    documents = (await response.json())['documents']
    assert [d['id'] for d in documents] == [d3.id, d1.id]


async def test_list_incoming_documents_v2_cursor(aiohttp_client):
    app, client, recipient = await prepare_client(aiohttp_client)

    owner = await prepare_user_data(app, company_edrpou='12331212', email='<EMAIL>')

    doc_ids = []
    for _ in range(10):
        doc_ = await prepare_document_data(
            app,
            owner,
            document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': None}],
        )
        doc_ids.append(doc_.id)

    async with with_elastic(app, doc_ids):
        response = await client.get(
            API_V2_INCOMING_DOCUMENTS_URL,
            params={'limit': 6},
            headers=prepare_auth_headers(recipient),
        )
    assert response.status == 200
    response_json = await response.json()
    ids = [r['id'] for r in response_json['documents']]
    assert len(ids) == 6
    next_cursor = response_json['next_cursor']

    async with with_elastic(app, doc_ids):
        response = await client.get(
            API_V2_INCOMING_DOCUMENTS_URL,
            params={'cursor': next_cursor, 'limit': 6},
            headers=prepare_auth_headers(recipient),
        )
    assert response.status == 200
    response_json = await response.json()
    new_ids = [r['id'] for r in response_json['documents']]
    assert len(new_ids) == 4
    assert response_json['next_cursor'] is None
    assert not (set(ids) & set(new_ids))


async def test_tags_filter_on_incoming_documents(aiohttp_client):
    app, client, recipient = await prepare_client(aiohttp_client)
    owner = await prepare_user_data(app, company_edrpou='12331212', email='a@b.c')

    tag1_name, tag2_name = 'TAG1', 'TAG2'
    document1 = await prepare_document_data(app, owner, another_recipients=[recipient])
    document2 = await prepare_document_data(app, owner, another_recipients=[recipient])
    document3 = await prepare_document_data(app, owner, another_recipients=[recipient])
    document4 = await prepare_document_data(app, owner, another_recipients=[recipient])

    async with app['db'].acquire() as conn:
        # Document1 has both tags
        tags = await create_new_tags_for_documents(
            conn=conn,
            tags_names=[tag1_name, tag2_name],
            documents_ids=[document1.id],
            company_id=recipient.company_id,
            company_edrpou=recipient.company_edrpou,
            assigner_role_id=recipient.role_id,
        )
        tags_mapping = {tag.name: tag.id for tag in tags}
        tag1_id, tag2_id = tags_mapping[tag1_name], tags_mapping[tag2_name]

        # Document2 has only one tag1
        await create_document_tags_with_access(
            conn=conn,
            document_id=document2.id,
            tag_id=tag1_id,
            company_id=recipient.company_id,
            company_edrpou=recipient.company_edrpou,
            assigner_role_id=recipient.role_id,
        )

        # Document4 has tags from recipient side
        tags = await create_new_tags_for_documents(
            conn=conn,
            tags_names=['TAG3'],
            documents_ids=[document4.id],
            company_id=owner.company_id,
            company_edrpou=owner.company_edrpou,
            assigner_role_id=owner.role_id,
        )
        tag4_id = tags[0].id

    async def check_documents(documents_ids, params):
        async with client.get(
            path=API_V2_INCOMING_DOCUMENTS_URL,
            headers=prepare_auth_headers(recipient),
            params=params,
        ) as response:
            assert response.status == HTTPStatus.OK
            data = await response.json()
            documents = data['documents']
            assert len(documents) == len(documents_ids)
            assert {document['id'] for document in documents} == set(documents_ids)

    async def check_tags(tags_ids, documents_ids):
        await check_documents(
            documents_ids=documents_ids,
            params=MultiDict(('tag_id', tag_id) for tag_id in tags_ids),
        )

    async with with_elastic(app, [document1.id, document2.id, document3.id, document4.id]):
        await check_tags([], [document1.id, document2.id, document3.id, document4.id])
        await check_tags([tag1_id], [document1.id, document2.id])
        await check_tags([tag1_id, tag2_id], [document1.id])
        await check_tags([tag4_id], [])

        await check_documents(
            documents_ids=[document3.id, document4.id], params={'not_tagged': '1'}
        )


@pytest.mark.parametrize('api', [API_V1_INCOMING_DOCUMENTS_URL, API_V2_INCOMING_DOCUMENTS_URL])
async def test_list_incoming_documents_with_recipients(aiohttp_client, api):
    app, client, user1 = await prepare_client(aiohttp_client)

    user2 = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou='00000123', company_name='C2'
    )
    user3 = await prepare_user_data(app, email='<EMAIL>', company_edrpou='00000124')

    document1 = await prepare_document_data(app, user1, another_recipients=[user2])
    document2 = await prepare_document_data(
        app, user1, another_recipients=[user2], is_recipients_hidden=True
    )
    document3 = await prepare_document_data(
        app, user1, another_recipients=[user2, user3], is_recipients_hidden=True
    )

    async with client.get(
        path=api, headers=prepare_auth_headers(user2), params={'with_recipients': '1'}
    ) as response:
        data = await response.json()

    documents = data['documents']
    expected = {
        document3.id: [
            {
                'edrpou': '00000123',
                'emails': ['<EMAIL>'],  # show user2 company email
                'name': 'C2',
                'is_emails_hidden': True,
            },
            {
                'edrpou': '00000124',
                'emails': None,  # hide another recipients emails
                'name': None,
                'is_emails_hidden': True,
            },
        ],
        document2.id: [
            {
                'edrpou': '00000123',
                'emails': ['<EMAIL>'],
                'name': 'C2',
                'is_emails_hidden': True,
            }
        ],
        document1.id: [
            {
                'edrpou': '00000123',
                'emails': ['<EMAIL>'],
                'name': 'C2',
                'is_emails_hidden': False,
            }
        ],
    }
    case = unittest.TestCase()
    for document in documents:
        document_id = document['id']
        case.assertCountEqual(expected[document_id], document['recipients'])


async def test_list_incoming_documents_v2_with_connections(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)

    await prepare_company_data(app, id=COMPANY_ID_1, edrpou=COMPANY_EDRPOU_1)
    await prepare_company_data(app, id=COMPANY_ID_2, edrpou=COMPANY_EDRPOU_2)

    user1 = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=COMPANY_EDRPOU_1, company_name='C1'
    )
    user2 = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=COMPANY_EDRPOU_2, company_name='C2'
    )
    parent = await prepare_document_data(app, user1, another_recipients=[user2])
    child_1 = await prepare_document_data(app, user1, another_recipients=[user2])
    child_2 = await prepare_document_data(app, user1, another_recipients=[user2])
    company_1_parent_doc = await prepare_document_data(app, user1, another_recipients=[user2])
    company_2_child_doc = await prepare_document_data(app, user2)
    doc_ids = [parent.id, child_1.id, child_2.id, company_1_parent_doc.id, company_2_child_doc.id]

    # company_id, company_edrpou, child_id, parent_id
    links_mapping = [
        (COMPANY_ID_1, COMPANY_EDRPOU_1, child_1.id, parent.id),
        (COMPANY_ID_1, COMPANY_EDRPOU_1, child_2.id, parent.id),
        (
            COMPANY_ID_2,
            COMPANY_EDRPOU_2,
            company_2_child_doc.id,
            company_1_parent_doc.id,
        ),
    ]
    for company_id, company_edrpou, child_id, parent_id in links_mapping:
        await insert_values(
            app=app,
            table=document_link_table,
            company_id=company_id,
            company_edrpou=company_edrpou,
            parent_id=parent_id,
            child_id=child_id,
        )

    async with with_elastic(app, doc_ids):
        async with client.get(
            path=API_V2_INCOMING_DOCUMENTS_URL,
            headers=prepare_auth_headers(user2),
            params={'with_connections': '1'},
        ) as response:
            data = await response.json()
            documents = data['documents']

    expected_children = {
        parent.id: [child_1.id, child_2.id],
        child_1.id: [],
        child_2.id: [],
        company_1_parent_doc.id: [company_2_child_doc.id],
    }
    expected_parents = {
        parent.id: None,
        child_1.id: parent.id,
        child_2.id: parent.id,
        company_1_parent_doc.id: None,
    }
    assert len(documents) == len(expected_children) == len(expected_parents)
    case = unittest.TestCase()
    for document in documents:
        document_id = document['id']
        case.assertCountEqual(expected_children[document_id], document['children'])
        case.assertEqual(expected_parents[document_id], document['parent'])


async def test_list_incoming_documents_by_field(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    partner = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=TEST_RECIPIENT_EDRPOU
    )

    document = await prepare_document_data(
        app=app,
        owner=owner,
        create_document_access_for_recipients=True,
        another_recipients=[partner],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )

    used_field = await create_documents_field(app, partner)
    unused_field = await create_documents_field(app, partner, name='unused_field')
    owner_field = await create_documents_field(app, owner, name='owner_field')

    await create_document_parameter(
        app,
        partner,
        document_id=document.id,
        field_id=used_field.id,
        value='partner_field_value',
    )
    await create_document_parameter(
        app,
        owner,
        document_id=document.id,
        field_id=owner_field.id,
        value='owner_field_value',
    )

    async def list_documents(params):
        async with with_elastic(app, [document.id]):
            return await client.get(
                path=API_V2_INCOMING_DOCUMENTS_URL,
                headers=prepare_auth_headers(partner),
                params=params,
            )

    # no field_value given
    r = await list_documents({'document_field': f'{used_field}'})
    assert r.status == HTTPStatus.BAD_REQUEST

    # field is unused, empty list
    r = await list_documents({'document_field': f'{unused_field.id}_value'})
    assert r.status == HTTPStatus.OK

    data = await r.json()
    documents = data['documents']
    assert len(documents) == 0

    # field belongs to another company
    r = await list_documents({'document_field': f'{owner_field.id}_owner_field_value'})
    assert r.status == HTTPStatus.NOT_FOUND

    # incoming document with given field exists
    r = await list_documents({'document_field': f'{used_field.id}_partner_field_value'})
    assert r.status == HTTPStatus.OK

    data = await r.json()
    documents = data['documents']
    assert len(documents) == 1


async def test_list_incoming_documents_by_multiple_fields(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    partner = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=TEST_RECIPIENT_EDRPOU
    )

    # incoming documents from partner
    document1 = await prepare_document_data(
        app=app,
        owner=partner,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )
    document2 = await prepare_document_data(
        app=app,
        owner=partner,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )
    document3 = await prepare_document_data(
        app=app,
        owner=partner,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )

    # document2
    field = await create_documents_field(app, owner, name='field', type=DocumentFieldType.text)
    # document1 & document3
    text_field = await create_documents_field(
        app, owner, name='text_field', type=DocumentFieldType.text
    )
    # document2 & document3
    numeric_field = await create_documents_field(
        app, owner, name='numeric_field', type=DocumentFieldType.number
    )
    # document1 & document2
    date_field = await create_documents_field(
        app, owner, name='date_field', type=DocumentFieldType.date
    )

    # document1
    await create_document_parameter(
        app,
        owner,
        document_id=document1.id,
        field_id=text_field.id,
        value='text_field_1',
    )
    await create_document_parameter(
        app,
        owner,
        document_id=document1.id,
        field_id=date_field.id,
        value='2020-11-11 22:00:00+00:00',
    )

    # document2
    await create_document_parameter(
        app, owner, document_id=document2.id, field_id=field.id, value='field_value'
    )
    await create_document_parameter(
        app,
        owner,
        document_id=document2.id,
        field_id=numeric_field.id,
        value='3735928559',
    )
    await create_document_parameter(
        app,
        owner,
        document_id=document2.id,
        field_id=date_field.id,
        value='2020-10-10 22:00:00+00:00',
    )

    # document3
    await create_document_parameter(
        app,
        owner,
        document_id=document3.id,
        field_id=text_field.id,
        value='text_value_2',
    )
    await create_document_parameter(
        app, owner, document_id=document3.id, field_id=numeric_field.id, value='1961'
    )

    async def list_documents(params):
        async with with_elastic(app, [document1.id, document2.id, document3.id]):
            return await client.get(
                path=API_V2_INCOMING_DOCUMENTS_URL,
                headers=prepare_auth_headers(owner),
                params=params,
            )

    r = await list_documents(
        MultiDict(
            [
                ('document_field', f'{field.id}_field_value'),
                ('document_field', f'{date_field.id}_2020-10-11'),
            ]
        )
    )
    documents = (await r.json())['documents']
    assert len(documents) == 1
    assert [d['id'] for d in documents] == [document2.id]

    r = await list_documents(
        MultiDict(
            [
                ('document_field', f'{numeric_field.id}_3735928559'),
            ]
        )
    )
    documents = (await r.json())['documents']
    assert len(documents) == 1
    assert sorted(d['id'] for d in documents) == [document2.id]

    r = await list_documents(
        MultiDict(
            [
                ('document_field', f'{date_field.id}_2020-11-12'),
            ]
        )
    )
    documents = (await r.json())['documents']
    assert len(documents) == 1
    assert sorted(d['id'] for d in documents) == [document1.id]

    r = await list_documents(
        MultiDict(
            [
                ('document_field', f'{numeric_field.id}_1961'),
            ]
        )
    )
    documents = (await r.json())['documents']
    assert len(documents) == 1
    assert sorted(d['id'] for d in documents) == [document3.id]

    r = await list_documents(MultiDict([('document_field', f'{field.id}_field_value')]))
    documents = (await r.json())['documents']
    assert len(documents) == 1
    assert [d['id'] for d in documents] == [document2.id]

    # add duplicate field that is present for document2 to document1
    await create_document_parameter(
        app, owner, document_id=document1.id, field_id=field.id, value='field_value'
    )

    # got one more document - document1
    r = await list_documents(MultiDict([('document_field', f'{field.id}_field_value')]))
    documents = (await r.json())['documents']
    assert len(documents) == 2
    assert sorted(d['id'] for d in documents) == sorted([document1.id, document2.id])


async def test_list_incoming_documents_by_owner_edrpous(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    partner = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=TEST_RECIPIENT_EDRPOU
    )
    partner2 = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=TEST_RECIPIENT_EDRPOU_2
    )

    # incoming documents from partner
    document1 = await prepare_document_data(
        app=app,
        owner=partner,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )
    document2 = await prepare_document_data(
        app=app,
        owner=partner,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )
    document3 = await prepare_document_data(
        app=app,
        owner=partner2,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )

    async def list_documents(params):
        async with with_elastic(app, [document1.id, document2.id, document3.id]):
            return await client.get(
                path=API_V2_INCOMING_DOCUMENTS_URL,
                headers=prepare_auth_headers(owner),
                params=params,
            )

    r = await list_documents(
        MultiDict(
            [
                ('edrpou_owner', partner.company_edrpou),
                ('edrpou_owner', partner2.company_edrpou),
            ]
        )
    )

    documents = (await r.json())['documents']
    assert len(documents) == 3

    r = await list_documents(
        MultiDict(
            [
                ('edrpou_owner', partner.company_edrpou),
            ]
        )
    )
    documents = (await r.json())['documents']
    assert len(documents) == 2
    assert sorted(d['id'] for d in documents) == sorted([document1.id, document2.id])

    r = await list_documents(
        MultiDict(
            [
                ('edrpou_owner', partner2.company_edrpou),
            ]
        )
    )
    documents = (await r.json())['documents']
    assert len(documents) == 1
    assert sorted(d['id'] for d in documents) == sorted([document3.id])


async def test_list_incoming_documents_by_statuses(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    partner = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=TEST_RECIPIENT_EDRPOU
    )

    # incoming documents from partner
    document1 = await prepare_document_data(
        app=app,
        owner=partner,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )
    document2 = await prepare_document_data(
        app=app,
        owner=partner,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.approved.value,
    )
    document3 = await prepare_document_data(
        app=app,
        owner=partner,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.reject.value,
    )
    document4 = await prepare_document_data(
        app=app,
        owner=partner,
        create_document_access_for_recipients=True,
        another_recipients=[owner],
        status_id=DocumentStatus.reject.value,
    )

    async def list_documents(params):
        async with with_elastic(
            app,
            [document1.id, document2.id, document3.id, document4.id],
        ):
            return await client.get(
                path=API_V2_INCOMING_DOCUMENTS_URL,
                headers=prepare_auth_headers(owner),
                params=params,
            )

    r = await list_documents(
        MultiDict(
            [
                ('status', DocumentStatus.sent.value),
            ]
        )
    )

    documents = (await r.json())['documents']
    assert len(documents) == 0

    r = await list_documents(
        MultiDict(
            [
                ('status', DocumentStatus.reject.value),
            ]
        )
    )

    documents = (await r.json())['documents']
    assert len(documents) == 2
    assert sorted(d['id'] for d in documents) == sorted([document3.id, document4.id])

    r = await list_documents(
        MultiDict(
            [
                ('status', DocumentStatus.approved.value),
                ('status', DocumentStatus.ready_to_be_signed.value),
            ]
        )
    )

    documents = (await r.json())['documents']
    assert len(documents) == 2
    assert sorted(d['id'] for d in documents) == sorted([document1.id, document2.id])


async def test_list_incoming_documents_by_category(aiohttp_client):
    # Prepare
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_public_document_categories(amount=3)

    partner = await prepare_user_data(
        app=app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    # Prepare 2 documents with categories and 1 without
    document_1 = await prepare_document_data(
        app=app,
        owner=user,
        category=1,
        another_recipients=[partner],
        create_document_access_for_recipients=True,
    )
    document_2 = await prepare_document_data(
        app=app,
        owner=user,
        category=2,
        another_recipients=[partner],
        create_document_access_for_recipients=True,
    )
    document_3 = await prepare_document_data(
        app=app,
        owner=user,
        another_recipients=[partner],
        create_document_access_for_recipients=True,
    )

    # Act
    async with with_elastic(app, [document_1.id, document_2.id, document_3.id]):
        response = await client.get(
            path=API_V2_INCOMING_DOCUMENTS_URL,
            headers=prepare_auth_headers(partner),
            params={'category': 1},
        )
        assert response.status == HTTPStatus.OK
        data = await response.json()
        documents = data['documents']
        assert len(documents) == 1
        assert [document['id'] for document in documents][0] == document_1.id

        response = await client.get(
            path=API_V2_INCOMING_DOCUMENTS_URL,
            headers=prepare_auth_headers(partner),
            params={'category': 2},
        )
        assert response.status == HTTPStatus.OK
        data = await response.json()
        documents = data['documents']
        assert len(documents) == 1
        assert [document['id'] for document in documents][0] == document_2.id

        response = await client.get(
            path=API_V2_INCOMING_DOCUMENTS_URL,
            headers=prepare_auth_headers(partner),
            params=MultiDict([('category', 1), ('category', 2)]),
        )
        assert response.status == HTTPStatus.OK
        data = await response.json()
        documents = data['documents']
        assert len(documents) == 2
        assert {document['id'] for document in documents} == {document_1.id, document_2.id}

        response = await client.get(
            path=API_V2_INCOMING_DOCUMENTS_URL,
            headers=prepare_auth_headers(partner),
            params={'category': 0},  # other, no category
        )
        assert response.status == HTTPStatus.OK
        data = await response.json()
        documents = data['documents']
        assert len(documents) == 1
        assert {document['id'] for document in documents} == {document_3.id}


@pytest.mark.parametrize(
    'amount, expected',
    [(10000, 10000), (-25000, -25000), (None, None), (0, 0)],
)
async def test_list_incoming_documents_amount(aiohttp_client, amount, expected):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client, create_billing_account=True)
    partner = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )
    doc = await prepare_document_data(app, user, another_recipients=[partner], amount=amount)

    # Act
    async with with_elastic(app, [doc.id]):
        resp = await client.get(
            API_V2_INCOMING_DOCUMENTS_URL, headers=prepare_auth_headers(partner)
        )
    documents = (await resp.json())['documents']

    # Assert
    assert len(documents) == 1
    assert documents[0]['amount'] == expected


@pytest.mark.parametrize(
    'is_archived, query, expected_count',
    [
        pytest.param(
            True,
            {'is_archived': 0},
            0,
            id='archived_query_false',
        ),
        pytest.param(
            True,
            {'is_archived': 1},
            1,
            id='archived_query_true',
        ),
        pytest.param(
            False,
            {'is_archived': 0},
            1,
            id='not_archived_query_false',
        ),
        pytest.param(
            False,
            {'is_archived': 1},
            0,
            id='not_archived_query_true',
        ),
        pytest.param(
            True,
            {},
            1,
            id='archived_query_empty',
        ),
        pytest.param(
            False,
            {},
            1,
            id='not_archived_query_empty',
        ),
    ],
)
async def test_list_incoming_documents_is_archived(
    aiohttp_client, is_archived, query, expected_count
):
    app, client, user = await prepare_client(aiohttp_client)

    partner = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )
    document = await prepare_document_data(
        app, partner, another_recipients=[user], status_id=DocumentStatus.finished.value
    )

    if is_archived:
        await archive_documents_request(
            client=client,
            user=user,
            documents=[document],
        )

    async with with_elastic(app, [document.id]):
        response = await client.get(
            API_V2_INCOMING_DOCUMENTS_URL, params=query, headers=prepare_auth_headers(user)
        )

    assert response.status == 200
    documents = (await response.json())['documents']
    assert len(documents) == expected_count
    if expected_count:
        document = documents[0]
        assert document['is_archived'] == is_archived


@pytest.mark.parametrize(
    'date_review_approved, expected_docs_count',
    [
        ('2024-01-01', 1),
        (None, 2),
    ],
)
async def test_list_incoming_documents_date_review_approved(
    aiohttp_client, date_review_approved, expected_docs_count
):
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)
    partner = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )
    document1 = await prepare_document_data(app, user, another_recipients=[partner])
    document2 = await prepare_document_data(app, user, another_recipients=[partner])

    params = {}
    if date_review_approved is not None:
        await prepare_review_db(
            document_id=document1.id,
            user=partner,
            date_created=date_review_approved,
        )
        params['date_review_approved_from'] = date_review_approved

    # Act
    async with with_elastic(
        app,
        [document1.id, document2.id],
    ):
        resp = await client.get(
            API_V2_INCOMING_DOCUMENTS_URL,
            headers=prepare_auth_headers(partner),
            params=params,
        )
        documents = (await resp.json())['documents']

    # Assert
    assert len(documents) == expected_docs_count


@pytest.mark.parametrize(
    'query_params, review_type, expected_docs_count',
    [
        ({}, None, 1),
        ({'review_state': DocumentReviewState.without_any.value}, None, 1),
        ({'review_state': DocumentReviewState.without_any.value}, ReviewType.approve, 0),
        ({'review_state': DocumentReviewState.approved.value}, None, 0),
        ({'review_state': DocumentReviewState.approved.value}, ReviewType.approve, 1),
    ],
)
async def test_list_incoming_documents_reviews_filter(
    aiohttp_client, query_params, review_type: ReviewType, expected_docs_count
):
    app, client, owner = await prepare_client(aiohttp_client)
    partner = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )

    document = await prepare_document_data(app, owner, another_recipients=[partner])
    if review_type:
        # prepare reviews on partner side
        coworker = await prepare_user_data(
            app,
            company_edrpou=TEST_RECIPIENT_EDRPOU,
            email='<EMAIL>',
            user_role=UserRole.user.value,
        )
        await prepare_review_requests(client, document, partner, reviewers=[coworker])
        await prepare_review(client, document=document, user=coworker, review_type=review_type)

    async with with_elastic(app, [document.id]):
        response = await client.get(
            API_V2_INCOMING_DOCUMENTS_URL,
            params=query_params,
            headers=prepare_auth_headers(partner),
        )
    assert response.status == 200
    response_json = await response.json()
    assert len(response_json['documents']) == expected_docs_count


@pytest.mark.skip(reason='Waiting for build_access_filter to be updated')
async def test_multiple_private_documents_filtering(aiohttp_client, test_flags):
    """
    Test filtering of multiple private documents by a user
    """
    test_flags[FeatureFlags.ES_SEARCH_API.value] = True
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    recipient = await prepare_user_data(
        app=app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )
    recipient_coworker = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou=TEST_RECIPIENT_EDRPOU,
        can_view_document=True,  # can view all, except private
        user_role=UserRole.user.value,
    )

    # Document 1: Private for an owner
    document1 = await prepare_document_data(
        app=app,
        owner=owner,
        id=DOCUMENT_ID_1,
        another_recipients=[recipient],
        create_document_access_for_recipients=True,
    )
    await prepare_private_document(
        document_id=document1.id,
        company_edrpou=owner.company_edrpou,
    )

    # Document 2: Private for recipient
    document2 = await prepare_document_data(
        app=app,
        owner=owner,
        id=DOCUMENT_ID_2,
        another_recipients=[recipient],
        create_document_access_for_recipients=True,
    )
    await prepare_private_document(
        document_id=document2.id,
        company_edrpou=recipient.company_edrpou,
    )

    # Document 3: Private for both
    document3 = await prepare_document_data(
        app=app,
        owner=owner,
        id=DOCUMENT_ID_3,
        another_recipients=[recipient],
        create_document_access_for_recipients=True,
    )
    await prepare_private_document(
        document_id=document3.id,
        company_edrpou=owner.company_edrpou,
    )
    await prepare_private_document(
        document_id=document3.id,
        company_edrpou=recipient.company_edrpou,
    )

    # Document 4: Not private
    document4 = await prepare_document_data(
        app=app,
        owner=owner,
        id=DOCUMENT_ID_4,
        another_recipients=[recipient],
        create_document_access_for_recipients=True,
    )

    async with with_elastic(app, [document1.id, document2.id, document3.id, document4.id]):
        # Recipient coworker tries to access documents
        response = await client.get(
            path=API_V2_INCOMING_DOCUMENTS_URL,
            headers=prepare_auth_headers(recipient_coworker),
        )

        assert response.status == 200

        response = await response.json()

        documents_ids = {doc['id'] for doc in response['documents']}

        assert len(documents_ids) == 2
        assert document2.id in documents_ids
        assert document3.id in documents_ids
        assert document1.id not in documents_ids
        assert document4.id not in documents_ids
