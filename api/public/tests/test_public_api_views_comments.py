import pytest
import sqlalchemy as sa

from app.comments.tables import comment_table
from app.models import select_all
from app.tests.common import (
    API_V2_DOCUMENTS_URL,
    cleanup_on_teardown,
    prepare_auth_headers,
    prepare_client,
    prepare_comment_data,
    prepare_document_data,
    prepare_user_data,
)


@pytest.mark.parametrize('content_type', ['application/xml', 'text/xml'])
async def test_create_comment(aiohttp_client, content_type):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    body = """
    <comment>
        <text>Foo</text>
    </comment>
    """

    try:
        headers = prepare_auth_headers(user)
        headers['content-type'] = content_type

        response = await client.post(
            f'{API_V2_DOCUMENTS_URL}/{document.id}/comments',
            headers=headers,
            data=body.encode('utf8'),
        )
        assert response.status == 201
        assert response.headers['Content-Type'] == 'application/xml; charset=utf-8'

        async with app['db'].acquire() as conn:
            comments = await select_all(conn, sa.select([comment_table]))

        assert len(comments) == 1
        assert comments[0].document_id == document.id
        assert comments[0].text == 'Foo'
    finally:
        await cleanup_on_teardown(app)


async def test_list_comments(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, first_name='foo', last_name='bar')
    recipient = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou='11223344'
    )

    first_document = await prepare_document_data(app, user)
    second_document = await prepare_document_data(app, owner=user, another_recipients=[recipient])

    await prepare_comment_data(
        app, document_id=first_document.id, role_id=user.role_id, text='comment1 text'
    )
    await prepare_comment_data(
        app, document_id=first_document.id, role_id=user.role_id, text='comment12 text'
    )

    await prepare_comment_data(
        app, document_id=second_document.id, role_id=user.role_id, text='comment2 text'
    )
    await prepare_comment_data(
        app,
        access_company_id=user.company_id,
        document_id=second_document.id,
        role_id=user.role_id,
        text='comment22 text',
    )
    await prepare_comment_data(
        app,
        access_company_id=recipient.company_id,
        document_id=second_document.id,
        role_id=recipient.role_id,
        text='comment23 text',
    )

    try:
        response = await client.get(
            f'{API_V2_DOCUMENTS_URL}/{first_document.id}/comments',
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200
        comments = (await response.json())['comments']
        assert len(comments) == 2
        assert comments[0]['text'] == 'comment1 text'
        assert comments[1]['text'] == 'comment12 text'

        response = await client.get(
            f'{API_V2_DOCUMENTS_URL}/{second_document.id}/comments',
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200
        comments = (await response.json())['comments']
        assert len(comments) == 3
        assert comments[0]['text'] == 'comment2 text'
        assert comments[1]['text'] == 'comment22 text'

        response = await client.get(
            f'{API_V2_DOCUMENTS_URL}/{second_document.id}/comments',
            headers=prepare_auth_headers(recipient),
        )
        assert response.status == 200
        comments = (await response.json())['comments']
        assert len(comments) == 3
        assert comments[0]['text'] == 'comment2 text'
        assert comments[2]['text'] == 'comment23 text'
    finally:
        await cleanup_on_teardown(app)
