MS_OFFICE_EXTENSIONS = {'.doc', '.docx', '.xls', '.xlsx'}

#: Buffer chunk size for streaming response
BUFFER_CHUNK_SIZE = 4096  # Bytes

#: Generate JSON for given file extensions
GENERATE_JSON_EXTENSIONS = {'.txt', '.xml'}

#: Allow to download print/signed file for given file extensions
PRINT_FILE_EXTENSIONS = (
    {'.pdf', '.png', '.jpg', '.jpeg'} | GENERATE_JSON_EXTENSIONS | MS_OFFICE_EXTENSIONS
)

#: XML utils
XML_BODY_GOODS_TAG = 'РеквизитыТабличнойЧасти_Товары'
XML_BODY_SERVICES_TAG = 'РеквизитыТабличнойЧасти_Услуги'
XML_BODY_ROW_TAG = 'Номенклатура'
XML_ROW_NUMBER_TAG = 'НомерСтроки'

# How much time lock key for generating archive will be live in Redis
TTL_GENERATE_ARCHIVE_LOCK_IN_SEC = 60 * 30  # 30 minutes


# NTFS has a 256-character limit for the path length, including the filename. But to fit
# within this limit, we account for the path to the Downloads directory.
# Use last number to adjust the result, it was caclulated by using example of some quite long
# possible path: "C:\Users\<USER>\Завантаження\" (47 characters) + some gap.
NTFS_REAL_MAX_PATH_LENGTH = 200  # 256 - len("C:\Users...)


# Most of the OS have a limit of 255 characters for the filename. We have to make filenames
# shorter to fit into this limit.
MAX_FILENAME_LENGTH = 255


# Max documents allowed to pack in archive during download archived documents
MAX_ARCHIVED_DOCUMENTS_COUNT = 1000
