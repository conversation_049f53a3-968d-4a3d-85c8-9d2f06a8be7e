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
