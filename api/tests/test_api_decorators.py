from api.public.tests.test_public_api_views_documents import API_V2_DOCUMENT_FIELDS_BY_COMPANY
from app.tests.common import (
    TEST_USER_EMAIL,
    TEST_USER_PASSWORD,
    login,
    prepare_auth_headers,
    prepare_client,
    prepare_referer_headers,
)


async def test_login_required(monkeypatch, aiohttp_client, concierge_emulation):
    monkeypatch.setenv('VCHASNO_LEVEL', 'local')

    app, client, user = await prepare_client(
        aiohttp_client, email=TEST_USER_EMAIL, password=TEST_USER_PASSWORD
    )

    response = await client.get(
        API_V2_DOCUMENT_FIELDS_BY_COMPANY, headers=prepare_auth_headers(user)
    )
    assert response.status == 200

    await login(client, TEST_USER_EMAIL, TEST_USER_PASSWORD)
    response = await client.get(
        API_V2_DOCUMENT_FIELDS_BY_COMPANY, headers=prepare_referer_headers(client)
    )
    assert response.status == 403

    data = await response.json()
    assert data['code'] == 'invalid_auth_method'
