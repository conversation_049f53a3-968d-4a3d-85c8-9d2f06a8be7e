from http import HTTPStatus

from api.private.integrations.hrs.tests import common as hrs_common
from api.private.integrations.hrs.tests.common import COMPANY_EDRPOU_1, prepare_hrs_user_data
from app.tests import common


class TestCompanyRetrieve:
    async def test_company_not_exists(self, aiohttp_client) -> None:
        """
        Retrieve non existing company
        """
        # Arrange
        app, client = await common.prepare_app_client(aiohttp_client)

        # Act
        response = await client.get(
            path=f'/api/private/integrations/hrs/companies/{COMPANY_EDRPOU_1}',
            headers=hrs_common.prepare_hrs_auth_headers(),
        )

        # Assert
        data = await response.json()
        assert response.status == HTTPStatus.NOT_FOUND
        assert data == {
            'code': 'object_does_not_exist',
            'details': {'edrpou': COMPANY_EDRPOU_1, 'type': 'company', 'type_label': 'Компанія'},
            'reason': 'Компанію не знайдено у базі даних',
        }

    async def test_company_exists(self, aiohttp_client) -> None:
        """
        Retrieve existing company
        """
        # Arrange
        app, client = await common.prepare_app_client(aiohttp_client)
        await prepare_hrs_user_data(app=app, company_edrpou=COMPANY_EDRPOU_1)

        # Act
        response = await client.get(
            path=f'/api/private/integrations/hrs/companies/{COMPANY_EDRPOU_1}',
            headers=hrs_common.prepare_hrs_auth_headers(),
        )

        # Assert
        data = await response.json()
        assert response.status == HTTPStatus.OK
        assert data == {'edrpou': COMPANY_EDRPOU_1, 'name': ''}
