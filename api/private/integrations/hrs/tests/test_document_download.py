import io
from http import HTTPStatus

from api.downloads import pdf
from api.private.integrations.hrs.tests.common import (
    COMPANY_EDRPOU_1,
    COMPANY_EDRPOU_2,
    USER_EMAIL_2,
    USER_TOKEN_HEADER,
    get_user_token,
    prepare_hrs_auth_headers,
    prepare_hrs_user_data,
)
from app.tests.common import (
    prepare_app_client,
    prepare_document_data,
)


async def test_document_download_success(aiohttp_client, monkeypatch) -> None:
    app, client = await prepare_app_client(aiohttp_client)

    user = await prepare_hrs_user_data(app=app)
    target_user_1 = await prepare_hrs_user_data(
        app=app, email=USER_EMAIL_2, company_edrpou=COMPANY_EDRPOU_2
    )
    test_content = b'hello world'
    document = await prepare_document_data(
        app=app,
        owner=user,
        document_recipients=[
            {
                'edrpou': COMPANY_EDRPOU_2,
                'emails': [target_user_1.email],
            }
        ],
        title='hello-world',
        extension='.pdf',
        content=test_content,
    )
    monkeypatch.setattr(pdf, 'generate_signed_file', lambda *ar, **kw: io.BytesIO(test_content))

    response = await client.get(
        path=f'/api/private/integrations/hrs/documents/{document.id}',
        headers={
            **prepare_hrs_auth_headers(),
            USER_TOKEN_HEADER: get_user_token(hr_edrpou=COMPANY_EDRPOU_1, hr_email=user.email),
        },
    )
    assert response.status == HTTPStatus.OK, response.content
    assert await response.content.read() == test_content


async def test_document_download_print_success(aiohttp_client, monkeypatch) -> None:
    app, client = await prepare_app_client(aiohttp_client)

    test_content = b'hello world'
    user = await prepare_hrs_user_data(app=app)
    target_user_1 = await prepare_hrs_user_data(
        app=app, email=USER_EMAIL_2, company_edrpou=COMPANY_EDRPOU_2
    )
    document = await prepare_document_data(
        app=app,
        owner=user,
        document_recipients=[
            {
                'edrpou': COMPANY_EDRPOU_2,
                'emails': [target_user_1.email],
            }
        ],
        title='hello-world',
        extension='.pdf',
        content=test_content,
    )
    monkeypatch.setattr(pdf, 'generate_print_file', lambda *ar, **kw: io.BytesIO(test_content))

    response = await client.get(
        path=f'/api/private/integrations/hrs/documents/{document.id}/print',
        headers={
            **prepare_hrs_auth_headers(),
            USER_TOKEN_HEADER: get_user_token(hr_edrpou=COMPANY_EDRPOU_1, hr_email=user.email),
        },
    )
    assert response.status == HTTPStatus.OK, response.content
    assert await response.content.read() == test_content
