import datetime
from typing import Any, Literal

import pydantic
from aiohttp import web
from pydantic import ConfigDict
from vchasno_crm.types import dict_without_empty_values

from api.errors import DoesNotExist, InvalidRequest, Object
from api.private.super_admin.types import SendDocumentCtx
from app.auth.types import Role
from app.auth.validators import validate_user_by_role_exists
from app.billing.enums import AccountRate, CompanyRateStatus
from app.documents.validators import validate_document_access, validate_document_exists
from app.flags import FeatureFlags
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.profile.validators import validate_role_exists, validate_user_not_exists

DAY_IN_SECONDS = 60 * 60 * 24


class SendPushNotificationToMobileAppSchema(pydantic.BaseModel):
    user_id: str
    title: str
    description: str
    payload: dict[str, Any] | None = None


class UpdatePricePerDocumentSchema(pydantic.BaseModel):
    company_id: pv.UUID
    price_per_document: float = pydantic.Field(ge=0)


class FetchBankTransactionsSchema(pydantic.BaseModel):
    start_date: datetime.datetime | None = None
    end_date: pv.RightDatetime | None = None


class LinkCompaniesBalanceSchema(pydantic.BaseModel):
    edrpous: list[pv.EDRPOU]


class SoftDeleteUserSchema(pydantic.BaseModel):
    email: pv.Email


class Reset2FASchema(pydantic.BaseModel):
    email: pv.Email


class ChangeEmailForUserSchema(pydantic.BaseModel):
    email: pv.Email | None = None
    user_id: pv.UUID | None = None

    new_email: pv.Email
    update_user: bool = True
    update_recipients: bool = True
    update_documents: bool = True
    update_automations: bool = True
    update_concierge: bool = True


class SendDocumentSchema(pydantic.BaseModel):
    document_id: pv.UUID
    role_id: str


class DownloadUserActionsSchema(pydantic.BaseModel):
    date_from: datetime.datetime | None = None
    date_to: datetime.datetime | None = None


class BulkTokenGenerationSchema(pydantic.BaseModel):
    email: pv.Email


class CreateUserSchema(pydantic.BaseModel):
    email: pv.Email
    is_email_confirmed: bool


class ActivateTrialCompanyRatesSchema(pydantic.BaseModel):
    edrpous: list[pv.EDRPOU]
    days: int = pydantic.Field(ge=1)


class RestoreDocumentSchema(pydantic.BaseModel):
    document_id: pv.UUID
    notification_email: pv.Email


class CrmSyncRatesFiltersSchema(pydantic.BaseModel):
    model_config = ConfigDict(extra='forbid')

    company_id: pv.UUID | None = None
    rates: list[AccountRate] | None = None
    date_from: datetime.date | None = None
    date_to: datetime.date | None = None
    cursor: pv.UUID | None = None
    status: CompanyRateStatus | None = None
    only_with_bills: bool | None = None

    @pydantic.model_validator(mode='after')
    def validate_filters(self) -> 'CrmSyncRatesFiltersSchema':
        data = dict_without_empty_values(self.dict())
        if not data:
            raise ValueError('At least one filter must be provided!')
        return self


class UltimateUpdatePricePerUserSchema(pydantic.BaseModel):
    edrpou: str
    price_per_user: int


class ManageAwsSesBlacklistSchema(pydantic.BaseModel):
    email: pv.Email
    action: Literal['check', 'unblock', 'block'] = 'check'


class SurveyUserListSchema(pydantic.BaseModel):
    emails: list[pv.Email] = pydantic.Field(min_length=0, max_length=1_000)
    survey_id: str = pydantic.Field(min_length=1, max_length=100)
    ttl: int = pydantic.Field(DAY_IN_SECONDS * 60, ge=DAY_IN_SECONDS, le=DAY_IN_SECONDS * 90)


def validate_feature_flag(name: str) -> FeatureFlags:
    try:
        return FeatureFlags(name)
    except ValueError:
        raise DoesNotExist(Object.feature_flag)


async def validate_activate_trial_companies_rates(
    request: web.Request,
) -> ActivateTrialCompanyRatesSchema:
    data = await validators.validate_json_request(request)
    return validators.validate_pydantic(ActivateTrialCompanyRatesSchema, data)


def validate_get_es_document(request: web.Request) -> str:
    document_id = request.match_info.get('document_id')
    return validators.validate_pydantic_adapter(pv.UUIDAdapter, value=document_id)


async def validate_send_document(
    conn: DBConnection,
    request: web.Request,
) -> SendDocumentCtx:
    """
    Validate request for send send document by super admin
    """
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(SendDocumentSchema, raw_data)

    user = await validate_user_by_role_exists(conn, role_id=data.role_id)
    await validate_document_exists(conn, data={'document_id': data.document_id})
    await validate_document_access(conn, user=user, document_id=data.document_id)

    return SendDocumentCtx(
        document_id=data.document_id,
        user=user,
    )


async def validate_create_user(request: web.Request, conn: DBConnection) -> CreateUserSchema:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(CreateUserSchema, raw_data)
    email: str = data.email

    await validate_user_not_exists(conn, email=email)

    return data


async def validate_delete_role(conn: DBConnection, request: web.Request) -> Role:
    raw_role_id = request.match_info.get('role_id')
    role_id = validators.validate_pydantic_adapter(pv.UUIDAdapter, value=raw_role_id)

    role = await validate_role_exists(conn, role_id=role_id)

    if role.has_hrs_role:
        raise InvalidRequest(
            reason=_('Співробітник має кадрову роль. Видаліть роль із сервісу Вчасно.Кадри'),
        )

    return role


async def validate_manage_aws_ses_blacklist(request: web.Request) -> ManageAwsSesBlacklistSchema:
    raw_data = await validators.validate_json_request(request)
    return validators.validate_pydantic(ManageAwsSesBlacklistSchema, raw_data)
