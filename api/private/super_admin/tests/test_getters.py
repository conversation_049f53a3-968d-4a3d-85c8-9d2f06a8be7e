from api.private.super_admin.tables import super_admin_actions_table
from app.lib.enums import DocumentStatus, SuperAdminActionType
from app.models import select_all
from app.tests.common import (
    PRIVATE_API_GET_DOCUMENT_STATE,
    TEST_COMPANY_EDRPOU,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_DOCUMENT_NUMBER,
    TEST_DOCUMENT_TITLE,
    TEST_TAG_NAME,
    TEST_USER_EMAIL,
    VCHASNO_EDRPOU,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_user_data,
    request_connect_tags_and_documents,
    request_create_tags_for_documents,
    set_company_config,
)


async def test_get_document_state(aiohttp_client):
    app, client, super_admin = await prepare_client(
        aiohttp_client,
        email=TEST_USER_EMAIL,
        company_edrpou=VCHASNO_EDRPOU,
        is_admin=True,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(
        app, company_id=super_admin.company_id, master=True, admin_is_superadmin=True
    )

    # Create recipient
    recipient = await prepare_user_data(
        app, email=TEST_DOCUMENT_EMAIL_RECIPIENT, company_edrpou=TEST_COMPANY_EDRPOU
    )

    document = await prepare_document_data(
        app,
        super_admin,
        number=TEST_DOCUMENT_NUMBER,
        document_recipients=[
            {'edrpou': TEST_COMPANY_EDRPOU, 'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT]}
        ],
        status_id=DocumentStatus.sent.value,
    )

    # create doc tags
    tags = await request_create_tags_for_documents(
        client=client,
        user=super_admin,
        names=[TEST_TAG_NAME],
        documents_ids=[document.id],
    )
    tag1_id = tags[0]['id']

    await request_connect_tags_and_documents(
        client=client, user=super_admin, tags_ids=[tag1_id], documents_ids=[document.id]
    )

    response = await client.get(
        PRIVATE_API_GET_DOCUMENT_STATE.format(document_id=document.id),
        headers=prepare_auth_headers(super_admin),
    )

    assert response.status == 200

    data = await response.json()

    # check base doc info
    assert data['edrpou_owner'] == VCHASNO_EDRPOU
    assert data['edrpou_recipient'] == TEST_COMPANY_EDRPOU
    assert data['title'] == TEST_DOCUMENT_TITLE
    assert data['number'] == TEST_DOCUMENT_NUMBER
    assert data['status'] == DocumentStatus.sent.value

    # check doc listings
    assert len(data['listings']) == 2
    recipient_listing = next(
        item for item in data['listings'] if item['edrpou'] == TEST_COMPANY_EDRPOU
    )
    assert recipient_listing['role'] == recipient.role_id
    assert recipient_listing['user_id'] == recipient.id
    assert recipient_listing['user_email'] == recipient.email

    # check doc tags
    tag = data['tags'][0]
    assert tag['id'] == tag1_id
    assert tag['name'] == TEST_TAG_NAME

    # Assert document state retrieving triggers super_admin_actions_table object creation
    async with app['db'].acquire() as conn:
        actions = await select_all(
            conn,
            super_admin_actions_table.select().where(
                super_admin_actions_table.c.type != SuperAdminActionType.update_sa_permissions
            ),
        )

    assert len(actions) == 1

    action = actions[0]
    assert action.role_id == super_admin.role_id
    assert action.email == TEST_USER_EMAIL
    assert action.type == SuperAdminActionType.document_request_state
    assert len(action.extra_details) == 1

    document_info = action.extra_details[0]

    assert 'document_id' in document_info
