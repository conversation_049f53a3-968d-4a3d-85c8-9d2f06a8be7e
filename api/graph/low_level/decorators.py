from collections.abc import Callable
from functools import wraps
from typing import Any

from hiku.engine import Context

from api.graph.constants import (
    AUTH_SUPER_ADMIN_PERMISSIONS,
    AUTH_USER_KEY,
    DB_ENGINE_KEY,
)
from api.graph.utils import is_sign_session
from app.auth.utils import (
    get_company_config,
    has_super_admin_access,
)
from app.lib.types import AnyAwaitable, DataDict

ResolverFunc = Callable[..., AnyAwaitable]
ResolverDecorator = Callable[[ResolverFunc], ResolverFunc]


def super_admin_resolver(
    resolver_func: ResolverFunc | None = None,
    *,
    response: Any | None = None,
    required_permissions: set[str],
) -> ResolverDecorator:
    """Restrict resolver data only for users with one of required permissions or admins."""

    def decorator(
        resolver: ResolverFunc,
    ) -> Callable[[Context, DataDict], AnyAwaitable]:
        @wraps(resolver)
        async def wrapper(ctx: Context, options: DataDict) -> Any:
            if is_sign_session(ctx):
                return []

            user = ctx[AUTH_USER_KEY]
            super_admin_permissions = ctx[AUTH_SUPER_ADMIN_PERMISSIONS]

            async with ctx[DB_ENGINE_KEY].acquire() as conn:
                company_config = await get_company_config(
                    conn, company_edrpou=getattr(user, 'company_edrpou', None)
                )

            return (
                await resolver(ctx, options)
                if has_super_admin_access(
                    user=user,
                    company_config=company_config,
                    required_permissions=required_permissions,
                    super_admin_permissions=super_admin_permissions,
                )
                else response or []
            )

        return wrapper

    # fmt: off
    return (
        decorator(resolver_func)  # type: ignore
        if callable(resolver_func)
        else decorator
    )
    # fmt: on
