import asyncio
import json
import logging
from collections.abc import Callable
from functools import partial
from typing import Any, TypeVar

from aiohttp import web
from hiku.engine import Context, Engine
from hiku.graph import Graph
from hiku.readers.graphql import read
from hiku.result import denormalize

from api.graph.constants import (
    APP_KEY,
    AUTH_METHOD_KEY,
    AUTH_SUPER_ADMIN_PERMISSIONS,
    AUTH_USER_KEY,
    CONTEXT_STORAGE,
    DB_ENGINE_KEY,
    DB_READONLY_KEY,
    ES_KEY,
    REQUEST_KEY,
    SIGN_SESSION_ID_KEY,
)
from api.graph.exceptions import (
    GraphQLBaseError,
    GraphQLCancelledError,
    GraphQLError,
    GraphQLSyntaxError,
    GraphQLUnhandledError,
    GraphQLValidationError,
)
from api.graph.types import LinkResolver
from api.graph.validators import QueryValidator, validate_query_graph
from app.auth.constants import AUTH_METHOD_APP_KEY, AUTH_USER_SUPER_ADMIN_PERMISSIONS
from app.auth.enums import Auth<PERSON>ethod
from app.auth.types import AuthUser
from app.auth.utils import get_sign_session_id_from_request
from app.lib import validators
from app.lib.enums import UserRole
from app.lib.helpers import soft_json_serializer
from app.lib.types import DataDict, StrList
from app.services import services

logger = logging.getLogger(__name__)
T = TypeVar('T')


def graphql_response(
    *,
    data: DataDict | None = None,
    error: GraphQLBaseError | None = None,
) -> web.Response:
    """
    Prepare response for GraphQL query
    """
    if error:
        return web.json_response(
            # currently we support only one error to simplify the logic, but in general
            # graphql spec allows multiple errors at once
            data={'data': None, 'errors': [error.to_response()]},
            status=error.http_status or 400,
        )

    if data:
        return web.json_response(
            data={'data': data},
            dumps=partial(json.dumps, default=soft_json_serializer),
            status=200,
        )

    raise TypeError('data or errors keyword argument missed')


async def query_graph(
    request: web.Request,
    user: AuthUser,
    query: str,
    variables: DataDict | None = None,
) -> DataDict:
    """Process request to GraphQL.

    1. Prepare and validate Hiku query
    2. Execute query in graph
    3. Denormalize results
    """
    engine: Engine = request.app['hiku_engine']
    graph: Graph = request.app['hiku_graph']
    query = read(query, variables)

    # Validate query
    validator = QueryValidator(graph)
    validator.visit(query)
    # usually 10 is good enough, but we have some complex queries
    # for introspection, so increase it a bit
    validator.check_depth(13)
    if validator.errors.list:
        raise GraphQLValidationError(validator.errors.list)

    app = request.app

    # Execute graph
    context = {
        AUTH_USER_KEY: user,
        APP_KEY: app,
        AUTH_METHOD_KEY: request.get(AUTH_METHOD_APP_KEY),
        DB_ENGINE_KEY: services.db_readonly,
        DB_READONLY_KEY: services.db_readonly,
        SIGN_SESSION_ID_KEY: get_sign_session_id_from_request(request),
        ES_KEY: services.es,
        REQUEST_KEY: request,
        AUTH_SUPER_ADMIN_PERMISSIONS: request.get(AUTH_USER_SUPER_ADMIN_PERMISSIONS),
        CONTEXT_STORAGE: {},
    }
    result = await engine.execute(graph, query, context)

    # Denormalize results
    return denormalize(graph, result)


async def process_query_graph(request: web.Request, user: AuthUser) -> web.Response:
    data = validate_query_graph(await validators.validate_json_request(request))
    try:
        # Currently we have 30 sec timeout on istio level for all requests.
        # And we want to track slow graph queries,
        # so we set timeout to request_timeout * 2 = 29 sec.
        async with asyncio.timeout(services.config.app.request_timeout * 2):
            graph_data = await query_graph(request, user, data.query, data.variables)
    except GraphQLSyntaxError:
        logger.exception('GraphQL syntax error')
        return graphql_response(error=GraphQLSyntaxError())

    except GraphQLValidationError as err:
        logger.error(
            'Validation error on executing GraphQL',
            extra=dict(data, errors=err.errors),
        )
        return graphql_response(error=err)

    except GraphQLError as err:
        return graphql_response(error=err)

    except (asyncio.CancelledError, TimeoutError):
        logger.warning(
            'Slow query on executing GraphQL',
            extra={
                'query': data.query,
                'variables': str(data.variables),
            },
        )
        return graphql_response(error=GraphQLCancelledError())

    except Exception:
        logger.exception(
            'Unhandled exception on executing GraphQL',
            extra={
                'query': data.query,
                'variables': str(data.variables),
            },
        )
        return graphql_response(error=GraphQLUnhandledError())

    return graphql_response(data=graph_data)


def is_sign_session(ctx: Context) -> bool:
    return ctx[AUTH_METHOD_KEY] == AuthMethod.sign_session


def sign_session_secure_link(
    return_value: T,
) -> Callable[[LinkResolver[T]], LinkResolver[T]]:
    def decorator(func: LinkResolver[T]) -> LinkResolver[T]:
        async def wrapper(ctx: Context, ids: StrList, *args: Any) -> list[T]:
            if is_sign_session(ctx):
                return [return_value for _ in ids]

            return await func(ctx, ids, *args)

        return wrapper

    return decorator


def can_view_coworkers_restriction(
    return_value: T,
) -> Callable[[LinkResolver[T]], LinkResolver[T]]:
    def decorator(func: LinkResolver[T]) -> LinkResolver[T]:
        async def wrapper(ctx: Context, ids: StrList, *args: Any) -> list[T]:
            if (
                ctx[AUTH_USER_KEY].user_role != UserRole.admin.value
                and not ctx[AUTH_USER_KEY].can_view_coworkers
            ):
                return [return_value for _ in ids]

            return await func(ctx, ids, *args)

        return wrapper

    return decorator
