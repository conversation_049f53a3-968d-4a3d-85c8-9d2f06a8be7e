import datetime
import io
from decimal import Decimal
from http import HTTPStatus
from unittest import mock
from uuid import uuid4

import pytest
import ujson
from sqlalchemy.dialects.postgresql import insert

from api.graph.tests.common import (
    TODAY,
    TOMORROW,
    YESTERDAY,
)
from api.public.tests.common import (
    TEST_RECIPIENT_EDRPOU,
    TEST_RECIPIENT_EDRPOU_2,
    TEST_RECIPIENT_EMAIL,
)
from app.archive.tests.utils import archive_documents_request
from app.auth.db import (
    insert_token,
    select_role_by_id,
    select_user_by_token_hash,
    update_company_config,
    update_role,
)
from app.auth.enums import RoleActivationSource, RoleStatus
from app.auth.helpers import generate_hash_sha512
from app.auth.schemas import CompanyConfig
from app.auth.tables import user_onboarding_table
from app.auth.utils import delete_role_base, is_fop
from app.billing.db import insert_rate_extension
from app.billing.enums import (
    AccountRate,
    AccountStatus,
    AccountType,
    BillServicesType,
    BillStatus,
    CompanyLimit,
    CompanyRateStatus,
    CreatioBillType,
    FakeBillRate,
    RateExtensionStatus,
    RateExtensionType,
)
from app.billing.tables import billing_account_table
from app.billing.tests.test_billing_crm import CRM_AUTH_TOKEN
from app.billing.tests.utils import (
    LATEST_START_RATE,
    START_PRICE_TOV_SUBUNITS,
)
from app.billing.types import (
    AddBillServiceRateOptions,
)
from app.comments.tables import comment_table
from app.contacts.db import (
    insert_contact_person_phone,
)
from app.contacts.sync.db import insert_contact, insert_contact_person
from app.document_antivirus.enums import AntivirusCheckStatus
from app.document_antivirus.types import DocumentAntivirusCheck
from app.document_antivirus.utils import add_document_antivirus_check
from app.document_versions.enums import DocumentVersionType
from app.document_versions.tests.utils import prepare_document_version
from app.document_versions.utils import add_document_content_version
from app.documents import utils as documents_utils
from app.documents.db import (
    add_document_child,
    add_documents_meta,
    delete_documents_from_db,
    insert_listings,
    insert_recipients,
    select_document_by_id,
)
from app.documents.enums import (
    AccessSource,
    DocumentReviewState,
    FirstSignBy,
)
from app.documents.tests.utils import prepare_private_document
from app.documents.types import (
    Document,
    UpdateSignersDataSignerEntity,
    UpdateSignersDataSignerType,
)
from app.documents_fields.enums import DocumentFieldType
from app.documents_fields.tables import (
    document_parameters_table,
    documents_fields_table,
)
from app.flags import FeatureFlags
from app.flow.tests.test_flow_view import ADD_FLOW_URL
from app.groups.db import update_group_members
from app.groups.utils import (
    add_group,
    add_group_member,
)
from app.landing.tests.test_internal_landing_access import HEADER_KEY
from app.lib.datetime_utils import local_now, naive_local_now, utc_now
from app.lib.enums import (
    DocumentFolder,
    DocumentListOrder,
    DocumentListSortDate,
    DocumentStatus,
    UserRole,
)
from app.profile.constants import (
    KEP_POPUP_DATE_FORMAT,
    KEP_POPUP_DATE_KEY,
)
from app.reviews.db import (
    update_review_setting,
)
from app.reviews.enums import (
    ReviewRequestStatus,
    ReviewType,
)
from app.reviews.tables import review_request_table
from app.reviews.tests.test_reviews_views import REVIEW_URL
from app.reviews.tests.utils import insert_review
from app.services import services
from app.sign_sessions.enums import SignSessionStatus, SignSessionType
from app.signatures.db import insert_cloud_signer
from app.signatures.tables import document_signer_table
from app.signatures.utils import update_document_signers
from app.tags.db import (
    insert_document_tags,
    insert_tags_for_documents,
    select_tags_for_deletion,
)
from app.tags.tables import (
    contact_tag_table,
    document_tag_table,
    role_tag_table,
    tag_table,
)
from app.tags.tests.common import create_document_tags_with_access
from app.tags.utils import create_new_tags_for_documents
from app.tests.common import (
    BILLS_URL,
    CRM_BILLS_URL,
    FOP_EDRPOU,
    GRAPHQL_URL,
    GROUP,
    ROLE,
    TEST_COMPANY_EDRPOU,
    TEST_CONTACT,
    TEST_CONTACT_COMPANY_NAME,
    TEST_CONTACT_COMPANY_SHORT_NAME,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_DOCUMENT_NUMBER,
    TEST_DOCUMENT_TITLE,
    TEST_RECIPIENT_COMPANY_FULL_NAME,
    TEST_RECIPIENT_COMPANY_NAME,
    TEST_TAG_NAME,
    TEST_TAG_NAME1,
    TEST_TAG_NAME2,
    TEST_USER_PASSWORD,
    TOV_EDRPOU,
    GraphqlOrderedDocument,
    cleanup_on_teardown,
    fetch_graphql,
    fetch_graphql_raw,
    graphql_response,
    insert_values,
    login,
    prepare_app_client,
    prepare_auth_headers,
    prepare_bill,
    prepare_billing_account,
    prepare_client,
    prepare_company_config,
    prepare_company_data,
    prepare_contact,
    prepare_contacts,
    prepare_coworker_role,
    prepare_document_data,
    prepare_document_required_fields,
    prepare_document_signer,
    prepare_document_upload,
    prepare_flow_item,
    prepare_group,
    prepare_private_key,
    prepare_referer_headers,
    prepare_review,
    prepare_review_requests,
    prepare_sign_session_data,
    prepare_sign_session_headers,
    prepare_signature_data,
    prepare_signature_form_data,
    prepare_uploaded_signed_documents,
    prepare_user_data,
    request_create_tags_for_documents,
    request_document_update,
    send_document,
    set_billing_company_config,
    set_company_config,
    sign_and_send_document,
    with_elastic,
)

COUNT_TOTAL_UPLOADS = 5

GQL_CONTACTS = (
    '{ allContacts { count contacts { id companyId edrpou persons { id email mainRecipient } } } }'
)
GQL_CONTACT_PERSONS = '{ allContactPersons { contact_persons { contact { companyId } } } }'
GQL_CURRENT_ROLE = '{ currentRole { status } }'
GQL_CURRENT_ROLE_REGISTRATION_META = '{ currentRole { hasFewSignatures hasFewReviews } }'
GQL_CURRENT_ROLE_ADMIN_FLAGS = '{ currentRole { isAdmin isMasterAdmin isSuperAdmin } }'
GQL_CURRENT_ROLE_SUPER_ADMIN_FLAGS = (
    '{ currentRole { canViewClientData, canEditClientData, canEditSpecialFeatures } }'
)
GQL_COMPANY_ROLES_SUPER_ADMIN_FLAGS = (
    'query Company($id: String!) {company(id: $id) '
    '{roles {id canViewClientData canEditClientData canEditSpecialFeatures}}}'
)
GQL_COMPANY_ROLES_STATUSES = 'query Company($id: String!) {company(id: $id) {roles {id status}}}'
GQL_CURRENT_ROLE_COMPANY_CONFIG = '{ currentRole { company { config usedDocumentCount } } }'
GQL_CURRENT_ROLE_PERMISSIONS = (
    '{ currentRole { canViewCoworkers canViewDocument canCommentDocument canUploadDocument '
    'canDownloadDocument canPrintDocument canDeleteDocument '
    'canSignAndRejectDocument canInviteCoworkers canEditCompany canEditRoles '
    'canReceiveInbox canReceiveInboxAsDefault canReceiveComments canReceiveRejects '
    'canReceiveReminders '
    '} }'
)

GQL_CURRENT_ROLE_SORT_DOCUMENTS = '{ currentRole { sortDocuments } }'
GQL_COWORKER_ROLE_TOKENS = (
    '{ currentRole    { id companyEdrpou company        { roles { hasToken } } } }'
)
GQL_CURRENT_USER = '{ currentUser { id is2FAEnabledByRule createdBy } }'
GQL_CURRENT_USER_CURRENT_ROLE = '{ currentUser { id } currentRole { id } }'
GQL_DOCUMENTS_IS_ONE_SIGN = '{ allDocuments { documents { isOneSign } } }'
GQL_REVIEWS = (
    '{ allDocuments { documents { id reviews { id roleId type dateCreated documentId } } } }'
)
GQL_REVIEWS_IS_LAST = (
    '{ allDocuments { documents { id reviews (add_is_last_condition: true) { id roleId '
    'type dateCreated documentId } } } }'
)
GQL_REVIEW_REQUESTS_ACTIVE = (
    '{ allDocuments { documents { id reviewRequests { documentId status '
    'fromRoleId toRoleId dateCreated dateUpdated '
    'fromRole { user { email } } toRole { user { email } } } } } }'
)
GQL_REVIEW_REQUESTS = (
    '{ allDocuments { documents { id reviewRequests (is_all_requests: true)'
    '{ documentId status fromRoleId toRoleId dateCreated dateUpdated '
    'fromRole { user { email } } toRole { user { email } } } } } }'
)
GQL_CURRENT_SIGN_SESSION = (
    '{ currentSignSession { id documentId roleId edrpou email isLegal type '
    'status documentStatus finishUrl cancelUrl role { id } } }'
)
GQL_ALL_DOCUMENTS_SIGNERS = """
    {
        allDocuments(isWaitMySign: true) {
            count
            documents {
                emailRecipient
                signers {
                    roleId
                    order
                    assignerId
                    role {
                        user {
                            email
                        }
                    }
                }
            }
        }
    }
    """

GQL_WAIT_MY_REVIEW = f"""
        {{
          allDocuments(reviewFolder: "{DocumentReviewState.wait_my_review.value}") {{
            count
            documents {{
                id
                reviewRequests {{
                  toRoleId
                  toGroupId
                  order
                }}
            }}
          }}
        }}
"""

TEST_DOCUMENTS = [
    GraphqlOrderedDocument(
        '1',
        '2017-02-20T12:12:00',
        '2017-02-10T00:00:00',
        '12341234',
        '<EMAIL>',
        'number 1',
    ),
    GraphqlOrderedDocument(
        '2',
        '2017-02-21T13:00:00',
        None,
        '23452345',
        '<EMAIL>',
        'number 2',
    ),
    GraphqlOrderedDocument(
        '3',
        '2017-02-22T14:00:00',
        '2017-02-08T00:00:00',
        '34563456',
        '<EMAIL>',
        'number 3',
    ),
]

TEST_UUID_1 = '1397a003-92ff-470d-93ee-42562e2f1da1'
TEST_UUID_2 = '2e5d0e37-6762-49c3-9397-98a4ac37681f'
TEST_UUID_3 = '339427e1-f046-4a96-82fe-7c8a8e7a9db9'
TEST_UUID_4 = '44de4100-8a02-4d7b-adf6-fd6d3bbace97'
TEST_UUID_5 = '5162667e-bda8-4e67-8e0c-6ba6418bda5d'
TEST_UUID_6 = 'a9301327-df9d-4651-85a3-b7cdf0072e7f'

FIELD_ID_1 = '80f27a97-8bae-4c0f-8298-eed7d4fb37a6'
FIELD_ID_2 = '0fd62ff0-1839-4eeb-9240-91b9df9cae18'
FIELD_ID_3 = '97feeadc-7c98-4995-bcfd-505f931ed581'
FIELD_ID_4 = 'a4631afa-b091-4a96-9d4c-5a75d20c1cf8'
FIELD_ID_5 = '528b77ca-a045-4481-b40a-8fd26ea18b96'
FIELD_ID_6 = '2787f6bb-43cd-4da2-ac29-d9c3e30e4adb'

COMPANY_1 = '3d6f3e3b-5d20-4cba-904e-f4525b4b7686'
COMPANY_2 = 'b5b801ea-15f4-4741-9d68-b7b7f046e9fd'


async def test_contacts_access_after_invite(aiohttp_client):
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(
        app, company_edrpou='88887777', email='<EMAIL>'
    )
    user1_headers = prepare_auth_headers(user1)
    user2_headers = prepare_auth_headers(user2)
    link_name = 'contact'

    try:
        async with app['db'].acquire() as conn:
            await prepare_contact(conn, user1)

            # 1. User #2 has no access to User #1 contacts
            for headers, expected_count_contacts in [
                (user1_headers, 1),
                (user2_headers, 0),
            ]:
                result = (await fetch_graphql(client, GQL_CONTACT_PERSONS, headers))[
                    'allContactPersons'
                ]
                assert len(result['contact_persons']) == expected_count_contacts

            # 2. User #2 creates his own contacts
            await prepare_contact(conn, user2)

            for headers, user, expected_count_contacts in [
                (user1_headers, user1, 1),
                (user2_headers, user2, 1),
            ]:
                result = (await fetch_graphql(client, GQL_CONTACT_PERSONS, headers))[
                    'allContactPersons'
                ]
                assert len(result['contact_persons']) == expected_count_contacts
                assert result['contact_persons'][0][link_name]['companyId'] == user.company_id

            # User #1 invites User #2 to shared EDRPOU 00000000
            user2_new_role = await prepare_coworker_role(
                conn,
                {
                    'company_id': user1.company_id,
                    'status': RoleStatus.active,
                    'user_id': user2.id,
                },
            )
            user2_new_role_id = user2_new_role.id

            # 3. User #2 does not see contacts of User #1 without switching
            # to new role.
            for headers, user, expected_count_contacts in [
                (user1_headers, user1, 1),
                (user2_headers, user2, 1),
            ]:
                result = (await fetch_graphql(client, GQL_CONTACT_PERSONS, headers))[
                    'allContactPersons'
                ]
                assert len(result['contact_persons']) == expected_count_contacts
                assert result['contact_persons'][0][link_name]['companyId'] == user.company_id

            # User #2 activates new role
            response = await client.post(
                f'/internal-api/roles/{user2_new_role_id}/activate',
                headers=user2_headers,
            )
            assert response.status == 200

            # Prepare User #2 headers for new role
            user2_token = await insert_token(conn, {'role_id': user2_new_role_id})
            user2_coworker = await select_user_by_token_hash(
                conn, generate_hash_sha512(user2_token), raw_token=user2_token
            )
            user2_coworker_headers = prepare_auth_headers(user2_coworker)

            # User #2 now can access to contacts of User #1
            for headers, _, expected_count_contacts in [
                (user1_headers, user1, 1),
                (user2_coworker_headers, user2_coworker, 1),
            ]:
                result = (await fetch_graphql(client, GQL_CONTACT_PERSONS, headers))[
                    'allContactPersons'
                ]
                assert len(result) == expected_count_contacts
                assert result['contact_persons'][0][link_name]['companyId'] == user1.company_id
    finally:
        await cleanup_on_teardown(app)


async def test_contacts(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    company_id = user.company_id
    headers = prepare_auth_headers(user)

    async with app['db'].acquire() as conn:
        # One contact
        contact_id = await insert_contact(conn, TEST_CONTACT['company'].copy(), company_id)

        # Two contact persons with different emails
        person1 = TEST_CONTACT['person'].copy()
        await insert_contact_person(conn, person1, contact_id)
        person2 = TEST_CONTACT['person'].copy()
        person2['email'] = '<EMAIL>'
        person2['main_recipient'] = True
        person_2_id = await insert_contact_person(conn, person2, contact_id)

    data = await fetch_graphql(client, GQL_CONTACTS, headers)
    assert len(data['allContacts']['contacts']) == 1

    contact_person = data['allContacts']['contacts'][0]['persons']
    assert len(contact_person) == 2
    for item in contact_person:
        if item['id'] == person_2_id:
            assert item['mainRecipient'] is True
        else:
            assert item['mainRecipient'] is False

    assert data['allContacts']['count'] == 1


async def test_contact_registration_field_and_option(aiohttp_client):
    app, client, user1 = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user1)

    try:
        async with app['db'].acquire() as conn:
            reg_edrpous, unreg_edrpous = await prepare_contacts(app, conn, user1)

        # Getting registered and unregistered contacts using field isRegistered
        query = '{ allContacts { contacts { isRegistered, edrpou } } }'
        data = await fetch_graphql(client, query, headers)
        reg_edrpous_field = {
            contact['edrpou']
            for contact in data['allContacts']['contacts']
            if contact['isRegistered']
        }
        unreg_edrpous_field = {
            contact['edrpou']
            for contact in data['allContacts']['contacts']
            if not contact['isRegistered']
        }

        # Getting registered contacts using options isRegistered
        query = '{ allContacts(isRegistered: true){ contacts { edrpou } } }'
        data = await fetch_graphql(client, query, headers)
        reg_edrpous_option = {contact['edrpou'] for contact in data['allContacts']['contacts']}

        # Getting unregistered contacts using option isRegistered
        query = '{ allContacts(isRegistered: false){ contacts { edrpou } } }'
        data = await fetch_graphql(client, query, headers)
        unreg_edrpous_option = {contact['edrpou'] for contact in data['allContacts']['contacts']}

        # Getting count of registered contacts using options isRegistered
        query = '{ allContacts(isRegistered: true){ count } }'
        count_registred = await fetch_graphql(client, query, headers)

        # Getting count of registered contacts using options isRegistered
        query = '{ allContacts(isRegistered: false){ count } }'
        count_unregistred = await fetch_graphql(client, query, headers)

        assert reg_edrpous == reg_edrpous_field == reg_edrpous_option
        assert unreg_edrpous == unreg_edrpous_field == unreg_edrpous_option
        assert len(unreg_edrpous) == count_unregistred['allContacts']['count']
        assert len(reg_edrpous) == count_registred['allContacts']['count']
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'search, expected',
    [
        ('"<EMAIL>"', 1),
        ('"<EMAIL>"', 0),
        ('"Contact first name"', 1),
        ('"Missing first name"', 0),
        ('"Second name"', 1),
        ('"Missing second name"', 0),
        ('"Last name"', 1),
        ('"Missing Last name"', 0),
        ('"12345678"', 1),
        ('"invalid edrpou"', 0),
        ('"company name"', 1),
        ('"Missing company name"', 0),
        ('"380631234567"', 1),
        ('"missing phone"', 0),
    ],
)
async def test_contact_persons_search(aiohttp_client, search, expected):
    app, client, user = await prepare_client(aiohttp_client)
    company_id = user.company_id
    headers = prepare_auth_headers(user)

    try:
        async with app['db'].acquire() as conn:
            contact_id = await insert_contact(conn, TEST_CONTACT['company'].copy(), company_id)
            person1 = TEST_CONTACT['person'].copy()
            contact_person_id = await insert_contact_person(conn, person1, contact_id)
            await insert_contact_person_phone(
                conn=conn,
                person_id=contact_person_id,
                phone='+380631234567',
            )

        query = f'{{ allContactPersons(search: {search}) {{ contact_persons {{ id }} }} }}'
        data = await fetch_graphql(client, query, headers)
        assert len(data['allContactPersons']['contact_persons']) == expected
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'search, expected',
    [
        ('"<EMAIL>"', 1),
        ('"<EMAIL>"', 0),
        ('"@vchasno.com.ua"', 2),
        ('"Contact first name"', 1),
        ('"Missing first name"', 0),
        ('"Second name"', 2),
        ('"Missing second name"', 0),
        ('"Last name"', 1),
        ('"Missing Last name"', 0),
        ('"12345678"', 1),
        ('"invalid edrpou"', 0),
        ('"company name"', 1),
        ('"Missing company name"', 0),
        ('"+380631234567"', 1),
        ('"380631234567"', 1),
        ('"38063"', 0),
        ('"missing phone"', 0),
        ('"industries"', 1),
    ],
)
async def test_contacts_search(aiohttp_client, search, expected):
    app, client, user = await prepare_client(aiohttp_client)
    company_id = user.company_id
    headers = prepare_auth_headers(user)

    try:
        async with app['db'].acquire() as conn:
            contact_id = await insert_contact(conn, TEST_CONTACT['company'].copy(), company_id)

            person1 = TEST_CONTACT['person'].copy()
            contact_person_id = await insert_contact_person(conn, person1, contact_id)
            await insert_contact_person_phone(
                conn=conn,
                person_id=contact_person_id,
                phone='+380631234567',
            )

            person2 = TEST_CONTACT['person'].copy()
            person2['email'] = '<EMAIL>'
            await insert_contact_person(conn, person2, contact_id)

            second_company = TEST_CONTACT['company'].copy()
            second_company['edrpou'] = '87654321'
            second_company['name'] = 'Stark Industries'
            second_contact_id = await insert_contact(conn, second_company, company_id)

            person3 = TEST_CONTACT['person'].copy()
            person3['email'] = '<EMAIL>'
            person3['first_name'] = 'Bruce'
            person3['last_name'] = 'Wayne'
            await insert_contact_person(conn, person3, second_contact_id)

        query = f'{{ allContacts(search: {search}) {{ count contacts {{ id }} }} }}'

        data = await fetch_graphql(client, query, headers)

        assert len(data['allContacts']['contacts']) == expected
        assert data['allContacts']['count'] == expected
    finally:
        await cleanup_on_teardown(app)


async def test_current_role(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    data = await fetch_graphql(client, GQL_CURRENT_ROLE, prepare_auth_headers(user))
    role = data['currentRole']
    assert role['status'] == RoleStatus.active.value


async def test_current_role_signatures_and_reviews_meta(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, create_api_account=True)

    data = await fetch_graphql(
        client=client,
        query=GQL_CURRENT_ROLE_REGISTRATION_META,
        headers=prepare_auth_headers(user),
    )
    assert data['currentRole']['hasFewSignatures'] is True
    assert data['currentRole']['hasFewReviews'] is True

    async def __prepare_and_sign_document():
        document = await prepare_document_upload(
            app,
            client,
            user=user,
            signer_roles=[user.role_id],
            expected_recipient_signatures=0,
            parallel_signing=False,
            expected_owner_signatures=1,
        )

        await sign_and_send_document(
            client,
            document_id=document.id,
            signer=user,
            recipient_email=TEST_RECIPIENT_EMAIL,
            recipient_edrpou=TEST_RECIPIENT_EDRPOU,
        )
        return document

    async def __prepare_review(document):
        await prepare_review(
            client=client, document=document, user=user, review_type=ReviewType.approve
        )

    document = await __prepare_and_sign_document()
    await __prepare_review(document)
    data = await fetch_graphql(
        client=client,
        query=GQL_CURRENT_ROLE_REGISTRATION_META,
        headers=prepare_auth_headers(user),
    )
    assert data['currentRole']['hasFewSignatures'] is True
    assert data['currentRole']['hasFewReviews'] is True

    document = await __prepare_and_sign_document()
    await __prepare_review(document)
    data = await fetch_graphql(
        client=client,
        query=GQL_CURRENT_ROLE_REGISTRATION_META,
        headers=prepare_auth_headers(user),
    )
    assert data['currentRole']['hasFewSignatures'] is False
    assert data['currentRole']['hasFewReviews'] is False


@pytest.mark.parametrize(
    'data, query, expected',
    [
        ({'role_position': 'ІТ'}, '{ currentRole { position } }', {'position': 'ІТ'}),
        ({'role_position': None}, '{ currentRole { position } }', {'position': None}),
    ],
)
async def test_resolve_current_role_fields(aiohttp_client, data, query, expected):
    app, client, user = await prepare_client(aiohttp_client, **data)
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    role = data['currentRole']
    assert role == expected


@pytest.mark.parametrize(
    'data, query, expected',
    [
        (
            {'role_position': 'ІТ'},
            '{ currentCompanyRoles(search: "<EMAIL>") { position } }',
            {'position': 'ІТ'},
        ),
        (
            {'role_position': None},
            '{ currentCompanyRoles(search: "<EMAIL>") { position } }',
            {'position': None},
        ),
    ],
)
async def test_resolve_coworker_role_fields(aiohttp_client, data, query, expected):
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_user_data(app, email='<EMAIL>', **data)
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    role = data['currentCompanyRoles'][0]
    assert role == expected


@pytest.mark.parametrize(
    'is_admin, company_config, expected',
    [
        (False, {}, [False, False, False]),
        (True, {}, [True, False, False]),
        (True, {'master': True, 'admin_is_superadmin': False}, [True, True, False]),
        (True, {'master': True, 'admin_is_superadmin': True}, [True, True, False]),
    ],
)
async def test_current_role_admin_flags(aiohttp_client, is_admin, company_config, expected):
    app, client, user = await prepare_client(
        aiohttp_client, is_admin, company_edrpou=TEST_COMPANY_EDRPOU
    )
    if company_config:
        await set_company_config(app, company_id=user.company_id, **company_config)

    try:
        data = await fetch_graphql(client, GQL_CURRENT_ROLE_ADMIN_FLAGS, prepare_auth_headers(user))
        assert expected == [
            data['currentRole']['isAdmin'],
            data['currentRole']['isMasterAdmin'],
            data['currentRole']['isSuperAdmin'],
        ]
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('company_edrpou', ['11111111', '12345678'])
async def test_current_role_company_config(aiohttp_client, company_edrpou):
    app, client, user = await prepare_client(aiohttp_client, company_edrpou=company_edrpou)

    try:
        data = await fetch_graphql(
            client, GQL_CURRENT_ROLE_COMPANY_CONFIG, prepare_auth_headers(user)
        )
        config = data['currentRole']['company']['config']

        assert isinstance(config, dict)
    finally:
        await cleanup_on_teardown(app)


async def test_resolve_used_document_count(aiohttp_client, test_flags):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(
        app,
        user,
    )

    async with with_elastic(app, [document.id]):
        data = await fetch_graphql(
            client, GQL_CURRENT_ROLE_COMPANY_CONFIG, prepare_auth_headers(user)
        )

    count = data['currentRole']['company']['usedDocumentCount']

    assert count == 1


async def test_current_role_permissions(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    try:
        data = await fetch_graphql(client, GQL_CURRENT_ROLE_PERMISSIONS, prepare_auth_headers(user))
        role = data['currentRole']
        assert role['canViewCoworkers'] is True
        assert role['canViewDocument'] is True
        assert role['canCommentDocument'] is True
        assert role['canUploadDocument'] is True
        assert role['canDownloadDocument'] is True
        assert role['canPrintDocument'] is True
        assert role['canDeleteDocument'] is True
        assert role['canSignAndRejectDocument'] is True
        assert role['canEditCompany'] is False
        assert role['canEditRoles'] is False
        assert role['canReceiveInbox'] is True
        assert role['canReceiveInboxAsDefault'] is True
        assert role['canReceiveComments'] is True
        assert role['canReceiveRejects'] is True
        assert role['canReceiveReminders'] is True
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'sort_documents',
    [
        None,
        DocumentListSortDate.date_listing.value,
        DocumentListSortDate.date_document.value,
    ],
)
async def test_current_role_sort_documents(aiohttp_client, sort_documents):
    app, client, user = await prepare_client(aiohttp_client, sort_documents=sort_documents)

    try:
        data = await fetch_graphql(
            client, GQL_CURRENT_ROLE_SORT_DOCUMENTS, prepare_auth_headers(user)
        )
        assert data['currentRole']['sortDocuments'] == sort_documents
    finally:
        await cleanup_on_teardown(app)


async def test_coworker_role_tokens(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    data = await fetch_graphql(client, GQL_COWORKER_ROLE_TOKENS, prepare_auth_headers(user))
    coworker_role = data['currentRole']['company']['roles'][0]
    assert coworker_role['hasToken']


@pytest.mark.parametrize('use_auth_headers, expected', [(True, 200), (False, 403)])
async def test_current_user(aiohttp_client, use_auth_headers, expected):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user) if use_auth_headers else prepare_referer_headers(client)

    response = await client.post(
        GRAPHQL_URL, data=ujson.dumps({'query': GQL_CURRENT_USER}), headers=headers
    )
    assert response.status == expected

    if expected == 200:
        data = await graphql_response(response)
        assert data['currentUser']['id'] == user.id
        assert data['currentUser']['createdBy'] is None


@pytest.mark.parametrize('enable, expected', [(True, True), (False, False), (None, False)])
async def test_current_user_2fa(aiohttp_client, enable, expected):
    app, client, user = await prepare_client(aiohttp_client)
    if enable is not None:
        await set_company_config(
            app=app, company_id=user.company_id, enable_2fa_for_internal_users=enable
        )

    data = await fetch_graphql(client, GQL_CURRENT_USER, prepare_auth_headers(user))
    assert data['currentUser']['is2FAEnabledByRule'] == expected


@pytest.mark.parametrize(
    'extra, expected',
    [
        ({}, True),
        ({KEP_POPUP_DATE_KEY: utc_now().date().strftime(KEP_POPUP_DATE_FORMAT)}, False),
        ({KEP_POPUP_DATE_KEY: 'invalid_string'}, True),
        ({KEP_POPUP_DATE_KEY: True}, True),
        (
            {KEP_POPUP_DATE_KEY: (utc_now() - datetime.timedelta(days=365)).strftime('%Y-%m-%d')},
            True,
        ),
    ],
)
async def test_current_user_kep_popup(aiohttp_client, extra, expected):
    app, client, user = await prepare_client(aiohttp_client, extra=extra)

    data = await fetch_graphql(
        client, '{ currentUser { showKEPAppPopup } }', prepare_auth_headers(user)
    )
    assert data['currentUser']['showKEPAppPopup'] == expected


@pytest.mark.parametrize('has_role', [False, True])
async def test_current_user_current_role(aiohttp_client, has_role):
    app, client, user = await prepare_client(aiohttp_client, password=TEST_USER_PASSWORD)

    try:
        if not has_role:
            async with app['db'].acquire() as conn:
                role_id = user.role_id
                role = await select_role_by_id(conn, user.role_id)
                await delete_role_base(
                    conn,
                    role_id=role.id_,
                    role_user_id=role.user_id,
                    deleted_by=role_id,
                    status=RoleStatus.deleted,
                )

        await login(client, user.email, TEST_USER_PASSWORD)
        data = await fetch_graphql(
            client, GQL_CURRENT_USER_CURRENT_ROLE, prepare_referer_headers(client)
        )
        assert data['currentUser']['id'] == user.id

        if has_role:
            assert data['currentRole']['id'] == user.role_id
        else:
            assert data['currentRole'] is None
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'option, expected',
    [
        ('', COUNT_TOTAL_UPLOADS),
        ('(search: "bad query")', 0),
        (f'(search: "{TEST_DOCUMENT_TITLE}")', COUNT_TOTAL_UPLOADS),
        (f'(search: "{TEST_DOCUMENT_EMAIL_RECIPIENT}")', COUNT_TOTAL_UPLOADS),
        (f'(search: "{TEST_DOCUMENT_EDRPOU_RECIPIENT}")', COUNT_TOTAL_UPLOADS),
        (f'(search: "{TEST_COMPANY_EDRPOU}")', COUNT_TOTAL_UPLOADS),
        (f'(folderId: {DocumentFolder.inbox.value})', 0),
        (f'(statusId: {DocumentStatus.uploaded.value})', 0),
        (f'(statusId: {DocumentStatus.ready_to_be_signed.value})', 0),
        (f'(statusId: {DocumentStatus.sent.value})', 0),
        (f'(statusId: {DocumentStatus.signed_and_sent.value})', COUNT_TOTAL_UPLOADS),
        (f'(statusId: {DocumentStatus.finished.value})', 0),
        (
            f'(statusIds: [{DocumentStatus.uploaded.value},'
            f'{DocumentStatus.signed_and_sent.value}])',
            COUNT_TOTAL_UPLOADS,
        ),
        (f'(gte: "{YESTERDAY}")', COUNT_TOTAL_UPLOADS),
        (f'(lte: "{YESTERDAY}")', 0),
        (f'(gte: "{TOMORROW}")', 0),
        (f'(lte: "{TOMORROW}")', COUNT_TOTAL_UPLOADS),
        (f'(lte: "{TODAY}")', COUNT_TOTAL_UPLOADS),
        (f'(gte: "{TODAY}")', COUNT_TOTAL_UPLOADS),
        (f'(gte: "{TODAY}", lte: "{TODAY}")', COUNT_TOTAL_UPLOADS),
        (f'(folderId: {DocumentFolder.outgoing.value})', COUNT_TOTAL_UPLOADS),
    ],
)
async def test_count_documents_options(aiohttp_client, option, expected):
    app, client, user1 = await prepare_client(aiohttp_client)

    # User 1 sent some documents to User 2
    private_key = prepare_private_key(user1)
    recipient_data = [
        {
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
        }
    ]
    documents_ids = await prepare_uploaded_signed_documents(
        app, user1, recipient_data, private_key, COUNT_TOTAL_UPLOADS, True
    )
    headers = prepare_auth_headers(user1)
    option = option.format(
        key_owner_fullname=private_key.key_owner_fullname,
        key_owner_edrpou=private_key.key_owner_edrpou,
    )

    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(client, f'{{ allDocuments{option} {{ count }} }}', headers)

    assert result['allDocuments']['count'] == expected

    # Test if length of result from resolve_documents equals
    # resolve_count_documents (ensure pagination is correct).
    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ documents {{ id }} }} }}', headers
        )
    assert len(result['allDocuments']['documents']) == expected


@pytest.mark.parametrize(
    'options, parameters, expected',
    [
        (
            # By default we sort by listing
            f'(gte: "{YESTERDAY}", lte: "{YESTERDAY}")',
            {'date_listing': TODAY, 'date_document': YESTERDAY},
            0,
        ),
        (
            f'(gte: "{TODAY}", lte: "{TODAY}")',
            {'date_listing': TODAY, 'date_document': YESTERDAY},
            1,
        ),
        (
            f'(gte: "{YESTERDAY}", lte: "{YESTERDAY}", sortDate: "date_listing")',
            {'date_listing': TODAY, 'date_document': YESTERDAY},
            0,
        ),
        (
            f'(gte: "{TODAY}", lte: "{TODAY}", sortDate: "date_listing")',
            {'date_listing': TODAY, 'date_document': YESTERDAY},
            1,
        ),
        (
            f'(gte: "{TODAY}", lte: "{TODAY}", sortDate: "date_document")',
            {'date_listing': TODAY, 'date_document': YESTERDAY},
            0,
        ),
        (
            f'(gte: "{YESTERDAY}", lte: "{YESTERDAY}", sortDate: "date_document")',
            {'date_listing': TODAY, 'date_document': YESTERDAY},
            1,
        ),
        (
            f'(gte: "{YESTERDAY}", sortDate: "date_document")',
            {'date_listing': TODAY, 'date_document': YESTERDAY},
            1,
        ),
        (
            f'(gte: "{TODAY}", sortDate: "date_document")',
            {'date_listing': TODAY, 'date_document': YESTERDAY},
            0,
        ),
        (
            f'(gte: "{YESTERDAY}", lte: "{YESTERDAY}", sortDate: "date_document")',
            {'date_listing': TODAY},
            0,
        ),
        (
            f'(gte: "{TODAY}", lte: "{TODAY}", sortDate: "date_document")',
            {'date_listing': TODAY},
            1,
        ),
    ],
)
async def test_filter_by_document_date(
    aiohttp_client,
    options,
    parameters,
    expected,
):
    app, client, user1 = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user1)
    doc = await prepare_document_data(app, user1, **parameters)
    query = f'{{ allDocuments{options} {{ count }}}}'
    async with with_elastic(app, [doc.id]):
        result = await fetch_graphql(client, query, headers)
    assert result['allDocuments']['count'] == expected


@pytest.mark.parametrize(
    'options, parameters, expected',
    [
        (
            f'(gte: "{YESTERDAY}", lte: "{YESTERDAY}", sortDate: "date_finished")',
            {'date_finished': TODAY},
            0,
        ),
        (
            f'(gte: "{TODAY}", lte: "{TODAY}", sortDate: "date_finished")',
            {'date_finished': TODAY},
            1,
        ),
    ],
)
async def test_filter_by_date_finished(aiohttp_client, test_flags, options, parameters, expected):
    app, client, user1 = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user1)
    document = await prepare_document_data(app, user1, **parameters)
    query = f'{{ allDocuments{options} {{ count }} }}'
    async with with_elastic(app, [document.id]):
        result = await fetch_graphql(client, query, headers)
    assert result['allDocuments']['count'] == expected


@pytest.mark.parametrize(
    'option, expected',
    [
        (f'(folderId: {DocumentFolder.inbox.value})', COUNT_TOTAL_UPLOADS),
        (f'(statusId: {DocumentStatus.signed_and_sent.value})', COUNT_TOTAL_UPLOADS),
    ],
)
async def test_count_documents_options_by_recipient(aiohttp_client, option, expected):
    app, client, user1 = await prepare_client(aiohttp_client)

    # User 1 sent some documents to User 2
    private_key = prepare_private_key(user1)
    recipient_data = [
        {
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
        }
    ]
    doc_ids = await prepare_uploaded_signed_documents(
        app, user1, recipient_data, private_key, COUNT_TOTAL_UPLOADS, True
    )

    # Register User 2 to view documents from User 1.
    user2 = await prepare_user_data(
        app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
    )
    option = option.format(
        key_owner_fullname=private_key.key_owner_fullname,
        key_owner_edrpou=private_key.key_owner_edrpou,
    )

    query = f'{{ allDocuments{option} {{ count }} }}'
    async with with_elastic(app, doc_ids):
        result = await fetch_graphql(client, query, prepare_auth_headers(user2))
    assert result['allDocuments']['count'] == expected


async def test_document_comments(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(app, email='<EMAIL>', company_edrpou='********')
    document = await prepare_document_data(
        app,
        owner,
        another_recipients=[recipient],
        status_id=DocumentStatus.signed_and_sent.value,
    )
    document_id = document.id

    query = (
        f'{{ document(id: "{document_id}") {{ comments {{ documentId accessCompanyId text }} }} }}'
    )

    async with app['db'].acquire() as conn:
        await conn.execute(
            comment_table.insert().values(
                [
                    {
                        'document_id': document_id,
                        'access_company_id': owner.company_id,
                        'role_id': owner.role_id,
                        'text': 'Owner internal comment',
                        'date_created': local_now(),
                    },
                    {
                        'document_id': document_id,
                        'access_company_id': None,
                        'role_id': owner.role_id,
                        'text': 'External comment',
                        'date_created': (local_now() + datetime.timedelta(seconds=1)),
                    },
                    {
                        'document_id': document_id,
                        'access_company_id': recipient.company_id,
                        'role_id': recipient.role_id,
                        'text': 'Recipient internal comment',
                        'date_created': (local_now() + datetime.timedelta(seconds=2)),
                    },
                ]
            )
        )

    # Check available comments for owner
    data = await fetch_graphql(client, query, prepare_auth_headers(owner))
    comments = data['document']['comments']
    assert len(comments) == 2
    assert comments[0] == {
        'documentId': document_id,
        'accessCompanyId': owner.company_id,
        'text': 'Owner internal comment',
    }
    assert comments[1] == {
        'documentId': document_id,
        'accessCompanyId': None,
        'text': 'External comment',
    }

    # Check available comments for recipient
    data = await fetch_graphql(client, query, prepare_auth_headers(recipient))
    comments = data['document']['comments']
    assert len(comments) == 2
    assert comments[0] == {
        'documentId': document_id,
        'accessCompanyId': None,
        'text': 'External comment',
    }
    assert comments[1] == {
        'documentId': document_id,
        'accessCompanyId': recipient.company_id,
        'text': 'Recipient internal comment',
    }


@pytest.mark.parametrize(
    'extension, expected',
    [('', ''), ('.xml', '.xml'), ('.xml.bz2', '.xml'), ('.xml.zip', '.xml.zip')],
)
async def test_document_extension(aiohttp_client, extension, expected):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user, extension=extension)

    try:
        data = await fetch_graphql(
            client,
            f'{{ document(id: "{document.id}") {{ extension }} }}',
            prepare_auth_headers(user),
        )
        assert data['document']['extension'] == expected
    finally:
        await cleanup_on_teardown(app)


async def test_documents_display_fields(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        company_name='Recipient Company Name',
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    doc = await prepare_document_data(
        app,
        user,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': [recipient.email]}],
        status_id=DocumentStatus.signed_and_sent.value,
    )

    query = (
        '{ allDocuments { documents { isInput edrpouOwner edrpouRecipient'
        ' displayCompanyEdrpou displayCompanyEmail displayCompanyName } } }'
    )

    # Display fields for user
    async with with_elastic(app, [doc.id]):
        result = await fetch_graphql(client, query, prepare_auth_headers(user))
        assert result['allDocuments']['documents'] == [
            {
                'isInput': False,
                'edrpouOwner': user.company_edrpou,
                'edrpouRecipient': recipient.company_edrpou,
                'displayCompanyEdrpou': recipient.company_edrpou,
                'displayCompanyEmail': recipient.email,
                'displayCompanyName': recipient.company_name or '',
            }
        ]
        # Display fields for recipient
        result = await fetch_graphql(client, query, prepare_auth_headers(recipient))
        assert result['allDocuments']['documents'] == [
            {
                'isInput': True,
                'edrpouOwner': user.company_edrpou,
                'edrpouRecipient': recipient.company_edrpou,
                'displayCompanyEdrpou': user.company_edrpou,
                'displayCompanyEmail': user.email,
                'displayCompanyName': user.company_name or '',
            }
        ]


async def test_documents_display_company_name_from_contact(aiohttp_client):
    query = '{ allDocuments { documents { displayCompanyName } } }'

    app, client, user = await prepare_client(aiohttp_client)
    async with app['db'].acquire() as conn:
        await prepare_contact(conn, user)
    document_recipients = [
        {
            'edrpou': TEST_CONTACT['company']['edrpou'],
            'emails': [TEST_CONTACT['person']['email']],
        }
    ]
    doc = await prepare_document_data(
        app,
        user,
        document_recipients=document_recipients,
        status_id=DocumentStatus.signed_and_sent.value,
    )
    async with with_elastic(app, [doc.id]):
        result = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert result['allDocuments']['documents'] == [
        {'displayCompanyName': TEST_CONTACT['company']['short_name']}
    ]


@pytest.mark.parametrize('create_another_comment, expected', [(False, 1), (True, 2)])
async def test_documents_filter_by_comments(aiohttp_client, create_another_comment, expected):
    app, client, user = await prepare_client(aiohttp_client)
    doc1 = await prepare_document_data(app, user, create_comment=True)
    doc2 = await prepare_document_data(app, user, create_comment=create_another_comment)

    async with with_elastic(app, [doc1.id, doc2.id]):
        data = await fetch_graphql(
            client,
            '{ allDocuments(hasComments: true) { count documents { id } } }',
            prepare_auth_headers(user),
        )
    assert data['allDocuments']['count'] == expected
    assert len(data['allDocuments']['documents']) == expected


@pytest.mark.parametrize(
    'has_date_delivered, option_value, expected_recipient_signatures, status_id, expected',
    [
        (True, 'true', 1, DocumentStatus.sent, 1),
        (True, 'true', 1, DocumentStatus.signed_and_sent, 1),
        (True, 'false', 1, DocumentStatus.uploaded, 0),
        (True, 'false', 1, DocumentStatus.ready_to_be_signed, 0),
        (True, 'false', 1, DocumentStatus.sent, 0),
        (True, 'false', 1, DocumentStatus.signed_and_sent, 0),
        (True, 'false', 1, DocumentStatus.reject, 0),
        (True, 'false', 1, DocumentStatus.finished, 0),
        (False, 'false', 0, DocumentStatus.uploaded, 0),
        (False, 'false', 0, DocumentStatus.ready_to_be_signed, 0),
        (False, 'false', 0, DocumentStatus.sent, 0),
        (False, 'false', 0, DocumentStatus.signed_and_sent, 0),
        (False, 'false', 0, DocumentStatus.reject, 0),
        (False, 'false', 0, DocumentStatus.finished, 1),
    ],
)
async def test_documents_filter_by_has_date_delivered(
    aiohttp_client,
    has_date_delivered,
    option_value,
    expected_recipient_signatures,
    status_id,
    expected,
):
    app, client, user = await prepare_client(aiohttp_client)

    date_delivered = utc_now() if has_date_delivered else None
    document = await prepare_document_data(
        app,
        user,
        date_delivered=date_delivered,
        expected_recipient_signatures=expected_recipient_signatures,
        status_id=status_id.value,
    )

    options = f'hasDateDelivered: {option_value}'
    query = f'{{ allDocuments({options}){{ count documents {{ id dateDelivered}} }} }}'
    async with with_elastic(app, [document.id]):
        data = await fetch_graphql(client, query, prepare_auth_headers(user))

    assert data['allDocuments']['count'] == expected
    assert len(data['allDocuments']['documents']) == expected
    if expected:
        result = data['allDocuments']['documents'][0]
        assert result['id'] == document.id
        assert result['dateDelivered'] == (
            date_delivered.isoformat() if has_date_delivered else None
        )


@pytest.mark.parametrize('order', [True, False])
async def test_wait_my_sign_multilateral_with_next_signer_roles(aiohttp_client, order):
    app, client, user = await prepare_client(aiohttp_client)

    coworker = await prepare_user_data(app, email='<EMAIL>')

    recipient = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )

    document = await prepare_document_data(
        app, user, status_id=DocumentStatus.uploaded.value, is_multilateral=True
    )

    data = {
        'docs': [document.id],
        'receivers': [
            {
                'edrpou': user.company_edrpou,
                'emails': [user.email],
                'sign_num': 1,
            },
            {
                'edrpou': recipient.company_edrpou,
                'emails': [recipient.email],
                'sign_num': 1,
            },
        ],
    }

    if order:
        data['receivers'][0]['order'] = 0
        data['receivers'][1]['order'] = 1

    await client.post(path=ADD_FLOW_URL, json=data, headers=prepare_auth_headers(user))

    await prepare_document_signer(
        document_id=document.id,
        company_id=coworker.company_id,
        role_id=coworker.role_id,
        group_id=None,
    )

    async def check_wait_my_sign_folder(current_user, *, expected):
        user_headers = prepare_auth_headers(current_user)
        data = await fetch_graphql(
            client,
            GQL_ALL_DOCUMENTS_SIGNERS,
            user_headers,
        )
        assert len(data['allDocuments']['documents']) == data['allDocuments']['count'] == expected

    async with with_elastic(app, [document.id]):
        await check_wait_my_sign_folder(
            user, expected=0
        )  # user doesn't sign document but coworker does
        await check_wait_my_sign_folder(coworker, expected=1)
        await check_wait_my_sign_folder(recipient, expected=0 if order else 1)

    await sign_and_send_document(client, document_id=document.id, signer=coworker)
    async with with_elastic(app, [document.id]):
        await check_wait_my_sign_folder(user, expected=0)
        await check_wait_my_sign_folder(recipient, expected=1)
        await check_wait_my_sign_folder(coworker, expected=0)


async def test_wait_my_sign_multilateral_ordered(aiohttp_client, test_flags):
    app, client, user = await prepare_client(aiohttp_client)

    coworker = await prepare_user_data(app, email='<EMAIL>')

    recipient = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )

    document = await prepare_document_data(
        app, user, status_id=DocumentStatus.uploaded.value, is_multilateral=True
    )

    data = {
        'docs': [document.id],
        'receivers': [
            {
                'edrpou': user.company_edrpou,
                'emails': [user.email],
                'order': 0,
                'sign_num': 1,
            },
            {
                'edrpou': recipient.company_edrpou,
                'emails': [recipient.email],
                'order': 1,
                'sign_num': 1,
            },
            {
                'edrpou': coworker.company_edrpou,
                'emails': [coworker.email],
                'order': 2,
                'sign_num': 1,
            },
        ],
    }

    await client.post(path=ADD_FLOW_URL, json=data, headers=prepare_auth_headers(user))

    async def check_wait_my_sign_folder(current_user, *, expected):
        user_headers = prepare_auth_headers(current_user)
        data = await fetch_graphql(
            client,
            GQL_ALL_DOCUMENTS_SIGNERS,
            user_headers,
        )
        assert len(data['allDocuments']['documents']) == data['allDocuments']['count'] == expected

    async with with_elastic(app, [document.id]):
        await check_wait_my_sign_folder(user, expected=1)
        await check_wait_my_sign_folder(
            recipient, expected=0
        )  # user doesn't see document until it send
        await check_wait_my_sign_folder(coworker, expected=0)

    await sign_and_send_document(client, document_id=document.id, signer=user)
    async with with_elastic(app, [document.id]):
        await check_wait_my_sign_folder(user, expected=0)
        await check_wait_my_sign_folder(recipient, expected=1)
        await check_wait_my_sign_folder(coworker, expected=0)

    await sign_and_send_document(client, document_id=document.id, signer=recipient)
    async with with_elastic(app, [document.id]):
        await check_wait_my_sign_folder(user, expected=0)
        await check_wait_my_sign_folder(recipient, expected=0)
        await check_wait_my_sign_folder(coworker, expected=1)

    await sign_and_send_document(client, document_id=document.id, signer=coworker)
    async with with_elastic(app, [document.id]):
        await check_wait_my_sign_folder(user, expected=0)
        await check_wait_my_sign_folder(recipient, expected=0)
        await check_wait_my_sign_folder(coworker, expected=0)


@pytest.mark.parametrize(
    'status, expected',
    [
        (DocumentStatus.uploaded.value, 0),
        (DocumentStatus.ready_to_be_signed.value, 0),
        (DocumentStatus.sent.value, 0),
        (DocumentStatus.signed.value, 0),
        (DocumentStatus.signed_and_sent.value, 1),
        (DocumentStatus.approved.value, 1),
        (DocumentStatus.reject.value, 0),
        (DocumentStatus.finished.value, 0),
    ],
)
async def test_documents_folder_wait_my_sign_as_recipient(aiohttp_client, status, expected):
    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )
    doc = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=True,
        another_recipients=[recipient],
        status_id=status,
    )

    async with with_elastic(app, [doc.id]):
        data = await fetch_graphql(
            client,
            GQL_ALL_DOCUMENTS_SIGNERS,
            prepare_auth_headers(recipient),
        )
    assert data['allDocuments']['count'] == expected
    assert len(data['allDocuments']['documents']) == expected


@pytest.mark.slow
async def test_documents_folder_wait_my_sign_as_recipient_signer(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    edrpou = TEST_RECIPIENT_EDRPOU
    recipient1 = await prepare_user_data(app, email='<EMAIL>', company_edrpou=edrpou)
    recipient2 = await prepare_user_data(app, email='<EMAIL>', company_edrpou=edrpou)
    recipient3 = await prepare_user_data(app, email='<EMAIL>', company_edrpou=edrpou)

    document = await prepare_document_data(
        app,
        owner,
        another_recipients=[recipient1],
        status_id=DocumentStatus.ready_to_be_signed.value,
    )
    await sign_and_send_document(client, document.id, owner)

    async def check_documents(user, expected):
        options = '(isWaitMySign: true)'
        data = await fetch_graphql(
            client,
            f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            prepare_auth_headers(user),
        )
        assert len(data['allDocuments']['documents']) == expected

    async def update_sign_process(signers, parallel_signing=False):
        response = await client.patch(
            f'/internal-api/documents/{document.id}',
            json={
                'signers_settings': {
                    'parallel_signing': parallel_signing,
                    'entities': [{'id': _id, 'type': 'role'} for _id in signers],
                }
            },
            headers=prepare_auth_headers(recipient1),
        )
        assert response.status == 200

    async with with_elastic(app, [document.id]):
        await check_documents(recipient1, 1)
        await check_documents(recipient2, 0)
        await check_documents(recipient3, 0)

    await update_sign_process(
        signers=[recipient2.role_id, recipient3.role_id], parallel_signing=True
    )

    async with with_elastic(app, [document.id]):
        await check_documents(recipient1, 0)
        await check_documents(recipient2, 1)
        await check_documents(recipient3, 1)

    await update_sign_process(
        signers=[recipient2.role_id, recipient3.role_id], parallel_signing=False
    )

    async with with_elastic(app, [document.id]):
        await check_documents(recipient1, 0)
        await check_documents(recipient2, 1)
        await check_documents(recipient3, 0)

    await sign_and_send_document(client, document.id, recipient2)

    async with with_elastic(app, [document.id]):
        await check_documents(recipient1, 0)
        await check_documents(recipient2, 0)
        await check_documents(recipient3, 1)

    await sign_and_send_document(client, document.id, recipient3)

    async with with_elastic(app, [document.id]):
        await check_documents(recipient1, 0)
        await check_documents(recipient2, 0)
        await check_documents(recipient3, 0)


@pytest.mark.parametrize(
    'status, expected',
    [
        (DocumentStatus.uploaded.value, 0),
        (DocumentStatus.ready_to_be_signed.value, 0),
        (DocumentStatus.sent.value, 1),
        (DocumentStatus.signed.value, 1),
        (DocumentStatus.signed_and_sent.value, 0),
        (DocumentStatus.approved.value, 0),
        (DocumentStatus.reject.value, 0),
        (DocumentStatus.finished.value, 0),
    ],
)
async def test_documents_folder_wait_my_sign_as_recipient_3p(aiohttp_client, status, expected):
    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app, email=TEST_RECIPIENT_EMAIL, company_edrpou=TEST_RECIPIENT_EDRPOU
    )
    # Input document for recipient
    doc = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=True,
        first_sign_by=FirstSignBy.recipient,
        another_recipients=[recipient],
        status_id=status,
    )
    async with with_elastic(app, [doc.id]):
        data = await fetch_graphql(
            client,
            GQL_ALL_DOCUMENTS_SIGNERS,
            prepare_auth_headers(recipient),
        )
    assert data['allDocuments']['count'] == expected
    assert len(data['allDocuments']['documents']) == expected


@pytest.mark.parametrize(
    'first_sign_by, expected_owner_signatures, expected_recipient_signatures, expected',
    [
        (FirstSignBy.owner, 1, 1, False),
        (FirstSignBy.owner, 1, 0, True),
        (FirstSignBy.recipient, 1, 1, False),
        (FirstSignBy.recipient, 0, 1, True),
    ],
)
async def test_documents_is_one_sign(
    aiohttp_client,
    first_sign_by,
    expected_owner_signatures,
    expected_recipient_signatures,
    expected,
):
    app, client, user = await prepare_client(aiohttp_client)
    doc = await prepare_document_data(
        app,
        user,
        first_sign_by=first_sign_by,
        expected_owner_signatures=expected_owner_signatures,
        expected_recipient_signatures=expected_recipient_signatures,
    )

    async with with_elastic(app, [doc.id]):
        data = await fetch_graphql(client, GQL_DOCUMENTS_IS_ONE_SIGN, prepare_auth_headers(user))
    assert len(data['allDocuments']['documents']) == 1
    assert data['allDocuments']['documents'][0]['isOneSign'] is expected


@pytest.mark.parametrize(
    'documents, sort_date, expected',
    [
        (TEST_DOCUMENTS, None, ['3', '2', '1']),
        (TEST_DOCUMENTS, DocumentListSortDate.date_listing.value, ['3', '2', '1']),
        (TEST_DOCUMENTS, DocumentListSortDate.date_document.value, ['2', '1', '3']),
    ],
)
async def test_documents_sort_date_and_order(aiohttp_client, documents, sort_date, expected):
    app, client, user = await prepare_client(aiohttp_client, sort_documents=sort_date)
    options = f'sortDate: "{sort_date}", order: "date"' if sort_date else 'order: "date"'
    query = f'{{ allDocuments({options}) {{ documents {{ title }} }} }}'

    doc_ids = []
    for item in documents:
        title, date_listing, date_document, _, _, number = item
        doc_ = await prepare_document_data(
            app,
            user,
            date_listing=date_listing,
            date_document=date_document,
            title=title,
        )
        doc_ids.append(doc_.id)

    async with with_elastic(app, doc_ids):
        data = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert [item['title'] for item in data['allDocuments']['documents']] == expected


@pytest.mark.parametrize(
    'options, parameters, expected',
    [
        # Sort by text field
        (
            f'(orderField: "{FIELD_ID_1}", direction: "asc")',
            [
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_1,
                    'field_id': FIELD_ID_1,
                    'value': '1',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_2,
                    'field_id': FIELD_ID_1,
                    'value': '2',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_3,
                    'field_id': FIELD_ID_1,
                    'value': '120',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_4,
                    'field_id': FIELD_ID_1,
                    'value': '21',
                },
            ],
            [TEST_UUID_1, TEST_UUID_3, TEST_UUID_2, TEST_UUID_4],
        ),
        (
            f'(orderField: "{FIELD_ID_1}", direction: "desc")',
            [
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_1,
                    'field_id': FIELD_ID_1,
                    'value': '1',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_2,
                    'field_id': FIELD_ID_1,
                    'value': '2',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_3,
                    'field_id': FIELD_ID_1,
                    'value': '120',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_4,
                    'field_id': FIELD_ID_1,
                    'value': '21',
                },
            ],
            [TEST_UUID_4, TEST_UUID_2, TEST_UUID_3, TEST_UUID_1],
        ),
        # Sort by number
        (
            f'(orderField: "{FIELD_ID_2}", direction: "asc")',
            [
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_1,
                    'field_id': FIELD_ID_2,
                    'value': '1',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_2,
                    'field_id': FIELD_ID_2,
                    'value': '2',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_3,
                    'field_id': FIELD_ID_2,
                    'value': '120',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_4,
                    'field_id': FIELD_ID_2,
                    'value': '21',
                },
            ],
            [TEST_UUID_1, TEST_UUID_2, TEST_UUID_4, TEST_UUID_3],
        ),
        # Sort by date
        (
            f'(orderField: "{FIELD_ID_3}", direction: "asc")',
            [
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_1,
                    'field_id': FIELD_ID_3,
                    'value': '2020-10-24',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_2,
                    'field_id': FIELD_ID_3,
                    'value': '2019-01-01',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_3,
                    'field_id': FIELD_ID_3,
                    'value': '2020-10-23',
                },
            ],
            [TEST_UUID_2, TEST_UUID_3, TEST_UUID_1, TEST_UUID_4],
        ),
        (
            f'(orderField: "{FIELD_ID_3}", direction: "desc")',
            [
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_1,
                    'field_id': FIELD_ID_3,
                    'value': '2020-10-24',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_2,
                    'field_id': FIELD_ID_3,
                    'value': '2019-01-01',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_3,
                    'field_id': FIELD_ID_3,
                    'value': '2020-10-23',
                },
            ],
            [TEST_UUID_1, TEST_UUID_3, TEST_UUID_2, TEST_UUID_4],
        ),
        # Sort by ENUM as text
        (
            f'(orderField: "{FIELD_ID_4}", direction: "asc")',
            [
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_1,
                    'field_id': FIELD_ID_4,
                    'value': 'a',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_2,
                    'field_id': FIELD_ID_4,
                    'value': 'c',
                },
                {
                    'company_id': COMPANY_1,
                    'document_id': TEST_UUID_3,
                    'field_id': FIELD_ID_4,
                    'value': 'b',
                },
            ],
            [TEST_UUID_1, TEST_UUID_3, TEST_UUID_2, TEST_UUID_4],
        ),
    ],
)
async def test_documents_sort_by_parameter(aiohttp_client, options, parameters, expected):
    app, client = await prepare_app_client(aiohttp_client)
    await prepare_company_data(app, id=COMPANY_1)
    await prepare_company_data(app, id=COMPANY_2, edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT)

    user = await prepare_user_data(app, company_id=COMPANY_1)
    recipient = await prepare_user_data(
        app, company_id=COMPANY_2, email=TEST_DOCUMENT_EMAIL_RECIPIENT
    )

    await prepare_document_data(app, user, id=TEST_UUID_1)
    await prepare_document_data(app, user, id=TEST_UUID_2)
    await prepare_document_data(app, user, id=TEST_UUID_3)
    await prepare_document_data(app, user, id=TEST_UUID_4)

    # Create fields
    data = {
        'is_required': False,
        'company_id': user.company_id,
        'created_by': user.role_id,
    }
    fields = [
        {**data, 'id': FIELD_ID_1, 'name': '1', 'type': DocumentFieldType.text},
        {**data, 'id': FIELD_ID_2, 'name': '2', 'type': DocumentFieldType.number},
        {**data, 'id': FIELD_ID_3, 'name': '3', 'type': DocumentFieldType.date},
        {**data, 'id': FIELD_ID_4, 'name': '4', 'type': DocumentFieldType.enum},
        {
            **data,
            'id': FIELD_ID_5,
            'name': '5',
            'type': DocumentFieldType.text,
            'company_id': recipient.company_id,
        },
    ]
    for field in fields:
        await insert_values(app, documents_fields_table, **field)

    for parameter in parameters:
        parameter = {**parameter, 'updated_by': user.role_id}
        await insert_values(app, document_parameters_table, **parameter)

    sync_ids = [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(
            client=client,
            query=f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )

    documents_ids = [d['id'] for d in data['allDocuments']['documents']]
    assert len(data['allDocuments']['documents']) == len(expected)
    assert documents_ids == expected


@pytest.mark.parametrize(
    'documents, ordering, direction, expected',
    [
        # Dates
        (TEST_DOCUMENTS, None, None, ['3', '2', '1']),
        (TEST_DOCUMENTS, DocumentListOrder.date.value, None, ['3', '2', '1']),
        # Title
        (TEST_DOCUMENTS, DocumentListOrder.title.value, None, ['3', '2', '1']),
        (TEST_DOCUMENTS, DocumentListOrder.title.value, 'desc', ['3', '2', '1']),
        (TEST_DOCUMENTS, DocumentListOrder.title.value, 'asc', ['1', '2', '3']),
        # Number
        (TEST_DOCUMENTS, DocumentListOrder.number.value, None, ['3', '2', '1']),
        (TEST_DOCUMENTS, DocumentListOrder.number.value, 'desc', ['3', '2', '1']),
        (TEST_DOCUMENTS, DocumentListOrder.number.value, 'asc', ['1', '2', '3']),
        # Company email
        (TEST_DOCUMENTS, DocumentListOrder.company_email.value, None, ['3', '2', '1']),
        (
            TEST_DOCUMENTS,
            DocumentListOrder.company_email.value,
            'desc',
            ['3', '2', '1'],
        ),
        (TEST_DOCUMENTS, DocumentListOrder.company_email.value, 'asc', ['1', '2', '3']),
        # Company name
        (TEST_DOCUMENTS, DocumentListOrder.company_name.value, None, ['3', '1', '2']),
        (TEST_DOCUMENTS, DocumentListOrder.company_name.value, 'desc', ['3', '1', '2']),
        # TODO:It fails with elastic, need to investigate
        # (TEST_DOCUMENTS, DocumentListOrder.company_name.value, 'asc', ['1', '3', '2']),
        # Company edrpou
        (TEST_DOCUMENTS, DocumentListOrder.edrpou.value, None, ['3', '2', '1']),
        (TEST_DOCUMENTS, DocumentListOrder.edrpou.value, 'desc', ['3', '2', '1']),
        (TEST_DOCUMENTS, DocumentListOrder.edrpou.value, 'asc', ['1', '2', '3']),
    ],
)
async def test_documents_order_and_direction(
    aiohttp_client, documents, ordering, direction, expected
):
    app, client, user = await prepare_client(aiohttp_client)
    if ordering:
        query = (
            f'{{ allDocuments(order: "{ordering}", direction: "{direction}") '
            f'{{  documents {{ title }} }} }}'
        )
    else:
        query = '{ allDocuments { documents { title } } }'

    doc_ids = []
    for item in documents:
        title, date_listing, date_document, edrpou, email, number = item
        doc_ = await prepare_document_data(
            app,
            user,
            create_document_access_for_recipients=True,
            date_listing=date_listing,
            date_document=date_document,
            document_recipients=[{'edrpou': edrpou, 'emails': [email]}],
            title=title,
            number=number,
        )
        doc_ids.append(doc_.id)

    # Company data from contact list will appear as a fallback
    async with app['db'].acquire() as conn:
        await insert_contact(conn, {'name': '1', 'edrpou': '12341234'}, user.company_id)
        await insert_contact(conn, {'name': '3', 'edrpou': '34563456'}, user.company_id)

    async with with_elastic(app, doc_ids):
        data = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert [item['title'] for item in data['allDocuments']['documents']] == expected


@pytest.mark.parametrize('direction', ['asc', 'desc'])
async def test_no_duplicates_in_ordering(aiohttp_client, direction):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)

    # Prepare 50 documents with same dates
    expected_count = 50
    same_date = utc_now()
    doc_ids = []
    for _ in range(expected_count):
        doc_ = await prepare_document_data(
            app,
            user,
            date_created=same_date,
            date_document=same_date,
            date_listing=same_date,
        )
        doc_ids.append(doc_.id)

    document_ids = []
    limit = 5
    offset = 0
    async with with_elastic(app, doc_ids):
        while True:
            query = (
                f'{{ allDocuments(direction: {direction},'
                f' limit: 5, offset: {offset}) {{ documents {{ id }} }} }}'
            )
            chunk = await fetch_graphql(client, query, headers)

            ids = [item['id'] for item in chunk['allDocuments']['documents']]
            if not ids:
                break

            document_ids.extend(ids)
            offset += limit
    assert len(document_ids) == len(set(document_ids)) == expected_count


async def test_current_sign_session(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
        status_id=DocumentStatus.signed_and_sent.value,
    )
    sign_session = await prepare_sign_session_data(
        app=app,
        user=user,
        document=document,
        status=SignSessionStatus.started,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
    )

    try:
        data = await fetch_graphql(
            client,
            GQL_CURRENT_SIGN_SESSION,
            prepare_sign_session_headers(sign_session, client),
        )

        assert data['currentSignSession'] == {
            'id': sign_session.id,
            'documentId': document.id,
            'roleId': None,
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'email': TEST_DOCUMENT_EMAIL_RECIPIENT,
            'isLegal': True,
            'type': SignSessionType.sign_session.value,
            'status': SignSessionStatus.started.value,
            'documentStatus': None,
            'finishUrl': f'/sign-sessions/{sign_session.id}/finish',
            'cancelUrl': f'/sign-sessions/{sign_session.id}/cancel',
            'role': None,
        }
    finally:
        await cleanup_on_teardown(app)


async def test_current_sign_session_empty(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)

    try:
        data = await fetch_graphql(client, GQL_CURRENT_SIGN_SESSION, headers)
        assert data['currentSignSession'] is None
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('is_dealer', [True, False])
async def test_referral_url(aiohttp_client, is_dealer):
    app, client, user = await prepare_client(aiohttp_client, is_dealer=is_dealer)
    headers = prepare_auth_headers(user)

    try:
        data = await fetch_graphql(client, '{ currentRole { registrationReferralUrl }}', headers)

        url = data['currentRole']['registrationReferralUrl']
        if is_dealer:
            assert url == f'/auth/registration?ref={user.role_id}'
        else:
            assert url == ''
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', 1),
        ('(search: "invalid search value")', 0),
        (f'(search: "{TEST_DOCUMENT_NUMBER}")', 1),
        (f'(search: "{TEST_TAG_NAME}")', 1),
        (f'(search: "{TEST_TAG_NAME2}")', 0),
        (f'(search: "{TEST_TAG_NAME1}{TEST_TAG_NAME}")', 1),
        (f'(search: "{TEST_RECIPIENT_COMPANY_NAME}")', 1),
        (f'(search: "{TEST_TAG_NAME1} + {TEST_TAG_NAME}")', 1),
        (f'(search: "{TEST_TAG_NAME1},{TEST_TAG_NAME}")', 1),
    ],
)
async def test_resolve_documents_search(aiohttp_client, options, expected):
    app, client, user = await prepare_client(aiohttp_client)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        company_full_name=TEST_RECIPIENT_COMPANY_FULL_NAME,
        company_name=TEST_RECIPIENT_COMPANY_NAME,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    async with app['db'].acquire() as conn:
        await insert_contact(
            conn,
            {
                'edrpou': recipient.company_edrpou,
                'name': TEST_CONTACT_COMPANY_NAME,
                'short_name': TEST_CONTACT_COMPANY_SHORT_NAME,
            },
            user.company_id,
        )

        document = await prepare_document_data(
            app,
            user,
            another_recipients=[recipient],
            number=TEST_DOCUMENT_NUMBER,
            status_id=DocumentStatus.signed_and_sent.value,
        )

        await insert_tags_for_documents(
            conn=conn,
            names=[TEST_TAG_NAME, TEST_TAG_NAME1],
            documents_ids=[document.id],
            company_id=user.company_id,
            assigner_role_id=user.role_id,
        )

    async with with_elastic(app, [document.id]):
        data = await fetch_graphql(
            client,
            f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )

    assert len(data['allDocuments']['documents']) == expected


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]),
        ('(searchTitle: ["actdocu"])', [TEST_UUID_2]),
        ('(searchTitle: ["Док"])', [TEST_UUID_3, TEST_UUID_4]),
        ('(searchTitle: ["Док", "act"])', [TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]),
        ('(searchTitle: ["ТТН"])', []),
        ('(searchNumber: ["1234"])', [TEST_UUID_2]),
        ('(searchNumber: ["999"])', [TEST_UUID_3]),
        ('(searchNumber: ["97777"])', [TEST_UUID_4]),
        ('(searchNumber: ["97777", "999"])', [TEST_UUID_3, TEST_UUID_4]),
        ('(searchNumber: ["11111"])', []),
    ],
)
async def test_resolve_documents_search_title_number(aiohttp_client, options, expected):
    app, client, user = await prepare_client(aiohttp_client)

    await prepare_document_data(app, user, id=TEST_UUID_1)
    await prepare_document_data(app, user, id=TEST_UUID_2, number='123456', title='ActDocument')
    await prepare_document_data(app, user, id=TEST_UUID_3, number='9990', title='Док2')
    await prepare_document_data(app, user, id=TEST_UUID_4, number='97777', title='Документ')

    sync_ids = [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(
            client=client,
            query=f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )

    assert len(data['allDocuments']['documents']) == len(expected)
    assert {d['id'] for d in data['allDocuments']['documents']} == set(expected)


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', [TEST_UUID_1]),
        ('(searchTitle: ["АктЗвітПроПродажТоварів"])', [TEST_UUID_1]),
        ('(searchTitle: ["актзвіт"])', [TEST_UUID_1]),
        ('(searchTitle: ["77777777"])', []),
        ('(searchTitle: ["55555555_77777777_20180131_АктЗвітПроПродажТоварів"])', []),
        ('(searchTitle: ["20180131_Акт"])', []),
    ],
)
async def test_resolve_documents_search_display_title(aiohttp_client, options, expected):
    app, client, user = await prepare_client(aiohttp_client)

    # Document.display_title == 'АктЗвітПроПродажТоварів'
    await prepare_document_data(
        app=app,
        owner=user,
        id=TEST_UUID_1,
        type='АктЗвітПроПродажТоварів',
        title='55555555_77777777_20180131_АктЗвітПроПродажТоварів',
    )

    sync_ids = [TEST_UUID_1]
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(
            client=client,
            query=f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )

        assert len(data['allDocuments']['documents']) == len(expected), options
        assert {d['id'] for d in data['allDocuments']['documents']} == set(expected)


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]),
        ('(searchUserEmail: ["<EMAIL>"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["test"])', [TEST_UUID_1, TEST_UUID_2]),
        ('(searchUserEmail: ["@smartweb.com.ua"])', [TEST_UUID_3]),
        # ('(searchUserEmail: [".com.ua"])', [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3]),
        ('(searchUserEmail: ["vchasno.ua"])', []),
        # case-insensitive search
        ('(searchUserEmail: ["google.com"])', [TEST_UUID_4]),
        ('(searchUserEmail: ["s.super"])', [TEST_UUID_4]),
        ('(searchUserEmail: ["s.superadmin"])', [TEST_UUID_4]),
        ('(searchUserEmail: ["<EMAIL>"])', [TEST_UUID_4]),
        ('(searchUserEmail: ["<EMAIL>"])', [TEST_UUID_4]),
        # Fuzzy searches
        ('(searchUserEmail: ["tect"])', [TEST_UUID_1, TEST_UUID_2]),
        ('(searchUserEmail: ["@smartveb.com.ua"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["@smartweb.ua"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["@smartweb.org"])', []),
    ],
)
async def test_resolve_documents_search_owner_email(aiohttp_client, options, expected):
    app, client, user = await prepare_client(aiohttp_client)

    owner1 = await prepare_user_data(app, email='<EMAIL>')
    owner2 = await prepare_user_data(app, email='<EMAIL>')
    owner3 = await prepare_user_data(app, email='<EMAIL>')

    await prepare_document_data(app, owner1, id=TEST_UUID_1)
    await prepare_document_data(app, owner1, id=TEST_UUID_2)
    await prepare_document_data(app, owner2, id=TEST_UUID_3)
    await prepare_document_data(app, owner3, id=TEST_UUID_4)

    sync_ids = [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(
            client=client,
            query=f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )

        assert len(data['allDocuments']['documents']) == len(expected), options
        assert {d['id'] for d in data['allDocuments']['documents']} == set(expected)


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3]),
        # No direct matches
        ('(searchTag: ["aloha"])', []),
        # "Hello Kitty" and "Halloween"
        ('(searchTag: ["hello"])', [TEST_UUID_1, TEST_UUID_2]),
        # "Hello Kitty" and "Baby shark"
        ('(searchTag: ["kitty", "shark"])', [TEST_UUID_1, TEST_UUID_3]),
        # "Dobby is free" is recipient tag
        ('(searchTag: ["Dobby is free"])', []),
        # Direct match, one document
        ('(searchTag: ["Hello Kitty"])', [TEST_UUID_1]),
        # Direct match, two documents
        ('(searchTag: ["Halloween"])', [TEST_UUID_1, TEST_UUID_2]),
    ],
)
async def test_resolve_documents_search_tags(aiohttp_client, options, expected):
    app, client, user = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    await prepare_document_data(app, user, id=TEST_UUID_1)
    await prepare_document_data(app, user, id=TEST_UUID_2)
    await prepare_document_data(app, user, id=TEST_UUID_3)

    data = {'company_id': user.company_id}
    tag1 = await insert_values(app, tag_table, name='Hello Kitty', **data)
    tag2 = await insert_values(app, tag_table, name='Baby shark', **data)
    tag3 = await insert_values(app, tag_table, name='Halloween', **data)
    tag4 = await insert_values(
        app=app, table=tag_table, name='Dobby is free!', company_id=recipient.company_id
    )

    async with app['db'].acquire() as conn:
        await insert_document_tags(
            conn=conn,
            data=[
                {'tag_id': tag1.id, 'document_id': TEST_UUID_1},
                {'tag_id': tag2.id, 'document_id': TEST_UUID_3},
                {'tag_id': tag3.id, 'document_id': TEST_UUID_1},
                {'tag_id': tag3.id, 'document_id': TEST_UUID_2},
                {'tag_id': tag4.id, 'document_id': TEST_UUID_2},
            ],
        )

    sync_ids = [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3]
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(
            client=client,
            query=f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )

        assert len(data['allDocuments']['documents']) == len(expected)
        assert {d['id'] for d in data['allDocuments']['documents']} == set(expected)


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', [TEST_UUID_1, TEST_UUID_2, TEST_UUID_4]),
        (
            '(searchCompanyEdrpou: ["55555555"])',
            [TEST_UUID_1, TEST_UUID_2, TEST_UUID_4],
        ),
        ('(searchCompanyEdrpou: ["555555"])', [TEST_UUID_1, TEST_UUID_2, TEST_UUID_4]),
        ('(searchCompanyEdrpou: ["33334444"])', [TEST_UUID_1, TEST_UUID_4]),
        ('(searchCompanyEdrpou: ["77777777"])', []),
    ],
)
async def test_resolve_documents_search_recipient_edrpou(aiohttp_client, options, expected):
    app, client, user = await prepare_client(
        aiohttp_client, email='user@another', company_edrpou='55555555'
    )
    alien = await prepare_user_data(app, email='a@t.c', company_edrpou='33334444')

    await prepare_document_data(app, user, id=TEST_UUID_1, another_recipients=[alien])
    await prepare_document_data(app, user, id=TEST_UUID_2)
    await prepare_document_data(app, alien, id=TEST_UUID_3)
    await prepare_document_data(app, alien, id=TEST_UUID_4, another_recipients=[user])

    sync_ids = [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(
            client=client,
            query=f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )
        result_ids = {d['id'] for d in data['allDocuments']['documents']}
        assert len(data['allDocuments']['documents']) == len(expected), options
        assert result_ids == set(expected), options


async def test_resolve_with_missing_documents(aiohttp_client):
    """
    Test when user request document deleted from DB, but not deleted from ES,
    graph delete ES document and return requested amount of documents
    """

    app, client, user = await prepare_client(
        aiohttp_client, email='user@another', company_edrpou='55555555'
    )
    await prepare_document_data(app, user, id=TEST_UUID_1, title='a')
    await prepare_document_data(app, user, id=TEST_UUID_2, title='b')
    await prepare_document_data(app, user, id=TEST_UUID_3, title='c')
    await prepare_document_data(app, user, id=TEST_UUID_4, title='d')

    sync_ids = [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]
    query = '{ allDocuments(limit: 3, order:"title", direction: "asc") { documents { id } } }'

    headers = prepare_auth_headers(user)
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(client, query, headers)
        result_ids = [item['id'] for item in data['allDocuments']['documents']]
        assert result_ids == [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3]

        async with app['db'].acquire() as conn:
            # delete document from DB, but keep in ES
            tags_to_delete = await select_tags_for_deletion(conn, [TEST_UUID_2])
            await delete_documents_from_db(conn, [TEST_UUID_2], [t.tag_id for t in tags_to_delete])

        data = await fetch_graphql(client, query, headers)
        result_ids = [item['id'] for item in data['allDocuments']['documents']]
        # Deleted document is resolved, limit works as expected
        assert result_ids == [TEST_UUID_1, TEST_UUID_3, TEST_UUID_4]


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', [TEST_UUID_1, TEST_UUID_2, TEST_UUID_4]),
        (
            '(searchCompanyName: ["ТОВ НОВА ПОШТА"])',
            [TEST_UUID_1, TEST_UUID_2, TEST_UUID_4],
        ),
        (
            '(searchCompanyName: ["нова пошта"])',
            [TEST_UUID_1, TEST_UUID_2, TEST_UUID_4],
        ),
        (
            '(searchCompanyName: ["новая почта"])',
            [TEST_UUID_1, TEST_UUID_2, TEST_UUID_4],
        ),
        ('(searchCompanyName: ["філіп морис"])', [TEST_UUID_1, TEST_UUID_4]),
        ('(searchCompanyName: ["філіп моріс"])', [TEST_UUID_1, TEST_UUID_4]),
        ('(searchCompanyName: ["Вчасно"])', []),
    ],
)
async def test_resolve_documents_search_recipient_name(aiohttp_client, options, expected):
    app, client, user = await prepare_client(
        aiohttp_client,
        email='user@another',
        company_edrpou='55555555',
        company_name='ТОВ "НОВА ПОШТА"',
    )
    alien = await prepare_user_data(
        app, email='a@t.c', company_edrpou='77777777', company_name='Філіп моріс'
    )

    await prepare_document_data(app, user, id=TEST_UUID_1, another_recipients=[alien])
    await prepare_document_data(app, user, id=TEST_UUID_2)
    await prepare_document_data(app, alien, id=TEST_UUID_3)
    await prepare_document_data(app, alien, id=TEST_UUID_4, another_recipients=[user])

    sync_ids = [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(
            client=client,
            query=f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )
        result_ids = {d['id'] for d in data['allDocuments']['documents']}
        assert len(data['allDocuments']['documents']) == len(expected), options
        assert result_ids == set(expected), options


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]),
        ('(searchUserEmail: ["<EMAIL>"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["test"])', [TEST_UUID_1, TEST_UUID_2]),
        ('(searchUserEmail: ["<EMAIL>"])', [TEST_UUID_1]),
        ('(searchUserEmail: ["@smartweb.com.ua"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["vchasno.ua"])', []),
        # Fuzzy searches
        ('(searchUserEmail: ["tect"])', [TEST_UUID_1, TEST_UUID_2]),
        ('(searchUserEmail: ["@smartveb.com.ua"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["@smartweb.ua"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["@smartweb.org"])', []),
    ],
)
async def test_resolve_documents_search_recipient_email(aiohttp_client, options, expected):
    app, client, user = await prepare_client(aiohttp_client, email='user@another')

    await prepare_document_data(app, user, id=TEST_UUID_1)
    await prepare_document_data(app, user, id=TEST_UUID_2)
    await prepare_document_data(app, user, id=TEST_UUID_3)
    await prepare_document_data(app, user, id=TEST_UUID_4)

    item = {'edrpou': TEST_COMPANY_EDRPOU, 'emails': [], 'is_emails_hidden': False}
    items = [
        {
            **item,
            'document_id': TEST_UUID_1,
            'emails': ['<EMAIL>', '<EMAIL>'],
        },
        {**item, 'document_id': TEST_UUID_2, 'emails': ['<EMAIL>']},
        {**item, 'document_id': TEST_UUID_3, 'emails': ['<EMAIL>']},
        {
            **item,
            'document_id': TEST_UUID_4,
            'emails': ['<EMAIL>', '<EMAIL>'],
            'is_emails_hidden': True,
        },
    ]
    async with app['db'].acquire() as conn:
        await insert_recipients(conn, recipients=items)

    sync_ids = [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(
            client=client,
            query=f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )
        result_ids = {d['id'] for d in data['allDocuments']['documents']}
        assert len(data['allDocuments']['documents']) == len(expected), options
        assert result_ids == set(expected), options


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]),
        ('(searchUserEmail: ["<EMAIL>"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["test"])', [TEST_UUID_1, TEST_UUID_2]),
        ('(searchUserEmail: ["<EMAIL>"])', [TEST_UUID_1]),
        ('(searchUserEmail: ["@smartweb.com.ua"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["vchasno.ua"])', []),
        # Fuzzy searches
        ('(searchUserEmail: ["tect"])', [TEST_UUID_1, TEST_UUID_2]),
        ('(searchUserEmail: ["@smartveb.com.ua"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["@smartweb.ua"])', [TEST_UUID_3]),
        ('(searchUserEmail: ["@smartweb.org"])', []),
    ],
)
async def test_resolve_documents_search_signer_email(aiohttp_client, options, expected):
    app, client, user = await prepare_client(aiohttp_client, email='user@another')

    coworker1 = await prepare_user_data(app, email='<EMAIL>')
    coworker2 = await prepare_user_data(app, email='<EMAIL>')
    coworker3 = await prepare_user_data(app, email='<EMAIL>')

    doc1 = await prepare_document_data(app, user, id=TEST_UUID_1)
    doc2 = await prepare_document_data(app, user, id=TEST_UUID_2)
    doc3 = await prepare_document_data(app, user, id=TEST_UUID_3)
    doc4 = await prepare_document_data(app, user, id=TEST_UUID_4)

    doc_map = {
        TEST_UUID_1: doc1,
        TEST_UUID_2: doc2,
        TEST_UUID_3: doc3,
        TEST_UUID_4: doc4,
    }

    async with app['db'].acquire() as conn:

        async def create_signer(doc_id, roles_ids):
            raw_doc = doc_map[doc_id]
            await update_document_signers(
                conn=conn,
                document=Document.from_row(raw_doc),
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                signers=[
                    UpdateSignersDataSignerEntity(
                        type=UpdateSignersDataSignerType.role,
                        id=role_id,
                    )
                    for role_id in roles_ids
                ],
                parallel_signing=True,
                is_document_owner=True,
                current_role_id=user.role_id,
            )

        await create_signer(TEST_UUID_1, [coworker1.role_id, coworker3.role_id])
        await create_signer(TEST_UUID_2, [coworker1.role_id])
        await create_signer(TEST_UUID_3, [coworker2.role_id])

    sync_ids = [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]
    async with with_elastic(app, sync_ids):
        data = await fetch_graphql(
            client=client,
            query=f'{{ allDocuments{options} {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )
        result_ids = {d['id'] for d in data['allDocuments']['documents']}
        assert len(data['allDocuments']['documents']) == len(expected), options
        assert result_ids == set(expected), options


@pytest.mark.parametrize('title, expected', [(TEST_DOCUMENT_TITLE, 2), ('xyz', 1)])
async def test_resolve_document_with_tag_and_search(aiohttp_client, title, expected):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, title=TEST_DOCUMENT_TITLE)
    document2 = await prepare_document_data(app, user, title=title)

    async with app['db'].acquire() as conn:
        tags = await insert_tags_for_documents(
            conn=conn,
            names=[TEST_TAG_NAME],
            documents_ids=[document.id],
            company_id=user.company_id,
            assigner_role_id=user.role_id,
        )
        tag_id = list(tags)[0].id

        await create_document_tags_with_access(
            conn=conn,
            document_id=document2.id,
            tag_id=tag_id,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            assigner_role_id=user.role_id,
        )

    options = f'tags: ["{tag_id}"], search: "{TEST_DOCUMENT_TITLE[4:]}"'

    async with with_elastic(app, [document.id, document2.id]):
        data = await fetch_graphql(
            client,
            f'{{ allDocuments({options}) {{ documents {{ id }} }} }}',
            headers=prepare_auth_headers(user),
        )

    assert len(data['allDocuments']['documents']) == expected


async def test_resolve_document_with_multiple_tags(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    tag1_name, tag2_name, tag3_name = 'TAG1', 'TAG2', 'TAG3'
    document1 = await prepare_document_data(app, user)
    document2 = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        # Document1 has tag1, tag2
        tags = await create_new_tags_for_documents(
            conn=conn,
            tags_names=[tag1_name, tag2_name],
            documents_ids=[document1.id],
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            assigner_role_id=user.role_id,
        )
        tags_mapping = {tag.name: tag.id for tag in tags}
        tag1_id, tag2_id = tags_mapping[tag1_name], tags_mapping[tag2_name]

        # Document2 has tag2, tag3
        tags = await create_new_tags_for_documents(
            conn=conn,
            tags_names=[tag3_name],
            documents_ids=[document2.id],
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            assigner_role_id=user.role_id,
        )
        tag3_id = tags[0].id

        await create_document_tags_with_access(
            conn=conn,
            document_id=document2.id,
            tag_id=tag2_id,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            assigner_role_id=user.role_id,
        )

    async def check_tags(tags_ids, documents_ids):
        tags_option = ','.join([f'"{tag_id}"' for tag_id in tags_ids])
        options = f'tags: [{tags_option}]'

        async with with_elastic(app, documents_ids):
            data = await fetch_graphql(
                client,
                f'{{ allDocuments({options}) {{ documents {{ id }} }} }}',
                headers=prepare_auth_headers(user),
            )
        assert len(data['allDocuments']['documents']) == len(documents_ids)
        assert {document['id'] for document in data['allDocuments']['documents']} == set(
            documents_ids
        )

    await check_tags([tag1_id, tag2_id], [document1.id])
    await check_tags([tag2_id, tag3_id], [document2.id])
    await check_tags([], [document1.id, document2.id])
    await check_tags([tag1_id], [document1.id])
    await check_tags([tag2_id], [document1.id, document2.id])
    await check_tags([tag3_id], [document2.id])
    await check_tags([tag1_id, tag3_id], [])
    await check_tags([tag1_id, tag2_id, tag3_id], [])


async def test_resolve_document_without_tags(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        company_full_name=TEST_RECIPIENT_COMPANY_FULL_NAME,
        company_name=TEST_RECIPIENT_COMPANY_NAME,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    document_title = 'Hello world'

    async with app['db'].acquire() as conn:
        document = await prepare_document_data(
            app,
            user,
            another_recipients=[recipient],
            number=TEST_DOCUMENT_NUMBER,
            title=document_title,
            status_id=DocumentStatus.signed_and_sent.value,
        )

        await insert_tags_for_documents(
            conn=conn,
            names=[TEST_TAG_NAME, TEST_TAG_NAME1],
            documents_ids=[document.id],
            company_id=user.company_id,
            assigner_role_id=user.role_id,
        )
        document2 = await prepare_document_data(app, user)

        document3 = await prepare_document_data(app, user)

    documents_ids = [document.id, document2.id, document3.id]
    async with with_elastic(app, documents_ids):
        data = await fetch_graphql(
            client,
            f'{{ allDocuments(withoutTags: true) {{ documents {{ id }}  }} }}',  # noqa
            headers=prepare_auth_headers(user),
        )

    assert len(data['allDocuments']['documents']) == 2


async def test_reviews(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)
    doc = await prepare_document_data(app, user)

    collaborator = await prepare_user_data(app, email='<EMAIL>')
    other_user = await prepare_user_data(
        app, company_edrpou='00000123', email='<EMAIL>'
    )

    async with app['db'].acquire() as conn:
        await insert_review(
            conn,
            {
                'document_id': doc.id,
                'role_id': user.role_id,
                'type': ReviewType.approve,
            },
        )
        await insert_review(
            conn,
            {
                'document_id': doc.id,
                'role_id': user.role_id,
                'type': ReviewType.reject,
            },
        )
        await insert_review(
            conn,
            {
                'document_id': doc.id,
                'role_id': collaborator.role_id,
                'type': ReviewType.approve,
            },
        )
        await insert_review(
            conn,
            {
                'document_id': doc.id,
                'role_id': other_user.role_id,
                'type': ReviewType.approve,
            },
        )

    async with with_elastic(app, [doc.id]):
        response = await fetch_graphql(client, GQL_REVIEWS, headers)
    data = response['allDocuments']['documents']
    assert len(data) == 1
    reviews = data[0]['reviews']
    assert len(reviews) == 3
    assert reviews[0]['documentId'] == doc.id
    assert reviews[1]['documentId'] == doc.id
    assert reviews[2]['documentId'] == doc.id


async def test_reviews_is_last(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)
    doc = await prepare_document_data(app, user)

    collaborator = await prepare_user_data(app, email='<EMAIL>')
    other_user = await prepare_user_data(
        app, company_edrpou='00000123', email='<EMAIL>'
    )

    async with app['db'].acquire() as conn:
        await insert_review(
            conn,
            {
                'document_id': doc.id,
                'role_id': user.role_id,
                'type': ReviewType.approve,
            },
        )
        await insert_review(
            conn,
            {
                'document_id': doc.id,
                'role_id': user.role_id,
                'type': ReviewType.reject,
            },
        )
        await insert_review(
            conn,
            {
                'document_id': doc.id,
                'role_id': collaborator.role_id,
                'type': ReviewType.approve,
            },
        )
        await insert_review(
            conn,
            {
                'document_id': doc.id,
                'role_id': other_user.role_id,
                'type': ReviewType.approve,
            },
        )

    async with with_elastic(app, [doc.id]):
        response = await fetch_graphql(client, GQL_REVIEWS_IS_LAST, headers)
    data = response['allDocuments']['documents']
    assert len(data) == 1

    reviews = data[0]['reviews']
    assert len(reviews) == 2
    assert reviews[0]['documentId'] == doc.id
    assert reviews[1]['documentId'] == doc.id
    assert reviews[0]['type'] != reviews[1]['type']
    assert reviews[0]['roleId'] != reviews[1]['roleId']


async def test_review_requests(aiohttp_client):
    app, client, user_1 = await prepare_client(aiohttp_client)
    user_2 = await prepare_user_data(app, email='<EMAIL>')
    user_3 = await prepare_user_data(app, email='<EMAIL>')
    user_4 = await prepare_user_data(app, company_edrpou='00000123', email='<EMAIL>')
    headers = prepare_auth_headers(user_1)
    now_dt = utc_now()
    delta_dt = datetime.timedelta(minutes=1)

    async with app['db'].acquire() as conn:
        doc = await prepare_document_data(app, user_1)

        await conn.execute(
            insert(review_request_table).values(
                {
                    'document_id': doc.id,
                    'from_role_id': user_1.role_id,
                    'to_role_id': user_2.role_id,
                    'date_created': now_dt,
                }
            )
        )
        await conn.execute(
            insert(review_request_table).values(
                {
                    'document_id': doc.id,
                    'from_role_id': user_2.role_id,
                    'to_role_id': user_1.role_id,
                    'date_created': now_dt + delta_dt,
                }
            )
        )
        await conn.execute(
            insert(review_request_table).values(
                {
                    'document_id': doc.id,
                    'from_role_id': user_2.role_id,
                    'to_role_id': user_3.role_id,
                    'status': ReviewRequestStatus.deleted.value,
                    'date_created': now_dt + delta_dt * 2,
                }
            )
        )
        await conn.execute(
            insert(review_request_table).values(
                {
                    'document_id': doc.id,
                    'from_role_id': user_4.role_id,
                    'to_role_id': user_4.role_id,
                    'date_created': now_dt + delta_dt * 3,
                }
            )
        )

    async with with_elastic(app, [doc.id]):
        response = await fetch_graphql(client, GQL_REVIEW_REQUESTS, headers)
    data = response['allDocuments']['documents']
    assert len(data) == 1

    review_requests = data[0]['reviewRequests']

    assert len(review_requests) == 3
    assert review_requests[0]['documentId'] == doc.id
    assert review_requests[0]['status'] == ReviewRequestStatus.active.value
    assert review_requests[2]['status'] == ReviewRequestStatus.deleted.value

    assert review_requests[0]['fromRoleId'] == user_1.role_id
    assert review_requests[1]['fromRoleId'] == user_2.role_id

    assert review_requests[0]['toRoleId'] == user_2.role_id
    assert review_requests[1]['toRoleId'] == user_1.role_id

    assert review_requests[0]['fromRole']['user']['email'] == user_1.email
    assert review_requests[1]['fromRole']['user']['email'] == user_2.email

    assert review_requests[0]['toRole']['user']['email'] == user_2.email
    assert review_requests[1]['toRole']['user']['email'] == user_1.email


async def test_review_requests_active(aiohttp_client):
    app, client, user_1 = await prepare_client(aiohttp_client)
    user_2 = await prepare_user_data(app, email='<EMAIL>')
    user_3 = await prepare_user_data(app, email='<EMAIL>')
    user_4 = await prepare_user_data(app, company_edrpou='00000123', email='<EMAIL>')
    headers = prepare_auth_headers(user_1)

    async with app['db'].acquire() as conn:
        doc = await prepare_document_data(app, user_1)
        await conn.execute(
            insert(review_request_table).values(
                {
                    'document_id': doc.id,
                    'from_role_id': user_1.role_id,
                    'to_role_id': user_2.role_id,
                }
            )
        )
        await conn.execute(
            insert(review_request_table).values(
                {
                    'document_id': doc.id,
                    'from_role_id': user_2.role_id,
                    'to_role_id': user_1.role_id,
                }
            )
        )
        await conn.execute(
            insert(review_request_table).values(
                {
                    'document_id': doc.id,
                    'from_role_id': user_2.role_id,
                    'to_role_id': user_3.role_id,
                    'status': ReviewRequestStatus.deleted.value,
                }
            )
        )
        await conn.execute(
            insert(review_request_table).values(
                {
                    'document_id': doc.id,
                    'from_role_id': user_4.role_id,
                    'to_role_id': user_4.role_id,
                }
            )
        )

    async with with_elastic(app, [doc.id]):
        response = await fetch_graphql(client, GQL_REVIEW_REQUESTS_ACTIVE, headers)

    data = response['allDocuments']['documents']
    assert len(data) == 1

    review_requests = data[0]['reviewRequests']
    assert len(review_requests) == 2
    assert review_requests[0]['documentId'] == doc.id
    assert review_requests[0]['status'] == ReviewRequestStatus.active.value
    assert review_requests[1]['status'] == ReviewRequestStatus.active.value

    assert user_1.role_id in (
        review_requests[0]['fromRoleId'],
        review_requests[1]['fromRoleId'],
    )
    assert user_2.role_id in (
        review_requests[0]['fromRoleId'],
        review_requests[1]['fromRoleId'],
    )

    assert user_1.role_id in (
        review_requests[0]['toRoleId'],
        review_requests[1]['toRoleId'],
    )
    assert user_2.role_id in (
        review_requests[0]['toRoleId'],
        review_requests[1]['toRoleId'],
    )

    assert user_1.email in (
        review_requests[0]['fromRole']['user']['email'],
        review_requests[1]['fromRole']['user']['email'],
    )
    assert user_2.email in (
        review_requests[0]['fromRole']['user']['email'],
        review_requests[1]['fromRole']['user']['email'],
    )

    assert user_1.email in (
        review_requests[0]['toRole']['user']['email'],
        review_requests[1]['toRole']['user']['email'],
    )
    assert user_2.email in (
        review_requests[0]['toRole']['user']['email'],
        review_requests[1]['toRole']['user']['email'],
    )


@pytest.mark.parametrize(
    'search, expected',
    [
        ('"@vchasno.com.ua"', 3),
        ('"evo@vchasno"', 2),
        ('"alice-evo@vchasno"', 1),
        ('"bob-evo@vchasno"', 1),
        ('"Алис"', 1),
        ('"Боб"', 1),
        ('"invalid"', 0),
    ],
)
async def test_current_company_roles(aiohttp_client, search, expected):
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_user_data(app, email='<EMAIL>', first_name='Алиса')
    await prepare_user_data(app, email='<EMAIL>', last_name='Боб')

    headers = prepare_auth_headers(user)
    query = f'{{ currentCompanyRoles(search: {search}) {{ user {{ id }} }} }}'

    try:
        response = await fetch_graphql(client, query, headers)
        assert len(response['currentCompanyRoles']) == expected
    finally:
        await cleanup_on_teardown(app)


async def test_roles_search_can_sign_reject_document_as_admin(aiohttp_client):
    """
    Given an admin in company with can_sign_and_reject_documents = false
    When searching for roles who can sign and reject documents
    Expecting to return an admin
    """
    app, client, user = await prepare_client(aiohttp_client)
    await prepare_user_data(
        app=app,
        user_role=UserRole.admin.value,
        can_sign_and_reject_document=False,
        email='<EMAIL>',
        first_name='Алиса',
    )

    headers = prepare_auth_headers(user)
    query = '{ currentCompanyRoles(canSignAndRejectDocument: true) { user { id } } }'

    response = await fetch_graphql(client, query, headers)
    assert len(response['currentCompanyRoles']) == 2


async def test_separate_documents_access_rights(aiohttp_client):
    app, client, owner_admin = await prepare_client(aiohttp_client)

    owner_user_1 = await prepare_user_data(
        app,
        email='<EMAIL>',
        can_view_document=True,
        user_role=UserRole.user.value,
    )

    owner_user_2 = await prepare_user_data(
        app,
        email='<EMAIL>',
        can_view_document=False,
        user_role=UserRole.user.value,
    )

    recipient_admin = await prepare_user_data(
        app,
        company_edrpou='00000123',
        email='<EMAIL>',
        user_role=UserRole.admin.value,
    )

    recipient_user = await prepare_user_data(
        app,
        company_edrpou='00000123',
        email='<EMAIL>',
        can_view_document=False,
        user_role=UserRole.user.value,
    )

    doc1 = await prepare_document_data(app, owner_admin, another_recipients=[recipient_admin])
    doc2 = await prepare_document_data(app, owner_admin, another_recipients=[recipient_user])

    query = '{ allDocuments { documents { id } } }'

    async with with_elastic(app, [doc1.id, doc2.id]):
        data = await fetch_graphql(client, query, prepare_auth_headers(owner_admin))
        assert len(data['allDocuments']['documents']) == 2

        data = await fetch_graphql(client, query, prepare_auth_headers(owner_user_1))
        assert len(data['allDocuments']['documents']) == 2

        data = await fetch_graphql(client, query, prepare_auth_headers(owner_user_2))
        assert len(data['allDocuments']['documents']) == 0

        data = await fetch_graphql(client, query, prepare_auth_headers(recipient_admin))
        assert len(data['allDocuments']['documents']) == 2

        data = await fetch_graphql(client, query, prepare_auth_headers(recipient_user))
        assert len(data['allDocuments']['documents']) == 1


async def test_document_signers(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    doc = await prepare_document_data(app, user)
    coworker = await prepare_user_data(app, email='<EMAIL>')
    alien = await prepare_user_data(app, email='<EMAIL>', company_edrpou='33334444')

    query = '{ allDocuments { documents { signers { roleId order role { user { email } } } } } }'

    async with app['db'].acquire() as conn:
        await conn.execute(
            document_signer_table.insert().values(
                [
                    {
                        'document_id': doc.id,
                        'company_id': user.company_id,
                        'role_id': user.role_id,
                        'order': None,
                    },
                    {
                        'document_id': doc.id,
                        'company_id': coworker.company_id,
                        'role_id': coworker.role_id,
                        'order': 7,
                    },
                    {
                        'document_id': doc.id,
                        'company_id': alien.company_id,
                        'role_id': alien.role_id,
                        'order': None,
                    },
                ]
            )
        )

    async with with_elastic(app, [doc.id]):
        data = await fetch_graphql(client, query, prepare_auth_headers(user))
        assert len(data['allDocuments']['documents']) == 1

        signers = data['allDocuments']['documents'][0]['signers']
        assert len(signers) == 2

        roles = {s['roleId'] for s in signers}
        assert user.role_id in roles
        assert coworker.role_id in roles

        orders = {s['order'] for s in signers}
        assert None in orders
        assert 7 in orders

        emails = {s['role']['user']['email'] for s in signers}
        assert user.email in emails
        assert coworker.email in emails


async def test_document_links(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    parent = await prepare_document_data(app, user)
    child_1 = await prepare_document_data(app, user)
    child_2 = await prepare_document_data(app, user)
    children_ids = {child_1.id, child_2.id}

    alien = await prepare_user_data(app, company_edrpou='55555555', email='<EMAIL>')
    alien_parent = await prepare_document_data(app, alien)
    alien_child = await prepare_document_data(app, alien)

    query_1 = (
        f'{{ document(id: "{parent.id}") {{ '
        f'children {{ document {{ id }} }} parent {{ document {{ id }} }} }} }}'
    )

    query_2 = (
        f'{{ document(id: "{child_1.id}") {{ '
        f'children {{ document {{ id }} }} parent {{ document {{ id }} }} }} }}'
    )

    try:
        async with app['db'].acquire() as conn:
            await add_document_child(
                conn=conn,
                company_edrpou=user.company_edrpou,
                parent_id=parent.id,
                child_id=child_1.id,
            )
            await add_document_child(
                conn=conn,
                company_edrpou=user.company_edrpou,
                parent_id=parent.id,
                child_id=child_2.id,
            )
            await add_document_child(
                conn=conn,
                company_edrpou=alien.company_edrpou,
                parent_id=alien_parent.id,
                child_id=alien_child.id,
            )

        data = await fetch_graphql(client, query_1, prepare_auth_headers(user))

        assert not data['document']['parent']
        children = [child['document'] for child in data['document']['children']]
        assert len(children) == 2
        assert children[0]['id'] != children[1]['id']
        assert children[0]['id'] in children_ids
        assert children[1]['id'] in children_ids

        data = await fetch_graphql(client, query_2, prepare_auth_headers(user))

        assert not len(data['document']['children'])
        assert data['document']['parent']['document']['id'] == parent.id
    finally:
        await cleanup_on_teardown(app)


async def test_resolve_review_setting(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    doc1 = await prepare_document_data(app, user)
    doc2 = await prepare_document_data(app, user)
    doc3 = await prepare_document_data(app, user)

    query = '{ allDocuments { documents { id reviewSetting { isRequired } } } }'

    async with app['db'].acquire() as conn:
        await update_review_setting(conn, user, {'document_id': doc1.id, 'is_required': True})
        await update_review_setting(conn, user, {'document_id': doc2.id})

    async with with_elastic(app, [doc1.id, doc2.id, doc3.id]):
        data = await fetch_graphql(client, query, prepare_auth_headers(user))

    docs = data['allDocuments']['documents']
    assert len(docs) == 3

    settings = [d['reviewSetting'] for d in docs if d['reviewSetting']]
    assert len(settings) == 2

    assert len([s for s in settings if s['isRequired']]) == 1
    assert len([s for s in settings if not s['isRequired']]) == 1


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', 10),
        # TODO: don't work with elastic, investigate and fix if needed
        # missing contragent, ready to sign
        # ('(conditions2: ["60037000"])', 2),
        # ('(conditions2: ["60037000", "600270001"])', 3),
        # outgoing, ready to sign, is 3p
        ('(conditions2: ["600270001"])', 1),
        # inbox, sign and sent
        ('(conditions2: ["60017004"])', 1),
        # outgoing, ready to sign, is not 3p
        ('(conditions2: ["600270000"])', 3),
        # sum of previous two
        ('(conditions2: ["600270000", "60017004"])', 4),
        # Has all signatures
        ('(conditions2: ["60027000020"])', 3),
        ('(conditions2: ["60027007021"])', 1),  # Finished
        ('(conditions2: ["60027000022", "60027007022"])', 4),
        # One side sign
        ('(conditions2: ["60027000002"])', 2),
        ('(conditions2: ["60027007012", "600267000012"])', 1),
        ('(conditions2: ["60027000022", "60027007022"])', 4),
        ('(conditions2: ["60027007011", "60027000011"])', 1),
        ('(conditions2: ["***********", "***********"])', 1),
        ('(conditions2: ["***********", "***********"])', 0),
        # current company's order to sign
        ('(conditions2: ["************"])', 1),
        # recipient's order to sign
        ('(conditions2: ["************"])', 2),
    ],
)
async def test_filter_document_using_conditions(aiohttp_client, monkeypatch, options, expected):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=True,
        create_billing_account=True,
    )
    user2 = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou='********',
        create_billing_account=True,
    )
    user3 = await prepare_user_data(app, email='<EMAIL>')

    d1 = await prepare_document_data(app, user)
    d2 = await prepare_document_data(app, user, another_recipients=[user2])
    d3 = await prepare_document_data(
        app, user, first_sign_by=FirstSignBy.recipient, another_recipients=[user2]
    )
    d4 = await prepare_document_data(
        app, user2, first_sign_by=FirstSignBy.recipient, another_recipients=[user3]
    )

    doc_with_ordered_flows = await prepare_document_data(
        app, user, status_id=DocumentStatus.flow.value
    )

    await prepare_flow_item(app, doc_with_ordered_flows.id, order=0, edrpou=user.company_edrpou)

    await prepare_flow_item(app, doc_with_ordered_flows.id, order=1, edrpou=user2.company_edrpou)

    doc_with_ordered_flows2 = await prepare_document_data(
        app, user, status_id=DocumentStatus.flow.value
    )

    await prepare_flow_item(app, doc_with_ordered_flows2.id, order=1, edrpou=user.company_edrpou)

    await prepare_flow_item(app, doc_with_ordered_flows2.id, order=0, edrpou=user2.company_edrpou)

    doc_with_parallel_flows = await prepare_document_data(
        app, user, status_id=DocumentStatus.flow.value
    )

    await prepare_flow_item(
        app,
        doc_with_parallel_flows.id,
        edrpou=user.company_edrpou,
        pending_signatures_count=0,
    )

    await prepare_flow_item(app, doc_with_parallel_flows.id, edrpou=user2.company_edrpou)

    d5 = await prepare_document_data(app, user2, another_recipients=[user3])
    # Signed by owner
    await sign_and_send_document(
        client,
        d5.id,
        user2,
        sign_data=prepare_signature_form_data(
            user2,
            owner=user2,
            p7s=io.BytesIO(b'p7s'),
            archive='zip',
            recipient_edrpou=user3.company_edrpou,
            recipient_email=user3.email,
        ),
    )

    d6 = await prepare_document_data(
        app, user, expected_owner_signatures=1, expected_recipient_signatures=0
    )
    d7 = await prepare_document_data(
        app, user, expected_owner_signatures=1, expected_recipient_signatures=0
    )

    doc_ids = [
        d1.id,
        d2.id,
        d3.id,
        d4.id,
        doc_with_ordered_flows.id,
        doc_with_ordered_flows2.id,
        doc_with_parallel_flows.id,
        d5.id,
        d6.id,
        d7.id,
    ]

    # Do not send a document to test the condition in Approved/Signed status
    monkeypatch.setattr(documents_utils, 'autosend_document_on_sign', mock.AsyncMock())

    response = await client.post(
        f'/internal-api/documents/{d7.id}/signatures',
        data=prepare_signature_form_data(
            user, recipient_edrpou=user2.company_edrpou, recipient_email=user2.email
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201

    async with with_elastic(app, doc_ids):
        query1 = f'{{ allDocuments {options} {{ documents {{ id, statusId }} }} }}'
        data = await fetch_graphql(client, query1, prepare_auth_headers(user))
        assert len(data['allDocuments']['documents']) == expected


@pytest.mark.parametrize(
    'soft_access_to_tags, coworker1_all_tags, coworker2_all_tags',
    [
        (True, {TEST_TAG_NAME1, TEST_TAG_NAME}, {TEST_TAG_NAME1, TEST_TAG_NAME}),
        (False, {TEST_TAG_NAME1}, set()),
    ],
)
async def test_resolve_tags(
    aiohttp_client,
    soft_access_to_tags,
    coworker1_all_tags,
    coworker2_all_tags,
):
    app, client, admin = await prepare_client(
        aiohttp_client,
        is_admin=True,
        create_billing_account=True,
    )
    coworker1 = await prepare_user_data(
        app, email='2@a.b', user_role=UserRole.user.value, can_view_document=False
    )
    coworker2 = await prepare_user_data(
        app, email='3@a.b', user_role=UserRole.user.value, can_view_document=False
    )

    alien = await prepare_user_data(app, email='1@e.c', company_edrpou='********')
    recipient = await prepare_user_data(app, email='2@e.c', company_edrpou='*********')

    doc_1 = await prepare_document_data(app, admin, another_recipients=[recipient])
    doc_2 = await prepare_document_data(app, coworker1)
    doc_3 = await prepare_document_data(app, alien)
    doc_ids = [doc_1.id, doc_2.id, doc_3.id]

    # Admin can assign tag to own document and for document uploaded by any coworker
    await request_create_tags_for_documents(
        client=client,
        user=admin,
        names=[TEST_TAG_NAME],
        documents_ids=[doc_1.id, doc_2.id],
    )

    # Coworker can assign tag only to documents with access
    await request_create_tags_for_documents(
        client=client, user=coworker1, names=[TEST_TAG_NAME1], documents_ids=[doc_2.id]
    )

    # coworker2 can't create tag for document without document access
    await request_create_tags_for_documents(
        client=client,
        user=coworker2,
        names=[TEST_TAG_NAME2],
        documents_ids=[doc_2.id],
        status=HTTPStatus.FORBIDDEN,
    )
    async with app['db'].acquire() as conn:
        await set_company_config(
            app=app,
            company_id=admin.company_id,
            soft_access_to_tags=soft_access_to_tags,
        )

        # But someone provide coworker2 access to document as for viewer
        await insert_listings(
            conn=conn,
            data=[
                {
                    'document_id': document.id,
                    'access_edrpou': coworker2.company_edrpou,
                    'role_id': coworker2.role_id,
                    'sources': AccessSource.viewer,
                }
                for document in [doc_1]
            ],
        )

    # alien tag can not affect of result other company
    await request_create_tags_for_documents(
        client=client,
        user=alien,
        names=[TEST_TAG_NAME2],
        documents_ids=[doc_3.id],
        status=HTTPStatus.CREATED,
    )

    await sign_and_send_document(client, doc_1.id, admin)

    # Function helpers for assert viewed documents by current user
    async def check_document_tags(user, expected_documents, expected_names):
        data = await fetch_graphql(
            client=client,
            query='{ allDocuments { documents { id, tags { id, name } } } }',
            headers=prepare_auth_headers(user),
        )
        all_documents = data['allDocuments']['documents']
        assert len(all_documents) == expected_documents
        names = {tag['name'] for document in all_documents for tag in document['tags']}
        assert names == expected_names

    async def check_all_tags(user, expected_names):
        data = await fetch_graphql(
            client=client,
            query='{ allTags { id, name  } }',
            headers=prepare_auth_headers(user),
        )
        assert {tag['name'] for tag in data['allTags']} == expected_names

    async def check_tags_for_document_filters(user, expected_names):
        data = await fetch_graphql(
            client=client,
            query='{ allTagsForDocumentFilter { id, name  } }',
            headers=prepare_auth_headers(user),
        )
        tags_names = {tag['name'] for tag in data['allTagsForDocumentFilter']}
        assert tags_names == expected_names

    async with with_elastic(app, doc_ids):
        # Admin can see view all tags in company
        await check_document_tags(admin, 2, {TEST_TAG_NAME, TEST_TAG_NAME1})
        await check_all_tags(admin, {TEST_TAG_NAME, TEST_TAG_NAME1})
        await check_tags_for_document_filters(admin, {TEST_TAG_NAME1, TEST_TAG_NAME})

        # By default, user can see only own tags, buf for documents resolved by documents
        # user can see all tags associated with document. In case when
        # company has soft_access_to_tags is True flag in configs, then user
        # can see all tags in company
        await check_all_tags(coworker1, coworker1_all_tags)
        await check_document_tags(coworker1, 1, {TEST_TAG_NAME1, TEST_TAG_NAME})
        await check_tags_for_document_filters(coworker1, {TEST_TAG_NAME1, TEST_TAG_NAME})

        # By default, coworker2 can see all tag by documents with access by still
        # can't see tags without access. In case  soft_access_to_tags user can see
        # all tags
        await check_all_tags(coworker2, coworker2_all_tags)
        await check_document_tags(coworker2, 1, {TEST_TAG_NAME})
        await check_tags_for_document_filters(coworker2, {TEST_TAG_NAME})

        # Alien see only own tags
        await check_all_tags(alien, {TEST_TAG_NAME2})
        await check_document_tags(alien, 1, {TEST_TAG_NAME2})
        await check_tags_for_document_filters(alien, {TEST_TAG_NAME2})

        # Recipient can not see tags of sent document with tags on owner side
        await check_all_tags(recipient, set())
        await check_document_tags(recipient, 1, set())
        await check_tags_for_document_filters(recipient, set())


async def test_resolve_tags_by_roles(aiohttp_client):
    app, client, user1 = await prepare_client(
        aiohttp_client, is_admin=True, can_view_document=False
    )
    user2 = await prepare_user_data(app, email='1@e.c', company_edrpou='10010')
    user3 = await prepare_user_data(app, email='<EMAIL>')

    async with app['db'].acquire() as conn:
        tag1_id = await conn.scalar(
            insert(tag_table)
            .values({'name': TEST_TAG_NAME, 'company_id': user1.company_id})
            .returning(tag_table.c.id)
        )

        tag2_id = await conn.scalar(
            insert(tag_table)
            .values({'name': 'ANOTHER TAG1', 'company_id': user2.company_id})
            .returning(tag_table.c.id)
        )
        tag3_id = await conn.scalar(
            insert(tag_table)
            .values({'name': 'ANOTHER TAG2', 'company_id': user1.company_id})
            .returning(tag_table.c.id)
        )

        await conn.execute(
            insert(role_tag_table).values(
                [
                    {'role_id': user2.role_id, 'tag_id': tag2_id},
                    {'role_id': user1.role_id, 'tag_id': tag1_id},
                    {'role_id': user3.role_id, 'tag_id': tag1_id},
                    {'role_id': user3.role_id, 'tag_id': tag3_id},
                ]
            )
        )

    query2 = f'{{ company(id: "{user1.company_id}")   {{ id, roles {{ id, tags {{ id }} }} }}}}'

    data = await fetch_graphql(client, query2, prepare_auth_headers(user1))
    roles = data['company']['roles']
    for role in roles:
        if role['id'] == user3.role_id:
            assert {tag['id'] for tag in role['tags']} == {tag1_id, tag3_id}
        if role['id'] == user1.role_id:
            assert {tag['id'] for tag in role['tags']} == {tag1_id}


async def test_resolve_tags_by_contacts(aiohttp_client):
    app, client, user1 = await prepare_client(
        aiohttp_client, is_admin=True, can_view_document=False
    )
    user2 = await prepare_user_data(app, email='1@e.c', company_edrpou='10010')
    user3 = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        can_view_document=False,
    )
    tag1 = str(uuid4())
    tag2 = str(uuid4())
    tag3 = str(uuid4())
    tag4 = str(uuid4())
    async with app['db'].acquire() as conn:
        contact1 = TEST_CONTACT['company'].copy()
        contact1['edrpou'] = '00000001'
        contact2 = TEST_CONTACT['company'].copy()
        contact2['edrpou'] = '00000002'
        contact3 = TEST_CONTACT['company'].copy()
        contact3['edrpou'] = '00000004'

        contact1_id = await prepare_contact(conn, user1, contact1)
        contact2_id = await prepare_contact(conn, user1, contact2)
        contact3_id = await prepare_contact(conn, user2, contact3)
        await conn.execute(
            insert(tag_table).values(
                [
                    {'id': tag1, 'name': '1', 'company_id': user1.company_id},
                    {'id': tag2, 'name': '2', 'company_id': user2.company_id},
                    {'id': tag3, 'name': '3', 'company_id': user1.company_id},
                    {'id': tag4, 'name': '4', 'company_id': user3.company_id},
                ]
            )
        )

        await conn.execute(
            insert(contact_tag_table).values(
                [
                    {'contact_id': contact1_id, 'tag_id': tag1},
                    {'contact_id': contact1_id, 'tag_id': tag3},
                    {'contact_id': contact1_id, 'tag_id': tag4},
                    {'contact_id': contact2_id, 'tag_id': tag1},
                    {'contact_id': contact3_id, 'tag_id': tag2},
                ]
            )
        )

        await conn.execute(
            insert(role_tag_table).values({'role_id': user3.role_id, 'tag_id': tag4})
        )

    async def check(user, expected):
        query = '{ allContacts { contacts { id, tags { id } } } }'
        data = await fetch_graphql(client, query, prepare_auth_headers(user))
        mapping = {
            contact['id']: {tag['id'] for tag in contact['tags']}
            for contact in data['allContacts']['contacts']
        }
        assert expected == mapping

    await check(user1, {contact1_id: {tag1, tag3, tag4}, contact2_id: {tag1}})
    await check(user2, {contact3_id: {tag2}})
    await check(user3, {contact1_id: {tag4}, contact2_id: set()})


@pytest.mark.parametrize(
    'options, expected',
    [
        ('', {'NEW TAG (8239)', 'TEST TAG NAME'}),
        ('(search: "invalid search value")', set()),
        ('(search: "TEST TAG NAME")', {'TEST TAG NAME'}),
        ('(search: "tag")', {'NEW TAG (8239)', 'TEST TAG NAME'}),
    ],
)
async def test_resolve_tags_options(aiohttp_client, options, expected):
    app, client, user = await prepare_client(
        aiohttp_client, is_admin=False, can_view_document=False
    )
    coworker = await prepare_user_data(app, email='<EMAIL>', user_role=UserRole.admin.value)
    alien = await prepare_user_data(app, email='1@e.c', company_edrpou='10010123')

    doc_1 = await prepare_document_data(app, user)
    doc_2 = await prepare_document_data(app, user)
    doc_3 = await prepare_document_data(app, coworker)
    doc_4 = await prepare_document_data(app, alien)

    await request_create_tags_for_documents(
        client=client,
        user=user,
        names=['NEW TAG (8239)', 'TEST TAG NAME'],
        documents_ids=[doc_1.id, doc_2.id],
    )

    # This tags can't affect on result of user request
    await request_create_tags_for_documents(
        client=client,
        user=coworker,
        names=['NEW TAG (1123)'],
        documents_ids=[doc_3.id, doc_1.id],
    )

    # Alien can create the same tag, but this tag can be visible only by alien
    await request_create_tags_for_documents(
        client=client,
        user=alien,
        names=['NEW TAG (8239)', 'TEST TAG NAME'],
        documents_ids=[doc_4.id],
    )

    query = f'{{ allTags {options} {{ id, name }} }}'
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    all_tags = data['allTags']
    assert len(all_tags) == len(expected)
    assert {tag['name'] for tag in all_tags} == expected


async def test_resolve_tags_has_roles(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client, is_admin=False, can_view_document=False
    )
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='1@e.c', company_edrpou='********')

    doc_1 = await prepare_document_data(app, user)
    await prepare_document_data(app, user)
    doc_2 = await prepare_document_data(app, user2)
    await prepare_document_data(app, user3)

    async with app['db'].acquire() as conn:
        tag_id = await conn.scalar(
            insert(tag_table)
            .values({'name': TEST_TAG_NAME, 'company_id': user.company_id})
            .returning(tag_table.c.id)
        )

        await conn.scalar(
            insert(tag_table).values({'name': 'NEW TAG (8239)', 'company_id': user.company_id})
        )

        await conn.execute(
            insert(tag_table).values({'name': 'ANOTHER TAG', 'company_id': user3.company_id})
        )

        await conn.execute(
            insert(document_tag_table).values(
                [
                    {'document_id': doc_1.id, 'tag_id': tag_id},
                    {'document_id': doc_2.id, 'tag_id': tag_id},
                ]
            )
        )

        query = f'{{ allTags (hasRoles: true) {{ id, name }} }}'  # noqa
        data = await fetch_graphql(client, query, prepare_auth_headers(user))
        assert len(data['allTags']) == 0

        await conn.execute(
            insert(role_tag_table).values(
                [
                    {'role_id': user.role_id, 'tag_id': tag_id},
                    {'role_id': user2.role_id, 'tag_id': tag_id},
                ]
            )
        )

        query = f'{{ allTags (hasRoles: true) {{ id, name }} }}'  # noqa
        data = await fetch_graphql(client, query, prepare_auth_headers(user))
        assert len(data['allTags']) == 1


@pytest.mark.parametrize(
    'review_type, expected',
    [
        (ReviewType.reject.value, 'rejected'),
        (ReviewType.approve.value, 'approved'),
        (None, 'pending'),
    ],
)
async def test_resolve_reviews_status(aiohttp_client, review_type, expected):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)
    coworker = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(app, owner)

    # Add reviewer to review, make review required
    await prepare_review_requests(client, document, owner, reviewers=[coworker], is_required=True)

    if review_type:
        # Create review
        response = await client.post(
            REVIEW_URL,
            json={'document_id': document.id, 'type': review_type},
            headers=prepare_auth_headers(coworker),
        )
        assert response.status == HTTPStatus.CREATED

    query = f'{{ allDocuments (ids: ["{document.id}"]) {{ documents {{ id, reviewStatus }} }} }}'
    async with with_elastic(app, [document.id]):
        data = await fetch_graphql(client, query, prepare_auth_headers(owner))
    assert data['allDocuments']['documents'][0]['reviewStatus'] == expected


@pytest.mark.parametrize(
    'options, expected_emails',
    [
        # Three coworker and one test user
        ('', ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']),
        (f'(tags:["{TEST_UUID_2}"])', ['<EMAIL>', '<EMAIL>', '<EMAIL>']),
        (f'(tags:["{TEST_UUID_1}", "{TEST_UUID_2}"])', ['<EMAIL>']),
        (f'(tags:["{TEST_UUID_2}", "{TEST_UUID_3}"])', ['<EMAIL>']),
        (f'(tags:["{TEST_UUID_1}", "{TEST_UUID_3}"])', []),
        ('(hasInvalidSignatures:true)', []),
    ],
)
async def test_resolve_roles_for_company_with_tags(aiohttp_client, options, expected_emails):
    app, client, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    coworker1 = await prepare_user_data(app, email='<EMAIL>')
    coworker2 = await prepare_user_data(app, email='<EMAIL>')
    coworker3 = await prepare_user_data(app, email='<EMAIL>')
    alien = await prepare_user_data(app, email='<EMAIL>', company_edrpou='********11')

    async with app['db'].acquire() as conn:
        await conn.execute(
            insert(tag_table).values(
                [
                    {
                        'name': TEST_TAG_NAME,
                        'company_id': user.company_id,
                        'id': TEST_UUID_1,
                    },
                    {
                        'name': TEST_TAG_NAME1,
                        'company_id': user.company_id,
                        'id': TEST_UUID_2,
                    },
                    {
                        'name': TEST_TAG_NAME2,
                        'company_id': user.company_id,
                        'id': TEST_UUID_3,
                    },
                    {
                        'name': 'ANOTHER',
                        'company_id': alien.company_id,
                        'id': TEST_UUID_4,
                    },
                ]
            )
        )
        await conn.execute(
            insert(role_tag_table).values(
                [
                    {'role_id': coworker1.role_id, 'tag_id': TEST_UUID_1},
                    {'role_id': coworker1.role_id, 'tag_id': TEST_UUID_2},
                    {'role_id': coworker2.role_id, 'tag_id': TEST_UUID_2},
                    {'role_id': coworker3.role_id, 'tag_id': TEST_UUID_2},
                    {'role_id': coworker3.role_id, 'tag_id': TEST_UUID_3},
                    {'role_id': alien.role_id, 'tag_id': TEST_UUID_4},
                ]
            )
        )

    query = f"""
        {{
            company(id: "{user.company_id}") {{
                edrpou,
                id,
                roles{options} {{
                    user {{
                        email
                    }}
                }}
            }}
        }}
    """
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    roles = data['company']['roles']
    assert len(roles) == len(expected_emails)
    assert {role['user']['email'] for role in roles} == set(expected_emails)


@pytest.mark.parametrize(
    'email, is_email_hidden, expected',
    [
        ('<EMAIL>', False, '<EMAIL>'),
        ('<EMAIL>', True, None),
    ],
)
async def test_resolve_contact_email(aiohttp_client, email, is_email_hidden, expected):
    app, client, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    async with app['db'].acquire() as conn:
        contact = TEST_CONTACT['company'].copy()
        person1 = {
            **TEST_CONTACT['person'],
            'email': email,
            'is_email_hidden': is_email_hidden,
        }

        person2 = TEST_CONTACT['person'].copy()
        await prepare_contact(conn=conn, user=user, contact=contact, persons=[person1, person2])
    query = """
    {
        allContacts {
          contacts {
            persons {
                email,
                isEmailHidden
            }
          }
        }
    }
    """
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert len(data['allContacts']['contacts']) == 1
    persons = data['allContacts']['contacts'][0]['persons']
    result = [set(person.items()) for person in persons]
    assert {('email', expected), ('isEmailHidden', is_email_hidden)} in result
    assert {('email', person2['email']), ('isEmailHidden', False)} in result


@pytest.mark.parametrize(
    'search, is_email_hidden, expected',
    [
        ('smartweb.com.ua', False, {'<EMAIL>', '<EMAIL>'}),
        ('smartweb.com.ua', True, {'<EMAIL>', None}),
        ('c1@s', True, set()),
        ('c1@s', False, {'<EMAIL>', '<EMAIL>'}),
    ],
)
async def test_search_hidden_contact_email(aiohttp_client, search, is_email_hidden, expected):
    app, client, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    async with app['db'].acquire() as conn:
        contact = TEST_CONTACT['company'].copy()
        person1 = {
            **TEST_CONTACT['person'],
            'email': '<EMAIL>',
            'is_email_hidden': is_email_hidden,
        }

        person2 = {**TEST_CONTACT['person'].copy(), 'email': '<EMAIL>'}
        await prepare_contact(conn=conn, user=user, contact=contact, persons=[person1, person2])

    query = f"""
    {{
        allContacts(search: \"{search}\") {{
            contacts {{
                persons {{
                    email,
                    isEmailHidden
                }}
            }}
        }}
    }}
    """
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    if not expected:
        assert len(data['allContacts']['contacts']) == 0
        return
    assert len(data['allContacts']['contacts']) == 1
    persons = data['allContacts']['contacts'][0]['persons']
    assert len(persons) == len(expected)
    result = {person['email'] for person in persons}
    assert result == expected


@pytest.mark.parametrize('is_emails_hidden, expected', [(True, 0), (False, 1)])
async def test_search_email(aiohttp_client, is_emails_hidden, expected):
    app, client, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    recipient = await prepare_user_data(app, email='<EMAIL>', company_edrpou='********')
    document = await prepare_document_data(
        app,
        user,
        another_recipients=[recipient],
        is_recipients_hidden=is_emails_hidden,
        status_id=DocumentStatus.signed_and_sent.value,
    )

    query = f'{{ allDocuments(search: "{recipient.email}") {{ documents {{ id }} }} }}'
    async with with_elastic(app, [document.id]):
        data = await fetch_graphql(client, query, prepare_auth_headers(user))
    documents = data['allDocuments']['documents']
    assert len(documents) == expected
    if expected:
        assert documents[0]['id'] == document.id


@pytest.mark.parametrize(
    'option, expected',
    [
        ('', 3),
        (f'(folderId: {DocumentFolder.internal.value})', 1),
        (f'(folderId: {DocumentFolder.not_internal.value})', 2),
    ],
)
async def test_resolve_internal_documents(aiohttp_client, option, expected):
    app, client, user = await prepare_client(aiohttp_client)

    headers = prepare_auth_headers(user)

    doc1 = await prepare_document_data(app, user, is_internal=True)
    doc2 = await prepare_document_data(app, user)
    doc3 = await prepare_document_data(app, user)

    query = f'{{ allDocuments{option} {{ documents {{ id }} }} }}'
    async with with_elastic(app, [doc1.id, doc2.id, doc3.id]):
        result = await fetch_graphql(client, query, headers)
    assert len(result['allDocuments']['documents']) == expected


async def test_resolve_company_config(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    headers = prepare_auth_headers(user)
    query = f'{{ company(id: "{user.company_id}") {{ config }} }}'
    result = await fetch_graphql(client, query, headers)
    assert result['company']['config'] == CompanyConfig().model_dump(
        mode='json',
        exclude_unset=False,  # frontend should see whole object with defaults
        exclude_defaults=False,
    )

    await prepare_company_config(
        company_id=user.company_id,
        config=CompanyConfig(xml_parser='some_str'),
    )

    result = await fetch_graphql(client, query, headers)
    assert result['company']['config'] is not None
    assert result['company']['config']['xml_parser'] == 'some_str'

    assert result['company']['config'] == CompanyConfig(xml_parser='some_str').model_dump(
        mode='json',
        exclude_unset=False,  # frontend should see whole object with defaults
        exclude_defaults=False,
    )


async def test_resolve_required_fields_filters(aiohttp_client):
    # Arrange
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=True,
    )

    user2 = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=TEST_RECIPIENT_EDRPOU
    )
    user3 = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=TEST_RECIPIENT_EDRPOU_2
    )

    query = f"""
        {{
            allDocumentRequiredFields(
                companies_ids: ["{user2.company_id}"],
                edrpous: ["{TEST_RECIPIENT_EDRPOU_2}"],
            ) {{
             id
             documentCategory
             isNameRequired
             isTypeRequired
             isNumberRequired
             isDateRequired
             companyId
            }}
        }}
    """

    async with app['db'].acquire() as conn:
        await prepare_document_required_fields(
            conn=conn,
            company_id=user.company_id,
        )
        await prepare_document_required_fields(
            conn=conn,
            company_id=user2.company_id,
        )
        await prepare_document_required_fields(
            conn=conn,
            company_id=user3.company_id,
        )

    # Act
    data = await fetch_graphql(client, query, prepare_auth_headers(user))

    # Assert
    # Only user2 and user3 are selected
    assert len(data['allDocumentRequiredFields']) == 2


async def test_resolve_required_fields(aiohttp_client):
    # Arrange
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=True,
    )

    query = """
        {
            allDocumentRequiredFields {
             id
             documentCategory
             isNameRequired
             isTypeRequired
             isNumberRequired
             isDateRequired
             isAmountRequired
             companyId
            }
        }
    """

    async with app['db'].acquire() as conn:
        await prepare_document_required_fields(
            conn=conn,
            company_id=user.company_id,
        )

    # Act
    data = await fetch_graphql(client, query, prepare_auth_headers(user))

    # Assert
    # Only current user's fields are selected
    assert len(data['allDocumentRequiredFields']) == 1


async def test_resolve_antivirus_check(aiohttp_client):
    # Arrange
    query = """
        {
         allDocuments {
         documents {
          id
          antivirusChecks {
           id
           status
           provider
           dateCreated
           dateUpdated
          }
         }
         }
        }
    """

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    async with services.db.acquire() as conn:
        await add_document_antivirus_check(
            conn=conn,
            check=DocumentAntivirusCheck(
                document_id=document.id,
                status=AntivirusCheckStatus.checking,
            ),
        )

    # Act
    async with with_elastic(app, [document.id]):
        data = await fetch_graphql(client, query, prepare_auth_headers(user))

    # Assert
    assert len(data['allDocuments']['documents'][0]['antivirusChecks']) == 1


async def test_resolve_cloud_signers(aiohttp_client):
    # Arrange
    query = """
        {
         allCloudSigners {
           documentId
           operationId
         }
        }
    """

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)
    operation_id = uuid4().hex

    async with app['db'].acquire() as conn:
        await insert_cloud_signer(
            conn=conn,
            data={
                'role_id': user.role_id,
                'document_id': document.id,
                'operation_id': operation_id,
            },
        )

    # Act
    data = await fetch_graphql(client, query, prepare_auth_headers(user))

    # Assert
    assert len(data['allCloudSigners']) == 1
    assert data['allCloudSigners'][0]['documentId'] == document.id
    assert data['allCloudSigners'][0]['operationId'] == operation_id


@pytest.mark.parametrize(
    'data, user_data, expected',
    [
        # Request for user without onboarding fields also works
        pytest.param(
            None,
            {'date_created': '2021-01-01'},
            {
                'onboarding': {
                    'hasInvitedCoworker': False,
                    'hasInvitedRecipient': False,
                    'hasCheckedCompanies': False,
                    'hasUploadedDocument': False,
                    'hasSeenNewUploading': False,
                    'isSkipped': False,
                },
            },
            id='empty',
        ),
        # Check that OLD fields are working
        pytest.param(
            {
                'has_invited_coworker': True,
                'has_checked_companies': True,
                'has_uploaded_document': True,
            },
            {'date_created': '2021-01-01'},
            {
                'onboarding': {
                    'hasInvitedCoworker': True,
                    'hasInvitedRecipient': False,
                    'hasCheckedCompanies': True,
                    'hasUploadedDocument': True,
                    'hasSeenNewUploading': False,
                    'isSkipped': False,
                },
            },
            id='old_fields',
        ),
        # Flip flags and check that everything is working correctly
        pytest.param(
            {
                'has_invited_coworker': False,
                'has_checked_companies': True,
                'has_uploaded_document': True,
            },
            {'date_created': '2021-01-01'},
            {
                'onboarding': {
                    'hasInvitedCoworker': False,
                    'hasInvitedRecipient': False,
                    'hasCheckedCompanies': True,
                    'hasUploadedDocument': True,
                    'hasSeenNewUploading': False,
                    'isSkipped': False,
                },
            },
            id='flip_flags',
        ),
        # Check that new fields are working
        pytest.param(
            {
                'has_invited_recipient': True,
                'is_skipped': True,
            },
            {'date_created': '2021-01-01'},
            {
                'onboarding': {
                    'hasInvitedCoworker': False,
                    'hasInvitedRecipient': True,
                    'hasCheckedCompanies': False,
                    'hasUploadedDocument': False,
                    'hasSeenNewUploading': False,
                    'isSkipped': True,
                },
            },
            id='new_fields',
        ),
        pytest.param(
            {
                'has_seen_new_uploading': False,
            },
            {'date_created': '2024-04-20'},
            {
                'onboarding': {
                    'hasInvitedCoworker': False,
                    'hasInvitedRecipient': False,
                    'hasCheckedCompanies': False,
                    'hasUploadedDocument': False,
                    # even if a user has not seen new uploading, we pretend that user has seen it
                    # if user was registered after 2024-04-19
                    'hasSeenNewUploading': True,
                    'isSkipped': False,
                },
            },
            id='after_new_uploading_released',
        ),
        pytest.param(
            None,
            {'date_created': '2024-04-20'},
            {
                'onboarding': {
                    'hasInvitedCoworker': False,
                    'hasInvitedRecipient': False,
                    'hasCheckedCompanies': False,
                    'hasUploadedDocument': False,
                    'hasSeenNewUploading': True,
                    'isSkipped': False,
                },
            },
            id='empty_after_new_uploading_release',
        ),
    ],
)
async def test_resolve_user_onboarding(aiohttp_client, data, user_data, expected):
    app, client, user = await prepare_client(aiohttp_client, **user_data)
    query = """
        {
            currentUser {
                onboarding {
                    hasInvitedCoworker
                    hasInvitedRecipient
                    hasCheckedCompanies
                    hasUploadedDocument
                    hasSeenNewUploading
                    isSkipped
                }
            }
        }"""

    if data:
        async with services.db.acquire() as conn:
            await conn.execute(user_onboarding_table.insert().values({'user_id': user.id, **data}))

    resp = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert resp['currentUser'] == expected


async def test_wait_my_sign_filter_owner_signed(aiohttp_client):
    """
    Given:
    - documents sent to user
    - all documents singed by owner
    - one document singed by recipient
    When:
    - owner requests list of document that he/she should sign
    Then:
    - owner gets empty list
    """

    option = '(isWaitMySign: true)'

    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        enable_pro_functionality=True,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    recipient_data = [
        {
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
        }
    ]

    # User 1 sent some documents to User 2
    private_key = prepare_private_key(owner)
    private_key2 = prepare_private_key(recipient)

    documents_ids = await prepare_uploaded_signed_documents(
        app, owner, recipient_data, private_key, COUNT_TOTAL_UPLOADS, True
    )

    # sign one of the documents
    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, documents_ids[0])

    await prepare_signature_data(
        app,
        recipient,
        document,
        private_key2,
        False,
    )

    headers = prepare_auth_headers(owner)

    # Test if length of result from resolve_documents equals
    # resolve_count_documents (ensure pagination is correct).
    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ count documents {{ id }} }} }}', headers
        )

    assert len(result['allDocuments']['documents']) == 0
    assert result['allDocuments']['count'] == 0


async def test_wait_my_sign_filter_recipient_signed(aiohttp_client):
    """
    Given:
    - documents sent to user
    - all documents singed by owner
    - one document singed by recipient
    When:
    - recipient requests list of document that he/she should sign
    Then:
    - recipient gets all assigned documents without already signed
    """

    option = '(isWaitMySign: true)'

    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        enable_pro_functionality=True,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    recipient_data = [
        {
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
        }
    ]

    # User 1 sent some documents to User 2
    private_key = prepare_private_key(owner)
    private_key2 = prepare_private_key(recipient)

    documents_ids = await prepare_uploaded_signed_documents(
        app, owner, recipient_data, private_key, COUNT_TOTAL_UPLOADS, True
    )

    # sign one of the documents
    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, documents_ids[0])

    await prepare_signature_data(
        app,
        recipient,
        document,
        private_key2,
        False,
    )

    headers = prepare_auth_headers(recipient)

    # Test if length of result from resolve_documents equals
    # resolve_count_documents (ensure pagination is correct).
    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ count documents {{ id }} }} }}', headers
        )

    assert len(result['allDocuments']['documents']) == COUNT_TOTAL_UPLOADS - 1
    assert result['allDocuments']['count'] == COUNT_TOTAL_UPLOADS - 1


async def test_wait_my_sign_filter_anyone_from_recipient_signed(aiohttp_client):
    """
    Given:
    - documents sent to user
    - all documents singed by owner
    - documents sent to several recipients
    - one document singed by one of the recipient
    When:
    - recipient requests list of document that he/she should sign
    Then:
    - recipient gets all assigned documents without already signed
    """

    option = '(isWaitMySign: true)'

    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        enable_pro_functionality=True,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    recipient_2 = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        enable_pro_functionality=True,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email='1' + TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    recipient_data = [
        {
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'emails': [
                TEST_DOCUMENT_EMAIL_RECIPIENT,
                '1' + TEST_DOCUMENT_EMAIL_RECIPIENT,
            ],
        }
    ]

    # User 1 sent some documents to User 2
    private_key = prepare_private_key(owner)
    private_key2 = prepare_private_key(recipient)

    documents_ids = await prepare_uploaded_signed_documents(
        app, owner, recipient_data, private_key, COUNT_TOTAL_UPLOADS, True
    )

    # sign one of the documents
    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, documents_ids[0])

    await prepare_signature_data(
        app,
        recipient,
        document,
        private_key2,
        False,
    )

    headers = prepare_auth_headers(recipient_2)

    # Test if length of result from resolve_documents equals
    # resolve_count_documents (ensure pagination is correct).
    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ count documents {{ id }} }} }}', headers
        )

    assert len(result['allDocuments']['documents']) == COUNT_TOTAL_UPLOADS - 1
    assert result['allDocuments']['count'] == COUNT_TOTAL_UPLOADS - 1


async def test_wait_my_sign_filter_only_agreed_document(aiohttp_client):
    """
    Given:
    - documents sent to user
    - all documents singed by owner
    - one of document has not finished review
    When:
    - recipient requests list of document that he/she should sign
    Then:
    - recipient gets all assigned documents except document with review
    """

    option = '(isWaitMySign: true)'

    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        enable_pro_functionality=True,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    recipient_data = [
        {
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
        }
    ]

    # User 1 sent some documents to User 2
    private_key = prepare_private_key(owner)

    documents_ids = await prepare_uploaded_signed_documents(
        app, owner, recipient_data, private_key, COUNT_TOTAL_UPLOADS, True
    )

    # add review to one of the document
    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, documents_ids[0])
        await prepare_review_requests(
            client, document, recipient, reviewers=[recipient], is_required=True
        )

    headers = prepare_auth_headers(recipient)

    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(client, f'{{ countDocuments{option} }}', headers)

    assert result['countDocuments'] == COUNT_TOTAL_UPLOADS - 1

    # Test if length of result from resolve_documents equals
    # resolve_count_documents (ensure pagination is correct).
    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ count documents {{ id }} }} }}', headers
        )

    assert len(result['allDocuments']['documents']) == COUNT_TOTAL_UPLOADS - 1
    assert result['allDocuments']['count'] == COUNT_TOTAL_UPLOADS - 1


async def test_wait_my_sign_filter_only_recipient_should_sign(aiohttp_client):
    """
    Given:
    - documents sent to user
    - documents should be signed only by recipient
    - one of the documents is signed by recipient
    When:
    - recipient requests list of document that he/she should sign
    Then:
    - recipient gets all unsigned documents
    """

    option = '(isWaitMySign: true)'

    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        enable_pro_functionality=True,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    recipient_data = [
        {
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
        }
    ]

    documents_ids = []
    for _ in range(COUNT_TOTAL_UPLOADS):
        document = await prepare_document_data(
            app,
            owner,
            status_id=DocumentStatus.sent.value,
            first_sign_by=FirstSignBy.recipient,
            document_recipients=recipient_data,
            expected_owner_signatures=0,
            expected_recipient_signatures=1,
        )
        documents_ids.append(document.id)

    # sign one of the documents
    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, documents_ids[0])

    await prepare_signature_data(
        app,
        recipient,
        document,
        is_owner_signature=False,
    )

    headers = prepare_auth_headers(recipient)

    # Test if length of result from resolve_documents equals
    # resolve_count_documents (ensure pagination is correct).
    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ count documents {{ id }} }} }}', headers
        )
    assert len(result['allDocuments']['documents']) == COUNT_TOTAL_UPLOADS - 1
    assert result['allDocuments']['count'] == COUNT_TOTAL_UPLOADS - 1


async def test_wait_my_sign_filter_role_is_group_member(aiohttp_client):
    """
    Given:
    - documents uploaded
    - group with 2 users as members
    - another group without members
    - group assigned to document as signer
    - one of the documents is signed
    When:
    - user requests list of document that he/she should sign
    Then:
    - user gets all unsigned documents
    - but not document that was signed by team member
    """

    option = '(isWaitMySign: true)'

    app, client, owner = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')

    # User 1 sent some documents to User 2
    private_key = prepare_private_key(coworker)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=owner,
        )
        group2 = await add_group(
            conn=conn,
            name='test2',
            user=owner,
        )
        await add_group_member(
            conn=conn,
            group_id=group.id,
            role_id=owner.role_id,
            initiator=owner,
        )
        await add_group_member(
            conn=conn,
            group_id=group.id,
            role_id=coworker.role_id,
            initiator=owner,
        )

        documents_ids = []
        for _ in range(COUNT_TOTAL_UPLOADS):
            document = await prepare_document_data(
                app,
                owner=owner,
                groups=[group.id, group2.id],
                set_groups_as_signers=True,
                first_sign_by=FirstSignBy.owner,
                expected_owner_signatures=2,
                is_internal=True,
            )
            documents_ids.append(document.id)

    # sign one of the documents
    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, documents_ids[0])

    await prepare_signature_data(
        app,
        coworker,
        document,
        private_key,
        False,
    )

    headers = prepare_auth_headers(owner)

    # Test if length of result from resolve_documents equals
    # resolve_count_documents (ensure pagination is correct).
    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ count documents {{ id }} }} }}', headers
        )

    assert len(result['allDocuments']['documents']) == COUNT_TOTAL_UPLOADS - 1
    assert result['allDocuments']['count'] == COUNT_TOTAL_UPLOADS - 1


async def test_wait_my_sign_filter_ordered_with_groups(aiohttp_client):
    """
    Check that we properly filter documents by the `isWaitMySign` flag when
    there are ordered sign process with groups and roles as signers.
    """

    wait_my_sign_query = '{ allDocuments(isWaitMySign: true) { documents { id } } }'

    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    user_1 = await prepare_user_data(app, email='<EMAIL>')
    user_2 = await prepare_user_data(app, email='<EMAIL>')
    user_3 = await prepare_user_data(app, email='<EMAIL>')
    user_4 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(
        app=app,
        owner=owner,
        expected_owner_signatures=2,
        expected_recipient_signatures=1,
        first_sign_by=FirstSignBy.owner,
        recipient_edrpou=TEST_RECIPIENT_EDRPOU,
        recipient_email=TEST_RECIPIENT_EMAIL,
    )

    async with services.db.acquire() as conn:
        group = await add_group(conn, name='test', user=owner)
        await add_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user_2.role_id,
            initiator=owner,
        )
        await add_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user_3.role_id,
            initiator=owner,
        )

    response = await request_document_update(
        client,
        user=owner,
        document=document,
        signers_settings={
            'parallel_signing': False,
            'entities': [
                {'id': user_1.role_id, 'type': 'role'},
                {'id': group.id, 'type': 'group'},
                {'id': user_4.role_id, 'type': 'role'},
            ],
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async def _check(user, *, expected):
        r = await fetch_graphql(
            client=client,
            query=wait_my_sign_query,
            headers=prepare_auth_headers(user),
        )
        assert {doc['id'] for doc in r['allDocuments']['documents']} == expected

    # check that user1 can see the document by the filter
    async with with_elastic(app, [document.id]):
        await _check(user_1, expected={document.id})

        # other users should not see the document
        await _check(user_2, expected=set())
        await _check(user_3, expected=set())
        await _check(user_4, expected=set())

    # sign and send from user_1 to group
    await sign_and_send_document(client, document_id=document.id, signer=user_1)

    # check that both group members can see the document by the filter
    async with with_elastic(app, [document.id]):
        await _check(user_2, expected={document.id})
        await _check(user_3, expected={document.id})

        # other users should not see the document
        await _check(user_1, expected=set())
        await _check(user_4, expected=set())

    # sign by one member of the group and send to user_4
    await sign_and_send_document(client, document_id=document.id, signer=user_2)

    # check that the document is visible to the user_4
    async with with_elastic(app, [document.id]):
        await _check(user_4, expected={document.id})

        # other users should not see the document
        await _check(user_1, expected=set())
        await _check(user_2, expected=set())
        await _check(user_3, expected=set())

    await sign_and_send_document(client, document_id=document.id, signer=user_4)

    # check that the document is not visible to anyone
    async with with_elastic(app, [document.id]):
        await _check(user_1, expected=set())
        await _check(user_2, expected=set())
        await _check(user_3, expected=set())
        await _check(user_4, expected=set())


async def create_test_docs(app, user, user2) -> list[str]:
    doc1 = await prepare_document_data(
        app,
        user2,
        recipient_edrpou=user.company_edrpou,
        title='doc_1',
        first_sign_by=FirstSignBy.owner.value,
        status_id=DocumentStatus.signed_and_sent.value,
    )
    doc2 = await prepare_document_data(
        app,
        user2,
        recipient_edrpou=user.company_edrpou,
        title='doc_2',
        first_sign_by=FirstSignBy.recipient.value,
        status_id=DocumentStatus.uploaded.value,
    )
    doc3 = await prepare_document_data(
        app,
        user,
        recipient_edrpou=user2.company_edrpou,
        title='doc_3',
        first_sign_by=FirstSignBy.recipient.value,
        status_id=DocumentStatus.signed_and_sent.value,
    )
    doc4 = await prepare_document_data(
        app,
        user,
        recipient_edrpou=user2.company_edrpou,
        title='doc_4',
        first_sign_by=FirstSignBy.owner.value,
        status_id=DocumentStatus.signed_and_sent.value,
    )
    doc5 = await prepare_document_data(
        app,
        user,
        recipient_edrpou=user2.company_edrpou,
        title='doc_5',
        first_sign_by=FirstSignBy.recipient.value,
        status_id=DocumentStatus.sent.value,
    )
    doc6 = await prepare_document_data(
        app,
        user2,
        recipient_edrpou=user.company_edrpou,
        title='doc_6',
        first_sign_by=FirstSignBy.recipient.value,
        status_id=DocumentStatus.signed_and_sent.value,
    )
    return [doc1.id, doc2.id, doc3.id, doc4.id, doc5.id, doc6.id]


async def test_wait_my_sign_filter_recipient_of_version_should_sign(aiohttp_client):
    """
    Given:
    - documents sent to recipient with version
    - recipient sent version to owner
    When:
    - owner of documents requests list of document that he/she should sign
    - or recipient requests list of document that he/she should sign
    Then:
    - owner gets only documents with version that uploaded by recipient
    - recipient gets only documents with version that uploaded by document owner
    """

    option = '(isWaitMySign: true)'

    app, client, owner = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        user_role=UserRole.admin.value,
        enable_pro_functionality=True,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )

    recipient_data = [
        {
            'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
            'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
        }
    ]

    documents_ids = []
    for _ in range(COUNT_TOTAL_UPLOADS):
        document = await prepare_document_data(
            app,
            owner,
            status_id=DocumentStatus.sent.value,
            first_sign_by=FirstSignBy.recipient,
            document_recipients=recipient_data,
            expected_owner_signatures=1,
            expected_recipient_signatures=1,
            create_owner_signer=True,
        )
        documents_ids.append(document.id)

    for document_id in documents_ids:
        # mark document as versioned
        await prepare_document_version(
            document_id=document_id,
            role_id=owner.role_id,
            company_edrpou=owner.company_edrpou,
            is_sent=True,
            type=DocumentVersionType.new_upload,
            date_created=naive_local_now(),
        )

    # recipient uploads and sends new version
    await prepare_document_version(
        document_id=documents_ids[0],
        role_id=recipient.role_id,
        company_edrpou=recipient.company_edrpou,
        is_sent=True,
        type=DocumentVersionType.new_upload,
        date_created=naive_local_now() + datetime.timedelta(days=1),
    )

    # --- Assert owner should sign only his document where version was sent by recipient
    headers = prepare_auth_headers(owner)
    async with with_elastic(app, documents_ids):
        result = await fetch_graphql(client, f'{{ allDocuments{option} {{ count }} }}', headers)
        assert result['allDocuments']['count'] == 1

        # Test if length of result from resolve_documents equals
        # resolve_count_documents (ensure pagination is correct).
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ count documents {{ id }} }} }}', headers
        )
        assert len(result['allDocuments']['documents']) == 1
        assert result['allDocuments']['documents'][0]['id'] == documents_ids[0]

        # --- Assert recipient should sign only his document where version was sent by owner
        headers = prepare_auth_headers(recipient)
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ count documents {{ id }} }} }}', headers
        )
        assert result['allDocuments']['count'] == 4

        # Test if length of result from resolve_documents equals
        # resolve_count_documents (ensure pagination is correct).
        result = await fetch_graphql(
            client, f'{{ allDocuments{option} {{ documents {{ id }} }} }}', headers
        )
        assert len(result['allDocuments']['documents']) == 4
        assert sorted([doc['id'] for doc in result['allDocuments']['documents']]) == sorted(
            documents_ids[1:]
        )


async def test_wait_my_sign_after_versioned_review(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(app=app, owner=owner, is_internal=True)

    async def get_filter_documents():
        async with with_elastic(app, [document.id]):
            headers = prepare_auth_headers(owner)
            result = await fetch_graphql(
                client=client,
                query='{ allDocuments(isWaitMySign: true) { documents { id }  } }',
                headers=headers,
            )
            return [doc['id'] for doc in result['allDocuments']['documents']]

    await prepare_document_signer(
        document_id=document.id,
        company_id=owner.company_id,
        role_id=owner.role_id,
        group_id=None,
    )

    # Owner is signer of the document, so document is waiting for his signature
    assert await get_filter_documents() == [document.id]

    await prepare_document_version(
        document_id=document.id,
        role_id=owner.role_id,
        company_edrpou=owner.company_edrpou,
        is_sent=False,
        type=DocumentVersionType.new_upload,
        date_created=naive_local_now(),
    )

    await prepare_review_requests(
        client=client,
        document=document,
        initiator=owner,
        reviewers=[coworker],
        is_required=True,
    )

    # We added required review, so document is waiting
    # for review before starting signing process
    assert await get_filter_documents() == []

    # Coworker decides to reject the first version of the document
    await prepare_review(
        client=client,
        document=document,
        user=coworker,
        review_type='reject',
    )

    # After review is rejected, document can't be signed at all
    assert await get_filter_documents() == []

    # Owner uploads new version of the document
    await prepare_document_version(
        document_id=document.id,
        role_id=owner.role_id,
        company_edrpou=owner.company_edrpou,
        is_sent=False,
        type=DocumentVersionType.new_upload,
        date_created=naive_local_now(),
    )

    # Now coworker likes the new version of the document and approves it
    await prepare_review(
        client=client,
        document=document,
        user=coworker,
        review_type='approve',
    )

    # Now, after new version is approved, document is waiting for owner's signature
    assert await get_filter_documents() == [document.id]


async def test_wait_my_sign_change_signer(aiohttp_client):
    app, client, recipient = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')
    owner = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        create_billing_account=True,
    )
    document = await prepare_document_data(
        app,
        owner,
        first_sign_by=FirstSignBy.recipient,
        expected_recipient_signatures=1,
    )

    async def get_filter_documents(user):
        async with with_elastic(app, [document.id]):
            headers = prepare_auth_headers(user)
            result = await fetch_graphql(
                client=client,
                query='{ allDocuments(isWaitMySign: true) { documents { id }  } }',
                headers=headers,
            )
            return [doc['id'] for doc in result['allDocuments']['documents']]

    # no documents for recipient because document is not sent
    assert await get_filter_documents(recipient) == []
    assert await get_filter_documents(coworker) == []

    # send document to recipient
    await send_document(
        client=client,
        document_id=document.id,
        sender=owner,
        recipient_edrpou=recipient.company_edrpou,
        recipient_email=recipient.email,
    )

    # recipient should sign the document
    assert await get_filter_documents(recipient) == [document.id]
    assert await get_filter_documents(coworker) == []

    # change signer to coworker
    await prepare_document_signer(
        document_id=document.id,
        company_id=coworker.company_id,
        role_id=coworker.role_id,
        group_id=None,
    )

    # now coworker should sign the document
    assert await get_filter_documents(recipient) == []
    assert await get_filter_documents(coworker) == [document.id]


@pytest.mark.parametrize(
    'filter_data, expected',
    [
        (
            'folderIds: [6001]',
            {'doc_1', 'doc_2', 'doc_3'},
        ),
        (
            'folderIds: [6002]',
            {'doc_4', 'doc_5', 'doc_6'},
        ),
        (
            'folderIds: [6001, 6002]',
            {'doc_1', 'doc_2', 'doc_3', 'doc_4', 'doc_5', 'doc_6'},
        ),
    ],
)
async def test_folder_ids_filter(aiohttp_client, filter_data, expected):
    app, client, user = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(
        app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    doc_ids = await create_test_docs(app, user, user2)

    query = f'{{ allDocuments( {filter_data}) {{ documents {{ title }} }} }}'
    async with with_elastic(app, doc_ids):
        data = await fetch_graphql(client, query, prepare_auth_headers(user))

    result = {elem['title'] for elem in data['allDocuments']['documents']}
    assert result == expected


async def test_resolve_metadata(aiohttp_client):
    # Arrange
    query = """
        {
          allDocuments {
           documents {
            id
            metadata{
             contentHash
             contentLength
            }
           }
          }
        }
    """

    app, client, user = await prepare_client(aiohttp_client)
    document_with_metadata = await prepare_document_data(app, user)
    await prepare_document_data(app, user)

    hash_value = 'i am your very unique hash'
    length_value = 4321

    async with services.db.acquire() as conn:
        await add_documents_meta(
            conn,
            data=[
                {
                    'document_id': document_with_metadata.id,
                    'content_hash': hash_value,
                    'content_length': length_value,
                }
            ],
        )

    # Act
    data = await fetch_graphql(client, query, prepare_auth_headers(user))

    # Assert
    for doc in data['allDocuments']['documents']:
        if document_with_metadata.id == doc['id']:
            assert doc['metadata']['contentHash'] == hash_value
            assert doc['metadata']['contentLength'] == length_value
        else:
            assert doc['metadata']['contentHash'] is None
            assert doc['metadata']['contentLength'] is None


async def test_resolve_flow_is_complete(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    flows_data = [
        {
            'id': TEST_UUID_1,
            'pending_signatures_count': 0,
        },
        {
            'id': TEST_UUID_2,
            'pending_signatures_count': 1,
        },
        {
            'id': TEST_UUID_3,
            'pending_signatures_count': 0,
            'meta': {'unfinished': True},
        },
    ]

    for order, flow_data in enumerate(flows_data):
        await prepare_flow_item(app=app, document_id=document.id, order=order, **flow_data)

    query = f'{{ document(id: "{document.id}") {{ flows {{ id, isComplete }} }} }}'
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert data['document']['flows'] == [
        {'id': TEST_UUID_1, 'isComplete': True},
        {'id': TEST_UUID_2, 'isComplete': False},
        {'id': TEST_UUID_3, 'isComplete': False},
    ]


async def test_resolve_role_date_deleted_and_deleted_by(aiohttp_client):
    query = """
        {
          currentRole {
          id
          company {
            roles {
              id
              dateDeleted
              deletedBy
            }
          }
         }
        }
    """

    app, client, user = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    now = utc_now()

    async with app['db'].acquire() as conn:
        await update_role(
            conn=conn,
            role_id=user2.role_id,
            data={'deleted_by': user.role_id, 'date_deleted': now},
        )

    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    coworker_roles = data['currentRole']['company']['roles']

    expected = {
        user.role_id: {'dateDeleted': None, 'deletedBy': None},
        user2.role_id: {'dateDeleted': now.isoformat(), 'deletedBy': user.role_id},
    }

    for role in coworker_roles:
        assert role['dateDeleted'] == expected[role['id']]['dateDeleted']
        assert role['deletedBy'] == expected[role['id']]['deletedBy']


async def test_resolve_role_activation_fields(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, role_id=TEST_UUID_1)
    user2 = await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_2)
    now = utc_now()

    async with app['db'].acquire() as conn:
        await update_role(
            conn=conn,
            role_id=user2.role_id,
            data={
                'activated_by': user.role_id,
                'date_activated': now,
                'activation_source': RoleActivationSource.invite,
            },
        )

    data = await fetch_graphql(
        client=client,
        query="""
            {
              currentRole {
              id
              company {
                roles {
                  id
                  activatedBy
                  dateActivated
                  activationSource
                }
              }
             }
            }
        """,
        headers=prepare_auth_headers(user),
    )
    coworker_roles = data['currentRole']['company']['roles']
    actual = {
        role['id']: {
            'activatedBy': role['activatedBy'],
            'dateActivated': role['dateActivated'],
            'activationSource': role['activationSource'],
        }
        for role in coworker_roles
    }

    expected = {
        user.role_id: {
            'activatedBy': None,
            'dateActivated': None,
            'activationSource': None,
        },
        user2.role_id: {
            'activatedBy': user.role_id,
            'dateActivated': now.isoformat(),
            'activationSource': RoleActivationSource.invite.value,
        },
    }
    assert actual == expected


@pytest.mark.parametrize('date_deleted', [datetime.date.today(), None])
@pytest.mark.parametrize('status', [CompanyRateStatus.active, None])
async def test_resolve_billing_accounts(aiohttp_client, status, date_deleted):
    """Test return billing_accounts in GraphQL when units_left and amount_left == 0"""
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)
    await prepare_user_data(app, email='<EMAIL>', company_edrpou='********')

    headers = prepare_auth_headers(user)
    query = f'{{ company(id: "{user.company_id}") {{ billingAccounts {{ unitsLeft amountLeft pricePerUser }} }} }}'  # noqa
    result = await fetch_graphql(client, query, headers)
    assert len(result['company']['billingAccounts']) == 0

    await insert_values(
        app=app,
        table=billing_account_table,
        id=TEST_UUID_3,
        company_id=user.company_id,
        rate=AccountRate.integration,
        status=status,
        amount_left=0,
        units_left=0,
        type=AccountType.client_debit,
        date_deleted=date_deleted,
        # price_per_user used only in Ultimate rate, but in test we set value in integration rate
        price_per_user=10052,
    )

    result = await fetch_graphql(client, query, headers)
    if (not date_deleted and not status) or status:
        assert len(result['company']['billingAccounts']) == 1
        billing_accounts = result['company']['billingAccounts'][0]
        assert billing_accounts['amountLeft'] == 0
        assert billing_accounts['unitsLeft'] == 0
        assert billing_accounts['pricePerUser'] == 10052


PERMISSIONS_FLAGS_MATCHING = {
    'can_view_client_data': 'canViewClientData',
    'can_edit_client_data': 'canEditClientData',
    'can_edit_special_features': 'canEditSpecialFeatures',
}


@pytest.mark.parametrize(
    'permission, value',
    [
        ('can_view_client_data', True),
        ('can_view_client_data', False),
        ('can_edit_client_data', True),
        ('can_edit_client_data', False),
        ('can_edit_special_features', True),
        ('can_edit_special_features', False),
    ],
)
async def test_super_admin_permissions_flags(aiohttp_client, permission, value):
    app, client, admin = await prepare_client(
        aiohttp_client,
        super_admin_permissions={permission: value},
    )
    await set_company_config(
        app, company_id=admin.company_id, master=True, admin_is_superadmin=True
    )
    regular_user = await prepare_user_data(app, email='<EMAIL>')

    data = await fetch_graphql(
        client, GQL_CURRENT_ROLE_SUPER_ADMIN_FLAGS, prepare_auth_headers(admin)
    )
    role = data['currentRole']
    assert role[PERMISSIONS_FLAGS_MATCHING[permission]] == value

    from_company_data = await fetch_graphql(
        client,
        GQL_COMPANY_ROLES_SUPER_ADMIN_FLAGS,
        prepare_auth_headers(admin),
        variables={'id': admin.company_id},
    )
    company_roles = from_company_data['company']['roles']
    company_admin_role = next(item for item in company_roles if item['id'] == admin.role_id)
    company_user_role = next(item for item in company_roles if item['id'] == regular_user.role_id)
    assert company_admin_role[PERMISSIONS_FLAGS_MATCHING[permission]] == value
    assert company_user_role == {
        'id': regular_user.role_id,
        'canViewClientData': False,
        'canEditClientData': False,
        'canEditSpecialFeatures': False,
    }


@pytest.mark.parametrize('reviewer_type', [ROLE, GROUP])
@pytest.mark.parametrize('is_ordered', [True, False])
async def test_reviews_filters_elastic(aiohttp_client, reviewer_type, is_ordered):
    app, client, user = await prepare_client(aiohttp_client)

    coworker1 = await prepare_user_data(app, email='<EMAIL>', user_role=UserRole.user.value)
    group = await prepare_group(app, 'test_group', user, [user, coworker1])

    document = await prepare_document_data(app, user)
    # Documents which do not fit under the filter
    doc1 = await prepare_document_data(app, user)
    await prepare_review_requests(client, doc1, user, reviewers=[coworker1])
    doc2 = await prepare_document_data(app, user)
    await prepare_review_requests(client, doc2, user, reviewers=[coworker1, group], is_ordered=True)
    doc3 = await prepare_document_data(app, user)
    await prepare_review_requests(client, doc3, user, reviewers=[coworker1, user], is_ordered=True)
    doc4 = await prepare_document_data(app, user)

    if reviewer_type == ROLE:
        await prepare_review_requests(
            client, document, user, reviewers=[user], is_ordered=is_ordered
        )
    else:
        await prepare_review_requests(
            client, document, user, reviewers=[group], is_ordered=is_ordered
        )

    async with with_elastic(app, [document.id, doc1.id, doc2.id, doc3.id, doc4.id]):
        data = await fetch_graphql(client, GQL_WAIT_MY_REVIEW, prepare_auth_headers(user))
        assert len(data['allDocuments']['documents']) == 1
        filtered_doc = data['allDocuments']['documents'][0]
        assert filtered_doc['id'] == document.id
        assert filtered_doc['reviewRequests'][0]['order'] == (1 if is_ordered else None)
        if reviewer_type == ROLE:
            assert filtered_doc['reviewRequests'][0]['toRoleId'] == user.role_id
            assert filtered_doc['reviewRequests'][0]['toGroupId'] is None
        elif reviewer_type == GROUP:
            assert filtered_doc['reviewRequests'][0]['toRoleId'] is None
            assert filtered_doc['reviewRequests'][0]['toGroupId'] == group.id


@pytest.mark.parametrize('is_ordered', [True, False])
async def test_reviews_group_filter_elastic(aiohttp_client, is_ordered):
    """
    Test case for DOC-6237 with new elastic document structure
    Create group with user, coworker
    Upload doc with group as reviewer
    Add approve review from coworker
    ER: The document is missing in the filter wait_my_review (request from user)
    """

    app, client, user = await prepare_client(aiohttp_client)
    coworker1 = await prepare_user_data(app, email='<EMAIL>', user_role=UserRole.user.value)
    group = await prepare_group(app, 'test_group', user, [user, coworker1])
    document = await prepare_document_data(app, user)

    await prepare_review_requests(client, document, user, reviewers=[group], is_ordered=is_ordered)
    await prepare_review(
        client, user=coworker1, document=document, review_type=ReviewType.approve.value
    )

    async with with_elastic(app, [document.id]):
        data = await fetch_graphql(client, GQL_WAIT_MY_REVIEW, prepare_auth_headers(user))
        assert len(data['allDocuments']['documents']) == 0


@pytest.mark.parametrize('is_ordered', [True, False])
async def test_cancelled_review_filter_elastic(aiohttp_client, is_ordered):
    app, client, user = await prepare_client(aiohttp_client)

    coworker1 = await prepare_user_data(app, email='<EMAIL>', user_role=UserRole.user.value)
    group = await prepare_group(app, 'test_group', user, [user, coworker1])

    document = await prepare_document_data(app, user)
    # Documents which do not fit under the filter
    doc1 = await prepare_document_data(app, user)
    await prepare_review_requests(client, doc1, user, reviewers=[coworker1])
    doc2 = await prepare_document_data(app, user)
    await prepare_review_requests(client, doc2, user, reviewers=[coworker1, group], is_ordered=True)
    doc3 = await prepare_document_data(app, user)
    await prepare_review_requests(client, doc3, user, reviewers=[coworker1, user], is_ordered=True)
    doc4 = await prepare_document_data(app, user)

    await prepare_review_requests(client, document, user, reviewers=[group], is_ordered=is_ordered)
    await prepare_review(client, user=coworker1, document=document, review_type=ReviewType.approve)
    await prepare_review(client, user=coworker1, document=document, review_type=None)

    async with with_elastic(app, [document.id, doc1.id, doc2.id, doc3.id, doc4.id]):
        data = await fetch_graphql(client, GQL_WAIT_MY_REVIEW, prepare_auth_headers(user))
        assert len(data['allDocuments']['documents']) == 1


async def test_reviews_filters_versions_es_bug(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, create_billing_account=True)

    coworker1 = await prepare_user_data(app, email='<EMAIL>', user_role=UserRole.user.value)
    group = await prepare_group(app, 'test_group', user, [user, coworker1])

    document = await prepare_document_data(app, user)
    # Documents which do not fit under the filter
    doc1 = await prepare_document_data(app, user)
    await prepare_review_requests(client, doc1, user, reviewers=[coworker1])
    doc2 = await prepare_document_data(app, user)
    await prepare_review_requests(client, doc2, user, reviewers=[coworker1, group], is_ordered=True)
    doc3 = await prepare_document_data(app, user)
    await prepare_review_requests(client, doc3, user, reviewers=[coworker1, user], is_ordered=True)
    doc4 = await prepare_document_data(app, user)

    # mark document as versioned
    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            role_id=user.role_id,
            upload_type=DocumentVersionType.new_upload,
            content=b'First version content',
            name='#1',
            date_created='2021-01-01T00:00:00Z',
            extension='.pdf',
        )
    await sign_and_send_document(
        client,
        document_id=document.id,
        signer=user,
        recipient_email=TEST_RECIPIENT_EMAIL,
        recipient_edrpou=TEST_RECIPIENT_EDRPOU,
    )
    await prepare_review_requests(client, document, user, reviewers=[group])
    await prepare_review(client, user=coworker1, document=document, review_type=ReviewType.approve)

    # Add second version
    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            role_id=user.role_id,
            upload_type=DocumentVersionType.new_upload,
            content=b'new version content',
            name='#2',
            date_created='2022-01-01T00:00:00Z',
            extension='.pdf',
        )

    async with with_elastic(app, [document.id, doc1.id, doc2.id, doc3.id, doc4.id]):
        data = await fetch_graphql(client, GQL_WAIT_MY_REVIEW, prepare_auth_headers(user))
        # 1 - cause we leave a review in previous version, in last_version review not exist
        assert len(data['allDocuments']['documents']) == 1


async def test_wait_my_review(aiohttp_client):
    """
    Check that we correctly resolve documents with wait_my_review filter.
    """

    app, client, owner = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')

    document_1 = await prepare_document_data(app, owner, id=TEST_UUID_1)
    document_2 = await prepare_document_data(app, owner, id=TEST_UUID_2)
    document_3 = await prepare_document_data(app, owner, id=TEST_UUID_3)

    documents_ids: list[str] = [document_1.id, document_2.id]
    documents_ids.sort()

    async def fetch_documents_ids():
        # Fetch documents IDs with wait_my_review filter
        async with with_elastic(app, documents_ids=documents_ids):
            result = await fetch_graphql(
                client=client,
                query='{ allDocuments(reviewFolder: "wait_my_review") { documents { id } } }',
                headers=prepare_auth_headers(owner),
            )
        return sorted([doc['id'] for doc in result['allDocuments']['documents']])

    # No documents are waiting my review
    documents_ids_result = await fetch_documents_ids()
    assert documents_ids_result == []

    await prepare_review_requests(
        client=client,
        document=document_1,
        reviewers=[owner],
        is_required=True,
        initiator=owner,
    )
    await prepare_review_requests(
        client=client,
        document=document_2,
        reviewers=[owner],
        is_required=False,
        initiator=owner,
    )
    # The document_3 is not waiting review from target user,
    # so it should not be returned in any case
    await prepare_review_requests(
        client=client,
        document=document_3,
        reviewers=[coworker],
        is_required=True,
        initiator=owner,
    )

    # Document 1 and 2 are waiting my review
    documents_ids_result = await fetch_documents_ids()
    assert documents_ids_result == [document_1.id, document_2.id]

    # Approve review for document 1
    await prepare_review(client, user=owner, document=document_1, review_type='approve')

    # Document 2 is still waiting my review
    documents_ids_result = await fetch_documents_ids()
    assert documents_ids_result == [document_2.id]

    # Reject review for document 2
    await prepare_review(client, user=owner, document=document_2, review_type='reject')

    # No documents are waiting my review
    documents_ids_result = await fetch_documents_ids()
    assert documents_ids_result == []

    # Withdraw review for document 2
    await prepare_review(client, user=owner, document=document_2, review_type=None)

    # Document 2 again is waiting my review
    documents_ids_result = await fetch_documents_ids()
    assert documents_ids_result == [document_2.id]


async def test_wait_my_review_with_groups(aiohttp_client):
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')

    document_1 = await prepare_document_data(app, user1, id=TEST_UUID_1)
    document_2 = await prepare_document_data(app, user1, id=TEST_UUID_2)
    document_3 = await prepare_document_data(app, user1, id=TEST_UUID_3)

    group1 = await prepare_group(app, 'test_group', user1, [user1, user2])
    group2 = await prepare_group(app, 'test_group2', user1, [user1])

    documents_ids: list[str] = [document_1.id, document_2.id, document_3.id]
    documents_ids.sort()

    async def fetch_documents_ids():
        # Fetch documents IDs with wait_my_review filter
        async with with_elastic(app, documents_ids=documents_ids):
            result = await fetch_graphql(
                client=client,
                query='{ allDocuments(reviewFolder: "wait_my_review") { documents { id } } }',
                headers=prepare_auth_headers(user1),
            )
        return sorted([doc['id'] for doc in result['allDocuments']['documents']])

    # No documents are waiting my review
    documents_ids_result = await fetch_documents_ids()
    assert documents_ids_result == []

    # Document is reviewed by other user in group
    await prepare_review_requests(
        client=client,
        document=document_1,
        reviewers=[group1],
        is_required=True,
        initiator=user1,
    )
    await prepare_review(client, user=user2, document=document_1, review_type='approve')
    assert await fetch_documents_ids() == []

    # Document is reviewed by other group but user still should review
    await prepare_review_requests(
        client=client,
        document=document_2,
        reviewers=[group1, user1],
        is_required=True,
        initiator=user1,
    )
    await prepare_review(client, user=user2, document=document_2, review_type='approve')
    assert await fetch_documents_ids() == [document_2.id]

    # Document is reviewed by user in two groups
    await prepare_review_requests(
        client=client,
        document=document_3,
        reviewers=[group1, user3, group2],
        is_required=True,
        initiator=user1,
    )
    await prepare_review(client, user=user1, document=document_3, review_type='approve')
    assert await fetch_documents_ids() == [document_2.id]

    # Test case for checking behavior when a user is removed from a group
    # Create a new document that needs review from group1
    document_4 = await prepare_document_data(app, user1, id=TEST_UUID_4)
    documents_ids.append(document_4.id)
    documents_ids.sort()

    await prepare_review_requests(
        client=client,
        document=document_4,
        reviewers=[group1],
        is_required=True,
        initiator=user1,
    )

    # Initially, user1 should see the document in wait_my_review filter
    assert await fetch_documents_ids() == [document_2.id, document_4.id]

    # Remove user1 from group1
    async with services.db.acquire() as conn:
        await update_group_members(
            conn=conn,
            where_group_ids=[group1.id],
            where_role_ids=[user1.role_id],
            deleted_by=user1.role_id,
        )

    # After removal from group1, user1 should not see document_4 in wait_my_review filter
    assert await fetch_documents_ids() == [document_2.id]


async def test_resolve_bills_for_company(aiohttp_client, monkeypatch, test_flags):
    """
    Given a company with 4 bills
    When calling graphql query for company
    Expected to return all bills
    """
    test_flags[FeatureFlags.ENABLE_NEW_RATES.name] = True

    # Prepare
    app, client, user = await prepare_client(aiohttp_client)

    # Prepare start bills
    expected_bills = []
    for _ in range(3):
        headers = prepare_auth_headers(user)
        now = local_now()
        date_from = now.strftime('%Y-%m-%d')
        bill_data = {
            'date_from': date_from,
            'email': user.email,
            'edrpou': user.company_edrpou,
            'name': 'test company name',
            'services': [
                {
                    'type': 'rate',
                    'rate': AccountRate.latest_start().value,
                    'date_from': date_from,
                }
            ],
        }
        response = await client.post(BILLS_URL, json=bill_data, headers=headers)
        assert response.status == 201, await response.json()
        response_json = await response.json()
        bill_id = response_json['bill_id']
        expected_bills.append(
            {
                'id': bill_id,
                'services': [
                    {
                        'type': 'rate',
                        'units': 1,
                        'unitPrice': str(START_PRICE_TOV_SUBUNITS),
                        'dateFrom': now.strftime('%Y-%m-%dT00:00:00'),
                        'extension': None,
                        'rate': LATEST_START_RATE,
                        'limitsEmployeesCount': None,
                    }
                ],
                'servicesType': 'rate',
                'rate': LATEST_START_RATE,
                'count_documents': None,
                'max_employees_count': None,
                'documentId': None,
                'paymentStatus': None,
                'statusId': BillStatus.requested.value,
                'amount': START_PRICE_TOV_SUBUNITS,
            }
        )

    # Prepare bill sent from CRM (without role_id, user_id, company_id)
    bill_data = {
        'rate': 'ultimate',
        'custom_price': 2539.5,
        'Count': 100,
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
        'PriceAddEmployee': 480.0,
    }
    response = await client.post(
        CRM_BILLS_URL,
        json=bill_data,
        headers={HEADER_KEY: CRM_AUTH_TOKEN},
    )
    assert response.status == 201
    response_json = await response.json()
    expected_bills.append(
        {
            'id': response_json['bill_id'],
            'services': [
                {
                    'type': 'rate',
                    'units': 1,
                    'unitPrice': '253950',
                    'dateFrom': None,
                    'extension': None,
                    'rate': 'ultimate_2022_12',
                    'limitsEmployeesCount': 100,
                }
            ],
            'servicesType': 'rate',
            'rate': 'ultimate_2022_12',
            'count_documents': None,
            'max_employees_count': 100,
            'documentId': None,
            'paymentStatus': None,
            'statusId': None,
            'amount': 253950,
        }
    )

    # Act
    query = """
        query GetCompanyBills($id: ID!) {
            company(id: $id) {
                bills {
                    id
                    documentId
                    paymentStatus
                    statusId
                    amount
                    services {
                        type
                        units
                        unitPrice
                        rate
                        dateFrom
                        extension
                        limitsEmployeesCount
                    }
                    servicesType
                    rate
                    count_documents
                    max_employees_count
                }
            }
        }
    """
    result = await fetch_graphql(
        client=client,
        query=query,
        variables={'id': user.company_id},
        headers=prepare_auth_headers(user),
    )

    result_bills = sorted(result['company']['bills'], key=lambda x: x['id'])
    expected_bills = sorted(expected_bills, key=lambda x: x['id'])

    assert result_bills == expected_bills


async def test_resolve_add_employee_bills(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    custom_price = 2539.5
    async with app['db'].acquire() as conn:
        await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_ultimate(),
            status=AccountStatus.active,
            date_expired=local_now() + datetime.timedelta(days=365),
            billing_company_config={CompanyLimit.employees.value: 10},
            type=AccountType.client_rate,
            price_per_user=100052,  # 1000.52 UAH
        )

    bill_data = {
        'rate': CreatioBillType.add_employee.value,
        'custom_price': custom_price,
        'Count': 5,
        'PriceAddEmployee': 1200,
        'email': user.email,
        'edrpou': user.company_edrpou,
        'name': 'test company name',
    }
    response = await client.post(
        CRM_BILLS_URL,
        json=bill_data,
        headers={HEADER_KEY: CRM_AUTH_TOKEN},
    )
    assert response.status == 201

    query = (
        f'{{ company(id: "{user.company_id}") '
        f'{{ bills {{ id rate max_employees_count amount }} }} }}'
    )
    result = await fetch_graphql(
        client=client,
        query=query,
        headers=prepare_auth_headers(user),
    )
    assert result['company']['bills'][0]['max_employees_count'] == 5
    assert result['company']['bills'][0]['rate'] == FakeBillRate.add_employee.value
    assert result['company']['bills'][0]['amount'] == int(custom_price * 100)


async def test_filter_documents_by_access_level_private(aiohttp_client):
    """
    Test filtering documents by access level 'private'
    """
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )

    # Create private documents for an owner
    document_1_private = await prepare_document_data(app, owner)
    document_2_private = await prepare_document_data(app, owner)
    await prepare_private_document(
        document_id=document_1_private.id,
        company_edrpou=owner.company_edrpou,
    )
    await prepare_private_document(
        document_id=document_2_private.id,
        company_edrpou=owner.company_edrpou,
    )

    # Create extended (non-private) documents
    document_3_extended = await prepare_document_data(app, owner)
    document_4_extended = await prepare_document_data(app, owner)

    # Create a document that is private for recipient but not for owner
    document_5_recipient_private = await prepare_document_data(
        app=app, owner=owner, another_recipients=[recipient]
    )
    await prepare_private_document(
        document_id=document_5_recipient_private.id,
        company_edrpou=recipient.company_edrpou,
    )

    query = """
        query GetAllPrivateDocuments {
            allDocuments(accessLevel: "private") {
                documents { id }
                count
            }
        }
    """

    all_documents = [
        document_1_private.id,
        document_2_private.id,
        document_3_extended.id,
        document_4_extended.id,
        document_5_recipient_private.id,
    ]

    async with with_elastic(app, all_documents):
        # check docs from owner perspective
        response = await fetch_graphql(
            client=client,
            query=query,
            headers=prepare_auth_headers(owner),
        )
        assert response['allDocuments']['count'] == 2
        response_documents = {d['id'] for d in response['allDocuments']['documents']}
        assert response_documents == {document_1_private.id, document_2_private.id}

        # check documents from recipient perspective
        response = await fetch_graphql(
            client=client,
            query=query,
            headers=prepare_auth_headers(recipient),
        )

        assert response['allDocuments']['count'] == 1
        response_documents = {d['id'] for d in response['allDocuments']['documents']}
        assert response_documents == {document_5_recipient_private.id}


async def test_resolve_with_can_sign(aiohttp_client, test_flags):
    app, client, owner = await prepare_client(aiohttp_client, create_billing_account=True)

    await prepare_document_data(
        app,
        owner,
        document_recipients=[{'edrpou': TEST_RECIPIENT_EDRPOU, 'emails': [TEST_RECIPIENT_EMAIL]}],
        status_id=DocumentStatus.ready_to_be_signed.value,
        id=TEST_UUID_1,
    )
    await prepare_document_data(app, owner, id=TEST_UUID_2)
    await prepare_document_data(app, owner, id=TEST_UUID_3)
    await prepare_document_data(app, owner, id=TEST_UUID_4)
    query = '{ allDocuments { documents { id canSign } } }'

    headers = prepare_auth_headers(owner)
    async with with_elastic(app, [TEST_UUID_1, TEST_UUID_2, TEST_UUID_3, TEST_UUID_4]):
        data = await fetch_graphql(client, query, headers)
        res = {item['id']: item['canSign'] for item in data['allDocuments']['documents']}
        assert res == {
            TEST_UUID_1: True,
            TEST_UUID_2: False,
            TEST_UUID_3: False,
            TEST_UUID_4: False,
        }


async def test_all_documents_with_archive_filter(aiohttp_client):
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    recipient = await prepare_user_data(
        app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
    )

    archived = await prepare_document_data(
        app,
        owner,
        status_id=DocumentStatus.finished.value,
        is_archived=True,
    )
    doc1 = await prepare_document_data(
        app,
        owner,
        status_id=DocumentStatus.finished.value,
    )
    doc2 = await prepare_document_data(
        app,
        owner,
        status_id=DocumentStatus.finished.value,
    )
    shared_with_recipient = await prepare_document_data(
        app,
        owner,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': [recipient.email]}],
        status_id=DocumentStatus.finished.value,
    )

    async with with_elastic(app, [archived.id, doc1.id, doc2.id, shared_with_recipient.id]):
        #########
        # OWNER
        #########
        # Request for normal documents
        # archive should not appear here
        query = '{ allDocuments { count documents { id } } }'
        headers = prepare_auth_headers(owner)
        result = await fetch_graphql(client, query, headers)

        assert result['allDocuments']['count'] == 3
        assert {d['id'] for d in result['allDocuments']['documents']} == {
            doc1.id,
            doc2.id,
            shared_with_recipient.id,
        }

        # Request for archived documents
        # normal documents should not appear here
        options = 'isArchived: true'
        query = f'{{ allDocuments({options}){{ count documents {{ id }} }} }}'
        headers = prepare_auth_headers(owner)
        result = await fetch_graphql(client, query, headers)
        assert result['allDocuments']['count'] == 1  # archived
        assert {d['id'] for d in result['allDocuments']['documents']} == {archived.id}

        #########
        # RECIPIENT
        #########
        # Request for normal documents
        # archive should not appear here
        query = '{ allDocuments { count documents { id } } }'
        headers = prepare_auth_headers(recipient)
        result = await fetch_graphql(client, query, headers)

        assert result['allDocuments']['count'] == 1
        assert {d['id'] for d in result['allDocuments']['documents']} == {shared_with_recipient.id}

        # Request for archived documents
        # normal documents should not appear here
        options = 'isArchived: true'
        query = f'{{ allDocuments({options}){{ count documents {{ id }} }} }}'
        headers = prepare_auth_headers(recipient)
        result = await fetch_graphql(client, query, headers)

        assert result['allDocuments']['count'] == 0
        assert len(result['allDocuments']['documents']) == 0


async def test_archive_documents_not_appear_in_wait_for_my_sign_filter(aiohttp_client):
    """
    Given archived documents which are waiting for my sign
    When resolving all documents with wait for my sign filter
    Expected archive documents not to appear in list of documents.
    """
    # Arrange
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        is_admin=True,
    )

    # Arrange archive settings for company
    async with services.db.acquire() as conn:
        await update_company_config(
            conn=conn,
            company_id=owner.company_id,
            config={'archive_settings': {'allow_uploaded_documents': True}},
        )

    # Arrange document
    document = await prepare_document_data(app, owner, is_internal=True)
    await prepare_document_signer(
        document_id=document.id,
        company_id=owner.company_id,
        role_id=owner.role_id,
        group_id=None,
    )

    # Assert document appears in isWaitMySign filter
    query = '{ allDocuments(isWaitMySign: true) { count documents { id } } }'
    async with with_elastic(app, [document.id]):
        result = await fetch_graphql(client, query, prepare_auth_headers(owner))

    assert result['allDocuments']['count'] == 1
    assert {d['id'] for d in result['allDocuments']['documents']} == {document.id}

    # Archive document
    await archive_documents_request(client=client, user=owner, documents=[document])

    # Assert document does not appear in isWaitMySign filter
    async with with_elastic(app, [document.id]):
        result = await fetch_graphql(client, query, prepare_auth_headers(owner))

    assert result['allDocuments']['count'] == 0


async def test_archive_documents_not_appear_in_wait_for_my_review_filter(aiohttp_client):
    """
    Given archived documents which are waiting for my review
    When resolving all documents with wait for my review filter
    Expected archive documents not to appear in list of documents.
    """
    # Arrange
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        is_admin=True,
    )

    # Arrange archive settings for company
    async with services.db.acquire() as conn:
        await update_company_config(
            conn=conn,
            company_id=owner.company_id,
            config={'archive_settings': {'allow_uploaded_documents': True}},
        )

    # Arrange document
    document = await prepare_document_data(app, owner, is_internal=True)
    await prepare_review_requests(client, document, owner, reviewers=[owner])

    # Assert document appears in wait_for_my_review filter
    async with with_elastic(app, [document.id]):
        result = await fetch_graphql(client, GQL_WAIT_MY_REVIEW, prepare_auth_headers(owner))

    assert result['allDocuments']['count'] == 1
    assert {d['id'] for d in result['allDocuments']['documents']} == {document.id}

    # Archive document
    await archive_documents_request(client=client, user=owner, documents=[document])

    # Assert document does not appear in wait_for_my_review filter
    async with with_elastic(app, [document.id]):
        result = await fetch_graphql(client, GQL_WAIT_MY_REVIEW, prepare_auth_headers(owner))

    assert result['allDocuments']['count'] == 0


async def test_resolve_rate_extensions(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        enable_pro_functionality=False,
    )

    headers = prepare_auth_headers(user)
    query = (
        f'{{ company(id: "{user.company_id}") {{ rateExtensions '
        f'{{ id status type bill_id date_expiring bill_document_id }} }} }}'
    )

    result = await fetch_graphql(client, query, headers)
    assert len(result['company']['rateExtensions']) == 0

    async with app['db'].acquire() as conn:
        ultimate_rate = await prepare_billing_account(
            conn,
            company_id=user.company_id,
            rate=AccountRate.latest_ultimate(),
            status=AccountStatus.active,
            billing_company_config={CompanyLimit.employees.value: 10},
            activation_date=utc_now(),
            date_expired=utc_now() + datetime.timedelta(days=365),
        )

        # First bill
        bill = await prepare_bill(
            conn=conn,
            user=user,
            services_type=BillServicesType.rate,
            services=[
                AddBillServiceRateOptions(
                    units=1,
                    unit_price=100,
                    rate=AccountRate.latest_ultimate(),
                    date_from=None,
                    limits_employees_count=5,
                    price_per_user=Decimal('48000'),
                )
            ],
        )

        datetime_expiring = utc_now() + datetime.timedelta(days=5)
        await insert_rate_extension(
            conn,
            {
                'id': TEST_UUID_1,
                'bill_id': bill.id,
                'account_id': ultimate_rate.id,
                'type': RateExtensionType.employees,
                'status': RateExtensionStatus.active,
                'date_expiring': datetime_expiring,
                'config': {'units': 5},
            },
        )

        # Second bill
        second_bill = await prepare_bill(
            conn=conn,
            user=user,
            services_type=BillServicesType.rate,
            services=[
                AddBillServiceRateOptions(
                    units=1,
                    unit_price=100,
                    rate=AccountRate.latest_ultimate(),
                    date_from=None,
                    limits_employees_count=10,
                    price_per_user=Decimal('48000'),
                )
            ],
        )

        datetime_expiring_second_bill = utc_now() + datetime.timedelta(days=10)
        await insert_rate_extension(
            conn,
            {
                'id': TEST_UUID_2,
                'bill_id': second_bill.id,
                'account_id': ultimate_rate.id,
                'type': RateExtensionType.employees,
                'status': RateExtensionStatus.active_trial,
                'date_expiring': datetime_expiring_second_bill,
                'config': {'units': 10},
            },
        )

    result = await fetch_graphql(client, query, headers)

    assert len(result['company']['rateExtensions']) == 2
    extension_info, extension_info_2 = sorted(
        [result['company']['rateExtensions'][0], result['company']['rateExtensions'][1]],
        key=lambda x: x['id'],
    )

    assert extension_info['id'] == TEST_UUID_1
    assert extension_info['status'] == RateExtensionStatus.active.value
    assert extension_info['type'] == RateExtensionType.employees.value
    assert extension_info['bill_id'] == bill.id
    assert extension_info['date_expiring'] == str(datetime_expiring.date())
    assert extension_info['bill_document_id'] == bill.document_id

    assert extension_info_2['id'] == TEST_UUID_2
    assert extension_info_2['status'] == RateExtensionStatus.active_trial.value
    assert extension_info_2['type'] == RateExtensionType.employees.value
    assert extension_info_2['bill_id'] == second_bill.id
    assert extension_info_2['date_expiring'] == str(datetime_expiring_second_bill.date())
    assert extension_info_2['bill_document_id'] == second_bill.document_id


async def test_resolve_user_sessions(aiohttp_client, test_flags):
    query = """
    {
        currentUser {
            sessions {
                id
                ip
                accessedAt
                browser
                browserVersion
                device
                os
                osVersion
            }
        }
    }
    """
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>', user_role=UserRole.user.value)
    coworker_admin = await prepare_user_data(app, email='<EMAIL>')
    other_company_admin = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    other_company_user = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT + '42',
    )

    client.session.cookie_jar.clear()
    await login(client, user.email, TEST_USER_PASSWORD)

    headers = prepare_auth_headers(user)
    response = await fetch_graphql(client, query, headers)
    assert len(response['currentUser']['sessions']) == 1

    assert response['currentUser']['sessions'][0]['ip'] is not None
    assert response['currentUser']['sessions'][0]['accessedAt'] is not None
    assert response['currentUser']['sessions'][0]['id'] is not None
    assert 'aiohttp' in response['currentUser']['sessions'][0]['browser']
    assert response['currentUser']['sessions'][0]['browserVersion'] is not None
    assert response['currentUser']['sessions'][0]['device'] == 'Other'
    assert response['currentUser']['sessions'][0]['os'] == 'Other'
    assert response['currentUser']['sessions'][0]['osVersion'] is None

    # Try to add one more session
    client.session.cookie_jar.clear()
    await login(client, user.email, TEST_USER_PASSWORD)
    response = await fetch_graphql(client, query, headers)
    assert len(response['currentUser']['sessions']) == 2

    client.session.cookie_jar.clear()
    # User's company admin can see user's sessions as well
    query = f"""
    {{
      company(id: "{user.company_id}") {{
        roles {{
          id
          user {{
            id
            sessions {{
              id
            }}
          }}
        }}
      }}
    }}
    """

    headers = prepare_auth_headers(coworker_admin)
    response = await fetch_graphql(client, query, headers)
    for role in response['company']['roles']:
        if role['id'] == user.role_id:
            assert len(role['user']['sessions']) == 2

    # Other can't see user's sessions
    for actor in [coworker, other_company_admin, other_company_user]:
        headers = prepare_auth_headers(actor)
        response = await fetch_graphql(client, query, headers)
        for role in (response.get('company') or {}).get('roles', []):
            if role['id'] == user.role_id:
                assert len(role['user']['sessions']) == 0


async def test_resolve_coworker_is_registered(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    # fully registered coworker
    coworker1 = await prepare_user_data(
        app=app,
        role_id=TEST_UUID_1,
        email='<EMAIL>',
        role_status=RoleStatus.active,
        registration_completed=True,
        email_confirmed=True,
        is_logged_once=True,
    )
    # was deleted
    coworker2 = await prepare_user_data(
        app=app,
        role_id=TEST_UUID_2,
        email='<EMAIL>',
        role_status=RoleStatus.deleted,
        registration_completed=False,
        email_confirmed=True,
        is_logged_once=True,
    )
    # was invited by coworker, logged in, but not confirmed email yet
    coworker3 = await prepare_user_data(
        app=app,
        role_id=TEST_UUID_3,
        email='<EMAIL>',
        role_status=RoleStatus.active,
        registration_completed=True,
        email_confirmed=False,
        is_logged_once=True,
    )
    # was created by coworker, but never logged in
    coworker4 = await prepare_user_data(
        app=app,
        role_id=TEST_UUID_4,
        email='<EMAIL>',
        role_status=RoleStatus.active,
        registration_completed=True,
        email_confirmed=True,
        is_logged_once=False,
    )
    # not registered yet and canceled invitation
    coworker5 = await prepare_user_data(
        app=app,
        role_id=TEST_UUID_5,
        email='<EMAIL>',
        role_status=RoleStatus.rate_deleted,
        registration_completed=False,
        email_confirmed=False,
        is_logged_once=False,
    )

    headers = prepare_auth_headers(user)
    response = await fetch_graphql(
        client=client,
        query="""
            query GetCompany($id: ID!) {
                company(id: $id) {
                    roles {
                        id
                        status
                        user {
                            isRegistered
                        }
                    }
                }
            }
        """,
        variables={'id': user.company_id},
        headers=headers,
    )
    company_data = response['company']
    roles = company_data['roles']
    assert len(roles) == 6
    assert {role['id']: role for role in roles} == {
        user.role_id: {
            'id': user.role_id,
            'status': RoleStatus.active.value,
            'user': {'isRegistered': True},
        },
        coworker1.role_id: {
            'id': coworker1.role_id,
            'status': RoleStatus.active.value,
            'user': {'isRegistered': True},
        },
        coworker2.role_id: {
            'id': coworker2.role_id,
            'status': RoleStatus.deleted.value,
            'user': {'isRegistered': False},
        },
        coworker3.role_id: {
            'id': coworker3.role_id,
            'status': RoleStatus.active.value,
            'user': {'isRegistered': False},
        },
        coworker4.role_id: {
            'id': coworker4.role_id,
            'status': RoleStatus.active.value,
            'user': {'isRegistered': False},
        },
        coworker5.role_id: {
            'id': coworker5.role_id,
            'status': RoleStatus.rate_deleted.value,
            'user': {'isRegistered': False},
        },
    }


@pytest.mark.parametrize(
    'document_data, expected_status',
    [
        pytest.param(
            {'status_id': DocumentStatus.uploaded.value},
            'Завантажений',
            id='uploaded_uk',
        ),
        pytest.param(
            {'status_id': DocumentStatus.signed_and_sent.value},
            'Очікує підпису контрагента',
            id='ready_to_be_signed',
        ),
    ],
)
async def test_resolve_document_status_text(
    aiohttp_client,
    document_data: dict,
    expected_status: str,
):
    """
    Check that we correctly resolve document status text. This test have just basic checks,
    for more detailed tests check "test_get_document_status_text" tests.
    """

    app, client, owner = await prepare_client(aiohttp_client)

    document = await prepare_document_data(
        app=app,
        owner=owner,
        **document_data,
    )

    async with with_elastic(app, documents_ids=[document.id]):
        response = await fetch_graphql(
            client=client,
            query='{ allDocuments { documents { id displayStatusText } } }',
            headers=prepare_auth_headers(owner),
        )

    response_documents = response['allDocuments']['documents']
    assert len(response_documents) == 1
    response_document = response_documents[0]
    assert response_document['id'] == document.id
    assert response_document['displayStatusText'] == expected_status


@pytest.mark.parametrize(
    'max_documents_count, max_archive_documents_count, expected_visible_documents_count',
    [(30, 70, 100), (100, None, 100), (None, None, None)],
)
async def test_resolve_visible_documents_limit(
    aiohttp_client,
    max_documents_count,
    max_archive_documents_count,
    expected_visible_documents_count,
):
    """
    Given a client with visible documents limit set
    When querying visibleDocumentsLimit node
    Expected valid result
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)

    # Arrange company config
    await set_billing_company_config(
        company_id=user.company_id,
        max_documents_count=max_documents_count,
        max_archive_documents_count=max_archive_documents_count,
    )

    # Act
    query = """
    {
        currentRole {
            company {
                billingCompanyConfig {
                    maxVisibleDocumentsCount
                    maxDocumentsCount
                    maxArchiveDocumentsCount
                }
            }
        }
    }
    """
    response = await fetch_graphql(
        client=client,
        query=query,
        headers=prepare_auth_headers(user),
    )

    # Assert
    assert response['currentRole']['company']['billingCompanyConfig'] == {
        'maxVisibleDocumentsCount': expected_visible_documents_count,
        'maxDocumentsCount': max_documents_count,
        'maxArchiveDocumentsCount': max_archive_documents_count,
    }


@pytest.mark.parametrize('edrpou', [FOP_EDRPOU, TOV_EDRPOU])
async def test_resolve_company_is_fop(aiohttp_client, edrpou):
    """
    Given a company with specific edrpou
    When resolving isFop graphql parameter
    Expected valid isFop parameter returned
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client, company_edrpou=edrpou)

    # Act
    query = """{currentRole { company { isFop } } }"""
    response = await fetch_graphql(
        client=client,
        query=query,
        headers=prepare_auth_headers(user),
    )

    # Assert
    assert response['currentRole']['company']['isFop'] == is_fop(edrpou)


async def test_graph_depth_validation(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    company_id = user.company_id
    headers = prepare_auth_headers(user)

    async with app['db'].acquire() as conn:
        contact_id = await insert_contact(conn, TEST_CONTACT['company'].copy(), company_id)
        person1 = TEST_CONTACT['person'].copy()
        await insert_contact_person(conn, person1, contact_id)

    repeat_times = 10

    repeated = 'contact { persons { ' * repeat_times + 'id' + ' } }' * repeat_times
    query = f"""{{
    allContacts {{
      contacts {{
       persons {{
        {repeated}
       }}
      }}
     }}
    }}"""

    res = await fetch_graphql_raw(client, query, headers)
    assert res.status == 400


async def test_can_view_coworkers(aiohttp_client):
    """
    Given a user without permission to view coworkers
    When resolving company roles
    Expected empty list to return
    """
    # Arrange
    app, client, user = await prepare_client(aiohttp_client)

    # Arrange 2 coworkers
    for i in range(2):
        await prepare_user_data(
            app=app,
            email=f'test_user_{i}@gmail.com',
            company_edrpou=user.company_edrpou,
        )

    # Act
    query = """
        query GetCompanyRoles($companyId: ID!) {
            company(id: $companyId) {
                roles { id }
            }
        }
    """

    response = await fetch_graphql(
        client=client,
        query=query,
        variables={'companyId': user.company_id},
        headers=prepare_auth_headers(user),
    )

    # Assert coworkers appear
    assert len(response['company']['roles']) == 3

    # Update role to disable can_view_coworkers
    async with services.db.acquire() as conn:
        await update_role(
            conn=conn,
            role_id=user.role_id,
            data={'can_view_coworkers': False},
        )

    # Act
    response = await fetch_graphql(
        client=client,
        query=query,
        variables={'companyId': user.company_id},
        headers=prepare_auth_headers(user),
    )

    # Assert no coworkers appear
    assert len(response['company']['roles']) == 0
