import pytest
import ujson

from api.graph.tests.common import TOMORROW, YESTERDAY
from app.auth.utils import update_company_by_edrpou
from app.tests.common import (
    GRAPHQL_URL,
    SUPER_ADMIN_EDRPOU,
    TEST_USER_EMAIL,
    TEST_USER_PASSWORD,
    TEST_USER_PHONE,
    cleanup_on_teardown,
    graphql_response,
    insert_test_user,
    prepare_auth_headers,
    prepare_client,
    set_company_config,
)

TEST_COMPANY_NAME = 'Test Company'

GQL_COMPANIES = ujson.dumps({'query': '{ saAllCompanies { edrpou name } }'})
GQL_USERS_ID = ujson.dumps({'query': '{ saAllUsers { id } }'})


@pytest.mark.parametrize(
    'is_admin, sa_perms, expected_count',
    [
        (False, {}, 0),
        (True, {}, 0),
        (False, {'can_view_client_data': True}, 1),
        (False, {'can_edit_client_data': True}, 1),
        (False, {'can_edit_special_features': True}, 0),
    ],
)
async def test_companies(aiohttp_client, is_admin, sa_perms, expected_count):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions=sa_perms,
    )
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)
    response = await client.post(
        GRAPHQL_URL, data=GQL_COMPANIES, headers=prepare_auth_headers(user)
    )
    data = await graphql_response(response)
    assert len(data['saAllCompanies']) == expected_count


@pytest.mark.parametrize(
    'is_admin, sa_perms, has_access',
    [
        (False, {}, False),
        (True, {}, False),
        (False, {'can_view_client_data': True}, True),
        (False, {'can_edit_client_data': True}, True),
        (False, {'can_edit_special_features': True}, False),
    ],
)
async def test_company(aiohttp_client, is_admin, sa_perms, has_access):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions=sa_perms,
    )
    company_id = user.company_id
    await set_company_config(app, company_id=company_id, master=True, admin_is_superadmin=True)

    query = f'{{ saCompany(id: "{company_id}") {{ edrpou }} }}'
    response = await client.post(
        GRAPHQL_URL,
        data=ujson.dumps({'query': query}),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200, await response.text()
    result = await response.json()
    if has_access:
        assert result['data']['saCompany']['edrpou'] == user.company_edrpou
    else:
        assert result['data']['saCompany'] is None


@pytest.mark.parametrize(
    'option, expected_count',
    [
        (f'(search: "{SUPER_ADMIN_EDRPOU[:5]}")', 1),
        (f'(search: "{TEST_COMPANY_NAME[:5]}")', 1),
        ('(search: "invalid_search")', 0),
        ('(limit: 1)', 1),
        ('(offset: 0)', 1),
        ('(offset: 1)', 0),
    ],
)
async def test_companies_options(aiohttp_client, option, expected_count):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)

    async with app['db'].acquire() as conn:
        await update_company_by_edrpou(
            conn=conn,
            edrpou=SUPER_ADMIN_EDRPOU,
            data={'name': TEST_COMPANY_NAME},
        )

    payload = ujson.dumps({'query': f'{{ saAllCompanies {option} {{ edrpou name }} }}'})

    response = await client.post(GRAPHQL_URL, data=payload, headers=prepare_auth_headers(user))
    data = await graphql_response(response)
    assert len(data['saAllCompanies']) == expected_count
    if expected_count:
        assert data['saAllCompanies'][0]['edrpou'] == SUPER_ADMIN_EDRPOU


@pytest.mark.parametrize(
    'is_admin, sa_perms, expected_count',
    [
        (False, {}, 0),
        (True, {}, 0),
        (False, {'can_view_client_data': True}, 1),
        (False, {'can_edit_client_data': True}, 1),
        (False, {'can_edit_special_features': True}, 0),
    ],
)
async def test_users(aiohttp_client, is_admin, sa_perms, expected_count):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions=sa_perms,
    )
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)

    response = await client.post(GRAPHQL_URL, data=GQL_USERS_ID, headers=prepare_auth_headers(user))
    data = await graphql_response(response)
    assert len(data['saAllUsers']) == expected_count


@pytest.mark.parametrize(
    'option, expected',
    [
        ('(search: "<TEST_USER_ID>")', [{'email': TEST_USER_EMAIL}]),
        (f'(search: "{TEST_USER_EMAIL}")', [{'email': TEST_USER_EMAIL}]),
        (f'(search: "{TEST_USER_EMAIL[:10]}")', [{'email': TEST_USER_EMAIL}]),
        ('(search: "invalid_email")', []),
        (f'(search: "{SUPER_ADMIN_EDRPOU}")', [{'email': TEST_USER_EMAIL}]),
        (f'(search: "{TEST_USER_PHONE}")', [{'email': TEST_USER_EMAIL}]),
        (f'(gte: "{YESTERDAY}")', [{'email': TEST_USER_EMAIL}]),
        (f'(lte: "{YESTERDAY}")', []),
        (f'(gte: "{TOMORROW}")', []),
        (f'(lte: "{TOMORROW}")', [{'email': TEST_USER_EMAIL}]),
        ('(limit: 1)', [{'email': TEST_USER_EMAIL}]),
        ('(limit: 0)', []),
        ('(offset: 1)', []),
        ('(offset: 0)', [{'email': TEST_USER_EMAIL}]),
    ],
)
async def test_users_options(aiohttp_client, option, expected):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)

    if '<TEST_USER_ID>' in option:
        option = option.replace('<TEST_USER_ID>', user.id)

    payload = ujson.dumps({'query': f'{{ saAllUsers{option} {{ email }} }}'})

    try:
        response = await client.post(GRAPHQL_URL, data=payload, headers=prepare_auth_headers(user))
        data = await graphql_response(response)
        assert data['saAllUsers'] == expected
    finally:
        await cleanup_on_teardown(app)


async def test_users_options_without_role(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)

    try:
        async with app['db'].acquire() as conn:
            user_without_role = await insert_test_user(
                conn,
                {
                    'email': '<EMAIL>',
                    'password': TEST_USER_PASSWORD,
                },
            )
            user_email = user_without_role.email

        payload = ujson.dumps({'query': f'{{ saAllUsers(search: "{user_email}") {{ email }} }}'})

        response = await client.post(GRAPHQL_URL, data=payload, headers=prepare_auth_headers(user))
        data = await graphql_response(response)
        assert data['saAllUsers'][0]['email'] == user_email
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'is_admin, sa_perms, has_access',
    [
        (False, {}, False),
        (True, {}, False),
        (False, {'can_view_client_data': True}, True),
        (False, {'can_edit_client_data': True}, True),
        (False, {'can_edit_special_features': True}, False),
    ],
)
async def test_user(aiohttp_client, is_admin, sa_perms, has_access):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions=sa_perms,
    )
    user_id = user.id
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)

    query = f'{{ saUser(id: "{user_id}") {{ id }} }}'
    response = await client.post(
        GRAPHQL_URL,
        json={'query': query},
        headers=prepare_auth_headers(user),
    )
    assert response.status == 200, await response.text()
    result = await response.json()
    if has_access:
        assert result['data']['saUser']['id'] == user_id
    else:
        assert result['data']['saUser'] is None
