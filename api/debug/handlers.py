from aiohttp import web
from aiohttp.web_request import FileField

from api.debug import utils
from api.debug.validators import (
    GenerateSuperAdminTokenSchema,
    RenderDebugEmailSchema,
    SendDebugEmailSchema,
    StartWorkerJobSchema,
    validate_generate_super_admin_token,
    validate_render_debug_email,
    validate_send_debug_email,
    validate_start_worker_job,
)
from api.enums import Vendor
from api.graph.utils import process_query_graph
from api.public.types import CreateTokenOptions
from api.public.utils import create_token
from app.auth.db import select_role, upsert_super_admin_permissions
from app.auth.enums import RoleActivationSource
from app.auth.utils import get_expected_base_user
from app.lib.gotenberg import GotenbergClient
from app.lib.validators import validate_post_request
from app.openapi.decorators import openapi_docs
from app.profile.validators import validate_company_by_edrpou_exists
from app.registration.types import AddActiveRoleCtx
from app.registration.utils import add_active_role
from app.services import services

SUPER_ADMIN_EDRPOU = '41231992'


@openapi_docs(
    summary='Згенерувати токен для супер адміна',
    request_json=GenerateSuperAdminTokenSchema,
)
async def generate_super_admin_token(request: web.Request) -> web.Response:
    """
    Generate token for super admin:
     - adds user to vchasno company
     - sets super admin permissions
     - generates token

    Example:
    curl 'http://localhost:8000/api/debug/generate-super-admin-token' \
        --json '{"email": "<EMAIL>"}'
    """
    data = await validate_generate_super_admin_token(request)
    email = data.email

    async with services.db.acquire() as conn:
        company = await validate_company_by_edrpou_exists(conn, edrpou=SUPER_ADMIN_EDRPOU)
        user = await get_expected_base_user(conn, email=email)

        # Add user to a Vchasno company if not added yet
        role = await select_role(conn, user_id=user.id, company_id=company.id)
        if not role or not role.is_active:
            ctx = AddActiveRoleCtx(
                user=user,
                edrpou=SUPER_ADMIN_EDRPOU,
                activation_source=RoleActivationSource.super_admin,
                activated_by=None,
                signature_info=None,
            )
            output = await add_active_role(conn, ctx=ctx)
            role_id = output.role.id
        else:
            role_id = role.id

        await upsert_super_admin_permissions(
            conn=conn,
            role_id=role_id,
            super_admin_permissions={
                'can_view_client_data': True,
                'can_edit_client_data': True,
                'can_edit_special_features': True,
            },
            request_user=None,
        )

        options = CreateTokenOptions(
            edrpou=SUPER_ADMIN_EDRPOU,
            email=email,
            is_legal=True,
            vendor=Vendor.super_admin,
            role_id=role_id,
            existed_token=None,
        )
        token, token_raw = await create_token(conn, options=options)

    return web.json_response({'token': token_raw})


@openapi_docs(
    summary='Обробник, яким можна згенерувати схему GraphQL для фронтенду',
)
async def graphql_schema(request: web.Request) -> web.Response:
    """
    Handler for generating GraphQL schema for frontend
    """
    return await process_query_graph(request, user=None)  # type: ignore


@openapi_docs(
    summary='Додати задачу в Kafka',
    request_json=StartWorkerJobSchema,
)
async def start_worker_job(request: web.Request) -> web.Response:
    data = await validate_start_worker_job(request)
    await request.app['kafka'].send_record(topic=data.topic, value=data.value)
    return web.json_response({})


@openapi_docs(
    summary='Відправити тестовий лист на вказану адресу',
    description='Це API можна використовувати, що протестувати лист на локальному середовищі',
    request_json=SendDebugEmailSchema,
)
async def send_debug_email(request: web.Request) -> web.Response:
    """
    Render and send email template to given email. Use for quick testing email
    on local environment.
    """
    ctx = await validate_send_debug_email(request)
    await utils.send_debug_email(ctx=ctx)
    return web.json_response({})


@openapi_docs(
    summary='Сформувати HTML з шаблону листа',
    description='Це API можна використовувати, що протестувати лист на локальному середовищі',
    request_json=RenderDebugEmailSchema,
)
async def render_debug_email(request: web.Request) -> web.Response:
    """
    Render email template to HTML and return as response.
    For debug environment only
    """
    ctx = await validate_render_debug_email(request)
    html = await utils.render_debug_email(ctx=ctx)
    return web.Response(body=html, content_type='text/html')


@openapi_docs(
    summary='Конвертувати DOCX файл в PDF',
    description=(
        'Це API можна використовувати, що протестувати конвертацію файлів на локальному середовищі'
    ),
)
async def convert_docx_to_pdf(request: web.Request) -> web.Response:
    """
    Convert a DOCX file to PDF using Gotenberg service
    """
    raw_data = await validate_post_request(request)
    file: FileField = raw_data['file']  # type: ignore
    extension: str = raw_data['extension']  # type: ignore
    content_office = file.file.read()

    content_pdf = await GotenbergClient.convert_office_file_to_pdf(
        content=content_office,
        extension=extension,
    )

    return web.Response(body=content_pdf, content_type='application/pdf')
