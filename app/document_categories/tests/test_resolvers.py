from app.document_categories import db, types
from app.lib import types as core_types
from app.services import services
from app.tests import common

GQL_DOCUMENT_CATEGORIES = """
    {
        allDocumentCategories {
            count
            documentCategories {
                id
                title
                companyId
                dateCreated
                dateUpdated
                dateDeleted
            }
        }
    }
"""

GQL_DOCUMENT_CATEGORIES_WITH_TITLE_FILTER = """
    {{
        allDocumentCategories (title: "{title}") {{
            count
            documentCategories {{
                id
                title
                companyId
                dateCreated
                dateUpdated
                dateDeleted
            }}
        }}
    }}
"""

GQL_DOCUMENT_CATEGORIES_WITH_ONLY_INTERNAL_FILTER = """
    {{
        allDocumentCategories (onlyInternal: {only_internal}) {{
            count
            documentCategories {{
                id
                title
                companyId
                dateCreated
                dateUpdated
                dateDeleted
            }}
        }}
    }}
"""

GQL_DOCUMENT_CATEGORIES_WITH_ONLY_PUBLIC_FILTER = """
    {{
        allDocumentCategories (onlyPublic: {only_public}) {{
            count
            documentCategories {{
                id
                title
                companyId
                dateCreated
                dateUpdated
                dateDeleted
            }}
        }}
    }}
"""

GQL_DOCUMENT_CATEGORIES_WITH_IDS_FILTER = """
    {{
        allDocumentCategories (ids: [{ids}]) {{
            count
            documentCategories {{
                id
                title
                companyId
                dateCreated
                dateUpdated
                dateDeleted
            }}
        }}
    }}
"""


async def _prepare_public_document_categories(count: int) -> list[types.DocumentCategory]:
    document_categories = []
    async with services.db.acquire() as conn:
        for i in range(0, count):
            _category = await db.insert_document_category(
                conn=conn,
                title=f'Public document category {i}',
            )
            document_categories.append(_category)
    return document_categories


async def _prepare_internal_document_categories(
    count: int, company_id: str
) -> list[types.DocumentCategory]:
    document_categories = []
    async with services.db.acquire() as conn:
        for i in range(0, count):
            _category = await db.insert_document_category(
                conn=conn,
                title=f'Internal document category {i}',
                company_id=company_id,
            )
            document_categories.append(_category)
    return document_categories


async def _assert_document_categories_response(
    document_categories: list[core_types.DataDict],
    company_id: str,
    expected_public_categories_count: int,
    expected_internal_categories_count: int,
):
    """
    Assert graphql response for document categories
    """

    total_count = expected_public_categories_count + expected_internal_categories_count

    # Assert total count
    assert len(document_categories) == total_count

    # Assert public document categories count
    public_categories = [
        category for category in document_categories if category['companyId'] is None
    ]
    assert len(public_categories) == expected_public_categories_count

    # Assert internal document categories count
    internal_categories = [
        category for category in document_categories if category['companyId'] == company_id
    ]
    assert len(internal_categories) == expected_internal_categories_count


async def test_resolve_document_categories_for_company(aiohttp_client):
    """
    Given a user with company
    When trying to request graphql API for resolving document categories
    Expected to return correct document categories
    """

    # Prepare
    _, client, user = await common.prepare_client(aiohttp_client)

    # Prepare public document categories
    public_document_categories_count = 5
    await _prepare_public_document_categories(count=public_document_categories_count)

    # Prepare internal document categories
    internal_document_categories_count = 3
    await _prepare_internal_document_categories(
        count=internal_document_categories_count, company_id=user.company_id
    )

    # Act
    response = await common.fetch_graphql(
        client=client,
        query=GQL_DOCUMENT_CATEGORIES,
        headers=common.prepare_auth_headers(user=user),
    )
    document_categories = response['allDocumentCategories']['documentCategories']

    # Assert
    await _assert_document_categories_response(
        document_categories=document_categories,
        company_id=user.company_id,
        expected_public_categories_count=public_document_categories_count,
        expected_internal_categories_count=internal_document_categories_count,
    )


async def test_internal_document_categories_not_resolving_by_other_company(aiohttp_client):
    """
    Given 2 users from different companies
        each company has it's own internal document categories
    When trying to request graphql API for resolving document categories
    Expected to return correct document categories
    """

    # Prepare 2 users from different companies
    app, client, first_user = await common.prepare_client(aiohttp_client)
    second_user = await common.prepare_user_data(
        app=app, email='<EMAIL>', company_edrpou=common.TEST_COMPANY_EDRPOU_2
    )

    # Prepare public document categories
    public_document_categories_count = 5
    await _prepare_public_document_categories(count=public_document_categories_count)

    # Prepare internal document categories for first company
    first_company_internal_document_categories_count = 6
    await _prepare_internal_document_categories(
        count=first_company_internal_document_categories_count,
        company_id=first_user.company_id,
    )

    # Prepare internal document categories for second company
    second_company_internal_document_categories_count = 8
    await _prepare_internal_document_categories(
        count=second_company_internal_document_categories_count,
        company_id=second_user.company_id,
    )

    # Act as first company
    first_company_response = await common.fetch_graphql(
        client=client,
        query=GQL_DOCUMENT_CATEGORIES,
        headers=common.prepare_auth_headers(user=first_user),
    )

    # Act as second company
    second_company_response = await common.fetch_graphql(
        client=client,
        query=GQL_DOCUMENT_CATEGORIES,
        headers=common.prepare_auth_headers(user=second_user),
    )

    # Assert first company response
    await _assert_document_categories_response(
        document_categories=first_company_response['allDocumentCategories']['documentCategories'],
        company_id=first_user.company_id,
        expected_public_categories_count=public_document_categories_count,
        expected_internal_categories_count=first_company_internal_document_categories_count,
    )

    # Assert second company response
    await _assert_document_categories_response(
        document_categories=second_company_response['allDocumentCategories']['documentCategories'],
        company_id=second_user.company_id,
        expected_public_categories_count=public_document_categories_count,
        expected_internal_categories_count=second_company_internal_document_categories_count,
    )


async def test_resolve_document_categories_by_title(aiohttp_client):
    """
    Given a user with a company
    Given 3 document categories with different names
    When resolving document categories with title filter
    Expected to return valid response
    """

    # Prepare
    app, client, user = await common.prepare_client(aiohttp_client)

    # Prepare internal document categories
    internal_document_categories_count = 3
    await _prepare_internal_document_categories(
        count=internal_document_categories_count, company_id=user.company_id
    )

    # Act
    response = await common.fetch_graphql(
        client=client,
        query=GQL_DOCUMENT_CATEGORIES_WITH_TITLE_FILTER.format(
            title='Internal document category 1'
        ),
        headers=common.prepare_auth_headers(user=user),
    )
    document_categories = response['allDocumentCategories']['documentCategories']

    # Assert
    assert len(document_categories) == 1
    document_category = document_categories[0]

    assert document_category['title'] == 'Internal document category 1'
    assert document_category['companyId'] == user.company_id


async def test_resolve_only_internal_document_categories(aiohttp_client):
    """
    Given a user with a company
    Given 3 public and 3 internal document categories
    When resolving document categories with onlyInternal filter
    Expected to return valid response
    """

    # Prepare
    app, client, user = await common.prepare_client(aiohttp_client)

    # Prepare internal document categories
    internal_document_categories_count = 3
    await _prepare_internal_document_categories(
        count=internal_document_categories_count, company_id=user.company_id
    )
    # Prepare public document categories
    await _prepare_public_document_categories(count=3)

    # Act
    response = await common.fetch_graphql(
        client=client,
        query=GQL_DOCUMENT_CATEGORIES_WITH_ONLY_INTERNAL_FILTER.format(
            only_internal='true',
        ),
        headers=common.prepare_auth_headers(user=user),
    )
    document_categories = response['allDocumentCategories']['documentCategories']

    # Assert
    assert len(document_categories) == 3

    for document_category in document_categories:
        assert document_category['companyId'] is not None


async def test_resolve_only_public_document_categories(aiohttp_client):
    """
    Given a user with a company
    Given 3 public and 3 internal document categories
    When resolving document categories with onlyPublic filter
    Expected to return valid response
    """

    # Prepare
    app, client, user = await common.prepare_client(aiohttp_client)

    # Prepare internal document categories
    internal_document_categories_count = 3
    await _prepare_internal_document_categories(
        count=internal_document_categories_count, company_id=user.company_id
    )
    # Prepare public document categories
    await _prepare_public_document_categories(count=3)

    # Act
    response = await common.fetch_graphql(
        client=client,
        query=GQL_DOCUMENT_CATEGORIES_WITH_ONLY_PUBLIC_FILTER.format(
            only_public='true',
        ),
        headers=common.prepare_auth_headers(user=user),
    )
    document_categories = response['allDocumentCategories']['documentCategories']

    # Assert
    assert len(document_categories) == 3

    for document_category in document_categories:
        assert document_category['companyId'] is None


async def test_resolve_document_categories_with_ids_filter(aiohttp_client):
    """
    Given a user with a company
    Given 6 document categories
    When resolving document categories with ids filter
    Expected to return valid response
    """

    # Prepare
    app, client, user = await common.prepare_client(aiohttp_client)

    # Prepare document categories
    document_categories_count = 6
    document_categories = await _prepare_internal_document_categories(
        count=document_categories_count, company_id=user.company_id
    )

    # Act
    response = await common.fetch_graphql(
        client=client,
        query=GQL_DOCUMENT_CATEGORIES_WITH_IDS_FILTER.format(
            ids=','.join(str(category.id) for category in document_categories[:3])
        ),
        headers=common.prepare_auth_headers(user=user),
    )
    document_categories = response['allDocumentCategories']['documentCategories']

    # Assert
    assert len(document_categories) == 3
