import logging

from aiohttp import C<PERSON><PERSON>rror
from typing_extensions import deprecated

from api.errors import TemporaryUnavailableError
from app.crm.enums import LeadType
from app.i18n import _
from app.lib import types as core_types
from app.lib.database import DBRow
from app.services import services
from worker import topics
from worker.crm.enums import CRMEvents

logger = logging.getLogger(__name__)


async def send_user_to_crm(user_id: str) -> None:
    await services.kafka.send_record(
        topics.CRM_SEND_EVENT,
        value={
            'event': CRMEvents.upsert_user.value,
            'context': {'user_id': user_id},
        },
    )


async def send_rate_to_crm(account_id: str) -> None:
    """
    Send asynchronous job to worker to send rate to Creatio CRM
    """

    await services.kafka.send_record(
        topic=topics.CRM_SEND_EVENT,
        value={
            'event': CRMEvents.upsert_rate.value,
            'context': {'account_id': account_id},
        },
    )


async def send_bill_to_crm(bill_id: str) -> None:
    """
    Send asynchronous job to worker to send bill to Creatio CRM
    """

    await services.kafka.send_record(
        topic=topics.CRM_SEND_EVENT,
        value={
            'event': CRMEvents.upsert_bill.value,
            'context': {'bill_id': bill_id},
        },
    )


async def send_role_to_crm(role_id: str) -> None:
    await services.kafka.send_record(
        topics.CRM_SEND_EVENT,
        value={
            'event': CRMEvents.upsert_role.value,
            'context': {'role_id': role_id},
        },
    )


async def send_company_to_crm(company_id: str) -> None:
    await services.kafka.send_record(
        topics.CRM_SEND_EVENT,
        value={
            'event': CRMEvents.upsert_company.value,
            'context': {'company_id': company_id},
        },
    )


async def request_creatio_web_form_object_data(url: str, data: core_types.DataDict) -> None:
    try:
        async with services.http_client.post(
            url=url,
            headers={'referer': 'https://vchasno.ua/'},
            json=data,
        ) as response:
            response.raise_for_status()
    except ClientError:
        logger.error('Error while calling Creatio webform api', extra=data)


@deprecated('Use direct leads API from vchasno_crm')
async def create_lead_in_crm(user: DBRow) -> None:
    """
    Call creatio API and create a lead for contacting a manager
    """
    from app.auth.utils import build_user_full_name

    config = services.config.crm

    if not config or not config.landing_id or not config.creatio_base_url:
        raise TemporaryUnavailableError(reason=_('Функціонал недоступний.'))

    endpoint = '/0/ServiceModel/GeneratedObjectWebFormService.svc/SaveWebFormObjectData'
    user_full_name = build_user_full_name(user.first_name, user.second_name, user.last_name)
    data = {
        'formData': {
            'formId': config.landing_id,
            'formFieldsData': [
                {'name': 'Name', 'value': user_full_name},
                {'name': 'MobilePhone', 'value': user.phone},
                {'name': 'Email', 'value': user.email},
                {'name': 'Company', 'value': user.company_name},
                {'name': 'UsrEdrpou', 'value': user.company_edrpou},
            ],
            'contactFieldsData': [
                {'name': 'FullName', 'value': user_full_name},
                {'name': 'Phone', 'value': user.phone},
                {'name': 'Email', 'value': user.email},
            ],
        },
    }

    await request_creatio_web_form_object_data(
        url=f'{config.creatio_base_url}{endpoint}',
        data=data,
    )


async def send_transaction_to_crm(transaction_id: str) -> None:
    """
    Send asynchronous job to worker to send transaction to Creatio CRM
    """
    await services.kafka.send_record(
        topic=topics.CRM_SEND_EVENT,
        value={
            'event': CRMEvents.upsert_transaction.value,
            'context': {'transaction_id': transaction_id},
        },
    )


async def create_contact_manager_lead(user: DBRow, company_size: str | None = None) -> None:
    """
    Set proper context for specific lead_type(contact_manager)
    and send it to worker for sending to CRM proxy
    """
    context = {
        'user_id': user.id,
        'type': LeadType.contact_manager.value,
        'company_id': user.company_id,
    }
    if company_size:
        context['company_size'] = company_size

    await services.kafka.send_record(
        topics.CRM_SEND_EVENT,
        value={
            'event': CRMEvents.upsert_lead.value,
            'context': context,
        },
    )
