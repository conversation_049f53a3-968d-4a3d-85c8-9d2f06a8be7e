DEFAULT_TEST_ENV_VARS = {
    'FERNET_KEY': 'test_fernet_key',
    'DB_URL': 'test_db_url',
    'DB_READ_URL': 'test_db_read_url',
    'EVENT_DB_URL': 'test_event_db_url',
    'ES_USERNAME': 'test_es_username',
    'ES_PASSWORD': 'test_es_password',
    'REDIS_SENTINEL_USERNAME': 'test_redis_sentinel_username',
    'REDIS_SENTINEL_PASSWORD': 'test_redis_sentinel_password',
    'YOUCONTROL_PASSWORD': 'test_youcontrol_password',
    'YOUCONTROL_YOUSCORE_APIKEY': 'test_youcontrol_youscore_apikey',
    'SYNC_CONTACTS_API_PROMUA_SECRET_KEY': 'test_sync_contacts_api_promua_secret_key',
    'SYNC_CONTACTS_API_ZAKUPKI_SECRET_KEY': 'test_sync_contacts_api_zakupki_secret_key',
    'EDI_RPC_AUTH_TOKEN': 'test_edi_rpc_auth_token',
    'ESPUTNIK_PASSWORD': 'test_esputnik_password',
    'TTN_AUTH_TOKEN': 'test_ttn_auth_token',
    'KASSA_AUTH_TOKEN': 'supersecret',
    'DIIA_TOKEN': 'test_diia_token',
    'DIIA_OFFER_ID': 'test_diia_offer_id',
    'DIIA_BRANCH_ID': 'test_diia_branch_id',
    'DIIA_AUTH_ACQUIRER_TOKEN': 'test_diia_auth_acquirer_token',
    'SIGNER_WEB_SECRET': 'test_signer_web_secret',
    'SIGNER_WEB_KEY_ID': 'test_signer_web_key_id',
    'FIREBASE_PROJECT_ID': 'test_firebase_project_id',
    'FIREBASE_CONFIG_PATH': 'test_firebase_config_path',
    'EVOPAY_HOST': 'test_evopay_host',
    'EVOPAY_PUBLIC_KEY': 'test_evopay_public_key',
    'EVOPAY_PRIVATE_KEY': 'test_evopay_private_key',
    'EVOPAY_WEBHOOK_KEY': 'test_evopay_webhook_key',
    'EVOPAY_PURPOSE': 'test_evopay_purpose',
    'PRIVATBANK_INTEGRATION_ID': 'test_privatbank_integration_id',
    'PRIVATBANK_INTEGRATION_TOKEN': 'test_privatbank_integration_token',
    'KAFKA_BOOTSTRAP_SERVERS': 'test_kafka_bootstrap_servers',
    'KAFKA_SECURITY_PROTOCOL': 'PLAINTEXT',
    'KAFKA_SASL_USERNAME': 'test_kafka_sasl_username',
    'KAFKA_SASL_PASSWORD': 'test_kafka_sasl_password',
    'S3_NEW_ACCESS_KEY': 'test_s3_access_key',
    'S3_NEW_SECRET_KEY': 'test_s3_secret_key',
    'S3_NEW_ENCRYPTION_KEY': 'test_s3_encryption_key',
    'SMTP_USER': 'test_smtp_user',
    'SMTP_PASSWORD': 'test_smtp_password',
    'BEDROCK_KEY_ID': 'test_key',
    'BEDROCK_KEY_SECRET': 'test_secret',
    'CONCIERGE_TOKEN': 'test_concierge_token',
    'HRS_AUTH_TOKEN': 'fake-token',
    'FEATURE_FLAGS_URL': 'http://fake:8000',
}
