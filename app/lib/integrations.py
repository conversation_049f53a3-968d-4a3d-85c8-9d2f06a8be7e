import base64
import logging
import typing as t
from functools import wraps

from aiohttp import web
from argon2 import PasswordHasher
from argon2 import exceptions as argon_exc

from api.errors import Code, Error
from app.auth.utils import get_request_url
from app.config.schemas import IntegrationConfig, IntegrationName
from app.i18n import _
from app.lib.types import Handler, HandlerDecorator, HandlerResponse
from app.services import services

logger = logging.getLogger(__name__)


def validate_private_integration_token(
    request: web.Request,
    *,
    integration: IntegrationName,
) -> None:
    request_url = get_request_url(request)
    extra = {'url': request_url, 'remote_ip': request.remote}
    integrations = services.config.integrations
    if not integrations:
        logger.info('Integration API config is missing')
        raise Error(
            Code.invalid_action,
            status=400,
            reason=_('Private integrations are disabled by config'),
        )

    token = request.headers.get(integrations.header)
    if not token:
        logger.warning('Attempt to access integration API without token', extra)
        raise Error(
            code=Code.login_required,
            status=401,
            reason=_('Token is missing'),
        )

    hasher = PasswordHasher()
    services_dict = services.config.integrations.services_dict

    integration_config: IntegrationConfig | None = services_dict.get(integration)
    if not integration_config:
        raise Error(
            code=Code.invalid_credentials,
            status=401,
            reason=_('Invalid token'),
        )

    token_hash_b64 = integration_config.token_hash_b64
    try:
        token_hash = base64.b64decode(token_hash_b64.encode()).decode()
        hasher.verify(token_hash, token)
    except (argon_exc.VerificationError, argon_exc.VerifyMismatchError):
        logger.warning('Invalid integration API token', extra)
        raise Error(
            code=Code.invalid_credentials,
            status=401,
            reason=_('Invalid token'),
        )
    except argon_exc.InvalidHash:
        logger.warning('Invalid Argon2 hash specified in config')
        logger.warning('Invalid integration API token', extra)
        raise Error(
            code=Code.invalid_credentials,
            status=401,
            reason=_('Invalid token'),
        )


def private_integration(*, integration: IntegrationName) -> HandlerDecorator:
    """
    Decorator used for private integrations authentication.
    All stored tokens must be hashed with Argon2.

    NOTE: token for local/test environment is "701f62f6-c16d-4a5f-8190-1038533ed923"]
    or "fake-token" (without quotes).
    """

    def decorator(handler: Handler) -> Handler:
        @wraps(handler)
        async def wrapper(request: web.Request, *args: t.Any) -> HandlerResponse:
            validate_private_integration_token(request, integration=integration)
            return await handler(request, *args)

        return wrapper

    return decorator
