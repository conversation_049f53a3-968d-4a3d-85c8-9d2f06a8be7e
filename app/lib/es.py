from __future__ import annotations

import typing as t

import elasticsearch

# elasticmagic refers to BaseException, but it's changed in recent lib version
if not getattr(elasticsearch, 'ElasticsearchException', None):
    setattr(elasticsearch, 'ElasticsearchException', elasticsearch.ApiError)  # noqa

from elasticmagic.ext.asyncio import Async<PERSON>luster as BaseAsyncCluster
from elasticmagic.ext.asyncio import AsyncIndex as BaseAsyncIndex
from elasticmagic.result import Bulk<PERSON><PERSON>ult

from app.lib import tracking
from app.lib.types import DataDict


class AsyncIndex(BaseAsyncIndex):
    _cluster: AsyncCluster

    @property
    def name(self) -> str:
        return self.get_name()

    @property
    def cluster(self) -> AsyncCluster:
        return self._cluster


class AsyncCluster(BaseAsyncCluster):
    _index_cls = AsyncIndex
    _client: elasticsearch.AsyncElasticsearch

    @property
    def client(self) -> elasticsearch.AsyncElasticsearch:
        return self.get_client()

    async def _do_request(self, compiler: type, *args: t.Any, **kwargs: t.Any) -> BulkResult:
        try:
            return await super()._do_request(compiler, *args, **kwargs)
        finally:
            # For all elastic request types (get/search/count/etc.),
            # 'args[-1]' (last) must contain the dictionary of parameters sent to the
            # ElasticCluster, where 'index' key represents the name of the index.
            # Extract index name from the parameters. If the index name isn't provided,
            # set its value as 'undefined' as a fallback.
            index_params = args[-1] if len(args) > 1 else {}
            index_name = index_params.get('index', 'undefined')

            tracking.es_requests_counter.labels(index=index_name).inc()

    async def delete_index(
        self,
        index: str,
        *,
        ignore_unavailable: bool = True,
    ) -> None:
        """
        Delete index from Elasticsearch cluster.
        """
        await self.client.indices.delete(index=index, ignore_unavailable=ignore_unavailable)

    async def update_cluster_settings(self, settings: DataDict) -> None:
        """
        Update cluster settings.
        """
        await self.client.cluster.put_settings(body=settings)
