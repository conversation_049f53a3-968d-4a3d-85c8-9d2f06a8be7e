import asyncio
import logging

import aiohttp
from logevo import get_current_rxid

from api.errors import Error
from app.config import get_level
from app.config.schemas import GotenbergConfig
from app.i18n import _
from app.lib.enums import AppLevel
from app.services import services

logger = logging.getLogger(__name__)


def is_landscaped(ext: str) -> bool:
    return ext in ('.xlsx', '.xls')


class GotenbergConvertError(Error):
    status = 500
    reason = _('Не вдалося конвертувати файл в PDF')


class GotenbergClient:
    @staticmethod
    async def convert_office_file_to_pdf(
        *,
        content: bytes,
        extension: str,
        page_ranges: list[str] | None = None,
        landscape: bool = False,
    ) -> bytes:
        """
        Convert DOCX, XLSX, PPTX files to PDF using Gotenberg service.

        Args:
            content: file content
            extension: file extension, e.g. 'docx'
            page_ranges: list of page ranges to convert, e.g. ['1-1', '3-5']
            landscape: convert to landscape orientation

        https://gotenberg.dev/docs/routes#office-documents-into-pdfs-route
        """
        config: GotenbergConfig | None = services.config.gotenberg

        assert config, 'Gotenberg config is not set'

        logger.info(
            msg='Converting office file to PDF using Gotenberg',
            extra={'content_length': len(content)},
        )

        data = aiohttp.FormData()
        data.add_field('files', content, filename=f'file.{extension}')
        # parameter for export form fields as fixed print representation, not as PDF form
        data.add_field('exportFormFields', 'false')

        if page_ranges:
            data.add_field('nativePageRanges', ','.join(page_ranges))
        if landscape:
            data.add_field('landscape', 'true')

        request_id = get_current_rxid()

        try:
            async with asyncio.timeout(config.timeout):
                async with services.http_client.post(
                    url=f'{config.url}/forms/libreoffice/convert',
                    data=data,
                    headers={'Gotenberg-Trace': request_id},
                ) as response:
                    logger.info('Gotenberg response', extra={'status': response.status})
                    if response.status != 200:
                        logger.info(
                            msg='Gotenberg response error',
                            extra={
                                'status': response.status,
                                'reason': await response.text(),
                            },
                        )
                        raise GotenbergConvertError()

                    return await response.read()
        except aiohttp.ClientError as e:
            details = {}
            if get_level() == AppLevel.local:
                details['hint'] = 'Use `docker compose up gotenberg` command to start the service'

            logger.exception('Gotenberg request error', extra={'exception': e})
            raise GotenbergConvertError(details=details) from e
        except GotenbergConvertError:
            raise
        except Exception as e:
            logger.exception('Gotenberg error', extra={'exception': e})
            raise GotenbergConvertError() from e
