import pytest

from app.documents.db import delete_listings, insert_listings
from app.documents.enums import FirstSignBy
from app.documents.tables import company_listing_table, listing_table
from app.documents.tests.utils import prepare_document_recipient
from app.flow.db import (
    insert_flows,
    select_flow_by,
    select_flows_by,
    select_next_ordered_flow,
)
from app.flow.types import FlowMeta
from app.lib.enums import DocumentStatus
from app.models import select_all
from app.tests.common import (
    cleanup_on_teardown,
    prepare_client,
    prepare_document_data,
    prepare_flow_item,
)


async def test_insert_flows(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app,
        user,
        expected_owner_signatures=0,
        status_id=DocumentStatus.uploaded.value,
    )
    recipient = await prepare_document_recipient(
        document_id=document.id,
        emails=['<EMAIL>'],
        edrpou='10101010',
    )
    async with app['db'].acquire() as conn:
        result = await insert_flows(conn, [])
        assert result == []

        result = await insert_flows(
            conn,
            [
                {
                    'order': 1,
                    'company_id': None,
                    'document_id': document.id,
                    'edrpou': '10101010',
                    'signatures_count': 1,
                    'pending_signatures_count': 1,
                    'meta': FlowMeta().to_db(),
                    'receivers_id': recipient.id,
                }
            ],
        )
        assert result
        assert len(result) == 1
        assert document.id == result[0].document_id


async def test_insert_listings(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(
        app, user, expected_owner_signatures=0, status_id=DocumentStatus.uploaded.value
    )
    async with app['db'].acquire() as conn:
        result = await insert_listings(conn, [])
        assert result is None
        result = await insert_listings(
            conn,
            [
                {
                    'document_id': document.id,
                    'access_edrpou': user.company_edrpou,
                    'role_id': user.role_id,
                },
                {
                    'document_id': document.id,
                    'access_edrpou': user.company_edrpou,
                    'role_id': None,
                },
                {
                    'document_id': document.id,
                    'access_edrpou': 'edrpou',
                    'role_id': None,
                },
            ],
        )
        assert result is None
        doc_accesses = await select_all(
            conn,
            (listing_table.select().where(listing_table.c.document_id == document.id)),
        )
        assert len(doc_accesses) == 3
        company_first_access_to_doc = await select_all(
            conn,
            (
                company_listing_table.select().where(
                    company_listing_table.c.document_id == document.id
                )
            ),
        )
        assert len(company_first_access_to_doc) == 2


async def test_delete_listings(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)
    recipient_edrpou = '10101010'
    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=True,
        document_recipients=[
            {
                'edrpou': recipient_edrpou,
                'emails': None,
            }
        ],
        first_sign_by=FirstSignBy.owner,
        status_id=DocumentStatus.signed_and_sent.value,
    )

    async def _get_listings(conn_):
        return await select_all(conn_, listing_table.select())

    async def _get_company_listings(conn_):
        return await select_all(conn_, company_listing_table.select())

    async with app['db'].acquire() as conn:
        await insert_listings(
            conn,
            {
                'document_id': document.id,
                'access_edrpou': owner.company_edrpou,
                'role_id': None,
            },
        )
        doc_accesses = await _get_listings(conn)
        assert len(doc_accesses) == 3
        company_first_accesses = await _get_company_listings(conn)
        assert len(company_first_accesses) == 2

        await delete_listings(conn, filters=[listing_table.c.access_edrpou == owner.company_edrpou])
        doc_accesses = await _get_listings(conn)
        assert len(doc_accesses) == 1
        assert doc_accesses[0].access_edrpou == recipient_edrpou
        company_first_accesses = await _get_company_listings(conn)
        assert len(company_first_accesses) == 1
        assert company_first_accesses[0].edrpou == recipient_edrpou


async def test_select_flows_by(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    try:
        document_1 = await prepare_document_data(
            app,
            user,
            expected_owner_signatures=0,
            status_id=DocumentStatus.uploaded.value,
        )
        document_2 = await prepare_document_data(
            app,
            user,
            expected_owner_signatures=0,
            status_id=DocumentStatus.uploaded.value,
        )
        flow_1 = await prepare_flow_item(
            app,
            document_id=document_1.id,
            signatures_count=1,
            pending_signatures_count=1,
            order=None,
            company_id=user.company_id,
            edrpou=user.company_edrpou,
        )
        flow_2 = await prepare_flow_item(
            app,
            document_id=document_2.id,
            signatures_count=1,
            pending_signatures_count=1,
            order=None,
            edrpou='10101010',
        )
        async with app['db'].acquire() as conn:
            with pytest.raises(AssertionError):
                await select_flows_by(
                    conn=conn,
                    flow_id=None,
                    document_id=None,
                    edrpou=None,
                )
            result = await select_flows_by(
                conn,
                flow_id=flow_1.id_,
                document_id=document_1.id,
                edrpou=user.company_edrpou,
            )
            assert result
            assert len(result) == 1
            assert result[0] == flow_1
            result = await select_flows_by(conn, flow_id=flow_1.id_, document_id=None, edrpou=None)
            assert result
            assert len(result) == 1
            assert result[0] == flow_1
            result = await select_flows_by(
                conn, flow_id=None, document_id=document_1.id, edrpou=None
            )
            assert result
            assert len(result) == 1
            assert result[0] == flow_1
            result = await select_flows_by(
                conn, flow_id=None, document_id=None, edrpou=user.company_edrpou
            )
            assert result
            assert len(result) == 1
            assert result[0] == flow_1
            result = await select_flows_by(conn, flow_id=None, document_id=None, edrpou='10101010')
            assert result
            assert len(result) == 1
            assert result[0] == flow_2
    finally:
        await cleanup_on_teardown(app)


async def test_select_flow_by(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    try:
        document_1 = await prepare_document_data(
            app,
            user,
            expected_owner_signatures=0,
            status_id=DocumentStatus.uploaded.value,
        )
        document_2 = await prepare_document_data(
            app,
            user,
            expected_owner_signatures=0,
            status_id=DocumentStatus.uploaded.value,
        )
        flow_1 = await prepare_flow_item(
            app,
            document_id=document_1.id,
            signatures_count=1,
            pending_signatures_count=1,
            order=None,
            company_id=user.company_id,
            edrpou=user.company_edrpou,
        )
        flow_2 = await prepare_flow_item(
            app,
            document_id=document_2.id,
            signatures_count=1,
            pending_signatures_count=1,
            order=None,
            edrpou='10101010',
        )
        async with app['db'].acquire() as conn:
            with pytest.raises(AssertionError):
                await select_flow_by(
                    conn=conn,
                    flow_id=None,
                    document_id=None,
                    edrpou=None,
                )
            result = await select_flow_by(
                conn,
                flow_id=flow_1.id_,
                document_id=document_1.id,
                edrpou=user.company_edrpou,
            )
            assert result
            assert result == flow_1
            result = await select_flow_by(conn, flow_id=flow_1.id_, document_id=None, edrpou=None)
            assert result
            assert result == flow_1
            result = await select_flow_by(
                conn, flow_id=None, document_id=document_1.id, edrpou=None
            )
            assert result
            assert result == flow_1
            result = await select_flow_by(
                conn, flow_id=None, document_id=None, edrpou=user.company_edrpou
            )
            assert result
            assert result == flow_1
            result = await select_flow_by(conn, flow_id=None, document_id=None, edrpou='10101010')
            assert result
            assert result == flow_2
    finally:
        await cleanup_on_teardown(app)


async def test_select_current_ordered_flow(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)
    async with app['db'].acquire() as conn:
        document_1 = await prepare_document_data(
            app,
            user,
            expected_owner_signatures=0,
            status_id=DocumentStatus.uploaded.value,
        )
        document_2 = await prepare_document_data(
            app,
            user,
            expected_owner_signatures=0,
            status_id=DocumentStatus.uploaded.value,
        )
        await prepare_flow_item(
            app,
            document_id=document_1.id,
            signatures_count=1,
            pending_signatures_count=0,
            order=0,
            company_id=user.company_id,
            edrpou=user.company_edrpou,
        )

        result = await select_next_ordered_flow(conn, document_1.id)
        assert result is None

        flow_2 = await prepare_flow_item(
            app,
            document_id=document_1.id,
            signatures_count=1,
            pending_signatures_count=1,
            order=1,
            edrpou='10101010',
        )
        result = await select_next_ordered_flow(conn, document_1.id)
        assert result == flow_2

        await prepare_flow_item(
            app,
            document_id=document_2.id,
            signatures_count=1,
            pending_signatures_count=1,
            order=None,
            edrpou='10101010',
        )
        result = await select_next_ordered_flow(conn, document_1.id)
        assert result == flow_2
        result = await select_next_ordered_flow(conn, document_2.id)
        assert result is None
