import pydantic
from aiohttp import web

from api import errors
from app.lib import validators
from app.lib.types import DataDict
from app.mobile.notifications import db, types
from app.services import services

DEFAULT_LIMIT_FOR_LIST_MOBILE_NOTIFICATIONS = 20


class SetFirebaseIdValidator(pydantic.BaseModel):
    firebase_id: str


class UpdateMobileNotificationStatusValidator(pydantic.BaseModel):
    status: types.MobileNotificationStatus


class CountMobileNotificationsValidator(pydantic.BaseModel):
    status: types.MobileNotificationStatus


class ListMobileNotificationsValidator(pydantic.BaseModel):
    limit: int
    status: types.MobileNotificationStatus | None = None
    offset: int | None = None


def validate_set_firebase_id(raw_data: DataDict) -> SetFirebaseIdValidator:
    return validators.validate_pydantic(SetFirebaseIdValidator, raw_data)


def validate_list_mobile_notifications(
    request: web.Request,
) -> ListMobileNotificationsValidator:
    status = request.rel_url.query.get('status')
    limit = request.rel_url.query.get('limit', DEFAULT_LIMIT_FOR_LIST_MOBILE_NOTIFICATIONS)
    offset = request.rel_url.query.get('offset')

    return validators.validate_pydantic(
        ListMobileNotificationsValidator,
        {'status': status, 'limit': limit, 'offset': offset},
    )


async def validate_update_mobile_notification_status(
    notification_id: str,
    raw_data: DataDict,
) -> UpdateMobileNotificationStatusValidator:
    async with services.db_readonly.acquire() as conn:
        notification = await db.select_notification_by_id(conn, notification_id)

    if not notification:
        raise errors.DoesNotExist(obj=errors.Object.mobile_notification)

    return validators.validate_pydantic(UpdateMobileNotificationStatusValidator, raw_data)


async def validate_count_mobile_notifications(
    raw_data: DataDict,
) -> CountMobileNotificationsValidator:
    return validators.validate_pydantic(CountMobileNotificationsValidator, raw_data)
