import logging

from app.lib import types as core_types
from app.lib.datetime_utils import naive_local_now
from app.mobile.fcm import validators

logger = logging.getLogger(__name__)

ENABLE_SOUND_HEADER = {
    'headers': {'apns-priority': '10'},
    'payload': {'aps': {'sound': 'default'}},
}


def build_request_body(
    *,
    notification_id: str,
    title: str,
    description: str,
    firebase_id: str,
    payload: core_types.DataDict | None = None,
) -> core_types.DataDict | None:
    """
    Build body for FCM send message request
    """

    # Avoid passing custom parameters to this section.
    # Instead, use message_payload below.
    body: core_types.DataDict = {
        'validate_only': False,  # We don't validate request before triggering a push notification
        'message': {
            'token': firebase_id,
            'notification': {
                'title': title,
                'body': description,
            },
            'apns': ENABLE_SOUND_HEADER,  # Enable sound for notification
        },
    }

    message_payload = payload.copy() if payload else {}
    message_payload['notification_id'] = notification_id
    message_payload['timestamp'] = str(int(naive_local_now().timestamp()))

    try:
        validators.validate_payload_keys(payload=message_payload)
        body['message']['data'] = message_payload
    except validators.RestrictedPayloadKeyError:
        logger.info(
            msg=(
                """
                Unable to send push notification due to
                restricted keys persistence in payload.
                """
            ),
            extra={'payload': message_payload},
        )
        return None

    return body
