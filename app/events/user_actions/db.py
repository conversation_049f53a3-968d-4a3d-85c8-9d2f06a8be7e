import datetime
from collections.abc import AsyncIterator, Sequence

import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert

from app.events import enums as events_enums
from app.events import models as deprecated_event_models
from app.events import utils as events_utils
from app.events.user_actions import tables, types
from app.lib import types as core_types
from app.lib.database import DBConnection
from app.models import select_all, select_one, stream_chunks
from app.services import services


async def select_user_actions_for(
    email: str | None = None,
    company_id: str | None = None,
) -> list[types.UserActionDB]:
    """
    Select user actions by filters

    Used primarily for testing purposes
    """

    assert any([email, company_id]), 'Expected at least one filter'

    where_clauses = []
    if email is not None:
        where_clauses.append(tables.user_actions_table.c.email == email)

    if company_id is not None:
        where_clauses.append(tables.user_actions_table.c.company_id == company_id)

    query = (
        sa.select([tables.user_actions_table])
        .where(sa.and_(*where_clauses))
        .order_by(
            tables.user_actions_table.c.date_created.desc(),
            tables.user_actions_table.c.id,
        )
    )

    async with services.events_db.acquire() as conn:
        action_records = await select_all(conn=conn, query=query)

    return [types.UserActionDB.from_row(action_record) for action_record in action_records]


async def stream_user_actions_for_report(
    conn: DBConnection,
    *,
    company_id: str,
    date_to: datetime.datetime,
    last_date_from: datetime.datetime | None,
    last_record_id: str | None,
    limit: int,
    chunk_size: int,
) -> AsyncIterator[types.UserActionDB]:
    """Select user actions by filters"""

    assert company_id, 'Expected at least one filter'

    where_clauses = [
        tables.user_actions_table.c.company_id == company_id,
        tables.user_actions_table.c.date_created <= date_to,
    ]

    if last_record_id is None:
        # The first query should be done with equality to avoid skipping records for that day
        where_clauses.append(tables.user_actions_table.c.date_created >= last_date_from)
    else:
        # The next queries can be done with greater than operator to simplicity.
        # INFO: Postgres will check if there are any records with the same "date_created" as
        # provided and only then it will try to find the next record with higher "id" than
        # provided. Sometimes it is referred in the internet as lexicographical ordering.
        where_clauses.append(
            sa.tuple_(
                tables.user_actions_table.c.date_created,
                tables.user_actions_table.c.id,
            )
            > sa.tuple_(last_date_from, last_record_id)
        )

    query = (
        sa.select([tables.user_actions_table])
        .where(sa.and_(*where_clauses))
        .order_by(
            tables.user_actions_table.c.date_created,
            tables.user_actions_table.c.id,
        )
        .limit(limit)
    )

    async for rows in stream_chunks(conn=conn, query=query, size=chunk_size):
        for row in rows:
            yield types.UserActionDB.from_row(row)


async def insert_user_actions(
    user_actions: Sequence[types.UserAction],
) -> Sequence[types.UserActionDB]:
    """
    Insert multiple user actions
    """
    if not user_actions:
        return []

    async with services.events_db.acquire() as conn:
        await events_utils.ensure_actions_partition_exists(
            conn=conn, table_name=tables.user_actions_table.name
        )

        data = []
        for user_action in user_actions:
            _user_action = {
                'action': user_action.action,
                'source': user_action.source,
                'email': user_action.email,
                'company_id': user_action.company_id,
                'extra': user_action.extra,
            }

            if user_action.date_created is not None:
                _user_action['date_created'] = user_action.date_created
            if user_action.id is not None:
                _user_action['id'] = user_action.id

            data.append(_user_action)

        records = await select_all(
            conn=conn,
            query=(
                insert(tables.user_actions_table).values(data).returning(tables.user_actions_table)
            ),
        )

    return [types.UserActionDB.from_row(record) for record in records]


async def insert_user_action(
    action: types.Action,
    source: types.Source,
    email: str,
    company_id: str | None = None,
    extra: core_types.DataDict | None = None,
    date_created: datetime.datetime | None = None,
) -> types.UserActionDB:
    """
    Insert single user action
    """

    async with services.events_db.acquire() as conn:
        await events_utils.ensure_actions_partition_exists(
            conn=conn,
            table_name=tables.user_actions_table.name,
        )

        data = {
            'action': action,
            'source': source,
            'email': email,
            'company_id': company_id,
            'extra': extra,
        }

        if date_created is not None:
            data['date_created'] = date_created

        record = await select_one(
            conn=conn,
            query=(
                insert(tables.user_actions_table).values(data).returning(tables.user_actions_table)
            ),
        )

    return types.UserActionDB.from_row(record)


async def persist_events(events: list[deprecated_event_models.Event]) -> None:
    """
    Persist given events in database

    Note 04/24: this function maps old events format to new one
    Remove mapping in ~ 1 month
    """

    def _map_old_event_action_to_new(action: events_enums.EventType) -> types.Action:
        return {
            events_enums.EventType.LOGIN_SUCCESSFUL: types.Action.login_successful,
            events_enums.EventType.LOGIN_UNSUCCESSFUL: types.Action.login_fail,
            events_enums.EventType.LOGIN_TO_COMPANY: types.Action.login_to_company,
            events_enums.EventType.LOGOUT_FROM_COMPANY: types.Action.logout_from_company,
            events_enums.EventType.LOGOUT_SUCCESSFUL: types.Action.logout_successful,
            events_enums.EventType.UPDATE_ROLE_SUCCESSFUL: types.Action.role_update,
            events_enums.EventType.ROLE_DELETE: types.Action.role_delete,
            events_enums.EventType.ROLE_CREATE: types.Action.role_create,
            events_enums.EventType.GENERATE_TOKEN: types.Action.token_create,
            events_enums.EventType.DELETE_TOKEN: types.Action.token_delete,
            events_enums.EventType.UPDATE_ANTIVIRUS: types.Action.antivirus_update,
            events_enums.EventType.TEMPLATE_CREATE: types.Action.automation_create,
            events_enums.EventType.TEMPLATE_UPDATE: types.Action.automation_update,
            events_enums.EventType.TEMPLATE_DELETE: types.Action.automation_delete,
            events_enums.EventType.TEMPLATE_TOGGLE: types.Action.automation_toggle,
            events_enums.EventType.FIELD_CREATE: types.Action.document_field_create,
            events_enums.EventType.FIELD_UPDATE: types.Action.document_field_update,
            events_enums.EventType.FIELD_DELETE: types.Action.document_field_delete,
            events_enums.EventType.FIELD_ACCESS_CREATE: types.Action.document_field_access_create,
            events_enums.EventType.FIELD_ACCESS_DELETE: types.Action.document_field_access_delete,
            events_enums.EventType.REQUIRED_FIELD_CREATE: (
                types.Action.document_field_required_create
            ),
            events_enums.EventType.REQUIRED_FIELD_UPDATE: (
                types.Action.document_field_required_update
            ),
            events_enums.EventType.REQUIRED_FIELD_DELETE: (
                types.Action.document_field_required_delete
            ),
            events_enums.EventType.COMPANY_UPDATE: types.Action.company_update,
            events_enums.EventType.ROLE_TAG_CREATE: types.Action.role_tag_create,
            events_enums.EventType.ROLE_TAG_DELETE: types.Action.role_tag_delete,
            events_enums.EventType.GROUP_CREATE: types.Action.group_create,
            events_enums.EventType.GROUP_DELETE: types.Action.group_delete,
            events_enums.EventType.GROUP_RENAME: types.Action.group_rename,
            events_enums.EventType.GROUP_MEMBER_CREATE: types.Action.group_member_add,
            events_enums.EventType.GROUP_MEMBER_DELETE: types.Action.group_member_remove,
        }[action]

    def _map_old_source_to_new(source: events_enums.EventSource) -> types.Source:
        return {
            events_enums.EventSource.api_blackbox: types.Source.blackbox,
            events_enums.EventSource.api_internal: types.Source.internal,
            events_enums.EventSource.api_mobile: types.Source.mobile,
            events_enums.EventSource.api_public: types.Source.public,
        }[source]

    def _map_old_event_to_new_one(
        event: deprecated_event_models.Event,
    ) -> types.UserAction:
        return types.UserAction(
            action=_map_old_event_action_to_new(action=event.event_type),
            source=_map_old_source_to_new(source=event.request_source),
            email=event.actor_email,
            date_created=event.date_created,
            company_id=event.actor_company_id,
            extra=event.extra,
        )

    records = []
    for event in events:
        records.append(_map_old_event_to_new_one(event=event))
    await insert_user_actions(records)
