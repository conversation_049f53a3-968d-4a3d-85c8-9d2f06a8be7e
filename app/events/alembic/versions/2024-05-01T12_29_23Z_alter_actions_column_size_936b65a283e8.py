"""alter_actions_column_size

Revision ID: 936b65a283e8
Revises: d8b4d90cab2e
Create Date: 2024-05-01 12:29:23.464466+00:00

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '936b65a283e8'
down_revision = 'd8b4d90cab2e'
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        ALTER TABLE user_actions ALTER COLUMN action TYPE VARCHAR (256);
        ALTER TABLE user_actions ALTER COLUMN email TYPE VARCHAR (256);
        """
    )

def downgrade():
   op.execute(
        """
        ALTER TABLE user_actions ALTER COLUMN action TYPE VARCHAR (54);
        ALTER TABLE user_actions ALTER COLUMN email TYPE VARCHAR (54);
        """
   )
