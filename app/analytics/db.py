import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert

from app.analytics import tables, types
from app.lib.database import DBConnection
from app.lib.db import select_one


async def add_analytics_event(
    conn: DBConnection, event: types.AnalyticsEvent
) -> types.AnalyticsEvent | None:
    row = await select_one(
        conn=conn,
        query=(
            insert(tables.analytics_events_table)
            .values(event.to_db())
            .returning(tables.analytics_events_table)
            .on_conflict_do_nothing()
        ),
    )
    if not row:
        # Event already in database
        return None

    return types.AnalyticsEvent.from_db(row=row)


async def select_analytics_event_for_bill_id(
    conn: DBConnection, bill_id: str
) -> types.AnalyticsEvent | None:
    row = await select_one(
        conn=conn,
        query=(
            sa.select([tables.analytics_events_table]).where(
                sa.and_(
                    tables.analytics_events_table.c.bill_id == bill_id,
                    tables.analytics_events_table.c.type == types.AnalyticsEventType.bill_generated,
                )
            )
        ),
    )
    if not row:
        return None

    return types.AnalyticsEvent.from_db(row=row)
