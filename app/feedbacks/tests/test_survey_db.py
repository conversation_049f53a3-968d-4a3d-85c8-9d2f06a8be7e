from app.feedbacks.db import select_ai_survey, upsert_ai_survey
from app.services import services
from app.tests.common import prepare_client

SURVEY_DATA_1 = {
    'data': [
        {
            'name': 'Видаткові накладні',
            'customer': [],
            'executor': [],
            'other': ['Номер і дата договору'],
            'product': [],
            'requisites': ['Номер', 'Дата'],
            'is_custom_type': True,
        },
    ],
    'is_completed': False,
    'is_closed': False,
}

SURVEY_DATA_2 = {
    'data': [
        {
            'name': 'Видаткові накладні',
            'customer': [],
            'executor': [],
            'other': ['Номер і дата договору'],
            'product': [],
            'requisites': ['Номер', 'Дата'],
            'is_custom_type': True,
        },
        {
            'name': 'Акт<PERSON>',
            'customer': ['Найменування компанії', 'Банківські реквізити'],
            'executor': ['Найменування компанії', 'Код ЄДРПОУ'],
            'other': [],
            'product': ['Сума без ПДВ', 'Кількість'],
            'requisites': ['Дата'],
            'is_custom_type': False,
        },
    ],
    'is_completed': True,
    'is_closed': False,
}

SURVEY_DATA_3 = {
    'data': [
        {
            'name': 'Видаткові накладні',
            'customer': [],
            'executor': [],
            'other': ['Номер і дата договору'],
            'product': [],
            'requisites': ['Номер', 'Дата'],
            'is_custom_type': True,
        },
        {
            'name': 'Акти',
            'customer': ['Найменування компанії', 'Банківські реквізити'],
            'executor': ['Найменування компанії', 'Код ЄДРПОУ'],
            'other': [],
            'product': ['Сума без ПДВ', 'Кількість'],
            'requisites': ['Дата'],
            'is_custom_type': False,
        },
        {
            'name': 'Рахунки',
            'customer': ['Найменування компанії'],
            'executor': ['Найменування компанії'],
            'other': ['Термін оплати'],
            'product': ['Сума з ПДВ'],
            'requisites': ['Номер', 'Дата'],
            'is_custom_type': False,
        },
    ],
    'is_closed': True,
    'is_completed': True,
    'contact_phone': '+380501234567',
}


async def test_upsert_survey_data_multiple(aiohttp_client):
    """
    Test that multiple upsert operations for the same user correctly update the survey data.
    """
    app, client, user = await prepare_client(aiohttp_client)

    # First upsert - initial data
    async with services.db.acquire() as conn:
        data1 = SURVEY_DATA_1.copy()
        data1['user_id'] = user.id
        await upsert_ai_survey(conn=conn, data=data1)

        # Verify first upsert
        survey = await select_ai_survey(conn=conn, user_id=user.id)
        assert survey is not None
        assert len(survey.data) == 1
        assert survey.is_completed is False
        assert survey.is_closed is False
        assert survey.contact_phone is None

        # Second upsert - add more data and change the completion status
        data2 = SURVEY_DATA_2.copy()
        data2['user_id'] = user.id
        await upsert_ai_survey(conn=conn, data=data2)

        # Verify second upsert
        survey = await select_ai_survey(conn=conn, user_id=user.id)
        assert survey is not None
        assert len(survey.data) == 2
        assert survey.is_completed is True
        assert survey.is_closed is False
        assert survey.contact_phone is None

        # Third upsert - add more data and set additional fields
        data3 = SURVEY_DATA_3.copy()
        data3['user_id'] = user.id
        await upsert_ai_survey(conn=conn, data=data3)

        # Verify third upsert
        survey = await select_ai_survey(conn=conn, user_id=user.id)
        assert survey is not None
        assert len(survey.data) == 3
        assert survey.is_completed is True
        assert survey.is_closed is True
        assert survey.contact_phone == '+380501234567'
        assert sorted([item.name for item in survey.data]) == sorted(
            ['Видаткові накладні', 'Акти', 'Рахунки']
        )

        # Partial upsert - preserves existing data and updates only specified fields
        partial_data = {
            'is_completed': False,
            'user_id': user.id,
        }
        await upsert_ai_survey(conn=conn, data=partial_data)
        # Verify partial upsert
        survey = await select_ai_survey(conn=conn, user_id=user.id)
        assert survey is not None
        assert len(survey.data) == 3
        assert survey.is_completed is False
        assert survey.is_closed is True
        assert survey.contact_phone == '+380501234567'
        assert sorted([item.name for item in survey.data]) == sorted(
            ['Видаткові накладні', 'Акти', 'Рахунки']
        )
