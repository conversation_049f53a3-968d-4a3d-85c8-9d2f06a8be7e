import sqlalchemy as sa

from app.models import columns, metadata

feedback_table = sa.Table(
    'feedbacks',
    metadata,
    columns.UUID(),
    columns.ForeignKey('role_id', 'roles.id', ondelete='NO ACTION', nullable=False),
    columns.Text('feedback', nullable=True),
    columns.DateCreated(),
    columns.DateUpdated(),
)

# Temp table for collecting data for further analysis
survey_ai_table = sa.Table(
    'survey_ai_documents',
    metadata,
    columns.ForeignKey(
        'user_id',
        'users.id',
        ondelete='CASCADE',
        nullable=False,
        primary_key=True,
    ),
    # If user started a survey with one role, but finished with another one,
    # the latest role will be attached
    columns.ForeignKey(
        'role_id',
        'roles.id',
        ondelete='CASCADE',
        nullable=True,
    ),
    columns.JSONB('data', nullable=True),
    columns.Boolean('is_closed'),
    columns.Boolean('is_completed'),
    columns.Phone(name='contact_phone'),
    columns.DateCreated(),
    columns.DateUpdated(),
)
