from app.billing.db import select_billing_company_config, update_billing_company_config
from app.billing.types import BillingCompanyConfig
from app.billing.utils import get_billing_company_config
from app.services import services
from app.tests.common import prepare_client


async def test_update_billing_company_config(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    async with services.db.acquire() as conn:
        # Billing config not exists in database
        raw_config = await select_billing_company_config(conn, company_id=user.company_id)
        assert raw_config is None

        # But even if it does not exist, when we use "get_billing_company_config" it should
        # return default config
        config = await get_billing_company_config(conn, company_id=user.company_id)
        assert config is not None
        assert config.model_dump() == BillingCompanyConfig().model_dump()

        # Now let's create config via "update" funciton

        await update_billing_company_config(
            conn=conn,
            company_id=user.company_id,
            config={
                'max_archive_documents_count': 10,
                'max_additional_fields_count': 20,
            },
        )

        # And check that it was created
        raw_config = await select_billing_company_config(conn, company_id=user.company_id)
        assert raw_config is not None

        config = await get_billing_company_config(conn, company_id=user.company_id)
        assert config is not None
        expected_config = BillingCompanyConfig(
            max_archive_documents_count=10,
            max_additional_fields_count=20,
        )
        assert config.model_dump() == expected_config.model_dump()

        # Check that we can update the config, without changing other values
        await update_billing_company_config(
            conn=conn,
            company_id=user.company_id,
            config={
                'max_archive_documents_count': 100,
                'max_tags_count': 200,
            },
        )

        config = await get_billing_company_config(conn, company_id=user.company_id)
        assert config is not None
        expected_config = BillingCompanyConfig(
            max_archive_documents_count=100,
            max_additional_fields_count=20,
            max_tags_count=200,
        )
        assert config.model_dump() == expected_config.model_dump()


async def test_update_billing_company_config_remove_keys(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, enable_pro_functionality=False)

    async with services.db.acquire() as conn:
        await update_billing_company_config(
            conn=conn,
            company_id=user.company_id,
            config={
                'max_additional_fields_count': 20,
                'max_tags_count': 30,
                'max_versions_count': 40,
                'max_archive_documents_count': 50,
            },
        )

        raw_config = await select_billing_company_config(conn, company_id=user.company_id)
        assert raw_config is not None
        assert raw_config.max_additional_fields_count == 20
        assert raw_config.max_tags_count == 30
        assert raw_config.max_versions_count == 40
        assert raw_config.max_archive_documents_count == 50

        await update_billing_company_config(
            conn=conn,
            company_id=user.company_id,
            config={
                'max_additional_fields_count': 200,
            },
            remove_keys=['max_tags_count', 'max_versions_count'],
        )

        raw_config = await select_billing_company_config(conn, company_id=user.company_id)
        assert raw_config is not None
        assert raw_config.max_additional_fields_count == 200
        assert raw_config.max_tags_count is None
        assert raw_config.max_versions_count is None
        assert raw_config.max_archive_documents_count == 50
