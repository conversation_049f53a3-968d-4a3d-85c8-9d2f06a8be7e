from __future__ import annotations

import datetime
from decimal import Decimal
from typing import Annotated, Literal

import pydantic
from pydantic import BaseModel, Field

from api.errors import InvalidRequest
from app.billing.enums import (
    AccountRate,
    BillPaymentStatus,
    BillServicesType,
    BillServiceType,
    BillStatus,
    CreatioBillType,
    RateExtensionStatus,
    RateExtensionType,
)
from app.billing.types import BillService
from app.i18n import _
from app.lib import validators_pydantic as pv
from app.lib.datetime_utils import to_local_datetime
from app.lib.types import DataDict

UnitsCount = Annotated[int, Field(ge=0)]


class CreateBillServiceRateSchema(pydantic.BaseModel):
    type: Literal[BillServiceType.rate] = BillServiceType.rate
    rate: AccountRate
    # TODO: investigate if it's needed at all, because frontend doesn't send it,
    #  but it was presented in the original schema
    date_from: pv.Datetime | None = None


class CreateBillServiceExtensionSchema(pydantic.BaseModel):
    type: Literal[BillServiceType.extension] = BillServiceType.extension
    units: int
    extension: RateExtensionType


class CreateBillServiceUnitsSchema(pydantic.BaseModel):
    type: Literal[BillServiceType.units] = BillServiceType.units
    units: int


# Use 'type' field as a discriminator for tagged union
type CreateBillService = (
    CreateBillServiceRateSchema | CreateBillServiceExtensionSchema | CreateBillServiceUnitsSchema
)


class CreateBillSchema(BaseModel):
    """
    Base validator for creating a bill
    """

    # Common fields for all bill types
    email: pv.Email
    edrpou: pv.EDRPOU
    name: str = Field(min_length=1)
    agreement: str | None = None
    is_card_payment: bool = False

    # List of services for the bill: rate, extension, units
    services: list[CreateBillService]

    # Fields for analytics. Used to create GA event upon bill payment.
    client_id: str | None = None
    session_id: str | None = None
    session_number: str | None = None


class CreateBillResponseSchema(pydantic.BaseModel):
    bill_id: str
    number: str
    date: str
    buyer_name: str
    supplier_name: str
    service: str | None
    supplier_edrpou: str
    supplier_ipn: str
    supplier_bank: str
    supplier_iban: str
    amount: float

    # Not a part of bill schema, but needed include to response, because it's used in the frontend
    extension_status: RateExtensionStatus | None

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json')


class CreateBillCreatioSchema(pydantic.BaseModel):
    name: str
    edrpou: pv.EDRPOU
    email: pv.Email
    custom_price: Decimal | None = None  # в гривнях
    count_documents: int | None = pydantic.Field(default=None, gt=0)
    contract_basis: str | None = None
    payment_purpose: str | None = None
    rate: CreatioBillType | None = None
    extra_rate: CreatioBillType | None = None  # for web + archive rates
    # Used only in ultimate rate and add_employee extension
    employees_count: int | None = pydantic.Field(default=None, gt=0, alias='Count')
    # Used only in "add_employee" extension and "ultimate_*" rate,
    # to update "price_per_user" field in a billing account
    price_per_user: Decimal | None = pydantic.Field(default=None, alias='PriceAddEmployee')
    # used only in add_employee extension for delayed activation after paid
    planned_activation_date: datetime.date | None = pydantic.Field(default=None, alias='StartTarif')

    @pydantic.field_validator('extra_rate', mode='before')
    @staticmethod
    def validate_extra_rate(extra_rate: str | None) -> str | None:
        # Convert empty string '' to None
        return extra_rate or None

    @pydantic.model_validator(mode='after')
    def validate_bill_rate(self) -> CreateBillCreatioSchema:
        crm_rate = self.rate

        if crm_rate is None and self.extra_rate is not None:
            raise InvalidRequest(
                reason=_(
                    'При створенні рахунку з додатковим тарифом потрібно вказати основний тариф',
                ),
            )

        if self.extra_rate is not None and self.extra_rate not in (
            CreatioBillType.archive_small,
            CreatioBillType.archive_big,
        ):
            raise InvalidRequest(
                reason=_('Додатковий тариф може бути тільки на архівні тарифи'),
            )

        if crm_rate is None:
            return self

        if crm_rate == CreatioBillType.ultimate and not self.custom_price:
            raise InvalidRequest(
                reason=_(
                    'При створенні Максимального рахунку потрібно вказати ціну',
                ),
            )

        if crm_rate == CreatioBillType.ultimate and not self.employees_count:
            raise InvalidRequest(
                reason=_(
                    'При створенні Максимального рахунку потрібно вказати ліміт користувачів',
                ),
            )

        if crm_rate == CreatioBillType.add_employee and (
            not self.price_per_user or not self.employees_count
        ):
            raise InvalidRequest(
                reason=_(
                    'При створенні рахунку для поповнення користувачів '
                    'потрібно вказати ціну за користувача і ліміт користувачів',
                ),
            )

        return self


class RetrieveBillSchema(pydantic.BaseModel):
    id: str
    edrpou: str
    status_id: BillStatus | None
    payment_status: BillPaymentStatus | None
    payment_status_description: str | None
    services_type: str
    services: list[BillService]
    document_id: str | None

    # Deprecated fields, use "services" instead
    rate: str | None
    count_documents: int | None

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json')


class ActivateBillSuperAdmin(pydantic.BaseModel):
    services_type: BillServicesType | None = None
    start_date: pv.Datetime | None = None
    end_date: pv.Datetime | None = None

    @pydantic.model_validator(mode='after')
    def validate_with_rate_type(self) -> ActivateBillSuperAdmin:
        if self.services_type not in [BillServicesType.add_employee, BillServicesType.documents]:
            if not (self.start_date and self.end_date):
                raise InvalidRequest(
                    reason=_('Для активації тарифу потрібно вказати start_date i end_date')
                )
            if self.start_date > self.end_date:
                raise InvalidRequest(
                    reason=_('Дата початку тарифу повинна бути меншою за дату завершення')
                )

            # Convert to local timezone
            self.start_date = to_local_datetime(self.start_date)
            self.end_date = to_local_datetime(self.end_date)

        return self


class DeleteRateValidator(pydantic.BaseModel):
    rate_id: str
