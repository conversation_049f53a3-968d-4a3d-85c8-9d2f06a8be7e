import asyncio
import collections.abc as col_abc
import datetime
import io
import logging
import os
import random
import unittest
import uuid
from collections.abc import Iterator
from contextlib import (
    asynccontextmanager,
    contextmanager,
    suppress,
)
from http import HTTPStatus
from typing import (
    Any,
    NamedTuple,
    cast,
)
from unittest import mock

import pytest
import sqlalchemy as sa
import ujson
from _pytest.python_api import RaisesContext
from aiohttp import (
    ClientResponse,
    FormData,
    web,
)
from aiohttp.pytest_plugin import AiohttpClient
from aiohttp.streams import StreamReader
from aiohttp.test_utils import TestClient, _create_transport, sentinel
from aiohttp.test_utils import make_mocked_request as base_make_mocked_request
from elasticmagic import Bool
from multidict import MultiDict
from sqlalchemy.dialects.postgresql import insert
from stream_unzip import async_stream_unzip

from api.enums import Vendor
from api.private.super_admin.tables import super_admin_actions_table
from api.private.super_admin.types import SuperAdminAction
from api.uploads.utils import handle_encoding_bytes
from app.app import create_app
from app.archive.tables import document_archive_table
from app.auth.db import (
    SELECT_ROLE_QUERY,
    decrement_company_upload_documents_left,
    insert_company,
    insert_role,
    insert_token,
    select_company_by_edrpou,
    select_company_by_id,
    select_company_by_role_id,
    select_company_id,
    select_user_by_token_hash,
    update_user,
    upsert_super_admin_permissions,
)
from app.auth.enums import RoleStatus
from app.auth.helpers import generate_hash_sha512, generate_password_hash
from app.auth.schemas import CompanyConfig
from app.auth.tables import (
    company_config_table,
    company_statistic_table,
    company_table,
    user_table,
)
from app.auth.types import BaseUser, User
from app.auth.utils import (
    get_company_config,
    set_default_role_settings,
)
from app.billing.constants import ALL_PERMITTED
from app.billing.db import _insert_account as billing_insert_account
from app.billing.db import insert_bill, update_billing_company_config
from app.billing.db import select_accounts as billing_select_account
from app.billing.db import select_company_accounts as billing_select_company_accounts
from app.billing.enums import (
    AccountRate,
    CompanyPermission,
    CompanyRateStatus,
)
from app.billing.rates import (
    UNLIMITED,
)
from app.billing.tables import bank_transactions_table, billing_account_table
from app.billing.types import (
    Account,
    AccountType,
    AddBillOptions,
    Bill,
    CompanyRate,
    UpdateBillingCompanyConfigDict,
)
from app.billing.utils import get_date_expired
from app.comments.db import insert_comment
from app.comments.enums import CommentType
from app.comments.types import Comment
from app.config.schemas import Config
from app.contacts.db import (
    insert_contact_person_phone,
)
from app.contacts.sync.db import insert_contact, insert_contact_person
from app.directories.tables import directory_documents_table
from app.document_automation.enums import DocumentAutomationStatus
from app.document_automation.tables import (
    document_automation_condition_table,
    document_automation_template_table,
)
from app.document_categories.db import insert_document_category
from app.documents.db import (
    create_document_access_for_recipient,
    insert_delete_request,
    insert_listings,
    insert_recipients,
    select_document,
    select_document_by_id,
    select_document_meta,
    select_listings,
)
from app.documents.enums import (
    AccessSource,
    DeleteRequestStatus,
    DocumentSource,
    FirstSignBy,
)
from app.documents.tables import document_table
from app.documents.types import (
    Document,
    DocumentMeta,
    DocumentWithUploader,
    ListingDataAggregator,
)
from app.documents.utils import (
    _get_document_original_s3_key,
    delete_es_documents,
    get_external_key_signature_s3_key,
    get_external_stamp_signature_s3_key,
    get_internal_signature_s3_key,
)
from app.documents_fields.enums import DocumentFieldType
from app.documents_fields.tables import documents_fields_table
from app.documents_required_fields.enums import DocumentCategoryFields
from app.documents_required_fields.types import DocumentRequiredFieldOptions
from app.documents_required_fields.utils import add_document_required_field
from app.es.enums import ESQuerySource
from app.es.indexation import sync_documents
from app.es.models.document import Document as DocumentES
from app.es.utils import fetch_es
from app.events.tables import metadata as events_metadata
from app.flow.db import select_flow_by
from app.flow.tables import doc_flow_table
from app.flow.types import FlowItem, FlowMeta
from app.flow.utils import _get_flows_listing_data
from app.groups.db import insert_group_members_bulk
from app.groups.tables import group_document_access_table
from app.groups.utils import add_group
from app.lib import s3_utils
from app.lib.chunks import iter_bytes_by_chunks, read_from_bytes_chunks
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import (
    UTC_TZ,
    local_now,
    naive_local_now,
    parse_local_datetime,
    to_local_datetime,
    utc_now,
)
from app.lib.enums import (
    CommentStatus,
    DocumentStatus,
    SignatureFormat,
    SignatureType,
    SignersSource,
    Source,
    UserRole,
)
from app.lib.helpers import (
    ensure_mimetype,
    generate_uuid,
    get_list_or_none,
    split_comma_separated_emails,
    to_json,
)
from app.lib.types import DataDict
from app.mobile.auth import validators as mobile_validators
from app.mobile.auth.db import insert_mobile_auth_refresh_token
from app.mobile.auth.types import MobileAuthRefreshTokenStatus
from app.mobile.auth.utils import save_access_token
from app.mobile.notifications.utils import process_set_firebase_id
from app.models import (
    metadata,
    select_all,
    select_one,
)
from app.notifications.db import insert_notifications
from app.notifications.enums import (
    NotificationName,
    NotificationSource,
)
from app.reviews.db import insert_document_reviews
from app.reviews.enums import ReviewType
from app.reviews.types import ReviewsInfoCtx
from app.reviews.utils import (
    get_review_state,
    start_reviews_update_transaction,
)
from app.services import services
from app.sign_sessions.db import insert_sign_session
from app.sign_sessions.enums import (
    SignSessionSource,
    SignSessionVendor,
)
from app.signatures.db import insert_signature
from app.signatures.enums import SignatureAlgo
from app.signatures.tables import document_signer_table
from app.signatures.types import DocumentSigner, PrivateKey, Signature
from app.tags.db import select_tags_by_names
from app.trigger_notifications.tables import trigger_notification_table
from app.uploads.types import (
    File,
)
from app.vchasno_profile.tables import vchasno_user_table

AGROYARD_EDRPOU = '41184251'
DELIVERY_EDRPOU = '31738765'
FOP_EDRPOU = '********90'
TOV_EDRPOU = '55555555'
INTERTELECOM_EDRPOU = '30109015'
MASTER_ADMIN_EDRPOU = METINVEST_EDRPOU = '32036829'
NOVA_POSHTA_EDRPOU = '31316718'
KYIVSTAR_EDRPOU = '21673832'
OLX_EDRPOU = '34716896'
PERMITTED_EMAIL = '<EMAIL>'
SOVTES_EDRPOU = '37534432'
SUPER_ADMIN_EDRPOU = VCHASNO_EDRPOU = '41231992'
UAPROM_EDRPOU = '36507036'
UKRPOSHTA_EDRPOU = '21560045'
UKRPOSHTA_SUBSIDIARY_EDRPOU = '01189979'
WOLF_EDRPOU = '41271087'
ZAKUPKI_EDRPOU = '40283641'

API_GRAPHQL_URL = '/api/graphql'
API_V1_DOCUMENTS_URL = '/api/v1/documents'
API_V1_INCOMING_DOCUMENTS_URL = '/api/v1/incoming-documents'
API_V1_RECIPIENTS_URL = '/api/v1/recipients'
API_V1_ROLES_URL = '/api/v1/roles'
API_V1_SIGN_SESSIONS_URL = '/api/v1/sign-sessions'
API_V1_TOKENS_URL = '/api/v1/tokens'

API_V2_ADD_TRIAL_RATE_URL = '/api/v2/billing/companies/rates/trials'
API_V2_DOCUMENTS_URL = '/api/v2/documents'
API_V2_COMPANY_DELETE_REQUESTS_URL = '/api/v2/documents/delete-requests'
API_V2_DOWNLOAD_DOCUMENTS_URL = '/api/v2/download-documents'
API_V2_DOCUMENT_DELETE_REQUEST_URL = '/api/v2/documents/{document_id}/delete-requests'
API_V2_INCOMING_DOCUMENTS_URL = '/api/v2/incoming-documents'
API_V2_LOGGING_URL = '/api/v2/logging'
API_V2_ROLES_URL = '/api/v2/roles'
API_V2_SIGN_SESSIONS_URL = '/api/v2/sign-sessions'
API_V2_INVITE_COWORKERS = '/api/v2/invite/coworkers'
API_V2_COWORKER = '/api/v2/coworker'
API_V2_SEND_DOCUMENT_URL = '/api/v2/documents/{document_id}/send'
API_V2_ADD_FLOW_API_URL = '/api/v2/documents/{document_id}/flow'
API_V2_REVIEWS_STATUS_URL = '/api/v2/documents/{document_id}/reviews/status'
API_V2_REVIEWS_REQUESTS_URL = '/api/v2/documents/{document_id}/reviews/requests'
API_V2_REVIEWS_URL = '/api/v2/documents/{document_id}/reviews'
API_V2_DOCUMENT_URL = '/api/v2/documents/{document_id}'
API_V2_TOKENS = '/api/v2/tokens'
API_V2_RECREATE_TOKEN = '/api/v2/recreate-token'
API_V2_LIST_COMMENTS = '/api/v2/documents/comments'

PRIVATE_API_GET_DOCUMENT_STATE = '/api/private/documents/{document_id}/state'

BILLS_URL = '/internal-api/bills'
BILL_ACTIVATE_URL = '/internal-api/bills/{bill_id}/activate'
CRM_BILLS_URL = '/api/private/integrations/creatio/bill'
EVOPAY_PAGE_GENERATION_URL = '/internal-api/evopay/generate-page'
EVOPAY_WEBHOOK_URL = '/api/evopay/webhook'
GRAPHQL_URL = '/internal-api/graphql'
LOGIN_URL = '/auth-api/login'
LOGOUT_URL = '/auth-api/logout'
LOGOUT_ALL_SESSIONS_URL = '/auth-api/logout-all-sessions'
PROFILE_URL = '/internal-api/profile'
TOKENS_URL = '/internal-api/tokens'
LOGIN_PAGE_URL = '/auth/login'
AUTH_VERIFY_2FA_URL = '/auth/2fa/verify'
SAVE_REGISTRATION_INFO_URL = '/auth-api/registration/info'
UNSUBSCRIBE_URL = '/internal-api/notifications/unsubscribe/{jwt_token}'
UNSUBSCRIBE_UNREGISTERED_URL = '/internal-api/notifications/unregistered/unsubscriptions'
UPLOAD_DOCUMENT_URL = '/internal-api/documents'

DOWNLOAD_BILLS_URL = '/downloads/bills.xls'
DOWNLOAD_COMPANIES_URL = '/downloads/companies.xls'

ROLES_DENY_URL = '/internal-api/roles/{role_id}/deny'
ROLE_URL = '/internal-api/roles/{role_id}'

EDI_ROLE_URL = '/internal-api/roles/edi/{role_id}'

CREATE_TAG_MULTIPLE_URL = '/internal-api/documents/tags'
CONNECT_MULTIPLE_TAGS_URL = '/internal-api/documents/tags/connections'
DISCONNECT_MULTIPLE_TAGS_URL = '/internal-api/documents/tags/connections'
CREATE_TAG_FOR_ROLES_URL = '/internal-api/roles/tags'
CONNECT_TAG_ROLES_URL = '/internal-api/roles/tags/connections'
DISCONNECT_TAG_ROLES_URL = '/internal-api/roles/tags/connections'
CREATE_TAG_FOR_CONTACTS_URL = '/internal-api/contacts/tags'
CONNECT_TAG_AND_CONTACTS = '/internal-api/contacts/tags/connections'
DISCONNECT_TAG_AND_CONTACTS = '/internal-api/contacts/tags/connections'

SEND_DOCUMENT_URL = '/internal-api/documents/{document_id}/send'
ADD_COMMENT_URL = '/internal-api/documents/{document_id}/comments'
REVIEW_URL = '/internal-api/reviews'
REVIEWS_URL = '/internal-api/reviews-batch'
MOBILE_REVIEWS_URL = '/mobile-api/v1/reviews-batch'
DELETE_REVIEW_URL_TMPL = '/internal-api/{document_id}/reviews'
UPDATE_DOCUMENT_URL = '/internal-api/documents/{document_id}'
DELETE_DOCUMENT_URL = '/internal-api/documents/{document_id}'
DELETE_DOCUMENT_PUBLIC_URL = '/api/v2/documents/{document_id}'
FIND_DOCUMENT_RECIPIENT_URL = '/internal-api/documents/recipients/emails'
DOCUMENT_REJECT_URL = '/internal-api/documents/{document_id}/reject'

LOCK_DOCUMENTS_DELETION_URL = '/api/v2/documents/delete-requests/lock-delete'

UPDATE_TRIGGER_NOTIFICATION_URL = '/internal-api/notifications/triggers'
UPDATE_COMPANY_CONFIG_URL = '/internal-api/companies/{company_id}/configs'
UPDATE_ADDITIONAL_COMPANY_CONFIG_URL = '/internal-api/companies/additional/configs'

UPDATE_2FA_STATE_URL = '/internal-api/profile/2fa'

AUTH_HIDDEN_PHONE_2FA_URL = '/internal-api/2fa/hidden-phone'

PRIVATE_GET_COMPANIES_URL = '/api/private/integrations/companies'
PRIVATE_GET_COMPANY_BILLING_ACCOUNTS_URL = '/api/private/integrations/kep/company-billing-accounts'
PRIVATE_CHECK_CREDENTIALS_KEP_URL = '/api/private/integrations/kep/check-credentials'
PRIVATE_GET_ACTIVE_ROLES_KEP_URL = '/api/private/integrations/kep/user-companies'
PRIVATE_GET_DOCUMENT_SIGNATURES_KEP_URL = (
    '/api/private/integrations/kep/documents/{document_id}/signatures'
)
PRIVATE_CHECK_CREDENTIALS_KASA_URL = '/api/private/integrations/kasa/check-credentials'
PRIVATE_CHECK_CREDENTIALS_TTN_URL = '/api/private/integrations/ttn/check-credentials'
PRIVATE_INVITE_USER_TTN_URL = '/api/private/integrations/ttn/invite-user'

EMPTY_JSON = ujson.dumps({})

TEST_UUID = 'e38258d3-2c41-4e27-92b2-ca26e58ed67e'
TEST_UUID_2 = 'bde92bc6-69e4-4155-8bcc-69b3eed7e31e'
TEST_COMPANY_ID = 'd8c8f90e-78ce-41dd-bc6e-e1eb48a62f08'
TEST_COMPANY_EDRPOU = '********'
TEST_COMPANY_EDRPOU_2 = '********'
TEST_COMPANY_EDRPOU_3 = '********'
TEST_BANK_TRANSACTION_ID_1 = '111'
TEST_BANK_TRANSACTION_ID_2 = '222'
TEST_BANK_TRANSACTION_ID_3 = '333'
TEST_BANK_TRANSACTION_ID_4 = '444'
TEST_COMPANY_NAME = 'Test Company'
TEST_USER_EMAIL = '<EMAIL>'
TEST_USER_FULL_NAME = 'Test User'
TEST_USER_PASSWORD = 'SecRet_PassW0rD'
TEST_USER_PHONE = '+************'
TEST_USER_TELEGRAM_CHAT_ID = ********

TEST_COMMENT = 'Test Comment'
TEST_COMMENT_REJECTED = 'Test Rejected Comment'

TEST_DOCUMENT_EDRPOU_OWNER = TEST_COMPANY_EDRPOU
TEST_DOCUMENT_EDRPOU_RECIPIENT = '********'
TEST_DOCUMENT_EMAIL_OWNER = TEST_USER_EMAIL
TEST_DOCUMENT_EMAIL_RECIPIENT = '<EMAIL>'
TEST_DOCUMENT_IPN_OWNER = '********9012'
TEST_DOCUMENT_IPN_RECIPIENT = '********90'
TEST_DOCUMENT_COMPANY_NAME_RECIPIENT = 'OAO "Recipient Company Name"'
TEST_DOCUMENT_NUMBER = 'Test Number'
TEST_DOCUMENT_TITLE = 'Test Document'
TEST_TAG_NAME = 'TEST TAG NAME'
TEST_TAG_NAME1 = 'TEST TAG NAME 1'
TEST_TAG_NAME2 = 'TEST TAG NAME 2'

TEST_CONTACT = {
    'company': {
        'edrpou': '********',
        'name': 'Contact company name',
        'short_name': 'Contact short name',
        'accountant_email': '<EMAIL>',
    },
    'person': {
        'email': '<EMAIL>',
        'first_name': 'Contact first name',
        'second_name': 'Second name',
        'last_name': 'Last name',
        'main_recipient': False,
    },
    'phone': '+38**********',
}

TEST_COMPANY = {
    'edrpou': TEST_COMPANY_EDRPOU,
    'is_legal': True,
    'name': 'Company Name',
}

TEST_CONTACT_COMPANY_NAME = 'Contact Company Name'
TEST_CONTACT_COMPANY_SHORT_NAME = 'Contact Company Short Name'

TEST_RECIPIENT_COMPANY_FULL_NAME = 'Recipient Full Name'
TEST_RECIPIENT_COMPANY_NAME = 'Recipient Company Name'

TEST_SIGN_SESSION_ON_CANCEL_URL = 'https://vchasno.com.ua/cancelled'
TEST_SIGN_SESSION_ON_FINISH_URL = 'https://vchasno.com.ua/finished'

TEST_UPLOAD_LEFT_COUNTER = 100
TEST_BILLING_ACCOUNT = {
    'company_id': None,
    'initiator_id': 'bfd97067-83a7-4895-a494-5baf4b86292a',
    'type': AccountType.client_debit.value,
    'amount': 3000,
    'amount_left': 3000,
    'units': 30,
    'units_left': 30,
}
TEST_WEB_BILLING_ACCOUNT = {**TEST_BILLING_ACCOUNT, 'rate': AccountRate.start}
TEST_API_BILLING_ACCOUNT = {**TEST_BILLING_ACCOUNT, 'rate': AccountRate.integration}

ROLE = ReviewsInfoCtx.EntityType.role
GROUP = ReviewsInfoCtx.EntityType.group

TEST_DOCUMENT_CONTENT = b'TestTestContent'
TEST_INTERNAL_SIGNATURE_CONTENT = b'TestInternalSignatureContent'
TEST_EXTERNAL_KEY_SIGNATURE_CONTENT = b'TestExternalKeySignatureContent'
TEST_EXTERNAL_STAMP_SIGNATURE_CONTENT = b'TestExternalStampSignatureContent'

logger = logging.getLogger(__name__)


class FakeDatetime(datetime.datetime):
    @classmethod
    def utcnow(cls):
        return datetime.datetime(year=2000, month=1, day=1)

    @classmethod
    def now(cls, tz=None):
        return datetime.datetime(year=2000, month=1, day=1, tzinfo=tz)


class TimeManager:
    """
    Time manager used for testing cron jobs. This manager provides ability to
    freeze time and set up any time that needed.
    NOTE auto updated field can't be used with this TimeManager

    Example of using:
      async with app['db'].acquire() as conn:
         async with TimeManager(conn) as time:
              await time.set_now(date)
    """

    def __init__(self, conn):
        self.conn = conn
        self.real_datetime = None

    async def __aenter__(self):
        self.real_datetime = datetime.datetime
        datetime.datetime = FakeDatetime

        await self.set_now(await self.now())
        await self._rename_now()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self._rollback_now()

    async def now(self):
        result = await self.conn.execute('SELECT NOW();')
        rows = await result.fetchone()
        return rows[0]

    async def set_now(self, time: datetime.datetime):
        """Change current now"""
        time = time.replace(tzinfo=UTC_TZ)

        # patching database
        create_function = (
            'CREATE OR REPLACE FUNCTION now() RETURNS  timestamptz as $$'
            'SELECT %s as result; $$ LANGUAGE SQL;'
        )
        await self.conn.execute(create_function, [time])

        # patching utc_now()
        self._patch_datetime(time)

        return await self.now()

    def _patch_datetime(self, time):
        """Setting fake datetime class that return only one specific date"""
        FakeDatetime.utcnow = lambda: datetime.datetime(
            year=time.year,
            month=time.month,
            day=time.day,
            hour=time.hour,
            minute=time.minute,
            second=time.second,
        )

    async def add(self, delta: datetime.timedelta):
        """Add timedelta to current now"""
        now = await self.now()
        return await self.set_now(now + delta)

    async def _rollback_now(self):
        await self.conn.execute('ALTER FUNCTION now_old() RENAME TO now;')
        datetime.datetime = self.real_datetime

    async def _rename_now(self):
        await self.conn.execute('ALTER FUNCTION now() RENAME TO now_old;')


class AsyncBytesIO(io.BytesIO):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.content = self

    async def read(self, amt=-1):
        if amt == -1:  # aiohttp to regular response
            amt = None
        return super().read(amt)

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


def prepare_local_datetime(
    year=2018,
    month=1,
    day=1,
    hour=0,
    minute=0,
    second=0,
) -> datetime.datetime:
    naive_datetime = datetime.datetime(
        year=year,
        month=month,
        day=day,
        hour=hour,
        minute=minute,
        second=second,
    )
    return to_local_datetime(naive_datetime)


def prepare_datetime(
    year=2018,
    month=1,
    day=1,
    hour=0,
    minute=0,
    second=0,
    tzinfo=UTC_TZ,
):
    return datetime.datetime(
        year=year,
        month=month,
        day=day,
        hour=hour,
        minute=minute,
        second=second,
        tzinfo=tzinfo,
    )


class EnvironmentManager:
    """Simple context manager for environments variable

    Example of using:
      with EnvironmentManager('level', 'dev'):
          assert os.environ.get('level') == 'dev'
      assert os.environ.get('level') == 'test'
    """

    def __init__(self, key, value):
        self.has_old_value = key in os.environ
        self.old_value = os.environ.get(key)
        self.key = key
        self.value = value

    def __enter__(self):
        os.environ[self.key] = self.value

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.has_old_value:
            os.environ[self.key] = self.old_value
        else:
            os.environ.pop(self.key, None)


class EmailDocument(NamedTuple):
    document: DBRow
    owner: DBRow
    recipient: DBRow | None


class GraphqlOrderedDocument(NamedTuple):
    title: str
    date_listing: str
    date_document: str
    edrpou_recipient: str
    email_recipient: str
    number: str


class RequestPayloadStreamReader(StreamReader):
    def __init__(self, content):
        self._called = False
        self._content = content

        protocol = mock.Mock()
        protocol.transport = _create_transport()

        super().__init__(protocol=protocol, limit=2**16)

    async def readany(self):
        if self._called is False:
            self._called = True
            return self._content
        return b''


def append_form_data(form_data, name, path):
    with open(path, 'rb') as handler:
        file_like = io.BytesIO(handler.read())

    form_data.add_field(
        name,
        file_like,
        filename=str(path.absolute()),
        content_type=ensure_mimetype(path.name),
    )

    return form_data


async def flush_databases(app, *, clean_db=False, clean_redis=False, clean_es=False):
    if clean_db:
        db_keys = {'db': metadata, 'events_db': events_metadata}
        for db_key, db_metadata in db_keys.items():
            if db_key in app and not app[db_key].closed:
                async with app[db_key].acquire() as conn:
                    await flush_db(conn, db_metadata)

    if clean_redis:
        await flush_redis(app)

    if clean_es:
        await flush_es(app)


async def _restart_sequences(conn):
    """
    Some sequences are not restarting with TRUNCATE ...
    That's why we restart some of them directly

    Add your sequence here if you need it for testing purposes
    """
    await conn.execute('ALTER SEQUENCE category_seqnum RESTART WITH 0;')
    await conn.execute('ALTER SEQUENCE document_directories_id_seq RESTART WITH 1;')


async def flush_db(conn, metadata_):
    tables = ', '.join(metadata_.tables)

    # Restart sequences
    await _restart_sequences(conn=conn)

    result = await conn.execute(f'TRUNCATE {tables} CASCADE;')
    logger.info(f'Attempt to TRUNCATE {tables} ended with {result}')


async def flush_redis(app):
    with suppress(Exception):
        await app['redis'].flushdb()


async def flush_es(app):
    with suppress(Exception):
        await app['es'].documents.delete_by_query({'query': {'match_all': {}}}, conflicts='proceed')
        await app['es'].comments.delete_by_query({'query': {'match_all': {}}}, conflicts='proceed')


async def cleanup_on_teardown(app: web.Application, *, clean_db=False, clean_redis=False):
    await flush_databases(app, clean_db=clean_db, clean_redis=clean_redis)
    await app.cleanup()


async def fetch_graphql(client, query, headers=None, *, url=GRAPHQL_URL, variables=None):
    request_data = {'query': query, 'variables': variables} if variables else {'query': query}
    response = await client.post(
        url,
        data=ujson.dumps(request_data),
        headers=headers or prepare_referer_headers(client),
    )
    return await graphql_response(response)


async def fetch_graphql_raw(
    client,
    query,
    headers=None,
    *,
    url=GRAPHQL_URL,
    variables=None,
) -> ClientResponse:
    request_data = {'query': query, 'variables': variables} if variables else {'query': query}
    return await client.post(
        url,
        data=ujson.dumps(request_data),
        headers=headers or prepare_referer_headers(client),
    )


async def graphql_response(response):
    assert response.status == 200, await response.json()
    return (await response.json())['data']


async def login(
    client: TestClient,
    email: str,
    password: str,
    remember: bool | None = None,
    *,
    ensure_success: bool = True,
    **kwargs,
) -> ClientResponse:
    """Make a request to the login endpoint.

    If `ensure_success` is `True`, asserts that the endpoint's response has an
    HTTP 200 OK status. `kwargs` are passed to the `client.post` call.
    """
    response = await client.post(
        LOGIN_URL,
        data=ujson.dumps(
            {
                'email': email,
                'password': password,
                'remember': remember,
            }
        ),
        headers=prepare_referer_headers(client),
        **kwargs,
    )
    if ensure_success:
        assert response.status == 200
    return response


def make_mocked_request(method, url, payload=sentinel, **kwargs):
    if payload is not sentinel:
        payload = RequestPayloadStreamReader(payload)
    return base_make_mocked_request(method, url, payload=payload, **kwargs)


async def prepare_app_client(
    aiohttp_client,
    *,
    feature_flags=None,
) -> tuple[web.Application, TestClient]:
    app = create_app()
    client = await aiohttp_client(app, json_serialize=ujson.dumps)

    if feature_flags:
        config: Config = app['config']
        config.feature_flags.update(feature_flags)

    return app, client


def prepare_auth_headers(user: DBRow) -> dict:
    return {'Authorization': f'Token {user.raw_token}'}


def prepare_raw_auth_headers(raw_token: str) -> dict:
    return {'Authorization': f'Token {raw_token}'}


def prepare_old_format_auth_headers(user, vendor=None):
    return {'Authorization': f'Token {str(uuid.uuid4())}'}


def prepare_add_bill_options(**kwargs) -> AddBillOptions:
    return AddBillOptions(
        **{
            'name': 'Test Bill',
            'edrpou': '********',
            'email': '<EMAIL>',
            'company_id': None,
            'role_id': None,
            'user_id': None,
            'agreement': None,
            'source': None,
            'contract_basis': None,
            'payment_purpose': None,
            'services_type': None,
            'services': [],
            'custom_price': None,
            'status': None,
            'is_card_payment': False,
            **kwargs,
        }
    )


async def prepare_bill(conn: DBConnection, user: DBRow | User | None = None, **kwargs) -> Bill:
    if user is not None:
        kwargs['company_id'] = user.company_id
        kwargs['edrpou'] = user.company_edrpou
        kwargs['email'] = user.email

    options = prepare_add_bill_options(**kwargs)
    return await insert_bill(conn=conn, options=options)


async def prepare_billing_account(
    conn,
    company_id,
    rate=None,
    type_=None,
    status=None,
    billing_company_config: UpdateBillingCompanyConfigDict | DataDict | None = None,
    **kwargs,
) -> Account:
    """Prepare test billing account for company with ``client_debit`` type.

    Note, that for this test account, appropriate transaction from
    ``service_credit_external`` account is not created, so this function can
    be used without preparing billing schema.
    """

    data = {**TEST_WEB_BILLING_ACCOUNT, 'company_id': company_id, **kwargs}
    if rate:
        data['rate'] = rate
    if type_:
        data['type'] = type_
    if status:
        data['status'] = status

    if status and billing_company_config:
        await update_billing_company_config(
            conn=conn,
            company_id=company_id,
            config=billing_company_config,
        )
    return await billing_insert_account(conn, data)


async def prepare_pro_functionality(
    conn: DBConnection,
    company_id: str,
    company_edrpou: str,
) -> None:
    """
    Update company with ALL pro functionality enabled. Because permissions is tightly
    coupled with billing account, we need to create billing account with pro rate
    """

    activation_date = local_now()
    rate = CompanyRate(
        id_=str(generate_uuid()),
        company_id=company_id,
        company_edrpou=company_edrpou,
        # do not use "pro" rate, because it is chargless
        rate=AccountRate.free,
        status=CompanyRateStatus.active,
        amount=0,
        start_date=activation_date,
        end_date=get_date_expired(365),
    )
    await billing_insert_account(conn, rate.to_db())

    config = {
        **ALL_PERMITTED,
        CompanyPermission.api: True,
        CompanyPermission.tags_enabled: True,
        CompanyPermission.allow_ordered_reviews: True,
        CompanyPermission.document_templates_enabled: True,
        CompanyPermission.custom_documents_fields_enabled: True,
        **UNLIMITED,
    }
    config = {k.value: v for k, v in config.items()}
    await update_billing_company_config(
        conn=conn,
        company_id=rate.company_id,
        config=config,
        update_extra_roles=False,
    )


async def prepare_api_billing_account(conn, company_id):
    """
    Prepare test API billing account for company with ``client_debit`` type.

    Note, that for this test account, appropriate transaction from
    ``service_credit_external`` account is not created, so this function can
    be used without preparing billing schema.
    """
    await billing_insert_account(
        conn,
        {
            **TEST_API_BILLING_ACCOUNT,
            'company_id': company_id,
        },
    )


async def prepare_billing_schema(conn):
    """Prepare service accounts that linked to Vchasno company."""

    # Check for existed service accounts, service accounts duplication
    # leads to errors
    service_accounts = await billing_select_account(conn, only_service_accounts=True)
    if len(service_accounts):
        return

    # Get Vchasno company ID, create one if not existed
    vchasno_company = await select_company_by_edrpou(conn, SUPER_ADMIN_EDRPOU)
    if vchasno_company:
        vchasno_company_id = vchasno_company.id
    else:
        vchasno_company_id = await insert_company(
            conn,
            {
                'edrpou': SUPER_ADMIN_EDRPOU,
                'ipn': None,
                'name': None,
                'full_name': None,
                'date_created': sa.text('now()'),
                'is_legal': True,
                'is_dealer': False,
                'upload_documents_left': TEST_UPLOAD_LEFT_COUNTER,
            },
        )

    # Create service accounts and client credit account for Vchasno
    data = {
        'company_id': vchasno_company_id,
        'initiator_id': None,
        'type': None,
        'amount': 0,
        'amount_left': 0,
        'units': 0,
        'units_left': 0,
    }
    types = [
        AccountType.service_credit_external.value,
        AccountType.service_credit_bonus.value,
        AccountType.service_debit_external.value,
        AccountType.service_debit_bonus.value,
        AccountType.service_debt.value,
        AccountType.client_credit.value,
    ]

    for type_ in types:
        await billing_insert_account(conn, {**data, 'type': type_})


async def insert_values(
    app: web.Application,
    table: sa.Table,
    **kwargs,
) -> DBRow:
    async with app['db'].acquire() as conn:
        return await select_one(
            conn=conn,
            query=(table.insert().values(kwargs).returning(table)),
        )


async def prepare_company_config(
    *,
    company_id: str | None = None,
    company_edrpou: str | None = None,
    config: CompanyConfig,
):
    assert company_id or company_edrpou, 'company_id or company_edrpou is required'

    async with services.db.acquire() as conn:
        if not company_id:
            company_id = await select_company_id(conn, edrpou=company_edrpou)
            assert company_id, f'Company with edrpou {company_edrpou} not found'

        config_db = config.model_dump(mode='json', exclude_unset=True)
        await update_company_config(
            conn=conn,
            company_id=company_id,
            update_admin=True,  # admin config has higher priority than config
            **config_db,
        )


async def set_company_config(
    app: web.Application,
    company_id: str,
    update_admin: bool = True,
    **kwargs,
) -> None:
    if not kwargs:
        return
    async with services.db.acquire() as conn:
        await update_company_config(
            conn=conn,
            company_id=company_id,
            update_admin=update_admin,
            **kwargs,
        )


async def set_billing_company_config(
    company_id: str,
    **kwargs,
) -> None:
    if not kwargs:
        return
    async with services.db.acquire() as conn:
        await update_billing_company_config(
            conn=conn,
            company_id=company_id,
            config=kwargs,
        )


async def update_company_config(
    conn: DBConnection,
    company_id: str,
    update_admin: bool = True,
    **kwargs,
) -> None:
    assert 'config' not in kwargs and 'admin_config' not in kwargs, (
        'Do not use "config" or "admin_config" as argument, '
        'pass config parameters as keyword arguments instead'
    )

    values = {'config': kwargs, 'admin_config': {}}
    update_ = {'config': sa.text('companies_config.config || EXCLUDED.config')}
    if update_admin:
        values = {'config': {}, 'admin_config': kwargs}
        update_ = {
            'admin_config': sa.text('companies_config.admin_config || EXCLUDED.admin_config')
        }

    await conn.execute(
        insert(company_config_table)
        .values(company_id=company_id, **values)
        .on_conflict_do_update(
            index_elements=[company_config_table.c.company_id],
            set_=update_,
        )
    )


async def prepare_client(
    aiohttp_client: AiohttpClient,
    is_admin: bool = False,
    *,
    feature_flags=None,
    enable_pro_functionality: bool = True,
    **kwargs,
) -> tuple[web.Application, TestClient, DBRow]:
    app, client = await prepare_app_client(
        aiohttp_client,
        feature_flags=feature_flags,
    )

    if 'user_role' in kwargs:
        user_role = kwargs.pop('user_role')  # expected to be value already
    else:
        user_role = UserRole.admin.value if is_admin else UserRole.user.value

    user = await prepare_user_data(
        app,
        user_role=user_role,
        enable_pro_functionality=enable_pro_functionality,
        **kwargs,
    )

    return app, client, user


async def prepare_comment_data(
    app,
    *,
    document_id,
    role_id,
    text,
    id_=None,
    access_company_id=None,
    type_=None,
    date_created=None,
    date_edited=None,
    user_id=None,
    status_id=None,
    version_id=None,
) -> Comment:
    async with app['db'].acquire() as conn:
        comment = await insert_comment(
            conn,
            Comment(
                id=id_ or str(uuid.uuid4()),
                access_company_id=access_company_id,
                document_id=document_id,
                document_version_id=version_id,
                role_id=role_id,
                type=type_ or CommentType.comment,
                text=text,
                date_created=date_created or utc_now(),
                date_edited=date_edited,
                user_id=user_id,
                status_id=status_id or CommentStatus.ordinary.value,
            ),
        )
    return comment


async def prepare_company_data(
    app,
    edrpou=TEST_COMPANY_EDRPOU,
    is_legal=True,
    upload_documents_left=TEST_UPLOAD_LEFT_COUNTER,
    **kwargs,
) -> str:
    data = {
        'edrpou': edrpou,
        'is_legal': is_legal,
        'upload_documents_left': upload_documents_left,
        **kwargs,
    }
    async with app['db'].acquire() as conn:
        return await insert_company(conn, data)


async def prepare_contact(
    conn,
    user,
    contact=None,
    persons=None,
    ignore_persons=False,
):
    contact = contact or TEST_CONTACT['company'].copy()
    contact_id = await insert_contact(conn, contact, user.company_id)
    persons = persons or []
    if not persons and not ignore_persons:
        persons = [TEST_CONTACT['person'].copy()]

    for person in persons:
        person_id = await insert_contact_person(conn, person, contact_id)
        await insert_contact_person_phone(
            conn=conn,
            person_id=person_id,
            phone=TEST_CONTACT['phone'],
        )
    return contact_id


def _prepare_recipient_for_document(
    document_recipients: list[DataDict] | None,
    another_recipients: list[DBRow] | None,
    recipient_edrpou: str | None,
    recipient_email: str | list[str] | None,
    is_recipients_hidden: bool,
) -> DataDict:
    """Prepare recipients for document."""

    recipient_edrpou_old: str | None = None
    recipient_email_old: str | None = None

    # {edrpou: [emails]}
    recipients: dict[str, DataDict] = {}

    if another_recipients:
        recipient_email_old = another_recipients[0].email
        recipient_edrpou_old = another_recipients[0].company_edrpou
        for recipient in another_recipients:
            edrpou = recipient.company_edrpou
            if edrpou in recipients:
                recipient_data = recipients[edrpou]
                recipient_data['emails'].append(recipient.email)
            elif edrpou:
                recipients[edrpou] = {
                    'edrpou': edrpou,
                    'emails': [recipient.email],
                    'is_emails_hidden': is_recipients_hidden,
                }

    elif document_recipients:
        emails = document_recipients[0]['emails']
        recipient_email_old = ', '.join(emails) if emails else None
        recipient_edrpou_old = document_recipients[0]['edrpou']
        for document_recipient in document_recipients:
            edrpou = document_recipient['edrpou']
            emails = document_recipient['emails']
            document_recipient.setdefault('is_emails_hidden', is_recipients_hidden)
            assert isinstance(emails, list) or emails is None, (
                'document_recipients -> emails - must be a list of string'
            )
            assert edrpou not in recipients, (
                "EDRPOU can't be duplicated in document_recipients parameter"
            )
            recipients[edrpou] = {
                'edrpou': edrpou,
                'emails': emails,
                'is_emails_hidden': document_recipient['is_emails_hidden'],
            }

    elif recipient_edrpou:
        if isinstance(recipient_email, list):
            recipient_email = ', '.join(recipient_email)
        recipient_email_old = recipient_email
        recipient_edrpou_old = recipient_edrpou
        recipients[recipient_edrpou] = {
            'edrpou': recipient_edrpou,
            'emails': [recipient_email] if recipient_email else None,
            'is_emails_hidden': is_recipients_hidden,
        }

    # After 2 recipients, document is considered as multilateral and "documents.email_recipient"
    # and "documents.edrpou_recipient" are set to NULL in the database
    if len(recipients) > 2:
        recipient_edrpou_old = None
        recipient_email_old = None

    return {
        'recipient_edrpou_old': recipient_edrpou_old,
        'recipient_email_old': recipient_email_old,
        'recipients': list(recipients.values()),
    }


async def prepare_public_document_categories(amount: int):
    async with services.db.acquire() as conn:
        await _restart_sequences(conn)
        for i in range(0, amount):
            await insert_document_category(conn=conn, title=f'public_category_{i}')


async def prepare_document_data(  # noqa: C901
    app: web.Application | None,
    owner: DBRow | User,
    *,
    date_listing=None,
    date_listing_recipient=None,
    create_comment=False,
    create_notification=False,
    create_owner_signer=False,
    another_owners: list[User] | None = None,
    document_recipients: list[DataDict] | None = None,
    another_recipients: list[User] | None = None,
    recipient_edrpou: str | None = None,
    recipient_email: str | None = None,
    create_document_access_for_recipients=True,
    flows: list[DataDict] | None = None,
    is_recipients_hidden=False,
    groups: list[str] | None = None,
    set_groups_as_signers=False,
    set_groups_as_viewers=False,
    upload_document: bool = True,
    content: bytes = TEST_DOCUMENT_CONTENT,
    **kwargs,
) -> DocumentWithUploader:
    """
    Preparing document for tests

    :param another_owners:
        list of users that will have access to a document

    :param document_recipients:
        list of dictionaries with EDRPOU and with
        emails, it represents natural way how user assign recipients for
        the document. Note: this parameter will be extended by
        another_recipients parameter for inserting to document_recipient_table.

    :param another_recipients:
        list of DBRow that represent users that will be added to
        document_recipient_table. Used to simplify creating recipients when
        there are already selected users object.

    :param recipient_edrpou:
        shortcut for adding single recipient edrpou, direct alternative to parameter
        "document_recipients"

    :param recipient_email:
        shortcut for adding single recipient email, direct alternative to parameter
        "document_recipients"


    :param create_document_access_for_recipients:
        the boolean parameter, that when is True all recipients will be
        selected by email and edrpou and all will be added to listing_table

    """
    kwargs.setdefault('date_delivered', None)
    kwargs.setdefault('number', TEST_DOCUMENT_NUMBER)
    kwargs.setdefault('title', TEST_DOCUMENT_TITLE)
    kwargs.setdefault('extension', '.pdf')
    kwargs.setdefault('status_id', DocumentStatus.uploaded.value)
    kwargs.setdefault('edrpou_owner', owner.company_edrpou)
    kwargs.setdefault('role_id', owner.role_id)
    kwargs.setdefault('uploaded_by', owner.role_id)
    kwargs.setdefault('is_internal', False)
    kwargs.setdefault('is_multilateral', False)
    kwargs.setdefault('signature_format', SignatureFormat.external_separated)
    kwargs.setdefault('source', DocumentSource.vchasno)

    if kwargs.get('status_id') in (DocumentStatus.finished.value, DocumentStatus.reject.value):
        kwargs.setdefault('date_finished', naive_local_now())

    directory_id = kwargs.pop('directory_id', None)
    is_archived = kwargs.pop('is_archived', None)

    _recipients = _prepare_recipient_for_document(
        document_recipients=document_recipients,
        another_recipients=another_recipients,
        recipient_edrpou=recipient_edrpou,
        recipient_email=recipient_email,
        is_recipients_hidden=is_recipients_hidden,
    )

    async with services.db.acquire() as conn:
        async with conn.begin():
            document_id = await conn.scalar(
                document_table.insert()
                .values(
                    user_id=owner.id,
                    email_recipient=_recipients['recipient_email_old'],
                    edrpou_recipient=_recipients['recipient_edrpou_old'],
                    **kwargs,
                )
                .returning(document_table.c.id)
            )

            owners = [owner]
            if another_owners:
                owners = [*owners, *another_owners]
            listing_data = ListingDataAggregator()
            for owner in owners:
                listing_data.add(
                    access_edrpou=kwargs['edrpou_owner'],
                    role_id=owner.role_id,
                    document_id=document_id,
                    source=AccessSource.default,
                )
            values = [
                {**item, 'date_created': date_listing or sa.text('now()')}
                for item in listing_data.as_db()
            ]
            await insert_listings(conn, values)

            recipients_data = [
                {**recipient, 'document_id': document_id} for recipient in _recipients['recipients']
            ]

            if recipients_data:
                await insert_recipients(conn, recipients=recipients_data)

            if create_document_access_for_recipients and recipients_data:
                clauses = [
                    sa.and_(
                        company_table.c.edrpou == item['edrpou'],
                        user_table.c.email.in_(item['emails']),
                    )
                    for item in recipients_data
                    if item['emails']
                ]
                if clauses:
                    recipient_roles = await select_all(
                        conn, (SELECT_ROLE_QUERY.where(sa.or_(*clauses)))
                    )
                else:
                    recipient_roles = []

                listing_data = ListingDataAggregator()
                edrpous_with_role_id = set()
                date_created = date_listing_recipient or sa.func.now()
                for recipient in recipient_roles:
                    if recipient.company_edrpou and recipient.id:
                        listing_data.add(
                            document_id=document_id,
                            access_edrpou=recipient.company_edrpou,
                            role_id=recipient.id,
                            source=AccessSource.default,
                        )
                        edrpous_with_role_id.add(recipient.company_edrpou)

                for item in recipients_data:
                    if item['edrpou'] not in edrpous_with_role_id:
                        listing_data.add(
                            document_id=document_id,
                            access_edrpou=item['edrpou'],
                            role_id=None,
                            source=AccessSource.default,
                        )
                if listing_data:
                    await insert_listings(
                        conn=conn,
                        data=[
                            {**item, 'date_created': date_created} for item in listing_data.as_db()
                        ],
                    )

            if create_comment:
                await insert_comment(
                    conn=conn,
                    comment=Comment(
                        id=str(uuid.uuid4()),
                        document_id=document_id,
                        document_version_id=None,
                        role_id=owner.role_id,
                        user_id=owner.id,
                        status_id=CommentStatus.ordinary.value,
                        type=CommentType.comment,
                        text=TEST_COMMENT,
                        date_created=utc_now(),
                        access_company_id=None,
                        date_edited=None,
                    ),
                )

            if create_notification:
                name = NotificationName.reminder_about_new_documents
                await insert_notifications(
                    conn,
                    [
                        {
                            'document_id': document_id,
                            'source': NotificationSource.email,
                            'name': name,
                            'email': TEST_DOCUMENT_EMAIL_RECIPIENT,
                        },
                    ],
                )

            if create_owner_signer:
                await conn.execute(
                    document_signer_table.insert().values(
                        [
                            {
                                'document_id': document_id,
                                'company_id': owner.company_id,
                                'role_id': owner.role_id,
                                'order': None,
                            }
                            for owner in owners
                        ]
                    )
                )

            if groups and set_groups_as_signers:
                await conn.execute(
                    document_signer_table.insert().values(
                        [
                            {
                                'document_id': document_id,
                                'company_id': owner.company_id,
                                'group_id': group_id,
                                'order': None,
                            }
                            for group_id in groups
                        ]
                    )
                )

            if groups and set_groups_as_viewers:
                await conn.execute(
                    group_document_access_table.insert().values(
                        [
                            {
                                'group_id': group_id,
                                'document_id': document_id,
                                'company_id': owner.company_id,
                                'created_by': owner.role_id,
                            }
                            for group_id in groups
                        ]
                    )
                )

            if flows:
                for flow_data in flows:
                    await prepare_flow_item(
                        app=app,
                        document_id=document_id,
                        **flow_data,
                    )

            # Create document_to_directory relation
            if directory_id:
                await conn.execute(
                    directory_documents_table.insert().values(
                        [
                            {
                                'directory_id': directory_id,
                                'document_id': document_id,
                            }
                        ]
                    )
                )

            # Create document-archive relation
            if is_archived:
                await conn.execute(
                    document_archive_table.insert().values(
                        [
                            {
                                'document_id': document_id,
                                'company_id': owner.company_id,
                                'created_by': owner.role_id,
                            }
                        ]
                    )
                )

            # Update counters respectively
            uploader_company = await select_company_by_role_id(
                conn,
                kwargs['uploaded_by'],
            )
            await decrement_company_upload_documents_left(conn, uploader_company.id)

        document = await select_document_by_id(conn, document_id)

        if upload_document:
            s3_key = _get_document_original_s3_key(document_id=document_id)
            await s3_utils.upload(
                item=s3_utils.UploadFile(
                    key=s3_key,
                    body=content,
                )
            )

        return DocumentWithUploader.from_row(document)


async def prepare_listing_access(
    *,
    role_id: str,
    access_edrpou: str,
    document_id: str,
    source: AccessSource = AccessSource.default,
) -> None:
    async with services.db.acquire() as conn:
        await insert_listings(
            conn=conn,
            data=[
                {
                    'role_id': role_id,
                    'access_edrpou': access_edrpou,
                    'document_id': document_id,
                    'sources': source,
                },
            ],
        )


async def prepare_listings_accesses(data: list[DataDict]) -> None:
    if not data:
        return
    async with services.db.acquire() as conn:
        await insert_listings(conn, data)


async def prepare_document_data_for_email(
    app: web.Application,
    *,
    owner=None,
    create_recipient=True,
    owner_can_receive_notifications=True,
    can_receive_review_process_finished=False,
    can_receive_sign_process_finished=False,
    recipient_can_receive_notifications=True,
    recipient_first_name=None,
    create_comment=False,
    another_owners: list[DBRow] | None = None,
    start_app: bool = True,
    **kwargs,
):
    if owner is None:
        # TODO: remove app startup from that function
        if start_app:
            await app.startup()

        owner = await prepare_user_data(
            app,
            email=TEST_DOCUMENT_EMAIL_OWNER,
            company_edrpou=TEST_DOCUMENT_EDRPOU_OWNER,
            can_receive_notifications=owner_can_receive_notifications,
            can_receive_comments=owner_can_receive_notifications,
            can_receive_inbox=owner_can_receive_notifications,
            can_receive_rejects=owner_can_receive_notifications,
            can_receive_reminders=owner_can_receive_notifications,
            can_receive_reviews=owner_can_receive_notifications,
            can_receive_review_process_finished=can_receive_review_process_finished,
            can_receive_sign_process_finished=can_receive_sign_process_finished,
            can_receive_access_to_doc=owner_can_receive_notifications,
            can_receive_delete_requests=owner_can_receive_notifications,
        )

    recipient = None
    if create_recipient:
        recipient = await prepare_user_data(
            app,
            email=TEST_DOCUMENT_EMAIL_RECIPIENT,
            first_name=recipient_first_name,
            company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            user_role=UserRole.user.value,
            can_receive_notifications=recipient_can_receive_notifications,
            can_receive_comments=recipient_can_receive_notifications,
            can_receive_inbox=recipient_can_receive_notifications,
            can_receive_rejects=recipient_can_receive_notifications,
            can_receive_reminders=recipient_can_receive_notifications,
            can_receive_reviews=recipient_can_receive_notifications,
            can_receive_review_process_finished=can_receive_review_process_finished,
            can_receive_sign_process_finished=can_receive_sign_process_finished,
            can_receive_access_to_doc=recipient_can_receive_notifications,
            can_receive_delete_requests=recipient_can_receive_notifications,
        )

    document = await prepare_document_data(
        app,
        owner,
        another_owners=another_owners,
        create_comment=create_comment,
        create_document_access_for_recipients=create_recipient,
        another_recipients=[recipient] if recipient else None,
        **kwargs,
    )

    return EmailDocument(document, owner, recipient)


def prepare_form_data(*paths, **kwargs):
    form_data = FormData()

    for idx, path in enumerate(paths):
        append_form_data(form_data, f'file{idx}', path)

    for name, path in kwargs.items():
        append_form_data(form_data, name, path)

    return form_data


def prepare_private_key(
    user=None,
    *,
    company_edrpou=None,
    key_serial_number=None,
    stamp_serial_number=None,
    is_legal=True,
    is_internal=False,
    key=None,
    stamp=None,
    with_stamp=True,
):
    if user:
        company_full_name = user.company_name or TEST_COMPANY_NAME
        first_name = user.first_name or ''
        second_name = user.second_name or ''
        last_name = user.last_name or ''
        company_edrpou = user.company_edrpou
    else:
        company_full_name = TEST_COMPANY_NAME
        first_name = ''
        second_name = ''
        last_name = ''
        company_edrpou = company_edrpou
    full_name = ' '.join((first_name, second_name, last_name)).strip() or TEST_USER_FULL_NAME

    pk_data = {
        'key': key or '********',
        'key_acsk': 'key_acsk',
        'key_serial_number': key_serial_number or str(uuid.uuid4()),
        'key_timemark': utc_now(),
        'key_company_fullname': company_full_name,
        'key_owner_edrpou': company_edrpou,
        'key_owner_fullname': full_name,
        'key_owner_position': None,
        'key_is_legal': is_legal,
        'is_internal': is_internal,
        'stamp': None,
        'stamp_acsk': None,
        'stamp_serial_number': None,
        'stamp_timemark': None,
        'stamp_company_fullname': None,
        'stamp_owner_edrpou': None,
        'stamp_owner_fullname': None,
        'stamp_owner_position': None,
        'stamp_is_legal': None,
    }

    if with_stamp:
        pk_data |= {
            'stamp': stamp or '********',
            'stamp_acsk': 'stamp_acsk',
            'stamp_serial_number': stamp_serial_number or str(uuid.uuid4()),
            'stamp_timemark': utc_now(),
            'stamp_company_fullname': company_full_name,
            'stamp_owner_edrpou': company_edrpou,
            'stamp_owner_fullname': full_name,
            'stamp_owner_position': None,
            'stamp_is_legal': is_legal,
        }

    return PrivateKey(**pk_data)


def prepare_referer_headers(client, path='/'):
    return {'Referer': f'http://{client.host}:{client.port}{path}', 'X-Real-IP': '127.0.0.1'}


async def prepare_rejected_document_data(
    app, owner, *, recipient=None, comment_date_created=None, **kwargs
):
    if recipient is None:
        recipient = await prepare_user_data(
            app,
            email=TEST_DOCUMENT_EMAIL_RECIPIENT,
            company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        )

    document = await prepare_document_data(
        app,
        owner,
        status_id=DocumentStatus.reject.value,
        create_document_access_for_recipients=True,
        another_recipients=[recipient],
        **kwargs,
    )

    await prepare_comment_data(
        app,
        document_id=document.id,
        role_id=recipient.role_id,
        user_id=recipient.id,
        status_id=CommentStatus.reject.value,
        type_=CommentType.rejection,
        text=TEST_COMMENT_REJECTED,
        date_created=comment_date_created,
    )

    return document


async def prepare_sign_session_data(
    app,
    edrpou: str,
    email: str,
    role_id: str | None = None,
    user: DBRow | None = None,
    document: DBRow | None = None,
    source: SignSessionSource = SignSessionSource.api,
    **kwargs,
):
    if user:
        kwargs.setdefault('created_by', user.role_id)

    if document:
        kwargs.setdefault('document_id', document.id)
        kwargs.setdefault('edrpou', document.edrpou_recipient)
        kwargs.setdefault('email', document.email_recipient)

    kwargs.setdefault('is_legal', True)
    kwargs.setdefault('vendor', SignSessionVendor.vchasno)

    kwargs.setdefault('on_finish_url', TEST_SIGN_SESSION_ON_FINISH_URL)
    kwargs.setdefault('on_cancel_url', TEST_SIGN_SESSION_ON_CANCEL_URL)
    kwargs['source'] = source
    kwargs['edrpou'] = edrpou
    kwargs['email'] = email
    kwargs['role_id'] = role_id

    async with app['db'].acquire() as conn:
        return await insert_sign_session(conn, kwargs)


def prepare_sign_session_headers(sign_session, client):
    return dict(prepare_referer_headers(client), **{'X-SignSession-ID': sign_session.id})


async def prepare_signature_row(
    *,
    document_id: str,
    owner_edrpou: str | None = None,
    role_id: str | None = None,
    user_id: str | None = None,
    user_email: str | None = None,
    owner: DBRow | None = None,
    format: SignatureFormat = SignatureFormat.external_separated,
    algo: SignatureAlgo = SignatureAlgo.DSTU,
    source: str | None = None,
    date_created: datetime.datetime | None = None,
    is_internal: bool = False,
    id: str | None = None,
    _set_key_fields: bool = True,
    _set_stamp_fields: bool = False,
    **kwargs,
) -> Signature:
    """
    Prepare signature for tests. That is simpler version of prepare_signature_data, that
    just insert signature to a database and return it
    """
    data = {
        'document_id': document_id,
        'edrpou_recipient': owner_edrpou,
        'format': format,
        'algo': algo,
        'source': source,
        'date_created': date_created,
        'is_internal': is_internal,
        'key': None,
        'key_exists': False,
        'key_acsk': None,
        'key_serial_number': None,
        'key_timemark': None,
        'key_company_fullname': None,
        'key_owner_edrpou': None,
        'key_owner_fullname': None,
        'key_owner_position': None,
        'key_is_legal': True,
        'stamp': None,
        'stamp_exists': False,
        'stamp_acsk': None,
        'stamp_serial_number': None,
        'stamp_timemark': None,
        'stamp_company_fullname': None,
        'stamp_owner_edrpou': None,
        'stamp_owner_fullname': None,
        'stamp_owner_position': None,
        'stamp_is_legal': True,
    }

    if id:
        data['id'] = id

    if owner:
        data['user_role_id'] = owner.role_id
        data['user_id'] = owner.id
        data['user_email'] = owner.email
        data['owner_edrpou'] = owner.company_edrpou

    # You can override an owner fields by passing them as kwargs
    if role_id:
        data['user_role_id'] = role_id
    if user_id:
        data['user_id'] = user_id
    if user_email:
        data['user_email'] = user_email
    if owner_edrpou:
        data['owner_edrpou'] = owner_edrpou

    if _set_key_fields:
        data.update(
            {
                'key': 'key',
                'key_exists': True,
                'key_acsk': 'key_acsk',
                'key_serial_number': str(uuid.uuid4()),
                'key_timemark': utc_now(),
                'key_company_fullname': TEST_COMPANY_NAME,
                'key_owner_edrpou': owner_edrpou,
                'key_owner_fullname': TEST_USER_FULL_NAME,
                'key_owner_position': None,
                'key_is_legal': True,
            }
        )

    if _set_stamp_fields:
        data.update(
            {
                'stamp': 'stamp',
                'stamp_exists': True,
                'stamp_acsk': 'stamp_acsk',
                'stamp_serial_number': str(uuid.uuid4()),
                'stamp_timemark': utc_now(),
                'stamp_company_fullname': TEST_COMPANY_NAME,
                'stamp_owner_edrpou': owner_edrpou,
                'stamp_owner_fullname': TEST_USER_FULL_NAME,
                'stamp_owner_position': None,
                'stamp_is_legal': True,
            }
        )

    # You can override auto-generated key or stamp fields by passing them as kwargs
    data.update(kwargs)

    async with services.db.acquire() as conn:
        return await insert_signature(conn, data)


async def prepare_signature_data(
    app,
    owner,
    document,
    private_key=None,
    is_owner_signature=True,
    *,
    date_created=None,
    is_first_sign_by_recipient_document=False,
    next_status_id=None,
    is_internal=False,
    update_access=True,
    internal_file_name=None,
    is_valid=True,
    **kwargs,
) -> Signature:
    if private_key is None:
        private_key = prepare_private_key(owner, is_internal=is_internal)
    data = {
        'is_first_sign_by_recipient_document': is_first_sign_by_recipient_document,
        'is_owner_signature': is_owner_signature,
        'document_id': document.id,
        'edrpou_recipient': document.edrpou_recipient,
        'emails_recipient': split_comma_separated_emails(document.email_recipient),
        'user_company_id': owner.company_id,
        'user_id': owner.id,
        'user_is_legal': owner.is_legal,
        'user_edrpou': owner.company_edrpou,
        'user_email': owner.email,
        'user_role': owner.user_role,
        'user_role_id': owner.role_id,
        'next_status_id': next_status_id
        or getattr(DocumentStatus, 'signed_and_sent' if is_owner_signature else 'finished').value,
        'date_created': date_created,
        'internal_file_name': internal_file_name,
        'is_recipient_emails_hidden': False,
        'format': (
            SignatureFormat.internal_separated
            if private_key.is_internal
            else SignatureFormat.external_separated
        ),
        'algo': None,
        'is_valid': is_valid,
    }
    data.update(private_key._asdict())
    data.update(kwargs)

    async with app['db'].acquire() as conn:
        # Prepare uploader company_id, to use in decisions with upload counter
        uploader_company = await select_company_by_role_id(conn, document.uploaded_by)
        data['uploader_company_id'] = getattr(uploader_company, 'id', None)

        # Also company must have a billing account
        accounts = await billing_select_company_accounts(conn, owner.company_id)
        if not len(accounts):
            await prepare_billing_account(conn, owner.company_id)

        signature = await insert_signature(conn, data)
        if update_access and not is_first_sign_by_recipient_document and is_owner_signature:
            # blackbox doesn't support multiple signatures
            emails = get_list_or_none(data, 'email_recipient')
            await create_document_access_for_recipient(
                conn=conn,
                document_id=data['document_id'],
                edrpou=data['edrpou_recipient'],
                emails=emails,
            )

    # Upload signature content to S3
    if signature.is_internal:
        await s3_utils.upload(
            item=s3_utils.UploadFile(
                key=get_internal_signature_s3_key(
                    document_id=signature.document_id,
                    signature_id=signature.id,
                ),
                body=TEST_INTERNAL_SIGNATURE_CONTENT,
            )
        )
    else:
        if signature.key_exists:
            await s3_utils.upload(
                item=s3_utils.UploadFile(
                    key=get_external_key_signature_s3_key(
                        document_id=signature.document_id,
                        signature_id=signature.id,
                    ),
                    body=TEST_EXTERNAL_KEY_SIGNATURE_CONTENT,
                )
            )
        if signature.stamp_exists:
            await s3_utils.upload(
                item=s3_utils.UploadFile(
                    key=get_external_stamp_signature_s3_key(
                        document_id=signature.document_id,
                        signature_id=signature.id,
                    ),
                    body=TEST_EXTERNAL_STAMP_SIGNATURE_CONTENT,
                )
            )

    return signature


def prepare_signature_form_data(
    user=None,
    *,
    owner=None,
    company_edrpou=None,
    recipient=None,
    recipient_edrpou=None,
    recipient_email=None,
    is_legal=True,
    p7s=None,
    archive=None,
    append_key_data=True,
    append_stamp_data=False,
    key=None,
    key_serial_number=None,
    stamp=None,
    stamp_serial_number=None,
    format_: str | None = None,
    fields: DataDict | None = None,
    content: bytes | None = None,
    algo: SignatureAlgo | None = None,
    company_name: str | None = None,
) -> FormData:
    recipient_edrpou = recipient_edrpou or (recipient.company_edrpou if recipient else None)
    recipient_email = recipient_email or (recipient.email if recipient else None)

    default_format = (
        SignatureFormat.internal_separated.value
        if p7s
        else SignatureFormat.external_separated.value
    )
    data = {
        'edrpou_owner': owner.company_edrpou if owner else None,
        'email_owner': owner.email if owner else None,
        'edrpou_recipient': recipient_edrpou,
        'email_recipient': recipient_email,
        'key': key,
        'stamp': stamp,
        'archive': archive,
        'format': format_ or default_format,
        'algo': algo.value if algo else None,
    }
    if fields is not None:
        data['fields'] = fields

    if company_name is not None:
        data['company_name'] = company_name

    private_key = prepare_private_key(
        user=user,
        company_edrpou=company_edrpou,
        is_legal=is_legal,
        key_serial_number=key_serial_number,
        stamp_serial_number=stamp_serial_number,
        key=key,
        stamp=stamp,
    )

    if append_key_data:
        data.update(
            {
                'key': private_key.key,
                'key_acsk': private_key.key_acsk,
                'key_serial_number': private_key.key_serial_number,
                'key_timemark': private_key.key_timemark.isoformat(),
                'key_company_fullname': private_key.key_company_fullname,
                'key_owner_edrpou': private_key.key_owner_edrpou,
                'key_owner_fullname': private_key.key_owner_fullname,
                'key_owner_position': private_key.key_owner_position,
                'key_is_legal': private_key.key_is_legal,
            }
        )

    if append_stamp_data:
        data.update(
            {
                'stamp': private_key.stamp,
                'stamp_acsk': private_key.stamp_acsk,
                'stamp_serial_number': private_key.stamp_serial_number,
                'stamp_timemark': private_key.stamp_timemark.isoformat(),
                'stamp_company_fullname': private_key.stamp_company_fullname,
                'stamp_owner_edrpou': private_key.stamp_owner_edrpou,
                'stamp_owner_fullname': private_key.stamp_owner_fullname,
                'stamp_owner_position': private_key.stamp_owner_position,
                'stamp_is_legal': private_key.stamp_is_legal,
            }
        )

    # By default, ujson >= 3.0.0 rejects input of type `bytes` to be compatible
    # with the built-in `json` library
    # (https://github.com/ultrajson/ultrajson/releases/tag/3.0.0)
    form_data = FormData({'data': ujson.dumps(data, reject_bytes=False)})
    if p7s:
        form_data.add_field('p7s', p7s, filename='p7s.p7s')
    if content:
        form_data.add_field('p7s', content, filename='signature')
    return form_data


def prepare_upload_file(**kwargs):
    kwargs.setdefault('id', str(uuid.uuid4()))
    kwargs.setdefault('uploaded_by', str(uuid.uuid4()))
    kwargs.setdefault('title', TEST_DOCUMENT_TITLE)
    kwargs.setdefault('extension', 'pdf')
    kwargs.setdefault('status_id', DocumentStatus.uploaded.value)
    kwargs.setdefault('archive_name', None)
    kwargs.setdefault('edrpou_owner', TEST_DOCUMENT_EDRPOU_OWNER)
    kwargs.setdefault('ipn_owner', TEST_DOCUMENT_IPN_OWNER)
    kwargs.setdefault('has_changed_for_public_api', True)
    kwargs.setdefault('edrpou_recipient', [TEST_DOCUMENT_EDRPOU_RECIPIENT])
    kwargs.setdefault('ipn_recipient', TEST_DOCUMENT_IPN_RECIPIENT)
    kwargs.setdefault('recipient_emails', TEST_DOCUMENT_EMAIL_RECIPIENT)
    kwargs.setdefault('hide_recipient_emails', None)
    kwargs.setdefault('company_name_recipient', TEST_DOCUMENT_COMPANY_NAME_RECIPIENT)
    kwargs.setdefault('company_short_name_recipient', None)
    kwargs.setdefault('date_document', None)
    kwargs.setdefault('amount', None)
    kwargs.setdefault('type_', None)
    kwargs.setdefault('source', DocumentSource.vchasno.value)
    kwargs.setdefault('vendor', None)
    kwargs.setdefault('vendor_id', None)
    kwargs.setdefault('is_internal', False)
    kwargs.setdefault('is_multilateral', False)
    kwargs.setdefault('expected_owner_signatures', 1)
    kwargs.setdefault('expected_recipient_signatures', 1)
    kwargs.setdefault('first_sign_by', FirstSignBy.owner.value)
    kwargs.setdefault('body', None)
    kwargs.setdefault('content_hash', None)
    kwargs.setdefault('content_type', None)
    kwargs.setdefault('file_name', None)
    kwargs.setdefault('file_size', None)
    kwargs.setdefault('flows_settings', None)
    kwargs.setdefault('signature_format', SignatureFormat.external_separated)
    kwargs.setdefault('category', None)
    return File(**kwargs)


async def prepare_uploaded_signed_documents(
    app,
    user,
    recipient_data=None,
    private_key=None,
    total_count=1,
    is_owner_signature=False,
    **kwargs: Any,
):
    if recipient_data is None:
        recipient = await prepare_user_data(
            app,
            company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
            email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        )
        kwargs['another_recipients'] = [recipient]
    else:
        kwargs['document_recipients'] = recipient_data

    documents_ids = []

    for _ in range(total_count):
        document = await prepare_document_data(app, user, **kwargs)
        documents_ids.append(document.id)
        await prepare_signature_data(
            app,
            user,
            document,
            private_key,
            is_owner_signature,
        )

    return documents_ids


async def prepare_document_upload(
    app,
    client,
    *,
    user,
    # URL parameters
    parent_id=None,
    email_owner=None,
    recipient_edrpou=None,
    recipient_emails=None,
    hide_recipient_emails=None,
    share_to=None,
    signer_roles=None,
    signer_emails=None,
    parallel_signing=None,
    source=None,
    token_vendor=None,
    first_sign_by=None,
    is_internal=None,
    is_multilateral=None,
    expected_owner_signatures=None,
    expected_recipient_signatures=None,
    tags=None,
    new_tags=None,
    parallel_review=None,
    is_required_review=None,
    reviewers_ids=None,
    reviewers=None,
    filename=None,
    raw_response: bool = False,
    signature_format: str | None = None,
    is_chargeless: bool = False,
    template_id: str | None = None,
    category: int | None = None,
):
    filename = filename or f'{str(uuid.uuid4())}.txt'
    params = {
        'parent_id': parent_id,
        'email_owner': email_owner,
        'recipient_edrpou': recipient_edrpou,
        'recipient_emails': recipient_emails,
        'hide_recipient_emails': hide_recipient_emails,
        'share_to': share_to,
        'signer_roles': signer_roles,
        'signer_emails': signer_emails,
        'parallel_signing': parallel_signing,
        'source': source,
        'token_vendor': token_vendor,
        'first_sign_by': first_sign_by,
        'is_internal': is_internal,
        'is_multilateral': is_multilateral,
        'expected_owner_signatures': expected_owner_signatures,
        'expected_recipient_signatures': expected_recipient_signatures,
        'tags': tags,
        'new_tags': new_tags,
        'parallel_review': parallel_review,
        'is_required_review': is_required_review,
        'reviewers_ids': reviewers_ids,
        'signature_format': signature_format,
        'is_chargeless': is_chargeless,
        'template_id': template_id,
        'category': category,
    }
    prepared_params = MultiDict()
    convert_mapping = {
        True: '1',
        False: '0',
    }
    for key, value in params.items():
        if value is None:
            continue

        if isinstance(value, list):
            for item in value:
                prepared_params.add(key, convert_mapping.get(item, item))
        else:
            prepared_params[key] = convert_mapping.get(value, value)

    headers = prepare_auth_headers(user)
    data = FormData()
    data.add_field(
        'file',
        io.BytesIO(b'1234'),
        filename=filename,
    )
    if reviewers:
        data.add_field('params', value=to_json({'reviewers': reviewers}))

    response = await client.post(
        UPLOAD_DOCUMENT_URL,
        headers=headers,
        params=prepared_params,
        data=data,
    )
    if raw_response:
        return response

    assert response.status == HTTPStatus.CREATED, await response.json()

    title = '.'.join(filename.split('.')[:-1])
    async with app['db'].acquire() as conn:
        document = await select_one(
            conn, (sa.select([document_table]).where(document_table.c.title == title))
        )
    assert document is not None, 'Check file name, it must be unique for test'
    return document


async def request_document_update(
    client,
    *,
    user,
    document,
    signers: list[User | DBRow] | None = None,
    signers_settings: DataDict | None = None,
    parallel_signing: bool = True,
    viewers=None,
    document_settings=None,
    recipients_settings=None,
    recipients: list[DBRow] | None = None,
    recipients_is_ordered: bool | None = None,
    version_settings=None,
    tags_settings=None,
    reviews_settings=None,
    reviewers: list[DBRow] | None = None,
    reviews_is_ordered: bool | None = None,
    reviews_is_required: bool | None = None,
    excepted_status: HTTPStatus | None = None,
):
    data = {}

    if signers_settings is not None:
        data['signers_settings'] = signers_settings
    elif signers is not None:
        data['signers_settings'] = {
            'entities': [{'id': signer.role_id, 'type': 'role'} for signer in signers],
            'parallel_signing': parallel_signing,
        }

    if viewers is not None:
        data['viewers_settings'] = {'add_viewers': [viewer.role_id for viewer in viewers]}

    if document_settings is not None:
        data['document_settings'] = {
            'date_document': document.date_document,
            'number': document.number,
            'title': document.title,
            'category': document.category,
            **document_settings,
        }

    if recipients_settings is not None:
        data['recipients_settings'] = recipients_settings
    elif recipients is not None:
        data['recipients_settings'] = {
            'is_ordered': recipients_is_ordered or False,
            'recipients': [
                {
                    'edrpou': r.company_edrpou,
                    'emails': [r.email],
                    'is_email_hidden': False,
                    'role': 'signer',
                }
                for r in recipients
            ],
        }

    if reviews_settings is not None:
        data['reviews_settings'] = reviews_settings
    elif reviewers is not None:
        data['reviews_settings'] = {
            'reviewers': [{'id': r.role_id, 'type': 'role'} for r in reviewers],
            'is_required': reviews_is_required or False,
            'is_ordered': reviews_is_ordered or False,
        }

    if version_settings is not None:
        data['version_settings'] = version_settings

    if tags_settings is not None:
        data['tags_settings'] = tags_settings

    response = await client.patch(
        UPDATE_DOCUMENT_URL.format(document_id=document.id),
        json=data,
        headers=prepare_auth_headers(user),
    )
    if excepted_status:
        assert response.status == excepted_status, await response.json()
    return response


async def request_document_update_bilateral_recipient(
    client,
    *,
    owner: DBRow,
    document: DBRow,
    recipient: DBRow | None = None,
    recipient_edrpou: str | None = None,
    recipient_email: str | None = None,
    first_sign_by: FirstSignBy = FirstSignBy.owner,
    is_expected_owner_signature: bool = True,
    is_expected_recipient_signature: bool = True,
    expected_status: HTTPStatus | None = HTTPStatus.OK,
) -> web.Response:
    recipient_edrpou: str | None = recipient_edrpou
    recipient_email: str | None = recipient_email
    if recipient and not recipient_edrpou:
        recipient_edrpou = recipient.company_edrpou
    if recipient and not recipient_email:
        recipient_email = recipient.email

    assert recipient_edrpou is not None and recipient_email is not None, (
        'Recipient EDRPOU and email must be provided'
    )

    recipient_settings = {
        'edrpou': recipient_edrpou,
        'emails': [recipient_email],
        'is_email_hidden': False,
        'role': 'signer' if is_expected_recipient_signature else 'viewer',
    }
    owner_setting = {
        'edrpou': owner.company_edrpou,
        'emails': [owner.email],
        'is_email_hidden': False,
        'role': 'signer' if is_expected_owner_signature else 'viewer',
    }

    if first_sign_by == FirstSignBy.owner:
        recipients = [owner_setting, recipient_settings]
    else:
        recipients = [recipient_settings, owner_setting]

    response = await request_document_update(
        client=client,
        user=owner,
        document=document,
        recipients_settings={'is_ordered': True, 'recipients': recipients},
    )
    if expected_status:
        assert response.status == HTTPStatus.OK, await response.json()

    return response


async def prepare_base_user_data(
    app,
    *,
    user_id: str | None = None,
    email: str = TEST_USER_EMAIL,
    password: str = TEST_USER_PASSWORD,
    email_confirmed: bool = True,
    registration_completed: bool = True,
) -> None:
    """Prepare basic user without role and company"""

    data = {
        'email': email,
        'password': password,
        'email_confirmed': email_confirmed,
        'registration_completed': registration_completed,
    }
    if user_id is not None:
        data['id'] = user_id

    async with app['db'].acquire() as conn:
        return await insert_test_user(conn, data=data)


async def prepare_role(
    *,
    user_id: str,
    company_id: str,
    role_id=None,
    user_role=UserRole.admin.value,
    role_status=RoleStatus.active,
    can_view_document=True,
    can_comment_document=True,
    can_upload_document=True,
    can_download_document=True,
    can_print_document=True,
    can_delete_document=True,
    can_sign_and_reject_document=True,
    can_invite_coworkers=True,
    can_edit_company=None,
    can_edit_roles=None,
    can_create_tags=True,
    can_edit_document_automation=True,
    can_edit_document_fields=True,
    can_edit_document_category=True,
    can_receive_inbox=True,
    can_receive_comments=True,
    can_receive_rejects=True,
    can_receive_reminders=True,
    can_receive_reviews=True,
    can_receive_review_process_finished=False,
    can_receive_sign_process_finished=False,
    can_receive_access_to_doc=True,
    can_receive_delete_requests=True,
    can_receive_finished_docs=False,
    can_archive_documents=True,
    can_receive_email_change=False,
    can_delete_archived_documents=True,
    can_view_coworkers=True,
    can_edit_templates=True,
    can_edit_directories=True,
    can_remove_itself_from_approval=False,
    role_position=None,
    has_hrs_role=False,
    can_receive_notifications=True,
    allowed_ips=None,
    allowed_api_ips=None,
    is_default_recipient=False,
    sort_documents=None,
    date_agreed=None,
    date_created=None,
) -> DBRow:
    date_created = date_created or sa.text('now()')
    is_admin = user_role == UserRole.admin.value
    if can_edit_company is None:
        can_edit_company = bool(is_admin)
    if can_edit_roles is None:
        can_edit_roles = bool(is_admin)

    role_id = role_id or str(uuid.uuid4())
    async with services.db.acquire() as conn:
        return await insert_role(
            conn=conn,
            data={
                'id': role_id,
                'user_id': user_id,
                'company_id': company_id,
                'user_role': user_role,
                'status': role_status,
                'can_view_document': can_view_document,
                'can_comment_document': can_comment_document,
                'can_upload_document': can_upload_document,
                'can_download_document': can_download_document,
                'can_print_document': can_print_document,
                'can_delete_document': can_delete_document,
                'can_sign_and_reject_document': can_sign_and_reject_document,
                'can_invite_coworkers': can_invite_coworkers,
                'can_edit_company': can_edit_company,
                'can_edit_roles': can_edit_roles,
                'can_create_tags': can_create_tags,
                'can_edit_document_automation': can_edit_document_automation,
                'can_edit_document_fields': can_edit_document_fields,
                'can_edit_document_category': can_edit_document_category,
                'can_archive_documents': can_archive_documents,
                'can_delete_archived_documents': can_delete_archived_documents,
                'can_view_coworkers': can_view_coworkers,
                'can_edit_templates': can_edit_templates,
                'can_edit_directories': can_edit_directories,
                'can_remove_itself_from_approval': can_remove_itself_from_approval,
                'can_receive_inbox': can_receive_inbox,
                'can_receive_comments': can_receive_comments,
                'can_receive_rejects': can_receive_rejects,
                'can_receive_reminders': can_receive_reminders,
                'can_receive_reviews': can_receive_reviews,
                'can_receive_review_process_finished': can_receive_review_process_finished,
                'can_receive_sign_process_finished': can_receive_sign_process_finished,
                'can_receive_access_to_doc': can_receive_access_to_doc,
                'can_receive_delete_requests': can_receive_delete_requests,
                'can_receive_finished_docs': can_receive_finished_docs,
                'can_receive_notifications': can_receive_notifications,
                'has_hrs_role': has_hrs_role,
                'can_receive_email_change': can_receive_email_change,
                'allowed_ips': allowed_ips or [],
                'allowed_api_ips': allowed_api_ips,
                'sort_documents': sort_documents,
                'position': role_position,
                'is_default_recipient': is_default_recipient,
                'date_agreed': date_agreed or sa.text('now()'),
                'date_created': date_created,
            },
        )


async def prepare_coworker_role(conn: DBConnection, data: DataDict):
    company_id = data['company_id']
    assert company_id, 'Company ID is required'

    config = await get_company_config(conn, company_id=company_id)
    set_default_role_settings(
        data=data,
        default=config.default_role_permissions,
        invited_by=None,
    )

    data.setdefault('date_updated', sa.text('now()'))

    return await insert_role(conn, data=data)


async def prepare_mobile_active_session(conn, user):
    # Insert mobile auth refresh tokens
    access_token = mobile_validators.generate_access_token()
    _refresh_token = mobile_validators.generate_refresh_token()

    refresh_token = await insert_mobile_auth_refresh_token(
        conn=conn,
        user_id=user.id,
        token_hash=_refresh_token.token_hash,
        access_token_hash=access_token.token_hash,
        status=MobileAuthRefreshTokenStatus.ready,
    )
    await save_access_token(
        user_id=user.id,
        access_token_hash=access_token.token_hash,
        refresh_token_id=refresh_token.id,
        is_2fa_required=False,
        expires_in=3600,
    )

    # Insert firebase id
    await process_set_firebase_id(
        conn=conn,
        token_id=refresh_token.id,
        firebase_id='TEST_FIREBASE_ID',
    )


async def insert_test_user(
    conn: DBConnection,
    data: DataDict,
) -> BaseUser:
    """Insert new User instance into database.

    After insertion is done return new user data from DB via additional hit.
    """
    user_data = dict(data)

    # Generate password hash from raw string
    password_raw: str | None = user_data['password']
    if password_raw is not None:
        password_hash = generate_password_hash(password_raw)
        user_data['password'] = password_hash

    # Default values
    user_data.setdefault('phone', '')
    user_data.setdefault('email_confirmed', False)
    user_data.setdefault('registration_completed', False)
    user_data.setdefault('is_autogenerated_password', False)
    user_data.setdefault('date_updated', sa.text('now()'))

    async with conn.begin():
        vchasno_user_data = {
            'email': user_data['email'],
            'first_name': user_data.get('first_name'),
            'second_name': user_data.get('second_name'),
            'last_name': user_data.get('last_name'),
        }
        if 'id' in user_data:
            vchasno_user_data['id'] = user_data['id']

        row = await select_one(
            conn=conn,
            query=(
                insert(vchasno_user_table).values(vchasno_user_data).returning(vchasno_user_table)
            ),
        )
        user_data['id'] = row['id']

        row = await select_one(
            conn=conn,
            query=user_table.insert().values(user_data).returning(user_table),
        )
        return BaseUser.from_row(row)


async def prepare_user_data(
    app,
    *,
    user_id=None,
    is_legal=True,
    company_id=None,
    company_edrpou=TEST_COMPANY_EDRPOU,
    company_ipn=None,
    company_name=None,
    company_full_name=None,
    company_email_domains=None,
    role_id=None,
    user_role=UserRole.admin.value,
    role_status=RoleStatus.active,
    can_view_document=True,
    can_comment_document=True,
    can_upload_document=True,
    can_download_document=True,
    can_print_document=True,
    can_delete_document=True,
    can_sign_and_reject_document=True,
    can_invite_coworkers=True,
    can_edit_company=None,
    can_edit_roles=None,
    can_create_tags=True,
    can_edit_document_automation=True,
    can_edit_document_fields=True,
    can_edit_document_category=True,
    can_receive_inbox=True,
    can_receive_inbox_as_default=True,
    can_receive_comments=True,
    can_receive_rejects=True,
    can_receive_reminders=True,
    can_receive_reviews=True,
    can_receive_review_process_finished=False,
    can_receive_review_process_finished_assigner=True,
    can_receive_sign_process_finished=False,
    can_receive_sign_process_finished_assigner=True,
    can_receive_access_to_doc=True,
    can_receive_delete_requests=True,
    can_receive_finished_docs=False,
    can_receive_new_roles=False,
    can_receive_email_change=False,
    can_receive_token_expiration=False,
    can_receive_admin_role_deletion=False,
    can_archive_documents=True,
    can_delete_archived_documents=True,
    can_view_coworkers=True,
    can_edit_templates=True,
    can_edit_directories=True,
    can_edit_company_contact=False,
    can_remove_itself_from_approval=False,
    can_view_private_document=False,
    role_position=None,
    can_receive_notifications=True,
    allowed_ips=None,
    allowed_api_ips=None,
    is_default_recipient=False,
    sort_documents=None,
    date_agreed=None,
    date_created=None,
    token_vendor=Vendor.onec.value,
    create_billing_account=False,
    create_api_account=False,
    is_dealer=False,
    with_upload_documents_left=True,
    enable_pro_functionality=True,
    token_date_expired=None,
    super_admin_permissions: dict | None = None,
    create_mobile_active_session: bool = False,
    company_inactivity_timeout: int = 3600,
    has_hrs_role: bool = False,
    is_counted_in_billing_limit: bool = True,
    **kwargs,
) -> DBRow:
    date_created = date_created or sa.text('now()')
    kwargs.setdefault('email', TEST_USER_EMAIL)
    kwargs.setdefault('password', TEST_USER_PASSWORD)
    kwargs.setdefault('phone', TEST_USER_PHONE)
    kwargs.setdefault('email_confirmed', True)
    kwargs.setdefault('registration_completed', True)
    kwargs.setdefault('is_logged_once', True)
    kwargs.setdefault('telegram_chat_id', TEST_USER_TELEGRAM_CHAT_ID)
    if user_id is not None:
        kwargs['id'] = user_id
    kwargs['date_created'] = date_created

    is_admin = user_role == UserRole.admin.value
    if can_edit_company is None:
        can_edit_company = bool(is_admin)
    if can_edit_roles is None:
        can_edit_roles = bool(is_admin)

    async with services.db.acquire() as conn:
        async with conn.begin():
            if company_id:
                is_company_exists = await select_company_by_id(conn, company_id)

            if not company_id or not is_company_exists:
                company_id = await insert_company(
                    conn,
                    {
                        'id': company_id or str(uuid.uuid4()),
                        'edrpou': company_edrpou,
                        'ipn': company_ipn,
                        'name': company_name,
                        'full_name': company_full_name or company_name,
                        'date_created': date_created,
                        'email_domains': company_email_domains,
                        'is_legal': is_legal,
                        'is_dealer': is_dealer,
                        'upload_documents_left': (
                            TEST_UPLOAD_LEFT_COUNTER if with_upload_documents_left else 0
                        ),
                        'inactivity_timeout': company_inactivity_timeout,
                    },
                    ignore_existing=True,
                )

            user = await insert_test_user(conn, kwargs)
            role_id = role_id or str(uuid.uuid4())
            role = await insert_role(
                conn,
                {
                    'id': role_id,
                    'user_id': user.id,
                    'company_id': company_id,
                    'user_role': user_role,
                    'status': role_status,
                    'can_view_document': can_view_document,
                    'can_comment_document': can_comment_document,
                    'can_upload_document': can_upload_document,
                    'can_download_document': can_download_document,
                    'can_print_document': can_print_document,
                    'can_delete_document': can_delete_document,
                    'can_sign_and_reject_document': can_sign_and_reject_document,
                    'can_invite_coworkers': can_invite_coworkers,
                    'can_edit_company': can_edit_company,
                    'can_edit_roles': can_edit_roles,
                    'can_create_tags': can_create_tags,
                    'can_edit_document_automation': can_edit_document_automation,
                    'can_edit_document_fields': can_edit_document_fields,
                    'can_edit_document_category': can_edit_document_category,
                    'can_archive_documents': can_archive_documents,
                    'can_delete_archived_documents': can_delete_archived_documents,
                    'can_view_coworkers': can_view_coworkers,
                    'can_edit_templates': can_edit_templates,
                    'can_edit_directories': can_edit_directories,
                    'can_edit_company_contact': can_edit_company_contact,
                    'can_remove_itself_from_approval': can_remove_itself_from_approval,
                    'can_view_private_document': can_view_private_document,
                    'can_receive_inbox': can_receive_inbox,
                    'can_receive_inbox_as_default': can_receive_inbox_as_default,
                    'can_receive_comments': can_receive_comments,
                    'can_receive_rejects': can_receive_rejects,
                    'can_receive_reminders': can_receive_reminders,
                    'can_receive_reviews': can_receive_reviews,
                    'can_receive_review_process_finished': can_receive_review_process_finished,
                    'can_receive_review_process_finished_assigner': can_receive_review_process_finished_assigner,  # noqa: E501
                    'can_receive_sign_process_finished': can_receive_sign_process_finished,
                    'can_receive_sign_process_finished_assigner': can_receive_sign_process_finished_assigner,  # noqa: E501
                    'can_receive_access_to_doc': can_receive_access_to_doc,
                    'can_receive_delete_requests': can_receive_delete_requests,
                    'can_receive_finished_docs': can_receive_finished_docs,
                    'can_receive_notifications': can_receive_notifications,
                    'can_receive_new_roles': can_receive_new_roles,
                    'can_receive_email_change': can_receive_email_change,
                    'can_receive_token_expiration': can_receive_token_expiration,
                    'can_receive_admin_role_deletion': can_receive_admin_role_deletion,
                    'allowed_ips': allowed_ips or [],
                    'allowed_api_ips': allowed_api_ips,
                    'sort_documents': sort_documents,
                    'position': role_position,
                    'has_hrs_role': has_hrs_role,
                    'is_counted_in_billing_limit': is_counted_in_billing_limit,
                    'is_default_recipient': is_default_recipient,
                    'date_agreed': date_agreed or sa.text('now()'),
                    'date_created': date_created,
                },
            )
            await update_user(
                conn=conn,
                user_id=user.id,
                data={'last_role_id': role_id},
            )
            role_id = role.id
            token = await insert_token(
                conn,
                {
                    'role_id': role_id,
                    'vendor': token_vendor,
                    'date_expired': token_date_expired,
                },
            )

            # only web accounts
            if create_billing_account:
                await prepare_billing_account(conn, company_id)

            # only api accounts
            if create_api_account:
                await prepare_api_billing_account(conn, company_id)

            if enable_pro_functionality:
                await prepare_pro_functionality(
                    conn=conn,
                    company_id=company_id,
                    company_edrpou=company_edrpou,
                )

            if create_mobile_active_session:
                await prepare_mobile_active_session(conn, user)

        user = await select_user_by_token_hash(
            conn=conn,
            token_hash=generate_hash_sha512(token),
            raw_token=token,
            ensure_active_user=False,
        )

        await upsert_super_admin_permissions(conn, role_id, super_admin_permissions, user)

        # Since the function has previously inserted a user, the query should
        # return a DBRow, and not None
        user = cast(DBRow, user)
        return user


async def sign_and_send_document(
    client,
    document_id,
    signer,
    *,
    recipient_edrpou=None,
    recipient_email=None,
    sign_data=None,
    send_data=None,
    headers=None,
    append_key_data: bool = True,
    append_stamp_data: bool = False,
    content: bytes | None = None,
    format_: SignatureFormat | None = None,
    fields: DataDict | None = None,
):
    headers = headers or prepare_auth_headers(signer)

    data = sign_data or prepare_signature_form_data(
        signer,
        recipient_edrpou=recipient_edrpou,
        recipient_email=recipient_email,
        format_=format_ and format_.value,
        content=content,
        fields=fields,
        append_key_data=append_key_data,
        append_stamp_data=append_stamp_data,
    )
    response = await client.post(
        f'/internal-api/documents/{document_id}/signatures',
        data=data,
        headers=headers,
    )
    assert response.status == HTTPStatus.CREATED, await response.json()

    data = EMPTY_JSON
    if send_data:
        data = send_data
    elif recipient_edrpou and recipient_email:
        data = ujson.dumps(
            {
                'edrpou': recipient_edrpou,
                'email': recipient_email,
            }
        )
    response = await client.post(
        SEND_DOCUMENT_URL.format(document_id=document_id),
        data=data,
        headers=headers,
    )
    assert response.status == HTTPStatus.OK, await response.json()


async def send_document(
    client,
    document_id,
    headers=None,
    sender=None,
    recipient_edrpou=None,
    recipient_email=None,
    raw_response=False,
):
    headers = headers or prepare_auth_headers(sender)

    data = {}
    if recipient_edrpou and recipient_email:
        data = {
            'edrpou': recipient_edrpou,
            'email': recipient_email,
        }
    response = await client.post(
        SEND_DOCUMENT_URL.format(document_id=document_id),
        json=data,
        headers=headers,
    )
    if raw_response:
        return response

    assert response.status == HTTPStatus.OK, await response.json()
    return response


async def sign_document(
    client,
    *,
    document_id,
    signer,
    recipient_edrpou=None,
    recipient_email=None,
    sign_data=None,
    headers=None,
    params=None,
):
    """
    Natural way to send signature to backend.
    Note if owner is sending document that recipient_edrpou and recipient_email
    is required
    """
    headers = headers or prepare_auth_headers(signer)

    data = sign_data or prepare_signature_form_data(
        signer,
        recipient_edrpou=recipient_edrpou,
        recipient_email=recipient_email,
    )
    return await client.post(
        f'/internal-api/documents/{document_id}/signatures',
        params=params,
        data=data,
        headers=headers,
    )


async def wait_streaming_response_eof(response):
    await response.content.wait_eof()
    await asyncio.sleep(0.1)


async def prepare_contacts(app, conn, user):
    # 1 case: contact with  existed edrpou in company table
    # isRegistered == True
    company_edrpou1 = '1111111'
    await prepare_user_data(app, company_edrpou=company_edrpou1, email='<EMAIL>')
    contact1 = TEST_CONTACT['company'].copy()
    contact1['edrpou'] = company_edrpou1
    contact1_person = TEST_CONTACT['person'].copy()
    contact1_person['email'] = '<EMAIL>'
    await prepare_contact(
        conn,
        user,
        contact1,
        [
            contact1_person,
        ],
    )
    # await insert_contact(conn, contact1, user.company_id)

    # 2 case: contact is not registered in system
    # isRegistered == False
    company_edrpou2 = '*********'
    contact2 = TEST_CONTACT['company'].copy()
    contact2['edrpou'] = company_edrpou2
    contact2_person1 = TEST_CONTACT['person'].copy()
    contact2_person1['email'] = '<EMAIL>'
    contact2_person2 = TEST_CONTACT['person'].copy()
    contact2_person2['email'] = '<EMAIL>'
    await prepare_contact(
        conn,
        user,
        contact2,
        [
            contact2_person1,
            contact2_person2,
        ],
    )
    # await insert_contact(conn, contact2, user.company_id)

    # 3 case: contact is registered but has denied role status
    # isRegistered == False
    company_edrpou3 = '********33'
    await prepare_user_data(
        app,
        company_edrpou=company_edrpou3,
        email='<EMAIL>',
        role_status=RoleStatus.denied,
    )

    contact3 = TEST_CONTACT['company'].copy()
    contact3['edrpou'] = company_edrpou3
    await prepare_contact(conn, user, contact3)

    # 4 case: contact is registered but without completed registration
    # isRegistered == False
    company_edrpou4 = '44444444'
    await prepare_user_data(
        app,
        company_edrpou=company_edrpou4,
        email='<EMAIL>',
        registration_completed=False,
    )

    contact4 = TEST_CONTACT['company'].copy()
    contact4['edrpou'] = company_edrpou4
    await prepare_contact(conn, user, contact4)

    # 5 case: company is registered but without roles
    # isRegistered == False
    company_edrpou5 = '55555555'
    company5 = TEST_COMPANY.copy()
    company5['edrpou'] = company_edrpou5
    await insert_company(conn, company5)

    contact5 = TEST_CONTACT['company'].copy()
    contact5['edrpou'] = company_edrpou5
    await prepare_contact(conn, user, contact5)

    # 6 case: company in contact is registered and
    # has registered and unregistered users
    # isRegistered == True
    company_edrpou6 = '66666666666'
    await prepare_user_data(
        app,
        company_edrpou=company_edrpou6,
        email='<EMAIL>',
        registration_completed=False,
    )
    await prepare_user_data(
        app,
        company_edrpou=company_edrpou6,
        email='<EMAIL>',
        registration_completed=True,
    )
    contact6 = TEST_CONTACT['company'].copy()
    contact6['edrpou'] = company_edrpou6
    await prepare_contact(conn, user, contact6)

    return (
        # registered contacts
        {
            company_edrpou1,
            company_edrpou6,
        },
        # unregistered contacts
        {
            company_edrpou2,
            company_edrpou3,
            company_edrpou4,
            company_edrpou5,
        },
    )


async def prepare_flow_item(
    app: web.Application | None,
    document_id: str,
    *,
    company_id: str | None = None,
    order: int | None = None,
    signatures_count: int | None = None,
    pending_signatures_count: int = 1,
    edrpou: str = TEST_COMPANY_EDRPOU,
    meta: DataDict | None = None,
    receivers: list[str] | None = None,
    receivers_id: str | None = None,
    create_access: bool = True,
    id: str | None = None,
    date_sent: datetime.datetime | None = None,
) -> FlowItem:
    assert not bool(receivers) or not bool(receivers_id), (
        "Don't use `receivers` and `receivers_id` at same time"
    )

    if signatures_count is None:
        signatures_count = pending_signatures_count

    async with services.db.acquire() as conn:
        if not receivers_id and not receivers:
            receivers = [TEST_USER_EMAIL]

        if receivers:
            recipients_data = await insert_recipients(
                conn,
                recipients=[
                    {
                        'emails': receivers,
                        'edrpou': edrpou or TEST_COMPANY_EDRPOU,
                        'document_id': document_id,
                        'from_flow': True,
                    }
                ],
                with_return=True,
            )
            receivers_id = recipients_data[0].id if recipients_data else None

        data = {
            'company_id': company_id,
            'order': order,
            'signatures_count': signatures_count,
            'pending_signatures_count': pending_signatures_count,
            'edrpou': edrpou or TEST_COMPANY_EDRPOU,
            'meta': FlowMeta.from_db(meta or {}).to_db(),
            'document_id': document_id,
            'receivers_id': receivers_id,
            'date_sent': date_sent,
        }
        if id:
            data['id'] = id
        flow_row = await select_one(
            conn=conn,
            query=(insert(doc_flow_table).values(data).returning(doc_flow_table.c.id)),
        )
        flow_item = await select_flow_by(conn, flow_id=flow_row.id)

        if create_access:
            listing_data = await _get_flows_listing_data(conn, flows=[flow_item])
            await insert_listings(conn=conn, data=listing_data)

    return flow_item


async def prepare_flows_rows(data: list[DataDict]) -> list[FlowItem]:
    if not data:
        return []

    async with services.db.acquire() as conn:
        rows = await select_all(
            conn=conn,
            query=(insert(doc_flow_table).values(data).returning(doc_flow_table)),
        )
        return [FlowItem.from_row(row) for row in rows]


async def prepare_delete_document_request(
    app,
    user,
    document_id,
    recipients_emails=None,
    message=None,
    status: DeleteRequestStatus | None = None,
    reject_message=None,
    receiver_edrpou=None,
):
    request = {
        'document_id': document_id,
        'initiator_role_id': user.role_id,
        'initiator_edrpou': user.company_edrpou,
        'recipients_emails': recipients_emails or [],
        'status': status or DeleteRequestStatus.new,
        'message': message or 'message',
        'reject_message': reject_message or None,
        'receiver_edrpou': receiver_edrpou,
    }
    async with app['db'].acquire() as conn:
        return await insert_delete_request(conn, request)


async def prepare_trigger_notification(app, values):
    async with app['db'].acquire() as conn:
        return await conn.execute(trigger_notification_table.insert().values(values))


async def prepare_document_access_via_tag(
    client,
    conn,
    user,
    *,
    document_ids,
    tags_names,
    roles_ids,
):
    # Create both tags with document
    response = await client.post(
        CREATE_TAG_MULTIPLE_URL,
        headers=prepare_auth_headers(user),
        json={'names': tags_names, 'documents_ids': document_ids},
    )
    assert response.status == HTTPStatus.CREATED, await response.json()
    tags = await select_tags_by_names(
        conn,
        tags_names=tags_names,
        company_id=user.company_id,
    )
    tags_ids = [tag.id for tag in tags]

    response = await client.post(
        CONNECT_TAG_ROLES_URL,
        headers=prepare_auth_headers(user),
        json={'tags_ids': tags_ids, 'roles_ids': roles_ids},
    )
    assert response.status == HTTPStatus.CREATED, await response.json()
    return tags_ids


async def prepare_review_requests(
    client,
    document,
    initiator,
    *,
    reviewers,
    is_required=False,
    is_ordered=False,
    expected_status: HTTPStatus = HTTPStatus.OK,
) -> ClientResponse:
    """Create review request via internal API"""

    async def get_assert_message(response):
        return await response.json() or response.status

    reviewers_entity = []
    for reviewer in reviewers:
        try:
            id_, type_ = reviewer.role_id, ROLE
        except AttributeError:
            id_, type_ = reviewer.id, GROUP
        reviewers_entity.append({'id': id_, 'type': type_})
    params = {
        'reviews_settings': {
            'reviewers': reviewers_entity,
            'is_required': is_required,
            'is_ordered': is_ordered,
        },
    }

    response = await client.patch(
        UPDATE_DOCUMENT_URL.format(document_id=document.id),
        json=params,
        headers=prepare_auth_headers(initiator),
    )
    assert response.status == expected_status, await get_assert_message(response)
    return response


async def prepare_review(
    client,
    *,
    user,
    document,
    review_type: ReviewType | str | None,
    expected_status: HTTPStatus = HTTPStatus.CREATED,
):
    if isinstance(review_type, ReviewType):
        review_type = review_type.value

    response = await client.post(
        REVIEW_URL,
        headers=prepare_auth_headers(user),
        json={'document_id': document.id, 'type': review_type},
    )

    assert response.status == expected_status, await response.json() or response.status


async def prepare_review_db(
    document_id: str,
    user: DBRow,
    type: ReviewType = ReviewType.approve,
    date_created: str | None = None,
    request_source: Source = Source.api_internal,
) -> DBRow:
    data = {
        'document_id': document_id,
        'role_id': user.role_id,
        'type': type,
        'user_email': user.email,
    }
    if date_created is not None:
        data['date_created'] = date_created

    user = User.from_row(user)
    async with services.db.acquire() as conn:
        async with start_reviews_update_transaction(
            conn=conn,
            documents_ids=[document_id],
            user=user,
            request_source=request_source,
        ):
            reviews = await insert_document_reviews(conn, [data])
    return reviews[0]


async def prepare_batch_reviews(
    client,
    *,
    user,
    documents: list[Document],
    review_type: ReviewType | str | None,
    expected_status: HTTPStatus = None,
) -> list[DataDict]:
    if isinstance(review_type, ReviewType):
        review_type = review_type.value

    data = [{'document_id': document.id, 'type': review_type} for document in documents]
    response = await client.post(
        REVIEWS_URL,
        headers=prepare_auth_headers(user),
        json=data,
    )
    assert response.status == HTTPStatus.MULTI_STATUS
    data = await response.json()
    assert len(data) == len(documents)
    if expected_status:
        for document_response in data:
            assert_message = (document_response, expected_status.value)
            assert document_response['status'] == expected_status.value, assert_message
    return data


async def prepare_delete_review(
    client,
    *,
    user,
    document_id: str,
    expected_status: HTTPStatus = HTTPStatus.OK,
):
    url = DELETE_REVIEW_URL_TMPL.format(document_id=document_id)
    response = await client.delete(url, headers=prepare_auth_headers(user))
    assert response.status == expected_status, await response.json() or response.status


async def request_add_flows(
    *,
    client,
    user,
    document,
    recipients,
    is_ordered=False,
):
    def flow_data(recipient, index=None):
        item = {
            'edrpou': recipient.company_edrpou,
            'emails': [recipient.email],
            'sign_num': 1,
        }
        if index is not None:
            item['order'] = index
        return item

    flows_data = [
        flow_data(recipient, order if is_ordered else None)
        for order, recipient in enumerate(recipients)
    ]

    response = await client.post(
        API_V2_ADD_FLOW_API_URL.format(document_id=document.id),
        json=flows_data,
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.CREATED, await response.json()


async def request_add_comment(
    *,
    client,
    user,
    document: DBRow,
    comment: str,
    is_internal: bool = False,
    expected_status: HTTPStatus = HTTPStatus.CREATED,
):
    response = await client.post(
        ADD_COMMENT_URL.format(document_id=document.id),
        json={'text': comment, 'is_internal': is_internal},
        headers=prepare_auth_headers(user),
    )

    assert response.status == expected_status, await response.json()


async def request_find_recipients(
    *,
    client,
    user,
    edrpous,
    document=None,
    raw_response=False,
):
    data = {'edrpous': edrpous}
    if document:
        data['document_id'] = document.id

    response = await client.post(
        FIND_DOCUMENT_RECIPIENT_URL,
        json=data,
        headers=prepare_auth_headers(user),
    )
    if raw_response:
        return response

    json_data = await response.json()
    assert response.status == HTTPStatus.OK, json_data
    return json_data


async def request_create_tags_for_roles(
    *,
    client,
    user: DBRow,
    tags_names: list[str],
    roles_ids: list[str],
    expected_status: HTTPStatus = HTTPStatus.CREATED,
):
    response = await client.post(
        CREATE_TAG_FOR_ROLES_URL,
        headers=prepare_auth_headers(user),
        json={'names': tags_names, 'roles_ids': roles_ids},
    )
    assert response.status == expected_status, await response.json()
    return await response.json()


async def request_disconnect_roles_and_tags(
    *,
    client,
    user: DBRow,
    tags_ids: list[str],
    roles_ids: list[str],
    expected_status: HTTPStatus = HTTPStatus.OK,
):
    response = await client.delete(
        DISCONNECT_TAG_ROLES_URL,
        headers=prepare_auth_headers(user),
        json={'tags_ids': tags_ids, 'roles_ids': roles_ids},
    )
    json_data = await response.json()
    assert response.status == expected_status, json_data or response.status
    return json_data


async def request_create_tags_for_documents(
    *,
    client,
    user: DBRow,
    names: list[str],
    documents_ids: list[str],
    status: HTTPStatus = HTTPStatus.CREATED,
    raw_response: bool = False,
):
    response = await client.post(
        CREATE_TAG_MULTIPLE_URL,
        json={'names': names, 'documents_ids': documents_ids},
        headers=prepare_auth_headers(user),
    )
    if raw_response:
        return response

    json_data = await response.json()
    assert response.status == status, json_data
    return json_data


async def request_create_tags_for_contacts(
    *,
    client,
    user: DBRow,
    names: list[str],
    contacts_ids: list[str],
    status: HTTPStatus = HTTPStatus.CREATED,
    raw_response: bool = False,
):
    response = await client.post(
        CREATE_TAG_FOR_CONTACTS_URL,
        json={'names': names, 'contacts_ids': contacts_ids},
        headers=prepare_auth_headers(user),
    )
    if raw_response:
        return response

    json_data = await response.json()
    assert response.status == status, (json_data, response.status)
    return json_data


async def request_connect_tags_and_contacts(
    *,
    client,
    user: DBRow,
    tags_ids: list[str],
    contacts_ids: list[str],
    status: HTTPStatus = HTTPStatus.CREATED,
    raw_response: bool = False,
):
    response = await client.post(
        CONNECT_TAG_AND_CONTACTS,
        json={'tags_ids': tags_ids, 'contacts_ids': contacts_ids},
        headers=prepare_auth_headers(user),
    )
    if raw_response:
        return response

    json_data = await response.json()
    assert response.status == status, (json_data, response.status)
    return json_data


async def request_disconnect_tags_and_contacts(
    *,
    client,
    user: DBRow,
    tags_ids: list[str],
    contacts_ids: list[str],
    status: HTTPStatus = HTTPStatus.OK,
    raw_response: bool = False,
):
    response = await client.delete(
        DISCONNECT_TAG_AND_CONTACTS,
        json={'tags_ids': tags_ids, 'contacts_ids': contacts_ids},
        headers=prepare_auth_headers(user),
    )
    if raw_response:
        return response

    json_data = await response.json()
    assert response.status == status, (json_data, response.status)
    return json_data


async def request_connect_tags_and_documents(
    *,
    client,
    user: DBRow,
    tags_ids: list[str],
    documents_ids: list[str],
    status: HTTPStatus = HTTPStatus.OK,
    raw_response: bool = False,
):
    response = await client.post(
        CONNECT_MULTIPLE_TAGS_URL,
        json={'tags_ids': tags_ids, 'documents_ids': documents_ids},
        headers=prepare_auth_headers(user),
    )
    if raw_response:
        return response

    json_data = await response.json()
    assert response.status == status, json_data
    return json_data


async def request_disconnect_tags_and_documents(
    *,
    client,
    user: DBRow,
    tags_ids: list[str],
    documents_ids: list[str],
    status: HTTPStatus = HTTPStatus.OK,
    raw_response: bool = False,
):
    response = await client.delete(
        DISCONNECT_MULTIPLE_TAGS_URL,
        json={'tags_ids': tags_ids, 'documents_ids': documents_ids},
        headers=prepare_auth_headers(user),
    )
    if raw_response:
        return response

    json_data = await response.json()
    assert response.status == status, json_data
    return json_data


async def request_connect_tags_and_roles(
    *,
    client,
    user: DBRow,
    tags_ids: list[str],
    roles_ids: list[str],
    status: HTTPStatus = HTTPStatus.OK,
    raw_response: bool = False,
):
    response = await client.post(
        CONNECT_TAG_ROLES_URL,
        json={'tags_ids': tags_ids, 'roles_ids': roles_ids},
        headers=prepare_auth_headers(user),
    )
    if raw_response:
        return response

    json_data = await response.json()
    assert response.status == status, json_data
    return json_data


async def request_save_registration_info(
    *,
    client,
    user: DBRow,
    attempts: list[DataDict],
    status: HTTPStatus = HTTPStatus.CREATED,
    raw_response=False,
):
    response = await client.post(
        SAVE_REGISTRATION_INFO_URL,
        json={
            'attempts': attempts,
        },
        headers=prepare_auth_headers(user),
    )
    if raw_response:
        return response

    json_data = await response.json()
    assert response.status == status, json_data
    return json_data


async def request_delete_document(
    *,
    client,
    user: DBRow,
    document: DBRow,
    status: HTTPStatus = HTTPStatus.NO_CONTENT,
):
    response = await client.delete(
        DELETE_DOCUMENT_URL.format(document_id=document.id),
        headers=prepare_auth_headers(user),
    )
    assert response.status == status, (await response.json(), response.status)
    return await response.json()


async def request_update_trigger_notifications(
    *,
    client,
    user: DBRow,
    ids: list[str],
    status: HTTPStatus = HTTPStatus.OK,
):
    response = await client.patch(
        UPDATE_TRIGGER_NOTIFICATION_URL,
        json={'ids': ids},
        headers=prepare_auth_headers(user),
    )
    assert response.status == status, await response.json()


async def request_update_company_config(
    *,
    client,
    user: DBRow,
    company_id: str,
    config: DataDict,
    status: HTTPStatus = HTTPStatus.OK,
):
    response = await client.patch(
        UPDATE_COMPANY_CONFIG_URL.format(company_id=company_id),
        json=config,
        headers=prepare_auth_headers(user),
    )
    assert response.status == status, await response.text()


async def request_update_additional_company_config(
    *,
    client,
    user: DBRow,
    company_id: str,
    config: DataDict,
    status: HTTPStatus = HTTPStatus.OK,
):
    response = await client.patch(
        UPDATE_ADDITIONAL_COMPANY_CONFIG_URL,
        json=config,
        headers=prepare_auth_headers(user),
    )
    assert response.status == status, await response.text()


async def request_role_update(
    client: TestClient,
    user: DBRow,
    role_id: str,
    data: col_abc.Mapping[str, Any],
) -> ClientResponse:
    """Make a request to update a row on behalf of `user` and return the response.

    Args:
        client: the client used to make a request.
        user: the users on whose behalf to make a request.
        request: the request itself.
    """
    update_role_url = ROLE_URL.format(role_id=role_id)
    auth_headers = prepare_auth_headers(user)
    return await client.patch(update_role_url, json=data, headers=auth_headers)


async def select_all_rates(conn):
    return await select_all(
        conn=conn,
        query=(
            sa.select([billing_account_table]).where(
                billing_account_table.c.type == AccountType.client_rate
            )
        ),
    )


async def get_billing_accounts(company_id: str) -> list[DBRow]:
    async with services.db.acquire() as conn:
        return await select_all(
            conn=conn,
            query=(
                sa.select([billing_account_table]).where(
                    billing_account_table.c.company_id == company_id
                )
            ),
        )


def prepare_signature_info(type_, company_edrpou, *, is_legal=True, randomize_serial=False):
    drfo, title = '', ''
    time_dict = {
        'day': 21,
        'hour': 16,
        'year': 2017,
        'month': 9,
        'minute': 39,
        'second': 0,
        'day_of_week': 4,
        'milliseconds': 0,
    }

    if is_legal:
        drfo, edrpou = '**********', company_edrpou
    else:
        drfo, edrpou = company_edrpou, ''

    if type_ == SignatureType.stamp:
        drfo, name = '', ' '
    else:
        name, title = TEST_USER_FULL_NAME, 'Owner'

    if randomize_serial:
        serial = ''.join(random.choice('0********9B') for _ in range(40))
    else:
        serial = '3960B506******************************01'

    return {
        'filled': True,
        'issuer': (
            'O=ТОВ "Центр сертифікації ключів "Україна";OU=Відділ '
            'сертифікації;CN=АЦСК ТОВ "Центр сертифікації ключів '
            '"Україна";Serial=UA-36865753-0114;C=UA;L=Київ'
        ),
        'issuer_cn': 'АЦСК ТОВ "Центр сертифікації ключів "Україна"',
        'serial': serial,
        'subject': (
            f'O={TEST_COMPANY_NAME};PostalCode=02121;CN={TEST_COMPANY_NAME};'
            f'Serial={company_edrpou};C=UA;L=місто КИЇВ;StreetAddress=вулиця '
            'Харківське шосе, буд. 201-203, корпус 1А,Літера Ф, к. 60'
        ),
        'subject_address': '',
        'subject_cn': TEST_COMPANY_NAME,
        'subject_dns': '',
        'subject_drfo_code': drfo,
        'subject_edrpou_code': edrpou,
        'subject_email': TEST_USER_EMAIL,
        'subject_fullname': name,
        'subject_locality': 'місто КИЇВ',
        'subject_org': TEST_COMPANY_EDRPOU,
        'subject_org_unit': '',
        'subject_phone': '',
        'subject_state': '',
        'subject_title': title,
        'time': time_dict,
        'time_avail': True,
        'timestamp': True,
        'signature_type': type_.value,
    }


async def prepare_document_required_fields(
    conn: DBConnection,
    company_id: str,
    *,
    document_category: DocumentCategoryFields = DocumentCategoryFields.other,
    is_name_required: bool = True,
    is_type_required: bool = True,
    is_number_required: bool = True,
    is_date_required: bool = True,
    is_amount_required: bool = True,
    assigner_role_id: str | None = None,
) -> DBRow:
    option = DocumentRequiredFieldOptions(
        id_=None,
        company_id=company_id,
        document_category=document_category,
        is_name_required=is_name_required,
        is_type_required=is_type_required,
        is_number_required=is_number_required,
        is_date_required=is_date_required,
        is_amount_required=is_amount_required,
        assigner_role_id=assigner_role_id,
    )

    field = await add_document_required_field(conn=conn, field=option)

    return field


@asynccontextmanager
async def with_elastic(
    _: web.Application,
    documents_ids: list[str],
    *,
    disable: bool = False,
):
    # MAKE SURE THE ES SEARCH IS ENABLED

    if disable:
        yield
        return

    await sync_documents(
        documents_ids,
        refresh='wait_for',
    )
    try:
        yield
    finally:
        await delete_es_documents(documents_ids)


async def get_es_documents(documents_ids: list[str]) -> list[Document]:
    query = services.es.documents.search_query().query(
        Bool.must(
            DocumentES.document_id.in_(documents_ids),
        )
    )
    result = await fetch_es(query, source=ESQuerySource.api)
    return result.hits


async def select_company_stat(edrpou: str) -> DBRow | None:
    async with services.db.acquire() as conn:
        return await select_one(
            conn=conn,
            query=(
                company_statistic_table.select()
                .select_from(
                    company_statistic_table.join(
                        company_table,
                        company_table.c.id == company_statistic_table.c.company_id,
                    )
                )
                .where(company_table.c.edrpou == edrpou)
            ),
        )


def assert_list_any_order(data: list[Any], expected: list[Any]) -> bool:
    case = unittest.TestCase()
    case.maxDiff = None
    case.assertCountEqual(data, expected)
    return True


async def get_listings_map(app: web.Application, document: DBRow):
    async with app['db'].acquire() as conn:
        listings = await select_listings(conn, [document.id])
    return {listing.role_id: listing for listing in listings}


async def select_review_state(app: web.Application, document: DBRow, user: DBRow):
    async with app['db'].acquire() as conn:
        return await get_review_state(
            conn=conn,
            document_id=document.id,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
        )


async def prepare_automation_template(
    conn: DBConnection,
    user: DBRow,
    *,
    review_settings: DataDict | None = None,
    signers_settings: DataDict | None = None,
    viewers_settings: DataDict | None = None,
    fields_settings: DataDict | None = None,
) -> DBRow:
    query = document_automation_template_table.insert().values(
        {
            'company_id': user.company_id,
            'name': 'Test Template',
            'is_active': False,
            'review_settings': review_settings,
            'set_review': review_settings is not None,
            'signers_settings': signers_settings,
            'set_signers': signers_settings is not None,
            'viewers_settings': viewers_settings,
            'set_viewers': viewers_settings is not None,
            'fields_settings': fields_settings,
            'set_fields': fields_settings is not None,
            'created_by': user.role_id,
        }
    )
    return await select_one(conn, query)


async def prepare_automation(
    conn: DBConnection, conditions: DataDict, *, template_id: str, company_id: str
) -> DBRow:
    query = document_automation_condition_table.insert().values(
        {
            'conditions': conditions,
            'template_id': template_id,
            'order': 1,
            'status': DocumentAutomationStatus.enabled,
            'company_id': company_id,
        }
    )
    return await select_one(conn, query)


async def prepare_document_field(conn: DBConnection, user: DBRow) -> DBRow:
    query = documents_fields_table.insert().values(
        {
            'company_id': user.company_id,
            'name': 'Test Document Field',
            'type': DocumentFieldType.text,
            'is_required': False,
            'created_by': user.role_id,
        }
    )
    return await select_one(conn, query)


async def change_recipient(
    client: TestClient,
    user: DBRow,
    document_id: str,
    *,
    edrpou: str,
    emails: list[str],
    is_emails_hidden: bool = False,
) -> ClientResponse:
    return await client.post(
        f'/internal-api/documents/{document_id}/change-recipient',
        data=ujson.dumps(
            {'edrpou': edrpou, 'emails': emails, 'is_emails_hidden': is_emails_hidden}
        ),
        headers=prepare_auth_headers(user),
    )


async def get_raw_user(user_id: str) -> DBRow | None:
    query = sa.select([user_table]).where(user_table.c.id == user_id)
    async with services.db.acquire() as conn:
        return await select_one(conn, query=query)


async def get_document(
    *,
    document_id: str | None = None,
    document: DBRow | None = None,
) -> Document | None:
    if not document_id and document:
        document_id = document.id

    async with services.db.acquire() as conn:
        return await select_document(conn, document_id=document_id)


@contextmanager
def assert_exception(
    exception: type[BaseException] | None,
) -> Iterator[RaisesContext[type[BaseException]] | None]:
    """Similar to pytest.raises, but with support of None exception"""
    if exception is None:
        yield None
    else:
        with pytest.raises(exception) as err:
            yield err


def datetime_test(value: str):
    """
    Create TZ aware local datetime for testing purposes

    Currently, we use "Europe/Kyiv" timezone
    """
    return parse_local_datetime(value)


async def prepare_group(app, name, creator, members):
    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name=name,
            user=creator,
        )
        values_list = [{'group_id': group.id, 'role_id': member.role_id} for member in members]
        await insert_group_members_bulk(conn=conn, values_list=values_list)
    return group


async def prepare_document_recipients(data: list[DataDict]):
    if not data:
        return []

    async with services.db.acquire() as conn:
        return await insert_recipients(conn, recipients=data)


async def prepare_document_signer(
    document_id: str,
    company_id: str,
    role_id: str | None,
    group_id: str | None,
    date_signed: datetime.datetime | None = None,
    order: int | None = None,
    assigner: str | None = None,
    group_signer_id: str | None = None,
    source: SignersSource | None = None,
    id: str | None = None,
    date_created: datetime.datetime | None = None,
) -> DocumentSigner:
    data = {
        'document_id': document_id,
        'company_id': company_id,
        'role_id': role_id,
        'group_id': group_id,
        'date_signed': date_signed,
        'order': order,
        'assigner': assigner,
        'group_signer_id': group_signer_id,
        'source': source and source.value,
    }
    if id:
        data['id'] = id
    if date_created:
        data['date_created'] = date_created
    async with services.db.acquire() as conn:
        row = await select_one(
            conn=conn,
            query=document_signer_table.insert().values(data).returning(document_signer_table),
        )
        return DocumentSigner.from_row(row)


async def get_document_meta(document_id: str) -> DocumentMeta:
    async with services.db.acquire() as conn:
        return await select_document_meta(conn, document_id=document_id)


async def select_bank_transactions_by_transaction_ids(
    conn: DBConnection, transaction_ids: list[str]
) -> list[DBRow]:
    return await select_all(
        conn=conn,
        query=(
            sa.select([bank_transactions_table]).where(
                bank_transactions_table.c.transaction_id.in_(transaction_ids)
            )
        ),
    )


async def select_super_admin_actions(conn: DBConnection) -> list[SuperAdminAction]:
    """
    This function selects all superadmin actions from db
    Being used presumably for only testing purposes
    """

    rows = await select_all(conn=conn, query=sa.select([super_admin_actions_table]))

    return [SuperAdminAction.from_db(row) for row in rows]


async def get_super_admin_actions() -> list[SuperAdminAction]:
    async with services.db.acquire() as conn:
        return await select_super_admin_actions(conn)


async def unzip_archive_test_util(body: bytes) -> dict[str, bytes]:
    result = {}

    async for file_name, _, unzipped_chunks in async_stream_unzip(iter_bytes_by_chunks(body)):
        # Actually contains path/file_name inside archive: foo/bar/name.pdf
        file_name = handle_encoding_bytes(file_name)

        body = await read_from_bytes_chunks(unzipped_chunks)
        result[file_name] = body

    return result


async def prepare_document_reject(
    client,
    *,
    user,
    document,
    expected_status: HTTPStatus = HTTPStatus.OK,
):
    response = await client.post(
        DOCUMENT_REJECT_URL.format(document_id=document.id),
        headers=prepare_auth_headers(user),
        json={},
    )

    assert response.status == expected_status, await response.json() or response.status


async def prepare_data_for_list_comments(app, client, user):
    """
    Prepare data for testing list comments.
    """
    partner = await prepare_user_data(
        app=app, company_edrpou='55555555', email='<EMAIL>'
    )

    recipients = [{'edrpou': partner.company_edrpou, 'emails': [partner.email]}]
    document = await prepare_document_data(
        app, owner=user, document_recipients=recipients, status_id=DocumentStatus.finished.value
    )

    # owner internal comment will be listed
    await request_add_comment(
        client=client,
        user=user,
        document=document,
        comment='Internal owner comment',
        is_internal=True,
    )
    # owner external comment will be listed
    await request_add_comment(
        client=client,
        user=user,
        document=document,
        comment='External owner comment',
        is_internal=False,
    )
    # partner internal comment will NOT be listed
    await request_add_comment(
        client=client,
        user=partner,
        document=document,
        comment='Internal partner comment',
        is_internal=True,
    )
    # partner external comment will be listed
    await request_add_comment(
        client=client,
        user=partner,
        document=document,
        comment='External partner comment',
        is_internal=False,
    )

    await client.post(
        API_V2_DOCUMENT_DELETE_REQUEST_URL.format(document_id=document.id),
        headers=prepare_auth_headers(user),
        json={'message': 'Delete request'},
    )

    return partner, document
