from aiohttp import web

from app.auth.decorators import redirect_to_app
from app.tokens.utils import check_invite_token


@redirect_to_app
async def invite_token(request: web.Request) -> web.Response:
    """Validate invite token and redirect to registration page.

    If token is invalid or user is already registered redirect to static page
    instead of raising 400 error to user.
    """

    return await check_invite_token(request=request)
