from app.lib.database import DBConnection
from app.tokens import db


async def create_short_registration_token(
    conn: DBConnection,
    edrpou: str,
    email: str,
    invited_by_company_edrpou: str,
    invited_by_user_id: str,
    invited_with_document: str | None,
    email_domains: list[str] | None,
) -> str:
    return await db.insert_short_registration_token(
        conn=conn,
        data={
            'edrpou': edrpou,
            'email': email,
            'invited_by_company': invited_by_company_edrpou,
            'invited_by_user': invited_by_user_id,
            'invited_with_document': invited_with_document,
            'email_domains': email_domains,
        },
    )
