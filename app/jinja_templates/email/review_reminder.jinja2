{% extends "email/layout_v2.jinja2" %}
{% import "email/macros.jinja2" as macros %}
{% import "email/blocks/table_blocks.jinja2" as t %}
{% from "email/blocks/header_macros.jinja2" import header %}
{% from "email/blocks/about_macros.jinja2" import telegram_bot_block %}

{% block content %}
    {{
        header(
            text=_("Документи очікують вашого погодження"),
            image_link="images/emails/review_reminder.png",
            image_style="width: 206px; height: 159px",
            button_link=main_link,
            button_text=_("Погодити всі")
        )
    }}

    <div style="{{ macros.style_content_body() }};">
        <table style="{{ macros.style_table_base() }};">
            {{ t.greeting_row(name=first_name) }}
            {{ t.spacing_row(height="10px") }}

            <tr>
                <td colspan="2">
                    {% trans %}У компанії{% endtrans %}
                    {{ emailing.build_company_label_v2(edrpou=recipient_company_edrpou, name=recipient_company_name)}}
                    {% trans %} документи очікують на ваше погодження.{% endtrans %}
                </td>
            </tr>
            {{ t.spacing_row(height="15px") }}

            <tr>
                <td colspan="2">
                    {{ total_count_label }}
                </td>
            </tr>
            {{ t.spacing_row(height="15px") }}

            {% for document in documents %}

                {{ t.spacing_row(height="15px") }}
                {% call t.emoji_row(emoji="📄") %}
                    <div>
                        <p style="line-height:25px">
                            {% trans %}Назва:{% endtrans %} <a href="{{ document.link }}">{{ document.title }}</a><br/>
                            {% if document.recipient_label %}
                                {% trans %}Контрагент:{% endtrans %} <b>{{ document.recipient_label }}</b><br/>
                            {% endif %}
                            {% trans %}Номер:{% endtrans %} <b>{{ document.number }}</b><br/>
                        </p>
                    </div>
                {% endcall %}
            {% endfor %}

            {% if has_more_documents %}
                {{ t.spacing_row(height="10px") }}
                <tr>
                    <td colspan=2>
                        <a href="{{ main_link }}">{% trans %}Та інші...{% endtrans %}</a>
                    </td>
                </tr>
            {% endif %}

            {{ t.spacing_row(height="10px") }}
            {{ t.button_row(url=main_link, text=_("Погодити всі")) }}

        </table>
    </div>

    {{ telegram_bot_block() }}

{% endblock %}
