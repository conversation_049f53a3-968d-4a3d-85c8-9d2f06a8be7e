{% extends "email/layout_v2.jinja2" %}
{% import "email/macros.jinja2" as macros %}
{% import "email/blocks/table_blocks.jinja2" as t %}
{% from "email/blocks/header_macros.jinja2" import header_right %}
{% from "email/blocks/bill_macros.jinja2" import generate_bill_table %}

{% block content %}
    {{
        header_right(
            text=_("Електронну адресу користувача змінено"),
            image_link="images/emails/user_email_change.png",
            image_style="width: 140px; height: 140px",
        )
    }}
    <table border=0 display=inline style="{{ macros.style_content_body() }};">
        {# Greeting message with emoji #}
        {{ t.greeting_with_emoji(emoji="😊", name=first_name) }}

        {{ t.spacing_row("10") }}

        <tr>
            <td colspan="2">
                <p>
                    {% trans brand=app_brand %}
                    Повідомляємо, що електронну адресу {{ user_full_name }} було змінено з {{ old_email }}
                    на {{ new_email }}.
                    {% endtrans %}
                </p>
                <p>
                    {% if has_integration_rate %}
                        {% trans %}
                        Будь ласка, <b>використовуйте новий email для коректної роботи інтеграції.</b>
                        {% endtrans %}
                    {% else %}
                        {% trans %}
                        Потрібна допомога? Зв'яжіться з нами {{ support_email }}.
                        {% endtrans %}
                    {% endif %}
                </p>
            </td>
        </tr>

    </table>
{% endblock %}
