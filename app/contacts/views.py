import logging
from functools import partial

from aiohttp import web

from app.auth import utils as auth
from app.auth.decorators import login_required
from app.auth.types import to_auth_user
from app.auth.validators import validate_user_permission
from app.contacts import db, utils
from app.contacts.db import delete_all_contacts
from app.contacts.utils import (
    build_youcontrol_referral_url,
    get_contact_info_url,
    insert_contacts_on_import,
    update_contact_names,
    update_contact_person,
)
from app.contacts.validators import (
    ContactValidator,
    validate_create_contact_person,
    validate_edit_contact_person,
    validate_get_youcontrol_contact_url,
    validate_upload,
)
from app.lib import validators
from app.lib.database import DBRow
from app.lib.helpers import user_rate_limit
from app.lib.redirects import redirect_outside
from app.profile.utils import generate_esputnik_check_companies_event
from app.services import services
from app.trigger_notifications.utils import (
    TriggerNotificationInviteCompanies,
    delete_contacts_trigger_notification,
)
from worker import topics

logger = logging.getLogger(__name__)

redirect_login_required = partial(login_required, should_check_xsrf=False)


@login_required
async def clear(request: web.Request, user: DBRow) -> web.Response:
    """Remove all current company contacts"""

    validate_user_permission(to_auth_user(user), {'can_edit_company_contact'})
    async with request.app['db'].acquire() as conn:
        contacts_ids = await delete_all_contacts(conn, user.company_id)

    await utils.delete_contacts_from_index_job(contacts_ids=contacts_ids)

    return web.json_response()


@login_required
async def sync_contacts(request: web.Request, user: DBRow) -> web.StreamResponse:
    """Collect missing recipients for uploaded documents by EDRPOU or invoice.

    Only allow to sync contacts for users, which company's EDRPOU listed in
    config under `sync_contacts_api` section.

    Also known as "Синхронізація контактів з PROM" or "Синхронізація контактів
    з Закупками" in our team.
    """
    logger.info(
        'Processing request for sync contacts',
        extra={
            'role_id': user.role_id,
            'edrpou': user.company_edrpou,
        },
    )
    data = {'role_id': user.role_id}
    await request.app['kafka'].send_record(topics.SYNC_CONTACTS_FIRST_STEP, data)
    logger.warning('Added job to sync contacts', extra=data)
    return web.json_response()


@login_required
async def import_contacts(request: web.Request, user: DBRow) -> web.Response:
    """Use CSV/XLSX file to upload contacts. Duplicates and invalid rows ignored.

    Response: counters & invalid_row_numbers (List[int])
    """
    upload_size: int = request.content_length or 0
    log_extra = {
        'role_id': user.role_id,
        'edrpou': user.company_edrpou,
        'upload_size': upload_size,
    }
    logger.info('Processing upload contacts for a company', extra=log_extra)

    ctx = validate_upload(
        await validators.validate_post_request(request),
        services.config.contacts.uploads,
        upload_size,
        user,
    )
    async with request.app['db'].acquire() as conn:
        await insert_contacts_on_import(conn, user, ctx)
        await auth.update_user_onboarding(
            conn=conn,
            user_id=user.id,
            has_checked_companies=True,
        )
        logger.info('Contacts are inserted by upload request', extra=log_extra)

    await generate_esputnik_check_companies_event(user.id)

    # Trigger notifications
    trigger_notification = TriggerNotificationInviteCompanies()
    await trigger_notification.send_prepare_job(role_id=user.role_id)

    logger.info('Trigger notifications generated', extra=log_extra)

    # Reindex contacts for the whole company
    await utils.schedule_index_contact_recipients_job()

    return web.json_response(ctx.counters)


@login_required
async def edit_contact(request: web.Request, user: DBRow) -> web.Response:
    data = validators.validate(ContactValidator, await validators.validate_json_request(request))
    contact_id = request.match_info['contact_id']
    logger.info(
        'Contact edit request received',
        extra={
            'role_id': user.role_id,
            'edrpou': user.company_edrpou,
            'contact_id': contact_id,
        },
    )

    async with request.app['db'].acquire() as conn:
        await update_contact_names(
            conn=conn,
            contact_id=contact_id,
            company_name=data.get('company_name'),
            company_short_name=data.get('company_short_name'),
        )
        logger.info(
            'Contact updated by edit request',
            extra={
                'role_id': user.role_id,
                'edrpou': user.company_edrpou,
                'contact_id': contact_id,
                'data': data,
            },
        )

    await utils.schedule_index_contact_recipients_job()

    return web.json_response()


@login_required
async def create_contact_person(request: web.Request, user: DBRow) -> web.Response:
    app = request.app
    json_data = await validators.validate_json_request(request)
    async with app['db'].acquire() as conn:
        options = await validate_create_contact_person(conn=conn, user=user, raw_data=json_data)
        logger.info(
            'Contact person create',
            extra={
                'role_id': user.role_id,
                'options': options.as_db(),
            },
        )
        await utils.create_contact_person(conn=conn, user=user, options=options)

    await delete_contacts_trigger_notification(app['kafka'], user.role_id)
    if options.is_new_contact_company:
        trigger_notification = TriggerNotificationInviteCompanies()
        await trigger_notification.send_prepare_job(role_id=user.role_id)

    await utils.schedule_index_contact_recipients_job()

    return web.json_response()


@login_required
async def edit_contact_person(request: web.Request, user: DBRow) -> web.Response:
    data = await validators.validate_json_request(request)
    contact_person_id = request.match_info['contact_person_id']

    async with request.app['db'].acquire() as conn:
        options = await validate_edit_contact_person(
            conn=conn,
            raw_data={**data, 'contact_person_id': contact_person_id},
            user=user,
        )
        logger.info(
            'Contact person update',
            extra={
                'role_id': user.role_id,
                'options': options.as_db(),
            },
        )
        await update_contact_person(conn=conn, user=user, options=options)

    await utils.schedule_index_contact_recipients_job()

    return web.json_response()


@login_required
async def delete_contact_person(request: web.Request, user: DBRow) -> web.Response:
    validate_user_permission(to_auth_user(user), {'can_edit_company_contact'})

    async with request.app['db'].acquire() as conn:
        contact_person_id = request.match_info['contact_person_id']
        logger.info(
            'Contact person delete request',
            extra={
                'role_id': user.role_id,
                'contact_person_id': contact_person_id,
            },
        )
        await db.delete_contact_person(conn, contact_person_id)

    await utils.delete_contact_persons_from_index_job(person_id=contact_person_id)

    return web.json_response()


@redirect_login_required
@user_rate_limit(limit=30)
async def get_youcontrol_contact_url(request: web.Request, user: DBRow) -> web.Response:
    app = request.app

    edrpou = await validate_get_youcontrol_contact_url(request, user)

    contact_url = await get_contact_info_url(app, edrpou)
    referral_url = build_youcontrol_referral_url(app, contact_url)

    return redirect_outside(referral_url)
