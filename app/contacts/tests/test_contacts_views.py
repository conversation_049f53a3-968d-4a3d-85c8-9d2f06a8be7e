from http import HTTPStatus
from pathlib import Path

import pytest
import sqlalchemy as sa
import ujson
from aiohttp.web_request import <PERSON><PERSON>ield

from api.downloads.archives import STATIC_FILES_PATH
from app.app import create_app
from app.contacts.db import select_contact, select_contact_person, soft_create_contact
from app.contacts.tables import contact_person_table, contact_table
from app.contacts.types import CreateContactPersonOptions
from app.contacts.utils import create_contact_person
from app.lib.helpers import string_to_bool
from app.models import select_all
from app.services import services
from app.tests.common import (
    TEST_COMPANY_EDRPOU,
    cleanup_on_teardown,
    prepare_auth_headers,
    prepare_client,
    prepare_referer_headers,
    prepare_user_data,
)
from app.trigger_notifications.db import (
    delete_trigger_notification,
    select_all_trigger_notifications,
)
from app.trigger_notifications.enums import TriggerNotificationType

DATA_PATH = Path(__file__).parent / 'data'

CONTACT_PERSON_URL_TMPL = '/internal-api/contacts/persons/{contact_person_id}'
CONTACT_PERSONS_URL = '/internal-api/contacts/persons'
GET_YOUCONTROL_CONTACT_URL = '/internal-api/contacts/dossier'
CONTACT_URL_TMPL = '/internal-api/contacts/{contact_id}'
SYNC_URL = '/internal-api/contacts/sync'
UPLOAD_URL = '/internal-api/contacts/upload'

TEST_FILENAME = 'ArchiveReadme.pdf'


async def _select_contact_persons(conn, *, company_id, edrpou):
    return await select_all(
        conn,
        sa.select([contact_person_table])
        .select_from(
            contact_table.join(
                contact_person_table,
                contact_person_table.c.contact_id == contact_table.c.id,
            )
        )
        .where(
            sa.and_(
                contact_table.c.edrpou == edrpou,
                contact_table.c.company_id == company_id,
            )
        ),
    )


async def test_sync_forbidden(aiohttp_client):
    client = await aiohttp_client(create_app())
    response = await client.post(SYNC_URL, headers=prepare_referer_headers(client))
    assert response.status == 403


async def test_upload_forbidden(aiohttp_client):
    client = await aiohttp_client(create_app())
    response = await client.post(UPLOAD_URL, headers=prepare_referer_headers(client))
    assert response.status == 403


async def test_download_csv_example(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    try:
        response = await client.get(
            '/static/files/contacts_csv_template.xlsx',
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200
        assert response.content_type == 'application/octet-stream'
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'filename',
    [
        'contacts_csv_template.csv',
        'contacts_divided_by_semicolon.csv',
        'contacts_csv_template.xlsx',
    ],
)
async def test_upload(aiohttp_client, filename):
    app, client, user = await prepare_client(aiohttp_client)

    csv_file = open(DATA_PATH / filename, 'rb')

    try:
        response = await client.post(
            UPLOAD_URL, data=[('file', csv_file)], headers=prepare_auth_headers(user)
        )
        assert response.status == 200
        assert await response.json() == {
            'rows_total': 3,
            'rows_invalid': 0,
            'invalid_row_numbers': [],
        }

        async with app['db'].acquire() as conn:
            contact = await select_contact_person(
                conn=conn,
                email='<EMAIL>',
                edrpou='1111111113',
                company_id=user.company_id,
            )
            assert contact.main_recipient is True

            contact = await select_contact_person(
                conn=conn,
                email='<EMAIL>',
                edrpou='1111111111',
                company_id=user.company_id,
            )
            assert contact.main_recipient is False

    finally:
        if not csv_file.closed:
            csv_file.close()
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'filename, person_emails, result',
    [
        (
            'contacts_csv_with_and_without_emails.csv',
            [
                ('1111111111', ['<EMAIL>']),
                ('1111111112', []),
                ('1111111113', []),
                ('1111111114', []),
            ],
            {'rows_total': 6, 'rows_invalid': 1, 'invalid_row_numbers': [7]},
        ),
        (
            'contacts_csv_without_emails.csv',
            [
                ('1111111111', []),
                ('1111111112', []),
                ('1111111113', []),
                ('1111111114', []),
            ],
            {'rows_total': 6, 'rows_invalid': 0, 'invalid_row_numbers': []},
        ),
    ],
)
async def test_upload_without_emails(aiohttp_client, filename, person_emails, result):
    app, client, user = await prepare_client(aiohttp_client)
    csv_file = open(DATA_PATH / filename, 'rb')
    response = await client.post(
        UPLOAD_URL, data=[('file', csv_file)], headers=prepare_auth_headers(user)
    )
    assert response.status == 200
    assert await response.json() == result

    async with app['db'].acquire() as conn:

        async def check_person(edrpou, emails):
            contact = await select_contact(conn, contact_edrpou=edrpou, company_id=user.company_id)
            assert contact.name == ''
            assert contact.short_name == ''
            persons = await _select_contact_persons(
                conn=conn, edrpou=edrpou, company_id=user.company_id
            )
            assert len(persons) == len(emails)
            assert set(emails) == {person.email for person in persons}

        for edrpou, emails in person_emails:
            await check_person(edrpou, emails)


async def test_upload_duplicates(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    filename = 'contacts_csv_template.csv'
    duplicates = 'contacts_csv_duplicate.csv'
    csv_file = open(DATA_PATH / filename, 'rb')
    csv_file_duplicate = open(DATA_PATH / duplicates, 'rb')

    contact_email = '<EMAIL>'
    contact_edrpou = '1111111113'

    try:
        response = await client.post(
            UPLOAD_URL, data=[('file', csv_file)], headers=prepare_auth_headers(user)
        )
        assert response.status == 200
        assert await response.json() == {
            'rows_total': 3,
            'rows_invalid': 0,
            'invalid_row_numbers': [],
        }
        async with app['db'].acquire() as conn:
            contact = await select_contact(
                conn=conn,
                contact_edrpou=contact_edrpou,
                company_id=user.company_id,
            )
            assert contact.name == 'Компанія контрагента'
            assert contact.short_name == 'Коротка назва'
            contact = await select_contact_person(
                conn=conn,
                email=contact_email,
                edrpou=contact_edrpou,
                company_id=user.company_id,
            )
            assert contact.main_recipient is True
            assert contact.email == contact_email
            assert contact.first_name == 'Петро'
            assert contact.last_name == 'Іванів'
            assert contact.second_name == 'Миколайович'

        # Uploading duplicate updates existing contact
        response = await client.post(
            UPLOAD_URL,
            data=[('file', csv_file_duplicate)],
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200
        assert await response.json() == {
            'rows_total': 1,
            'rows_invalid': 0,
            'invalid_row_numbers': [],
        }
        async with app['db'].acquire() as conn:
            contact = await select_contact(
                conn, contact_edrpou=contact_edrpou, company_id=user.company_id
            )
            assert contact.name == 'Оновлена назва контрагента'
            assert contact.short_name == 'Оновлена коротка назва'
            contact = await select_contact_person(
                conn=conn,
                email=contact_email,
                edrpou=contact_edrpou,
                company_id=user.company_id,
            )
            assert contact.main_recipient is True
            assert contact.email == contact_email
            assert contact.first_name == 'Іван'
            assert contact.last_name == 'Передовий'
            assert contact.second_name == 'Петрович'
    finally:
        for file in (csv_file, csv_file_duplicate):
            if not file.closed:
                file.close()
        await cleanup_on_teardown(app)


async def test_upload_with_main_recipient_flag(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    existing = open(DATA_PATH / 'contacts_csv_main_exists.csv', 'rb')
    changed = open(DATA_PATH / 'contacts_csv_template.csv', 'rb')

    prev_main_recipient = '<EMAIL>'
    new_main_recipient = '<EMAIL>'
    contact_edrpou = '1111111113'

    try:
        response = await client.post(
            UPLOAD_URL, data=[('file', existing)], headers=prepare_auth_headers(user)
        )
        assert response.status == 200
        assert await response.json() == {
            'rows_total': 2,
            'rows_invalid': 0,
            'invalid_row_numbers': [],
        }
        async with app['db'].acquire() as conn:
            contact = await select_contact(
                conn=conn,
                contact_edrpou=contact_edrpou,
                company_id=user.company_id,
            )
            assert contact.name == 'Компанія контрагента'
            assert contact.short_name == 'Коротка назва'
            contact = await select_contact_person(
                conn=conn,
                email=prev_main_recipient,
                edrpou=contact_edrpou,
                company_id=user.company_id,
            )
            assert contact.main_recipient is True
            assert contact.email == prev_main_recipient
            assert contact.first_name == 'Василь'
            assert contact.last_name == 'Нагорний'
            assert contact.second_name == 'Миколайович'

        # Uploading changed main recipient
        response = await client.post(
            UPLOAD_URL, data=[('file', changed)], headers=prepare_auth_headers(user)
        )
        assert response.status == 200
        assert await response.json() == {
            'rows_total': 3,
            'rows_invalid': 0,
            'invalid_row_numbers': [],
        }
        async with app['db'].acquire() as conn:
            contact = await select_contact_person(
                conn=conn,
                email=new_main_recipient,
                edrpou=contact_edrpou,
                company_id=user.company_id,
            )
            assert contact.main_recipient is True
            assert contact.email == new_main_recipient
            assert contact.first_name == 'Петро'
            assert contact.last_name == 'Іванів'
            assert contact.second_name == 'Миколайович'

            prev_contact = await select_contact_person(
                conn=conn,
                email=prev_main_recipient,
                edrpou=contact_edrpou,
                company_id=user.company_id,
            )
            assert prev_contact.main_recipient is False
    finally:
        for file in (existing, changed):
            if not file.closed:
                file.close()
        await cleanup_on_teardown(app)


async def test_upload_unsupported_extension(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    test_file = open(STATIC_FILES_PATH / TEST_FILENAME, 'rb')

    try:
        payload = {'file': FileField('file', TEST_FILENAME, test_file, 'text/csv', {})}

        response = await client.post(UPLOAD_URL, data=payload, headers=prepare_auth_headers(user))
        assert response.status == 400
        data = await response.json()
        assert data['code'] == 'invalid_file_extension'
    finally:
        test_file.close()
        await cleanup_on_teardown(app)


async def test_create_contact_person(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)

    ok_data = {
        'edrpou': '12345678',
        'email': '<EMAIL>',
        'phone': '',
        'first_name': '',
        'main_recipient': '1',
    }

    bad_data = {
        'edrpou': '12345678',
        'email': '<EMAIL>',
        'phone': '+380637777777000000',
    }

    try:
        response = await client.post(
            CONTACT_PERSONS_URL, data=ujson.dumps(ok_data), headers=headers
        )
        assert response.status == 200

        response = await client.post(
            CONTACT_PERSONS_URL, data=ujson.dumps(bad_data), headers=headers
        )
        assert response.status == 400
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'data, expected',
    [
        (
            {
                'edrpou': TEST_COMPANY_EDRPOU,
                'email': '<EMAIL>',
                'is_email_hidden': False,
                'first_name': 'first_name2',
                'last_name': 'last_name2',
                'main_recipient': '0',
                'phone': '+380637777777',
                'second_name': 'second_name2',
            },
            '<EMAIL>',
        ),
        (
            {
                'edrpou': TEST_COMPANY_EDRPOU,
                'email': None,
                'is_email_hidden': True,
                'first_name': 'first_name2',
                'last_name': 'last_name2',
                'main_recipient': '0',
                'phone': '+380637777777',
                'second_name': 'second_name2',
            },
            '<EMAIL>',
        ),
    ],
)
async def test_edit_contact_person(aiohttp_client, data, expected):
    app, client, user = await prepare_client(aiohttp_client)
    headers = prepare_auth_headers(user)

    data1 = CreateContactPersonOptions(
        edrpou=user.company_edrpou,
        email='<EMAIL>',
        first_name='first_name1',
        last_name='last_name1',
        main_recipient=True,
        second_name='second_name1',
    )
    async with app['db'].acquire() as conn:
        await create_contact_person(conn, user, data1)
        contact_person = await select_contact_person(
            conn=conn,
            email=data1.email,
            edrpou=data1.edrpou,
            company_id=user.company_id,
        )

        edit_url = CONTACT_PERSON_URL_TMPL.format(contact_person_id=contact_person.id)

        response = await client.patch(edit_url, json=data, headers=headers)
        assert response.status == 200, await response.text()

        contact_person2 = await select_contact_person(
            conn=conn,
            email=expected,
            edrpou=data['edrpou'],
            company_id=user.company_id,
        )
        assert contact_person.id == contact_person2.id
        assert contact_person2.email == expected
        assert contact_person2.main_recipient == string_to_bool(data['main_recipient'])
        assert contact_person2.first_name == data['first_name']


async def test_delete_contact_person(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_company_contact=True)

    data = CreateContactPersonOptions(
        edrpou=user.company_edrpou,
        email='<EMAIL>',
    )

    async with services.db.acquire() as conn:
        await create_contact_person(conn, user, data)
        contact_person = await select_contact_person(
            conn=conn,
            email=data.email,
            edrpou=data.edrpou,
            company_id=user.company_id,
        )

        delete_url = CONTACT_PERSON_URL_TMPL.format(contact_person_id=contact_person.id)

        response = await client.delete(delete_url, headers=prepare_auth_headers(user))
        assert response.status == 200

        assert None is await select_contact_person(
            conn=conn,
            email=data.email,
            edrpou=data.edrpou,
            company_id=user.company_id,
        )


async def test_delete_contact_person_forbidden(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client, can_edit_company_contact=False)

    data = CreateContactPersonOptions(
        edrpou=user.company_edrpou,
        email='<EMAIL>',
    )

    async with services.db.acquire() as conn:
        await create_contact_person(conn, user, data)
        contact_person = await select_contact_person(
            conn=conn,
            email=data.email,
            edrpou=data.edrpou,
            company_id=user.company_id,
        )

        delete_url = CONTACT_PERSON_URL_TMPL.format(contact_person_id=contact_person.id)

        response = await client.delete(delete_url, headers=prepare_auth_headers(user))
        assert response.status == 403


async def test_edit_contact(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    data = {'company_name': 'Evo.company', 'company_short_name': 'evo'}

    try:
        async with app['db'].acquire() as conn:
            contact = await soft_create_contact(conn, user.company_id, '12345678')

            edit_url = CONTACT_URL_TMPL.format(contact_id=contact.id)

            response = await client.patch(
                edit_url, data=ujson.dumps(data), headers=prepare_auth_headers(user)
            )
            assert response.status == 200

            contact = await soft_create_contact(conn, user.company_id, '12345678')

            assert contact.name == data['company_name']
            assert contact.short_name == data['company_short_name']
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('email_dict', [{'email': None}, {}])
async def test_create_contact_without_email(aiohttp_client, email_dict):
    app, client, user = await prepare_client(aiohttp_client)
    edrpou = '12345678'
    data = {
        'edrpou': edrpou,
        'phone': '',
        'first_name': '',
        'main_recipient': '1',
        **email_dict,
    }
    headers = prepare_auth_headers(user)
    response = await client.post(CONTACT_PERSONS_URL, json=data, headers=headers)
    assert response.status == HTTPStatus.OK
    async with app['db'].acquire() as conn:
        contact = await select_contact(conn, contact_edrpou=edrpou, company_id=user.company_id)
        assert contact is not None
        assert contact.edrpou == edrpou

        persons = await _select_contact_persons(
            conn=conn, edrpou=edrpou, company_id=user.company_id
        )
        assert len(persons) == 0


async def test_trigger_notification_on_contact_create(aiohttp_client):
    edrpou1 = '11111111'
    edrpou2 = '11111112'
    edrpou3 = '11111113'
    edrpou4 = '11111114'
    app, client, user = await prepare_client(aiohttp_client, company_edrpou=edrpou1)
    headers = prepare_auth_headers(user)

    await prepare_user_data(app, email='<EMAIL>', company_edrpou=edrpou2)

    async with app['db'].acquire() as conn:
        # On startup there is not notifications
        notifications = await select_all_trigger_notifications(conn)
        assert len(notifications) == 0

        # We create first contact of company that is not registered in service. In
        # such case we expected to see notification with call to action to invite
        # companies
        response = await client.post(CONTACT_PERSONS_URL, json={'edrpou': edrpou3}, headers=headers)
        assert response.status == 200
        notifications = await select_all_trigger_notifications(conn)
        assert len(notifications) == 1
        assert notifications[0].type == TriggerNotificationType.invite_companies

        # Then try to create contact person with company that already is in contact
        # list, we expected that new notification was sent
        response = await client.post(
            CONTACT_PERSONS_URL,
            json={'edrpou': edrpou3, 'email': '<EMAIL>'},
            headers=headers,
        )
        assert response.status == 200
        notifications = await select_all_trigger_notifications(conn)
        assert len(notifications) == 1

        # Next try to create contact with another company that is not registered
        # in service. Such as role already has notification of such type, we expected
        # that role will not receive new notifications
        response = await client.post(
            CONTACT_PERSONS_URL,
            json={'edrpou': edrpou4, 'email': '<EMAIL>'},
            headers=headers,
        )
        assert response.status == 200
        notifications = await select_all_trigger_notifications(conn)
        assert len(notifications) == 1

        # Remove previous trigger notifications and try to add contact with registered
        # in service company. New notifications was not expected
        await delete_trigger_notification(conn, [notifications[0].id])
        response = await client.post(
            CONTACT_PERSONS_URL,
            json={'edrpou': edrpou2, 'email': '<EMAIL>'},
            headers=headers,
        )
        assert response.status == 200
        notifications = await select_all_trigger_notifications(conn)
        assert len(notifications) == 1


async def test_get_youcontrol_contact_url(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client)
    youcontrol_url = 'https://test.com/c/?id=8168&pr=kav6H'

    async def youcontrol(*args, **kwargs):
        return {'url': youcontrol_url, 'responseCode': 0}

    monkeypatch.setattr('app.contacts.utils.request_contact_info', youcontrol)

    response = await client.get(
        GET_YOUCONTROL_CONTACT_URL,
        params={'code': '12345678'},
        headers=prepare_auth_headers(user),
        allow_redirects=False,
    )

    assert response.status == HTTPStatus.FOUND
    redirected_url_actual = response.headers['Location']
    redirected_url_expected = (
        'https://referral/?returnUrl=https://test.com/c/?id%3D8168%26pr%3Dkav6H'
    )
    assert redirected_url_actual == redirected_url_expected
