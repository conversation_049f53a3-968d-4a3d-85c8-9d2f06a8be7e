from dataclasses import dataclass
from datetime import datetime
from typing import Self

import pydantic
from pydantic import BaseModel as _BaseModel
from pydantic import ConfigDict

from app.document_automation.enums import DocumentAutomationStatus
from app.documents.types import DocumentWithUploader
from app.lib.database import DBRow
from app.lib.types import DataDict


class _DocumentAutomationTemplateModel(_BaseModel):
    model_config = ConfigDict(extra='forbid')


class DocumentAutomationTemplateReviewEntity(_DocumentAutomationTemplateModel):
    type: str  # role | group
    id: str


class DocumentAutomationTemplateReviewSettings(_DocumentAutomationTemplateModel):
    is_required: bool
    reviewer_entities: list[DocumentAutomationTemplateReviewEntity] = []
    is_ordered: bool | None = None  # WHERE null == false

    # Deprecated. Use "reviewer_entities" for new data instead
    # and "reviewers" property for backward compatibility
    reviewers_ids: list[str] = []

    @property
    def is_parallel(self) -> bool:
        is_ordered = self.is_ordered or False
        return not is_ordered

    @property
    def reviewers(self) -> list[DocumentAutomationTemplateReviewEntity]:
        """
        Backward compatible list of reviewers
        """
        if self.reviewer_entities:
            return self.reviewer_entities

        return [
            DocumentAutomationTemplateReviewEntity(type='role', id=reviewer_id)
            for reviewer_id in self.reviewers_ids
        ]

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)


class DocumentAutomationTemplateSignerEntity(_DocumentAutomationTemplateModel):
    type: str  # role | group
    id: str


class DocumentAutomationTemplateSignersSettings(_DocumentAutomationTemplateModel):
    is_ordered: bool
    signer_entities: list[DocumentAutomationTemplateSignerEntity] = []

    # Deprecated. Use "signer_entities" for new data instead
    # and "signers" property for backward compatibility
    signers_ids: list[str] = []

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)

    @property
    def is_parallel(self) -> bool:
        return not self.is_ordered

    @property
    def signers(self) -> list[DocumentAutomationTemplateSignerEntity]:
        """
        Backward compatible list of signers
        """
        if self.signer_entities:
            return self.signer_entities

        return [
            DocumentAutomationTemplateSignerEntity(type='role', id=signer_id)
            for signer_id in self.signers_ids
        ]


class DocumentAutomationTemplateViewersSettings(_DocumentAutomationTemplateModel):
    viewers_ids: list[str] | None = None
    viewers_group_ids: list[str] | None = None

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)


class DocumentAutomationTemplateFieldSetting(pydantic.BaseModel):
    field_id: str
    value: str | None
    is_required: bool


class DocumentAutomationTemplateFieldsSettings(_DocumentAutomationTemplateModel):
    fields: list[DocumentAutomationTemplateFieldSetting]

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)


class DocumentTemplateTagsSettings(pydantic.BaseModel):
    tags: list[str]

    def to_db(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)


class DocumentAutomationTemplateBase(_DocumentAutomationTemplateModel):
    id_: str
    name: str
    company_id: str

    assigned_to: str | None

    review_settings: DocumentAutomationTemplateReviewSettings | None
    signers_settings: DocumentAutomationTemplateSignersSettings | None
    viewers_settings: DocumentAutomationTemplateViewersSettings | None
    fields_settings: DocumentAutomationTemplateFieldsSettings | None
    tags_settings: DocumentTemplateTagsSettings | None

    conditions: DataDict | None

    new_tags: list[str]
    tags_ids: list[str]

    def to_db(self) -> DataDict:
        return {
            'name': self.name,
            'assigned_to': self.assigned_to,
            'set_review': self.review_settings is not None,
            'review_settings': self.review_settings.to_db() if self.review_settings else None,
            'set_signers': self.signers_settings is not None,
            'signers_settings': self.signers_settings.to_db() if self.signers_settings else None,
            'set_viewers': self.viewers_settings is not None,
            'viewers_settings': self.viewers_settings.to_db() if self.viewers_settings else None,
            'set_fields': self.fields_settings is not None,
            'fields_settings': self.fields_settings.to_db() if self.fields_settings else None,
            'set_tags': self.tags_settings is not None,
            'tags_settings': self.tags_settings.to_db() if self.tags_settings else None,
        }

    def to_automation_db(self) -> DataDict:
        return {
            'conditions': self.conditions,
            'template_id': self.id_,
            'company_id': self.company_id,
            'status': DocumentAutomationStatus.disabled,
        }


class CreateTemplateCtx(DocumentAutomationTemplateBase):
    created_by: str

    def to_db(self) -> DataDict:
        return {
            **super().to_db(),
            'id': self.id_,
            'company_id': self.company_id,
            'created_by': self.created_by,
        }


class UpdateDocumentAutomationTemplateCtx(DocumentAutomationTemplateBase):
    deleted_tags_ids: list[str]


class DocumentAutomationAutomationTemplate(_DocumentAutomationTemplateModel):
    """
    Document template how it is stored in a database
    """

    id: str
    company_id: str
    name: str
    is_active: bool
    assigned_to: str | None

    set_review: bool
    review_settings: DocumentAutomationTemplateReviewSettings | None

    set_signers: bool
    signers_settings: DocumentAutomationTemplateSignersSettings | None

    set_viewers: bool
    viewers_settings: DocumentAutomationTemplateViewersSettings | None

    set_fields: bool
    fields_settings: DocumentAutomationTemplateFieldsSettings | None

    set_tags: bool
    tags_settings: DocumentTemplateTagsSettings | None

    created_by: str
    date_created: datetime
    date_deleted: datetime | None
    date_updated: datetime | None

    @classmethod
    def from_row(cls, row: DBRow) -> Self:
        return cls.model_validate(row)

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json', exclude_unset=True)

    def to_api(self) -> DataDict:
        return {
            'id': self.id,
            'name': self.name,
            'review_settings': self.review_settings.to_api() if self.review_settings else None,
            'signers_settings': self.signers_settings.to_api() if self.signers_settings else None,
            'viewers_settings': self.viewers_settings.to_api() if self.viewers_settings else None,
            'fields_settings': self.fields_settings.to_api() if self.fields_settings else None,
            'tags_settings': self.tags_settings.to_api() if self.tags_settings else None,
            'created_by': self.created_by,
            'date_created': self.date_created,
            'date_updated': self.date_updated,
        }

    @property
    def review_entities(self) -> list[DocumentAutomationTemplateReviewEntity]:
        settings = self.review_settings
        if not settings:
            return []

        return settings.reviewer_entities

    @property
    def signers_entities(self) -> list[DocumentAutomationTemplateSignerEntity]:
        settings = self.signers_settings
        if not settings:
            return []

        return settings.signer_entities


@dataclass(frozen=True)
class SaveAssignedDocumentAutomationTemplateCtx:
    document: DocumentWithUploader
    template: DocumentAutomationAutomationTemplate
    user: DBRow

    def to_db(self) -> DataDict:
        return {
            'assigned_by': self.user.role_id,
            'document_id': self.document.id,
            'template': self.template.to_dict(),
        }


@dataclass(frozen=True)
class ActivateAutomationCtx:
    automation_id: str
    template: DocumentAutomationAutomationTemplate
    status: DocumentAutomationStatus

    def to_db(self) -> DataDict:
        return {'status': self.status}
