import logging
from dataclasses import dataclass

from hiku.engine import Context, pass_context

from api.graph.constants import AUTH_USER_KEY, DB_ENGINE_KEY
from api.graph.utils import is_sign_session
from app.document_automation.db import (
    select_document_automation_templates,
    select_template_automation_for_graph,
)
from app.lib.helpers import group_list
from app.lib.types import DataDict, StrList

logger = logging.getLogger(__name__)

InputSettings = list[DataDict | None]


@pass_context
async def resolve_viewers_roles(_: Context, settings: InputSettings) -> list[StrList]:
    res = []
    for setting in settings:
        setting = setting or {}
        res.append(setting.get('viewers_ids', []))
    return res


@pass_context
async def resolve_viewer_groups(_: Context, settings: InputSettings) -> list[StrList]:
    res = []
    for setting in settings:
        setting = setting or {}
        res.append(setting.get('viewers_group_ids', []))
    return res


@pass_context
async def resolve_document_automation_templates(ctx: Context, options: DataDict) -> StrList:
    if is_sign_session(ctx):
        return []

    user = ctx[AUTH_USER_KEY]
    company_id = user.company_id

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        templates = await select_document_automation_templates(
            conn=conn,
            company_id=company_id,
            is_active=options.get('is_active'),
            limit=options['limit'],
            offset=options['offset'],
        )

    return [template.id for template in templates]


@pass_context
async def resolve_automation(ctx: Context, template_ids: StrList) -> list[StrList]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        automation = await select_template_automation_for_graph(conn, template_ids)

    mapping = group_list(automation, lambda a: a.template_id)
    return [[automation.id for automation in mapping[template_id]] for template_id in template_ids]


@dataclass(frozen=True)
class _Entity:
    id: str
    type: str


def _get_entities(
    settings: InputSettings,
    setting_key: str,
    fallback_setting_key: str,
) -> list[list[_Entity]]:
    """
    Tries to get data for given entity_type.
    Use a new format with support of different types.
    If a new format is not used, try to build it from deprecated format.

    References: _BaseTemplateValidator class
    """
    res = []
    for setting in settings:
        setting = setting or {}
        entities = setting.get(setting_key)
        if entities:
            items = []
            for item in entities:
                items.append(_Entity(id=item['id'], type=item['type']))
            res.append(items)

        # Fall back to deprecated signers_ids
        else:
            items = []
            for signers_id in setting.get(fallback_setting_key, []):
                items.append(_Entity(id=signers_id, type='role'))
            res.append(items)

    return res


async def resolve_signers(settings: InputSettings) -> list[list[_Entity]]:
    return _get_entities(
        settings,
        'signer_entities',
        'signers_ids',
    )


async def resolve_reviewers(settings: InputSettings) -> list[list[_Entity]]:
    return _get_entities(
        settings,
        'reviewer_entities',
        'reviewers_ids',
    )
