import sqlalchemy as sa

from app.document_versions.enums import DocumentVersionType
from app.models import columns, metadata

document_version_table = sa.Table(
    'document_versions',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        ondelete='NO ACTION',
        index=True,
    ),
    sa.Column('name', sa.Text(), nullable=False),
    # Represents why new version was created
    columns.SoftEnum('type', DocumentVersionType, nullable=False),
    # Represents extension of document version
    sa.Column('extension', sa.String(64), nullable=False),
    # Represents uploader role
    columns.ForeignKey(
        'role_id',
        'roles.id',
        ondelete='NO ACTION',
        index=True,
        nullable=False,
    ),
    # company EDRPOU of the uploader role
    columns.EDRPOU('company_edrpou', nullable=False),
    columns.DateCreated(),
    # Represents if document version was sent to recipient
    sa.Column('is_sent', sa.<PERSON>(), nullable=False, default=False),
    # eusign hash GOST 34.311
    sa.Column('content_hash', sa.String(128), nullable=False),
    sa.Column('content_length', sa.Integer(), nullable=False),
)
