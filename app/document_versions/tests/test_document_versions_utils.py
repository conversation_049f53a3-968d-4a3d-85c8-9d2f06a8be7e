import datetime

from app.document_versions.db import select_latest_document_version
from app.document_versions.enums import DocumentVersionType
from app.document_versions.utils import (
    add_document_content_version,
    get_document_version_key,
)
from app.lib.datetime_utils import utc_now
from app.tests.common import prepare_client, prepare_document_data


async def test_add_document_content_version(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, user)

    assert len(s3_emulation) == 1

    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            content=b'content_version1',
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            date_created=utc_now(),
            name='Test name',
            extension='.pdf',
            role_id=user.role_id,
            upload_type=DocumentVersionType.new_upload,
        )

        version = await select_latest_document_version(conn, document.id)

        version1_key = get_document_version_key(version_id=version.id)
        assert len(s3_emulation) == 2
        file = s3_emulation[version1_key]
        assert file.body == b'content_version1'
        assert file.key == version1_key

        await add_document_content_version(
            conn=conn,
            content=b'content_version2',
            document_id=document.id,
            company_edrpou=user.company_edrpou,
            role_id=user.role_id,
            date_created=utc_now() + datetime.timedelta(minutes=1),
            name='Test name',
            extension='.pdf',
            upload_type=DocumentVersionType.new_upload,
        )

        version = await select_latest_document_version(conn, document.id)

        version2_key = get_document_version_key(version_id=version.id)
        assert len(s3_emulation) == 3
        file = s3_emulation[version2_key]
        assert file.body == b'content_version2'
        assert file.key == version2_key
        assert version1_key != version2_key
