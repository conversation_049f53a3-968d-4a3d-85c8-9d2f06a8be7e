from app.i18n import _, set_locale_context


def test_translate():
    s = _('Токен')
    assert str(s) == 'Токен'

    with set_locale_context('en'):
        assert str(s) == 'The token'

    s = _('Переклад, який не існує')
    assert str(s) == 'Переклад, який не існує'
    with set_locale_context('en'):
        assert str(s) == 'Переклад, який не існує'


def test_translate_with_bind():
    s = _('Тип підпису: {0}').bind('internal')
    assert str(s) == 'Тип підпису: internal'

    with set_locale_context('en'):
        assert str(s) == 'Signature type: internal'

    s = _('Переклад, який не існує {lang}').bind(lang='uk')
    assert str(s) == 'Переклад, який не існує uk'
    with set_locale_context('en'):
        assert str(s) == 'Переклад, який не існує uk'
