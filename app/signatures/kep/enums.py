from enum import StrEnum, unique


@unique
class KEPUrl(StrEnum):
    # {
    #     "certificates": ["base64data"],
    #     "is_mobile_logged": true
    # }
    server_certificates = '/api/get-certificates'
    # {
    #     "certificates": [
    #         {
    #             "acsk_key_id": ...,
    #             "status": "not_registered" | "registered" | "rejected" | "blocked" | "restored",  # noqa: E501
    #             "date_started": "2022-11-17T14:39:10+00:00",
    #             "date_finished": "2024-11-17T14:39:10+00:00",
    #             "storage_type": "cloud" | "file" | "hardware_key" | "remote_file",
    #             "signature_type": "signature" | "stamp" | "rro",
    #             "validity_term": "one_year" | "two_years",
    #             "rro": "",
    #             "inn": "1212121212,
    #             "company_name": "Company" | null,
    #             "company_edrpou": "*********",
    #             "phone": "+380999999999",
    #         },
    #         ...
    #     ],
    #     "is_mobile_logged": true
    # }
    user_certificates = '/api/integration-encrypted/internal/user-certificates'

    # No response
    vchasno_profile_sync = '/api/integration-encrypted/internal/update-event'
