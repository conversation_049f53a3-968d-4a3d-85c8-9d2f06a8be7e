from unittest.mock import AsyncMock

from app.auth import types as auth_types
from app.signatures import kep
from app.signatures.kep import types
from app.tests import common


async def test_get_kep_user_certificates(aiohttp_client, monkeypatch):
    """
    Given a user
    When calling kep API for certificates
    Expected to return valid response
    """
    # Arrange
    app, client, user = await common.prepare_client(aiohttp_client)

    auth_user = auth_types.to_auth_user(user)

    # Mock kep response
    certificates_response = [
        {
            'acsk_key_id': 'test',
            'status': 'registered',
            'password_type': 'pin_code',
            'date_started': 'test',
            'date_finished': 'test',
            'storage_type': 'cloud',
            'signature_type': 'test',
            'validity_term': 'test',
            'rro': 'test',
            'inn': 'test',
            'surname': 'test',
            'name': 'test',
            'patronymic': 'test',
            'position': 'test',
            'company_name': 'test',
            'company_edrpou': 'test',
            'phone': 'test',
        },
        {
            'acsk_key_id': 'test_1',
            'status': 'restored',
            'password_type': 'password',
            'date_started': 'test_1',
            'date_finished': 'test_1',
            'storage_type': 'cloud',
            'signature_type': 'test_1',
            'validity_term': 'test_1',
            'rro': 'test_1',
            'inn': 'test_1',
            'surname': 'test_1',
            'name': 'test_1',
            'patronymic': 'test_1',
            'company_name': 'test_1',
            'company_edrpou': 'test_1',
            'phone': 'test_1',
            'position': 'test_1',
        },
    ]
    monkeypatch.setattr(
        kep.client,
        'vchasno_kep_integration_request',
        AsyncMock(return_value={'certificates': certificates_response}),
    )

    # Act
    result = await kep.client.get_kep_user_certificates(auth_user=auth_user)

    # Assert
    assert all(
        types.KEPCertificate(**cert) in result.certificates for cert in certificates_response
    )
    assert result.is_mobile_logged is False
