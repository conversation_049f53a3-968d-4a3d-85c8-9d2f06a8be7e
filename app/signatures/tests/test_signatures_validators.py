import pytest
import trafaret as t

from api.errors import AccessDenied
from app.auth.types import to_auth_user
from app.groups.db import insert_group_member
from app.groups.utils import add_group, get_group_members_by_group_ids
from app.lib.enums import DocumentStatus, SignatureFormat
from app.lib.types import AnyDict
from app.signatures.db import select_document_signers_extended, select_signatures
from app.signatures.enums import SignatureSource
from app.signatures.tables import document_signer_table
from app.signatures.validators import (
    ADD_SIGNATURE_TRAFARET,
    validate_add_signature_state,
    validate_document_signers,
)
from app.tests.common import (
    TEST_UUID,
    cleanup_on_teardown,
    prepare_client,
    prepare_document_data,
    prepare_flow_item,
    prepare_signature_data,
    prepare_user_data,
)


@pytest.mark.parametrize(
    'data, next_signer, is_valid',
    [
        ({'signers': [], 'signatures_from': [], 'parallel_signing': True}, None, True),
        (
            {
                'signers': ['<EMAIL>', '<EMAIL>'],
                'signatures_from': [],
                'parallel_signing': True,
            },
            None,
            True,
        ),
        (
            {
                'signers': ['<EMAIL>', '<EMAIL>'],
                'signatures_from': [],
                'parallel_signing': True,
            },
            None,
            False,
        ),
        (
            {
                'signers': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'signatures_from': ['<EMAIL>', '<EMAIL>'],
                'parallel_signing': True,
            },
            None,
            True,
        ),
        (
            {
                'signers': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'signatures_from': ['<EMAIL>'],
                'parallel_signing': False,
            },
            '<EMAIL>',
            False,
        ),
        (
            {
                'signers': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'signatures_from': ['<EMAIL>'],
                'parallel_signing': False,
            },
            '<EMAIL>',
            True,
        ),
        (
            {
                'signers': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'signatures_from': ['<EMAIL>'],
                'parallel_signing': False,
            },
            '<EMAIL>',
            True,
        ),
        (
            {
                'signers': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'signatures_from': ['<EMAIL>'],
                'parallel_signing': False,
            },
            '<EMAIL>',
            True,
        ),
        (
            {
                'signers': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'signatures_from': ['<EMAIL>', '<EMAIL>'],
                'parallel_signing': False,
            },
            '<EMAIL>',
            True,
        ),
    ],
)
async def test_validate_document_signers(aiohttp_client, data, is_valid, next_signer):
    app, _, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    coworker_2 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(app, user)

    signers_map = {
        '<EMAIL>': user,
        '<EMAIL>': coworker_1,
        '<EMAIL>': coworker_2,
    }

    try:
        async with app['db'].acquire() as conn:
            signers_data = [
                {
                    'document_id': document.id,
                    'company_id': signers_map[email].company_id,
                    'role_id': signers_map[email].role_id,
                    'order': None if data['parallel_signing'] else idx,
                }
                for idx, email in enumerate(data['signers'], start=1)
            ]
            if signers_data:
                await conn.execute(document_signer_table.insert().values(signers_data))

            for email in data['signatures_from']:
                await prepare_signature_data(app, signers_map[email], document, update_access=False)

            signatures = await select_signatures(conn, [document.id])
            signers = await select_document_signers_extended(
                conn, company_id=user.company_id, document_id=document.id
            )
            group_members = await get_group_members_by_group_ids(
                conn,
                group_ids=[s.group_id for s in signers if s.group_id],
            )

            if is_valid:
                next_signer_actual = validate_document_signers(
                    user=user,
                    document_signers=signers,
                    signatures=signatures,
                    group_members=group_members,
                )
                if next_signer_actual:
                    assert next_signer_actual.role_id == signers_map[next_signer].role_id
            else:
                with pytest.raises(AccessDenied):
                    validate_document_signers(
                        user=user,
                        document_signers=signers,
                        signatures=signatures,
                        group_members=group_members,
                    )
    finally:
        await cleanup_on_teardown(app)


async def test_validate_flow_add_signature(aiohttp_client):
    app, _, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(
        app,
        user,
        create_document_access_for_recipients=True,
        expected_owner_signatures=0,
        status_id=DocumentStatus.uploaded.value,
        document_recipients=[{'edrpou': '00000102', 'emails': None}],
        is_multilateral=True,
    )
    flow = await prepare_flow_item(
        app,
        document.id,
        company_id=user.company_id,
        edrpou=user.company_edrpou,
        pending_signatures_count=1,
        receivers=[user.email],
    )
    async with app['db'].acquire() as conn:
        # TODO: check if we can sign multilateral document on flow abscence
        result = await validate_add_signature_state(
            conn=conn,
            user=to_auth_user(user),
            document=document,
            signature_edrpou=user.company_edrpou,
            signatures=[],
        )
        assert result.flow.id_ == flow.id_
        result = await validate_add_signature_state(
            conn=conn,
            user=None,
            document=document,
            signature_edrpou=user.company_edrpou,
            signatures=[],
        )
        assert result.flow.id_ == flow.id_


@pytest.mark.parametrize(
    'data',
    [
        (
            {
                'document_id': TEST_UUID,
                'source': 'error',
                'format': SignatureFormat.internal_separated.value,
            },
            {
                'document_id': TEST_UUID,
                'source': SignatureSource.web.value,
                'format': 'error',
            },
        )
    ],
)
async def test_invalid_add_signature_trafaret(data):
    validator = ADD_SIGNATURE_TRAFARET
    with pytest.raises(t.dataerror.DataError):
        validator.check(data)


@pytest.mark.parametrize(
    'data',
    [
        {
            'document_id': TEST_UUID,
            'source': SignatureSource.web.value,
            'format': SignatureFormat.internal_separated.value,
        },
        {
            'document_id': TEST_UUID,
            'source': 'api',
            'format': SignatureFormat.internal_separated.value,
        },
    ],
)
async def test_add_signature_trafaret(data):
    ADD_SIGNATURE_TRAFARET.check(data)


async def test_validate_document_signers_with_group_parallel(aiohttp_client):
    """
    Given:
    - 3 roles
    - and one group with a member
    - document with parallel signing
    When:
    - validate document signers
    Then:
    - should pass validation
    """
    # Arrange
    app, _, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    coworker_2 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user.role_id,
            created_by=user.role_id,
        )

        signers_data: list[AnyDict] = [
            {
                'document_id': document.id,
                'company_id': role.company_id,
                'role_id': role.role_id,
                'group_id': None,
                'order': None,
            }
            for role in [user, coworker_1, coworker_2]
        ]
        signers_data.append(
            {
                'document_id': document.id,
                'company_id': user.company_id,
                'role_id': None,
                'group_id': group.id,
                'order': None,
            }
        )
        await conn.execute(document_signer_table.insert().values(signers_data))

        signatures = await select_signatures(conn, [document.id])
        signers = await select_document_signers_extended(
            conn, company_id=user.company_id, document_id=document.id
        )
        group_members = await get_group_members_by_group_ids(
            conn,
            group_ids=[s.group_id for s in signers if s.group_id],
        )

        # Act
        next_signer = validate_document_signers(
            user=user,
            document_signers=signers,
            signatures=signatures,
            group_members=group_members,
        )
        # Assert
        assert next_signer is None


async def test_validate_document_signers_with_group_ordered(aiohttp_client):
    """
    Given:
    - 3 roles
    - and one group with a member
    - document with ordered signing
    - document is signed by the first signer that are member of the group (who is the second signer)
    When:
    - validate document signers
    Then:
    - next signer should be the second signer
    """
    # Arrange
    app, _, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    coworker_2 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user.role_id,
            created_by=user.role_id,
        )

        signers_data = [
            {
                'document_id': document.id,
                'company_id': user.company_id,
                'role_id': user.role_id,
                'group_id': None,
                'order': 1,
            },
            {
                'document_id': document.id,
                'company_id': user.company_id,
                'role_id': None,
                'group_id': group.id,
                'order': 2,
            },
            {
                'document_id': document.id,
                'company_id': user.company_id,
                'role_id': coworker_1.role_id,
                'group_id': None,
                'order': 3,
            },
            {
                'document_id': document.id,
                'company_id': user.company_id,
                'role_id': coworker_2.role_id,
                'group_id': None,
                'order': 4,
            },
        ]
        await conn.execute(document_signer_table.insert().values(signers_data))

        await prepare_signature_data(app, user, document, update_access=False, group_ids=[group.id])

        signatures = await select_signatures(conn, [document.id])
        signers = await select_document_signers_extended(
            conn, company_id=user.company_id, document_id=document.id
        )
        group_members = await get_group_members_by_group_ids(
            conn,
            group_ids=[s.group_id for s in signers if s.group_id],
        )

        # Act
        next_signer = validate_document_signers(
            user=user,
            document_signers=signers,
            signatures=signatures,
            group_members=group_members,
        )

        # Assert
        assert next_signer.role_id == coworker_1.role_id


async def test_validate_document_signers_with_group_ordered_next_signer_is_group(aiohttp_client):
    """
    Given:
    - 3 roles
    - and one group with a member
    - document with ordered signing
    - document is signed by the first signer that is **NOT** member of
    the group (who is the second signer)
    When:
    - validate document signers
    Then:
    - next signer should be the second signer
      because the first signer is member of the group
    """
    # Arrange
    app, _, user = await prepare_client(aiohttp_client, email='<EMAIL>')
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    coworker_2 = await prepare_user_data(app, email='<EMAIL>')
    document = await prepare_document_data(app, user)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=coworker_1.role_id,
            created_by=user.role_id,
        )

        signers_data = [
            {
                'document_id': document.id,
                'company_id': user.company_id,
                'role_id': user.role_id,
                'group_id': None,
                'order': 1,
            },
            {
                'document_id': document.id,
                'company_id': user.company_id,
                'role_id': None,
                'group_id': group.id,
                'order': 2,
            },
            {
                'document_id': document.id,
                'company_id': user.company_id,
                'role_id': coworker_1.role_id,
                'group_id': None,
                'order': 3,
            },
            {
                'document_id': document.id,
                'company_id': user.company_id,
                'role_id': coworker_2.role_id,
                'group_id': None,
                'order': 4,
            },
        ]
        await conn.execute(document_signer_table.insert().values(signers_data))

        await prepare_signature_data(app, user, document, update_access=False)

        signatures = await select_signatures(conn, [document.id])
        signers = await select_document_signers_extended(
            conn, company_id=user.company_id, document_id=document.id
        )
        group_members = await get_group_members_by_group_ids(
            conn,
            group_ids=[s.group_id for s in signers if s.group_id],
        )

        # Act
        next_signer = validate_document_signers(
            user=user,
            document_signers=signers,
            signatures=signatures,
            group_members=group_members,
        )

        # Assert
        assert next_signer.group_id == group.id
