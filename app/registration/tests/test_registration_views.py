import secrets
import unittest
from datetime import datetime
from http import HTTPStatus
from pathlib import Path
from unittest import mock

import pytest
import sqlalchemy as sa
import ujson
from conciergelib import headers as concierge_headers
from redis.asyncio import Redis

from api.errors import Code
from app.app import create_app
from app.auth import concierge
from app.auth.db import (
    insert_company,
    select_base_user,
    select_company_by_edrpou,
    select_coworkers,
    select_role,
    select_role_by,
    select_roles,
    select_user_by_email,
    update_company_entity_count,
    update_user,
)
from app.auth.enums import RoleActivationSource, RoleStatus, StatEntity
from app.auth.helpers import check_password_hash
from app.auth.schemas import CompanyConfig, DefaultRolePermissionsKey
from app.auth.tables import company_table, role_table, user_table
from app.auth.tests.utils import (
    get_base_user,
    get_user_onboarding,
)
from app.billing.db import select_company_accounts, update_billing_company_config
from app.billing.enums import AccountRate, CompanyLimit
from app.events.user_actions import Action
from app.events.user_actions.tables import user_actions_table
from app.lib import eusign_utils
from app.lib.enums import DocumentStatus, SignatureType, UserRole
from app.models import count, select_all, select_one
from app.profile.emailing import generate_password_recover_token
from app.registration.enums import RegistrationMethod, RegistrationSource
from app.registration.tables import registration_sources_table
from app.registration.tests.utils import request_registration_token
from app.services import services
from app.tests.common import (
    LOGIN_URL,
    LOGOUT_URL,
    SUPER_ADMIN_EDRPOU,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    VCHASNO_EDRPOU,
    cleanup_on_teardown,
    insert_test_user,
    prepare_app_client,
    prepare_auth_headers,
    prepare_client,
    prepare_company_config,
    prepare_company_data,
    prepare_contact,
    prepare_contacts,
    prepare_coworker_role,
    prepare_document_data,
    prepare_form_data,
    prepare_referer_headers,
    prepare_sign_session_data,
    prepare_sign_session_headers,
    prepare_signature_form_data,
    prepare_signature_info,
    prepare_user_data,
    request_save_registration_info,
    set_company_config,
    sign_and_send_document,
)
from app.tokens.db import (
    insert_short_registration_token,
    select_short_registration_token,
)
from app.tokens.services import create_short_registration_token
from app.tokens.tables import short_registration_token_table
from app.tokens.utils import generate_jwt_token
from app.trigger_notifications.db import select_all_trigger_notifications
from app.trigger_notifications.enums import (
    TriggerNotificationStatus,
    TriggerNotificationType,
)
from app.vchasno_profile.db import select_vchasno_user
from app.vchasno_profile.tests.utils import get_vchasno_user, prepare_vchasno_user

DATA_PATH = Path(__file__).parent / 'data'

COMPLETE_REGISTRATION_URL = '/internal-api/registration/complete'
INVITE_NEW_USER_URL = '/internal-api/invite'
CHANGE_EMAIL_BEFORE_CONFIRMATION_URL = '/auth-api/registration/change-email'
REGISTRATION_URL = '/auth-api/registration'
REGISTRATION_WITH_TOKEN_URL = '/auth-api/registration/token'
RESEND_CONFIRMATION_EMAIL_URL = '/internal-api/registration/resend-confirmation-email'
INVITE_CONTACTS_URL = '/internal-api/invite/contacts'
CHECK_EMAIL_EXISTS_URL = '/internal-api/registration/check-email'

TEST_COMPANY_EDRPOU = '12345678'
TEST_COMPANY_NAME = 'Test Company'
TEST_NEW_COMPANY_EDRPOU = '87654321'
TEST_PARTNER_EDRPOU = '13245768'
TEST_PARTNER_EMAIL = '<EMAIL>'
TEST_NEW_PARTNER_EMAIL = '<EMAIL>'
TEST_EMAIL = '<EMAIL>'
TEST_NEW_EMAIL = '<EMAIL>'
TEST_NAME = 'Test User'
TEST_PASSWORD = ']Se@cRet926-PassW_0rD'
TEST_UUID = '22b60f5f-5617-47cc-b476-7d1db455fa5e'
SIGNED_AND_SENT = DocumentStatus.signed_and_sent.value

COMPANY_REGISTRATION_TOKEN_URL = '/companies/registration/token'


TEST_UUID_1 = '49cf0216-b36e-4b29-a096-4d3f34e9ab4a'
TEST_UUID_2 = '8940ac9c-6c25-4338-b604-baba747be1ee'
TEST_UUID_3 = '39d62f0a-0569-464e-8b77-28ad2d068f7e'
TEST_UUID_4 = '52282a9e-34b9-45e7-8fe6-07048fcf6fd6'
TEST_UUID_5 = '1467d588-0c03-40d3-99bd-37d823b9f087'

AUTOGENERATED_EDRPOU_1 = 'auto_Y4wRy2p7kasEgQmOSt6AKTPWC8M05rGF'


@pytest.mark.parametrize(
    'is_legal, expected_is_legal',
    [
        (None, True),
        (False, True),
        (True, True),
    ],
)
async def test_complete_registration(
    aiohttp_client,
    is_legal,
    expected_is_legal,
    esputnik_box,
):
    app, client = await prepare_app_client(aiohttp_client)
    headers = prepare_referer_headers(client)

    async with app['db'].acquire() as conn:
        user = await insert_test_user(
            conn,
            {
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
            },
        )
        assert user.registration_completed is False
        assert user.first_name is None

    data = ujson.dumps(
        {
            'company_edrpou': TEST_COMPANY_EDRPOU,
            'company_name': TEST_COMPANY_NAME,
            'name': TEST_NAME,
            'is_legal': is_legal,
        }
    )
    assert len(esputnik_box) == 0
    response = await client.post(
        LOGIN_URL,
        data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
        headers=headers,
    )
    assert response.status == 200

    response = await client.post(COMPLETE_REGISTRATION_URL, data=data, headers=headers)
    assert response.status == 200
    # registration event & first employee event
    assert len(esputnik_box) == 4
    async with app['db'].acquire() as conn:
        user = await select_user_by_email(conn, user.email)
        company_id = user.company_id
        assert user.registration_completed is True
        assert user.first_name == TEST_NAME
        assert user.company_edrpou == TEST_COMPANY_EDRPOU
        assert user.company_name == TEST_COMPANY_NAME
        assert user.is_legal is expected_is_legal
        # Check permissions
        assert user.can_edit_company is True
        assert user.can_edit_roles is True
        assert user.user_role == UserRole.admin.value

        # Check registration bonus account
        accounts = await select_company_accounts(conn, company_id)
        assert len(accounts) == 1
        assert accounts[0].rate == AccountRate.free

        role = await select_role(conn, company_id=company_id, user_id=user.id)
        assert role.status == RoleStatus.active
        assert role.activated_by is None
        assert role.activation_source == RoleActivationSource.signature
        assert role.date_activated is not None

        # Add second role
        second_user = await prepare_user_data(app, email='<EMAIL>')
        await prepare_coworker_role(
            conn,
            {
                'company_id': company_id,
                'status': RoleStatus.active,
                'user_id': second_user.id,
            },
        )

        # Check for company billing accounts
        accounts = await select_company_accounts(conn, company_id)
        assert len(accounts) == 1
        assert accounts[0].rate == AccountRate.free

        notifications = await select_all_trigger_notifications(conn)
        assert len(notifications) == 2
        for notification in notifications:
            assert notification.status == TriggerNotificationStatus.new
            assert notification.role_id == user.role_id

    response = await client.post(
        COMPLETE_REGISTRATION_URL,
        data=data,
        headers=headers,
    )
    assert response.status == 200


@pytest.mark.parametrize(
    'coworker, expected_events',
    (
        pytest.param(
            {'role_status': RoleStatus.pending},
            [
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/event/', 'EDO_First_employee_registration'),
                ('https://esputnik.com/api/v1/event/', 'EDO_Welcome_TOV'),
            ],
            id='coworker_pending',
        ),
        pytest.param(
            {'role_status': RoleStatus.denied},
            [
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/event/', 'EDO_First_employee_registration'),
                ('https://esputnik.com/api/v1/event/', 'EDO_Welcome_TOV'),
            ],
            id='coworker_denied',
        ),
        pytest.param(
            {'role_status': RoleStatus.deleted},
            [
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/event/', 'EDO_First_employee_registration'),
                ('https://esputnik.com/api/v1/event/', 'EDO_Welcome_TOV'),
            ],
            id='coworker_deleted',
        ),
        pytest.param(
            {'role_status': RoleStatus.blocked_2fa},
            [
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/event/', 'EDO_First_employee_registration'),
                ('https://esputnik.com/api/v1/event/', 'EDO_Welcome_TOV'),
            ],
            id='coworker_blocked_2fa',
        ),
        pytest.param(
            {'role_status': RoleStatus.user_deleted},
            [
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/event/', 'EDO_First_employee_registration'),
                ('https://esputnik.com/api/v1/event/', 'EDO_Welcome_TOV'),
            ],
            id='coworker_user_deleted',
        ),
        pytest.param(
            None,
            [
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/contacts/', None),
                ('https://esputnik.com/api/v1/event/', 'EDO_First_employee_registration'),
                ('https://esputnik.com/api/v1/event/', 'EDO_Welcome_TOV'),
            ],
            id='coworker_none',
        ),
    ),
)
async def test_create_company_events(
    aiohttp_client,
    esputnik_box: list[dict],
    coworker: dict | None,
    expected_events: list[tuple],
):
    """Test count esputnik events"""
    app, client = await prepare_app_client(aiohttp_client)
    headers = prepare_referer_headers(client)

    async with app['db'].acquire() as conn:
        user = await insert_test_user(
            conn,
            {
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
            },
        )
        assert user.registration_completed is False

    if coworker:
        await prepare_user_data(
            app,
            email='<EMAIL>',
            company_edrpou=TEST_COMPANY_EDRPOU,
            **coworker,
        )

    data = {
        'company_edrpou': TEST_COMPANY_EDRPOU,
        'company_name': TEST_COMPANY_NAME,
        'name': TEST_NAME,
        'is_legal': True,
    }

    response = await client.post(
        path=LOGIN_URL,
        json={'email': TEST_EMAIL, 'password': TEST_PASSWORD},
        headers=headers,
    )
    assert response.status == 200

    response = await client.post(COMPLETE_REGISTRATION_URL, json=data, headers=headers)
    assert response.status == 200
    assert len(esputnik_box) == len(expected_events)
    assert sorted([e['__key'] for e in esputnik_box]) == sorted(expected_events)


async def test_change_email_before_confirmation(aiohttp_client, monkeypatch, crm_box):
    app, client, master = await prepare_client(
        aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
    )
    headers = prepare_referer_headers(client)
    email_to_replace = '  <EMAIL>  '

    async with app['db'].acquire() as conn:
        user = await insert_test_user(
            conn,
            {
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
            },
        )
        assert user.registration_completed is False
        assert user.first_name is None
        assert user.email_confirmed is False

    response = await client.post(
        LOGIN_URL,
        data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
        headers=headers,
    )
    assert response.status == 200

    update_user_profile_mock = mock.AsyncMock()
    monkeypatch.setattr(concierge, 'update_user_profile', update_user_profile_mock)

    response = await client.patch(
        CHANGE_EMAIL_BEFORE_CONFIRMATION_URL,
        data=ujson.dumps(
            {
                'email': email_to_replace,
                'redirect': '/app/registration/choose-project?',
            },
        ),
        headers={
            **headers,
            concierge_headers.USER_ID: user.id,
            concierge_headers.ACCESS_TOKEN: 'fake-token',
        },
    )
    assert response.status == 206  # Partial Content
    async with app['db'].acquire() as conn:
        new_user = await select_base_user(conn, user_id=user.id)
    assert user.email != new_user.email
    assert new_user.email == email_to_replace.strip()

    vchasno_user = await get_vchasno_user(user_id=user.id)
    assert vchasno_user.email == email_to_replace.strip()
    assert vchasno_user.id == user.id

    update_user_profile_mock.assert_called_once()

    assert len(crm_box) == 1
    assert crm_box[0] == {
        'email': email_to_replace.strip(),
        'vchasno_id': user.id,
        'mobile_phone': '',
    }


async def test_complete_registration_after_invite_from_coworker(aiohttp_client):
    app, client, master = await prepare_client(
        aiohttp_client,
        company_edrpou=VCHASNO_EDRPOU,
    )
    headers = prepare_referer_headers(client)

    async with app['db'].acquire() as conn:
        user = await insert_test_user(
            conn,
            {
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
                'email_confirmed': True,
            },
        )
        assert user.registration_completed is False
        assert user.first_name is None

    try:
        # Invite user as coworker
        response = await client.post(
            INVITE_NEW_USER_URL,
            data=ujson.dumps(
                {
                    'email': user.email,
                    'edrpou': master.company_edrpou,
                }
            ),
            headers=prepare_auth_headers(master),
        )
        assert response.status == 201

        # User did not complete registration, but has active role, invited
        # as coworker
        async with app['db'].acquire() as conn:
            user_role = await select_one(
                conn, (role_table.select().where(role_table.c.user_id == user.id))
            )
            assert user_role.status == RoleStatus.active
            assert user_role.company_id == master.company_id

        # Complete users's registration
        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
            headers=headers,
        )
        assert response.status == 200

        async with app['db'].acquire() as conn:
            user = await select_user_by_email(conn, user.email, company_edrpou=VCHASNO_EDRPOU)

            assert user.registration_completed is True
            assert user.company_edrpou == VCHASNO_EDRPOU
            assert user.is_legal is True
            # Check permissions
            assert user.can_edit_company is False
            assert user.can_edit_roles is False
            assert user.user_role == UserRole.user.value
    finally:
        await cleanup_on_teardown(app)


async def test_complete_registration_forbidden(aiohttp_client):
    client = await aiohttp_client(create_app())
    response = await client.post(
        COMPLETE_REGISTRATION_URL,
        data=ujson.dumps(
            {
                'company_edrpou': TEST_COMPANY_EDRPOU,
                'company_name': TEST_COMPANY_NAME,
                'name': TEST_NAME,
            }
        ),
        headers=prepare_referer_headers(client),
    )
    assert response.status == 403


@pytest.mark.parametrize(
    'by_coworker, status, role_status, expected_role_status, expected_registration_completed',
    [
        (True, HTTPStatus.CREATED, RoleStatus.pending, RoleStatus.active, True),
        (True, HTTPStatus.CREATED, RoleStatus.denied, RoleStatus.active, True),
        (False, HTTPStatus.CREATED, RoleStatus.denied, RoleStatus.denied, False),
        (True, HTTPStatus.BAD_REQUEST, RoleStatus.deleted, RoleStatus.deleted, False),
        (False, HTTPStatus.BAD_REQUEST, RoleStatus.pending, RoleStatus.pending, False),
    ],
)
async def test_invite_pending_role(
    aiohttp_client,
    by_coworker,
    status,
    role_status,
    expected_role_status,
    expected_registration_completed,
):
    app, client, user = await prepare_client(aiohttp_client)

    # Create new user that is coworker for `user` and with pending role
    await prepare_user_data(
        app,
        email=TEST_EMAIL,
        company_edrpou=user.company_edrpou,
        role_status=role_status,
        registration_completed=False,
        email_confirmed=True,
    )

    # User that is not coworker for `user_coworker`
    alien = await prepare_user_data(app, email='<EMAIL>', company_edrpou='10203040')
    assert alien.company_edrpou != user.company_edrpou

    response = await client.post(
        INVITE_NEW_USER_URL,
        json={'email': TEST_EMAIL, 'edrpou': user.company_edrpou},
        headers=prepare_auth_headers(user if by_coworker else alien),
    )
    assert response.status == status

    async with app['db'].acquire() as conn:
        new_user = await select_one(
            conn=conn,
            query=(
                sa.select(
                    [
                        user_table.c.registration_completed,
                        role_table.c.status,
                    ]
                )
                .select_from(user_table.join(role_table, role_table.c.user_id == user_table.c.id))
                .where(
                    sa.and_(
                        user_table.c.email == TEST_EMAIL,
                        role_table.c.company_id == user.company_id,
                    )
                )
            ),
        )
        assert new_user is not None
        assert new_user.status == expected_role_status
        assert new_user.registration_completed is expected_registration_completed


@pytest.mark.parametrize(
    'data, status, expected',
    [
        # just try to invite coworker without data
        (
            {'email': TEST_EMAIL, 'edrpou': TEST_COMPANY_EDRPOU},
            HTTPStatus.CREATED,
            {'position': None},
        ),
        # position is None
        (
            {'email': TEST_EMAIL, 'edrpou': TEST_COMPANY_EDRPOU, 'position': None},
            HTTPStatus.CREATED,
            {'position': None},
        ),
        # position is not None
        (
            {'email': TEST_EMAIL, 'edrpou': TEST_COMPANY_EDRPOU, 'position': 'IT'},
            HTTPStatus.CREATED,
            {'position': 'IT'},
        ),
        # not coworker with position, user not exists
        (
            {'email': TEST_EMAIL, 'edrpou': '99999999', 'position': 'ІТ'},
            HTTPStatus.OK,
            None,  # role not exits
        ),
        # not coworker with position, user exists
        (
            {'email': '<EMAIL>', 'edrpou': '99999999', 'position': 'ІТ'},
            HTTPStatus.CREATED,
            None,  # cant control other company
        ),
    ],
)
async def test_invite_with_role_settings(aiohttp_client, data, status, expected):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )
    await prepare_user_data(app, company_edrpou='********', email='<EMAIL>')

    headers = prepare_auth_headers(user)
    response = await client.post(INVITE_NEW_USER_URL, json=data, headers=headers)
    assert response.status == status

    async with app['db'].acquire() as conn:
        role = await select_role_by(conn=conn, company_edrpou=data['edrpou'], mixed=data['email'])

    if expected is None:
        assert role is None
        return
    assert role.position == expected['position']


async def test_complete_registration_by_master_account(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)
    headers = prepare_referer_headers(client)

    async with app['db'].acquire() as conn:
        user = await insert_test_user(
            conn,
            {
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
            },
        )
        assert user.registration_completed is False
        assert user.first_name is None
        user_two = await insert_test_user(
            conn,
            {
                'email': TEST_NEW_EMAIL,
                'password': TEST_PASSWORD,
            },
        )
        assert user_two.registration_completed is False
        assert user_two.first_name is None

    data = ujson.dumps(
        {
            'company_edrpou': TEST_COMPANY_EDRPOU,
            'company_name': TEST_COMPANY_NAME,
            'name': TEST_NAME,
            'is_legal': True,
        }
    )

    try:
        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
            headers=headers,
        )
        assert response.status == 200

        response = await client.post(COMPLETE_REGISTRATION_URL, data=data, headers=headers)
        assert response.status == 200

        async with app['db'].acquire() as conn:
            user = await select_user_by_email(conn, user.email)
            assert user.registration_completed is True
            assert user.first_name == TEST_NAME
            assert user.company_name == TEST_COMPANY_NAME
            assert user.is_legal is True
            # Check permissions
            assert user.can_edit_company is True
            assert user.can_edit_roles is True
            assert user.user_role == UserRole.admin.value

        response = await client.post(LOGOUT_URL, headers=headers)
        assert response.status == 200

        # Complete registration for user_two
        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps({'email': TEST_NEW_EMAIL, 'password': TEST_PASSWORD}),
            headers=headers,
        )
        assert response.status == 200

        # with default role permissions settings in company next users also will be
        # admin and have permission to see documents in company
        response = await client.post(COMPLETE_REGISTRATION_URL, data=data, headers=headers)
        assert response.status == 200

        async with app['db'].acquire() as conn:
            user_two = await select_user_by_email(conn, user_two.email)
            assert user_two.registration_completed is True
            assert user_two.first_name == TEST_NAME
            assert user_two.company_name == TEST_COMPANY_NAME
            assert user_two.is_legal is True
            # Check permissions
            assert user_two.can_edit_company is False
            assert user_two.can_edit_roles is False
            assert user_two.user_role == UserRole.admin.value
            assert user_two.can_view_document is True
    finally:
        await cleanup_on_teardown(app)


async def test_complete_registration_natural_role_exists(aiohttp_client):
    app, client, natural_user = await prepare_client(aiohttp_client, is_legal=False)

    try:
        async with app['db'].acquire() as conn:
            user = await insert_test_user(
                conn,
                {
                    'email': TEST_NEW_EMAIL,
                    'password': TEST_PASSWORD,
                },
            )

        response = await client.post(
            COMPLETE_REGISTRATION_URL,
            data=ujson.dumps(
                {
                    'company_edrpou': natural_user.company_edrpou,
                    'company_name': TEST_COMPANY_NAME,
                    'name': TEST_NAME,
                    'is_legal': False,
                }
            ),
        )
        assert response.status == 400

        async with app['db'].acquire() as conn:
            user = await select_user_by_email(conn, user.email)
            assert user.registration_completed is False
    finally:
        await cleanup_on_teardown(app)


async def test_complete_registration_with_company(aiohttp_client):
    app, client = await prepare_app_client(aiohttp_client)
    headers = prepare_referer_headers(client)

    async with app['db'].acquire() as conn:
        company_id = await insert_company(
            conn,
            {'edrpou': TEST_COMPANY_EDRPOU},
        )
        user = await insert_test_user(
            conn,
            {
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
            },
        )
        role = await prepare_coworker_role(
            conn,
            {
                'company_id': company_id,
                'status': RoleStatus.active,
                'user_id': user.id,
            },
        )
        await update_user(
            conn=conn,
            user_id=user.id,
            data={'last_role_id': role.id},
        )

        user = await select_user_by_email(conn, user.email)
        assert user.registration_completed is False
        assert user.first_name is None

    data = ujson.dumps(
        {
            'company_edrpou': TEST_COMPANY_EDRPOU,
            'company_name': TEST_COMPANY_NAME,
            'name': TEST_NAME,
        }
    )

    try:
        async with app['db'].acquire() as conn:
            # Check registration bonus account
            accounts = await select_company_accounts(conn, company_id)
            assert len(accounts) == 0

        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
            headers=headers,
        )
        assert response.status == 200

        response = await client.post(COMPLETE_REGISTRATION_URL, data=data, headers=headers)
        assert response.status == 200

        async with app['db'].acquire() as conn:
            accounts = await select_company_accounts(conn, company_id)
            assert len(accounts) == 1
            assert accounts[0].rate == AccountRate.free

            company = await select_company_by_edrpou(conn, TEST_COMPANY_EDRPOU)
            assert company.name == TEST_COMPANY_NAME
            assert company.full_name == TEST_COMPANY_NAME

            user = await select_user_by_email(conn, user.email)
            assert user.registration_completed is True
            assert user.first_name == TEST_NAME
            assert user.company_edrpou == TEST_COMPANY_EDRPOU
            assert user.company_name == TEST_COMPANY_NAME
            # Check permissions
            assert user.can_edit_company is True
            assert user.can_edit_roles is True
            assert user.user_role == UserRole.admin.value

            # Add second role
            second_user = await prepare_user_data(app, email='<EMAIL>')
            await prepare_coworker_role(
                conn,
                {
                    'company_id': company_id,
                    'status': RoleStatus.active,
                    'user_id': second_user.id,
                },
            )

            # Check for company billing accounts
            accounts = await select_company_accounts(conn, company_id)
            assert len(accounts) == 1
            assert accounts[0].rate == AccountRate.free
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'filename',
    [
        f'{TEST_UUID}.p7s',
        'invalid.p7s',
        'invalid-content.p7s',
        'invalid-uuid.p7s',
    ],
)
async def test_complete_registration_with_p7s_invalid(aiohttp_client, filename):
    app, client = await prepare_app_client(
        aiohttp_client,
        feature_flags={'pass_sign_info_to_backend': False},
    )
    headers = prepare_referer_headers(client)

    async with app['db'].acquire() as conn:
        await insert_test_user(
            conn,
            {
                'id': TEST_UUID,
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
                'email_confirmed': True,
            },
        )

    try:
        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
            headers=headers,
        )
        assert response.status == 200

        response = await client.post(
            COMPLETE_REGISTRATION_URL,
            data=prepare_form_data(p7s=DATA_PATH / filename),
            headers=headers,
        )
        assert response.status == HTTPStatus.BAD_REQUEST

    finally:
        await cleanup_on_teardown(app)


async def test_complete_registration_with_p7s(aiohttp_client, monkeypatch):
    app, client = await prepare_app_client(
        aiohttp_client,
        feature_flags={'pass_sign_info_to_backend': False},
    )
    headers = prepare_referer_headers(client)
    edrpou = '77777777'

    async with app['db'].acquire() as conn:
        await insert_test_user(
            conn,
            {
                'id': TEST_UUID,
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
                'email_confirmed': True,
            },
        )

    response = await client.post(
        LOGIN_URL,
        data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
        headers=headers,
    )
    assert response.status == 200

    # headers = prepare_auth_headers(user)
    # get registration token
    response = await client.post('/companies/registration/token', headers=headers)
    assert response.status == HTTPStatus.OK, await response.json()
    data = await response.json()
    token = data['token']

    # monkeypatch verify function
    sign = prepare_signature_info(SignatureType.signature, edrpou)
    monkeypatch.setattr(eusign_utils, 'verify_sync', lambda *_, **__: sign)

    response = await client.post(
        COMPLETE_REGISTRATION_URL,
        json={
            'signature': 'SGVsbG8gZXZvZG9jIQ==',  # signature validation is mocked
            'original': token,
        },
        headers=headers,
    )
    assert response.status == HTTPStatus.OK

    # make sure signature info is logged on role activation
    async with services.events_db.acquire() as conn:
        events = await select_all(conn, sa.select([user_actions_table]))
        assert len(events) == 1
        event = events[0]
        assert event.action == Action.role_create
        assert event.extra == {
            'acsk': 'АЦСК ТОВ "Центр сертифікації ключів "Україна"',
            'serial_number': '3960B50600000000000000000000000000000001',
            'timemark': '2017-09-21T16:39:00+03:00',
        }


async def test_notification_on_new_company(monkeypatch, aiohttp_client):
    monkeypatch.setattr(eusign_utils, 'get_edrpou', lambda _: '11111112')
    edrpou = '11111112'

    app, client, user = await prepare_client(
        aiohttp_client,
        feature_flags={'pass_sign_info_to_backend': False},
        user_id=TEST_UUID,
    )

    async with app['db'].acquire() as conn:
        # First company, just have target company in contact list
        user1 = await prepare_user_data(
            app,
            company_edrpou='11111113',
            email='<EMAIL>',
        )
        await prepare_contact(conn, user1, {'edrpou': edrpou})

        # Register new company 11111112
        response = await client.post(
            COMPLETE_REGISTRATION_URL,
            data=prepare_form_data(p7s=DATA_PATH / f'{TEST_UUID}.p7s'),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200, await response.json()

        # Check trigger notification for company
        notifications = await select_all_trigger_notifications(conn)
        notifications = [
            notification
            for notification in notifications
            if notification.type == TriggerNotificationType.company_registration
        ]

        assert len(notifications) == 0


async def test_complete_registration_referrer(aiohttp_client):
    app, client, referrer = await prepare_client(aiohttp_client, is_dealer=True)
    headers = prepare_referer_headers(client)

    async with app['db'].acquire() as conn:
        user = await insert_test_user(
            conn,
            {
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
                'pending_referrer_role_id': referrer.role_id,
            },
        )

    company_edrpou = '55555555'
    data = ujson.dumps(
        {
            'company_edrpou': company_edrpou,
            'company_name': TEST_COMPANY_NAME,
            'name': TEST_NAME,
        }
    )

    try:
        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
            headers=headers,
        )
        assert response.status == 200

        response = await client.post(COMPLETE_REGISTRATION_URL, data=data, headers=headers)
        assert response.status == 200

        async with app['db'].acquire() as conn:
            user = await select_user_by_email(conn, user.email)
            assert user.registration_completed is True
            assert user.pending_referrer_role_id is None

            company = await select_company_by_edrpou(conn, company_edrpou)
            assert company.dealt_by_user == referrer.id
            assert company.dealt_by_company == referrer.company_edrpou
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('login', [True, False])
async def test_confirm_email(mailbox, esputnik_box, aiohttp_client, login):
    app = create_app()
    client = await aiohttp_client(app)

    async with app['db'].acquire() as conn:
        user = await insert_test_user(
            conn,
            {
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
                'source': RegistrationSource.vchasno.value,
            },
        )
        assert user.email_confirmed is False

    try:
        if login:
            response = await client.post(
                LOGIN_URL,
                data=ujson.dumps(
                    {
                        'email': TEST_EMAIL,
                        'password': TEST_PASSWORD,
                    }
                ),
                headers=prepare_referer_headers(client),
            )
            assert response.status == 200

        assert len(esputnik_box) == 0
        token = generate_jwt_token({'email': user.email}, services.config.tokens.private_key)
        response = await client.get(f'/registration/confirm/{token}')
        assert response.status == 200
        assert len(esputnik_box) == 1

        # TODO: remove mailbox check after full ESputnik integration
        assert len(mailbox) == 1, user.source
        assert mailbox[0]['To'] == TEST_EMAIL

        assert str(response.url.relative()) == ('/app' if login else '/auth/login')

        async with app['db'].acquire() as conn:
            user = await select_user_by_email(conn, user.email)
            assert user.email_confirmed is True
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'user_edrpou, user_role, new_user_edrpou, expected_status, tokens_count, expected_meta',
    [
        (
            TEST_COMPANY_EDRPOU,
            UserRole.user.value,
            TEST_COMPANY_EDRPOU,
            201,
            0,
            {'has_invited_coworker': True, 'has_invited_recipient': False},
        ),
        (
            TEST_COMPANY_EDRPOU,
            UserRole.admin.value,
            TEST_COMPANY_EDRPOU,
            201,
            0,
            {'has_invited_coworker': True, 'has_invited_recipient': False},
        ),
        (
            TEST_COMPANY_EDRPOU,
            UserRole.admin.value,
            TEST_NEW_COMPANY_EDRPOU,
            200,
            1,
            {'has_invited_coworker': False, 'has_invited_recipient': True},
        ),
    ],
)
async def test_invite_new_user(
    mailbox,
    aiohttp_client,
    user_edrpou,
    user_role,
    new_user_edrpou,
    expected_status,
    tokens_count,
    expected_meta,
):
    app = create_app()
    client = await aiohttp_client(app)
    headers = prepare_referer_headers(client)

    user = await prepare_user_data(
        app, company_edrpou=user_edrpou, password=TEST_PASSWORD, user_role=user_role
    )

    assert len(mailbox) == 0

    response = await client.post(
        LOGIN_URL,
        data=ujson.dumps(
            {
                'email': user.email,
                'password': TEST_PASSWORD,
            }
        ),
        headers=headers,
    )
    assert response.status == 200

    response = await client.post(
        INVITE_NEW_USER_URL,
        data=ujson.dumps(
            {
                'edrpou': new_user_edrpou,
                'email': TEST_NEW_EMAIL,
            }
        ),
        headers=headers,
    )
    assert response.status == expected_status
    assert len(mailbox) == 1
    assert mailbox[0]['To'] == TEST_NEW_EMAIL

    user_onboarding = await get_user_onboarding(user_id=user.id)
    assert user_onboarding.has_invited_recipient == expected_meta['has_invited_recipient']
    assert user_onboarding.has_invited_coworker == expected_meta['has_invited_coworker']

    async with app['db'].acquire() as conn:
        assert await count(conn, short_registration_token_table) == tokens_count

        if response.status == HTTPStatus.CREATED:
            invited_user = await select_user_by_email(conn, TEST_NEW_EMAIL)
            assert invited_user.created_by is not None

            vchasno_user = await select_vchasno_user(conn, email=TEST_NEW_EMAIL)
            assert vchasno_user is not None
            assert vchasno_user.id == invited_user.id
            assert vchasno_user.email == TEST_NEW_EMAIL


@pytest.mark.parametrize(
    'can_invite_coworkers, can_edit_roles, is_admin, permissions, expected_status',
    [
        (
            # Regular user can't invite other users
            False,
            False,
            False,
            {'is_admin': True},
            403,
        ),
        (
            # Admin can invite other admins
            False,
            False,
            True,
            {'is_admin': True},
            201,
        ),
        (
            # can_invite_coworkers only can't invite admins
            True,
            False,
            False,
            {'is_admin': True},
            403,
        ),
        (
            # can_edit_roles only can't invite admins
            False,
            True,
            False,
            {'is_admin': True},
            403,
        ),
        (
            # can_invite_coworkers and can_edit_roles can invite regular users
            True,
            True,
            False,
            {'can_create_tags': True},
            201,
        ),
    ],
)
async def test_invite_new_user_as_coworker_permissions(
    aiohttp_client,
    can_invite_coworkers,
    can_edit_roles,
    is_admin,
    permissions,
    expected_status,
):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        can_invite_coworkers=can_invite_coworkers,
        can_edit_roles=can_edit_roles,
        is_admin=is_admin,
    )
    response = await client.post(
        INVITE_NEW_USER_URL,
        data=ujson.dumps(
            {
                'edrpou': user.company_edrpou,
                'email': TEST_NEW_EMAIL,
                'permissions': {**permissions},
            }
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == expected_status


@pytest.mark.parametrize(
    'permissions',
    [
        {
            'can_view_document': True,
            'can_comment_document': True,
            'can_upload_document': True,
            'can_download_document': True,
            'can_print_document': True,
            'can_delete_document': True,
            'can_sign_and_reject_document': True,
            'can_invite_coworkers': True,
            'can_edit_company': True,
            'can_edit_roles': True,
            'can_create_tags': True,
            'can_edit_document_automation': True,
            'can_edit_document_fields': True,
            'can_edit_document_category': True,
            'can_archive_documents': True,
            'can_delete_archived_documents': True,
            'can_edit_templates': False,
            'can_edit_directories': False,
            'can_remove_itself_from_approval': False,
            'is_admin': True,
        },
        {
            'can_view_document': False,
            'can_comment_document': False,
            'can_upload_document': False,
            'can_download_document': False,
            'can_print_document': False,
            'can_delete_document': False,
            'can_sign_and_reject_document': False,
            'can_invite_coworkers': False,
            'can_edit_company': False,
            'can_edit_roles': False,
            'can_create_tags': False,
            'can_edit_document_automation': False,
            'can_edit_document_fields': False,
            'can_edit_document_category': True,
            'can_archive_documents': False,
            'can_delete_archived_documents': False,
            'can_edit_templates': False,
            'can_edit_directories': False,
            'can_remove_itself_from_approval': False,
            'is_admin': False,
        },
        {
            'can_view_document': False,
            'can_comment_document': True,
            'can_upload_document': False,
            'can_download_document': True,
            'can_print_document': False,
            'can_delete_document': True,
            'can_sign_and_reject_document': False,
            'can_invite_coworkers': True,
            'can_edit_company': False,
            'can_edit_roles': True,
            'can_create_tags': False,
            'can_edit_document_automation': True,
            'can_edit_document_fields': False,
            'can_edit_document_category': True,
            'can_archive_documents': False,
            'can_delete_archived_documents': False,
            'can_edit_templates': False,
            'can_edit_directories': False,
            'can_remove_itself_from_approval': False,
            'is_admin': True,
        },
        {
            'can_view_document': True,
            'can_comment_document': False,
            'can_upload_document': True,
            'can_download_document': False,
            'can_print_document': True,
            'can_delete_document': False,
            'can_sign_and_reject_document': True,
            'can_invite_coworkers': False,
            'can_edit_company': True,
            'can_edit_roles': False,
            'can_create_tags': True,
            'can_edit_document_automation': False,
            'can_edit_document_fields': True,
            'can_edit_document_category': True,
            'can_archive_documents': False,
            'can_delete_archived_documents': False,
            'can_edit_templates': False,
            'can_edit_directories': False,
            'can_remove_itself_from_approval': False,
            'is_admin': False,
        },
    ],
)
async def test_invite_new_user_as_coworker_with_permissions(aiohttp_client, permissions):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        is_admin=True,
    )
    response = await client.post(
        INVITE_NEW_USER_URL,
        data=ujson.dumps(
            {
                'edrpou': user.company_edrpou,
                'email': TEST_NEW_EMAIL,
                'permissions': {**permissions},
            }
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201

    async with services.db.acquire() as conn:
        coworker = await select_user_by_email(
            conn, TEST_NEW_EMAIL, company_edrpou=user.company_edrpou
        )
        if permissions['is_admin'] is True:
            assert coworker.user_role == UserRole.admin.value
        else:
            assert coworker.user_role == UserRole.user.value
        assert coworker.can_view_document == permissions['can_view_document']
        assert coworker.can_comment_document == permissions['can_comment_document']
        assert coworker.can_upload_document == permissions['can_upload_document']
        assert coworker.can_download_document == permissions['can_download_document']
        assert coworker.can_print_document == permissions['can_print_document']
        assert coworker.can_delete_document == permissions['can_delete_document']
        assert coworker.can_sign_and_reject_document == permissions['can_sign_and_reject_document']
        assert coworker.can_invite_coworkers == permissions['can_invite_coworkers']
        assert coworker.can_edit_company == permissions['can_edit_company']
        assert coworker.can_edit_roles == permissions['can_edit_roles']
        assert coworker.can_create_tags == permissions['can_create_tags']
        assert coworker.can_edit_document_automation == permissions['can_edit_document_automation']
        assert coworker.can_edit_document_fields == permissions['can_edit_document_fields']
        assert coworker.can_edit_roles == permissions['can_edit_roles']


@pytest.mark.parametrize(
    ('inviter', 'expected'),
    [
        pytest.param(
            {
                'company_email_domains': None,
                'is_admin': True,
                'can_invite_coworkers': True,
            },
            {'expected_status': 201, 'coworkers_count': 2},
            id='admin_can_invite_no_domain',
        ),
        pytest.param(
            {
                'company_email_domains': None,
                'is_admin': True,
                'can_invite_coworkers': False,
            },
            {'expected_status': 201, 'coworkers_count': 2},
            id='admin_can_invite_coworkers_no_domain',
        ),
        pytest.param(
            {
                'company_email_domains': None,
                'is_admin': False,
                'can_invite_coworkers': True,
            },
            {'expected_status': 201, 'coworkers_count': 2},
            id='non_admin_can_invite_no_domain',
        ),
        pytest.param(
            {
                'company_email_domains': None,
                'is_admin': False,
                'can_invite_coworkers': False,
            },
            {
                'expected_status': 403,
                'coworkers_count': 1,
                'reason': 'Доступ заборонено',
            },
            id='non_admin_cannot_invite_no_domain',
        ),
        pytest.param(
            {
                'company_email_domains': ['vchasno.com.ua'],
                'is_admin': True,
                'can_invite_coworkers': True,
            },
            {'expected_status': 201, 'coworkers_count': 2},
            id='admin_can_invite_valid_domain',
        ),
        pytest.param(
            {
                'company_email_domains': ['other.com.ua'],
                'is_admin': True,
                'can_invite_coworkers': True,
            },
            {
                'expected_status': 400,
                'coworkers_count': 1,
                'reason': (
                    'Неможливо запросити користувача з вказаним email. У компанії 11111111 '
                    'використовується email адреса, що закінчується на @other.com.ua'
                ),
            },
            id='admin_cannot_invite_invalid_domain',
        ),
    ],
)
async def test_invite_new_user_as_coworker(
    mailbox,
    aiohttp_client,
    fcm_message_handler,
    inviter: dict,
    expected: dict,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        can_invite_coworkers=inviter['can_invite_coworkers'],
        is_admin=inviter['is_admin'],
        company_email_domains=inviter['company_email_domains'],
    )

    # Prepare the user data for the new invitee
    other_user = await prepare_user_data(
        app,
        email=TEST_NEW_EMAIL,
        company_edrpou=TEST_NEW_COMPANY_EDRPOU,
        create_mobile_active_session=True,
    )

    # Ensure the mailbox is initially empty
    assert len(mailbox) == 0

    # Send the invitation request
    response = await client.post(
        INVITE_NEW_USER_URL,
        data=ujson.dumps(
            {
                'edrpou': user.company_edrpou,
                'email': other_user.email,
            }
        ),
        headers=prepare_auth_headers(user),
    )

    # Assert the response status
    assert response.status == expected['expected_status']

    if response.status == 201:
        # Verify that an email was sent
        assert len(mailbox) == 1

        assert len(fcm_message_handler) == 1
        assert (
            fcm_message_handler[0]['message']['notification']['title']
            == 'Ви отримали запрошення від колеги'
        )
        assert (
            fcm_message_handler[0]['message']['notification']['body']
            == f'Ваш колега запросив вас у компанію {user.company_name}'
        )

    # Verify the number of coworkers in the database
    async with app['db'].acquire() as conn:
        result = await select_coworkers(conn, user.company_id)
        assert len(result) == expected['coworkers_count']

        # If the invitation was successful, verify the new coworker's details
        if expected['expected_status'] == 201:
            coworker_role = await select_role(
                conn=conn,
                company_id=user.company_id,
                user_id=other_user.id,
            )
            assert coworker_role.user_role == UserRole.user.value
            assert coworker_role.can_edit_company is False
            assert coworker_role.can_edit_roles is False
            assert coworker_role.activation_source == RoleActivationSource.invite
            assert coworker_role.activated_by == user.role_id
            assert coworker_role.date_activated is not None
            assert coworker_role.status == RoleStatus.active
        else:
            response_data = await response.json()
            assert response_data['reason'] == expected['reason']


async def test_invite_new_user_as_coworker_failed(mailbox, aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    other_user = await prepare_user_data(
        app, email=TEST_NEW_EMAIL, company_edrpou=user.company_edrpou
    )

    try:
        # Other user is already a coworker
        async with app['db'].acquire() as conn:
            assert len(await select_coworkers(conn, user.company_id)) == 2

        assert len(mailbox) == 0
        response = await client.post(
            INVITE_NEW_USER_URL,
            data=ujson.dumps(
                {
                    'edrpou': user.company_edrpou,
                    'email': other_user.email,
                }
            ),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 400
        data = await response.json()
        assert data['code'] == 'object_already_exists'
        assert len(mailbox) == 0
    finally:
        await cleanup_on_teardown(app)


async def test_invite_new_user_no_new_role(mailbox, aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app, email=TEST_NEW_EMAIL, company_edrpou=TEST_NEW_COMPANY_EDRPOU
    )
    new_edrpou = '1234567890'

    assert len(mailbox) == 0
    response = await client.post(
        INVITE_NEW_USER_URL,
        data=ujson.dumps(
            {
                'edrpou': new_edrpou,
                'email': recipient.email,
            }
        ),
        headers=prepare_auth_headers(user),
    )
    assert response.status == 201
    assert len(mailbox) == 1


async def test_invite_new_user_exists(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    other_user = await prepare_user_data(
        app, email=TEST_NEW_EMAIL, company_edrpou=TEST_NEW_COMPANY_EDRPOU
    )

    try:
        response = await client.post(
            INVITE_NEW_USER_URL,
            data=ujson.dumps(
                {
                    'edrpou': other_user.company_edrpou,
                    'email': other_user.email,
                }
            ),
            headers=prepare_auth_headers(user),
        )
        assert response.status == 400
        data = await response.json()
        assert data['code'] == 'object_already_exists'
    finally:
        await cleanup_on_teardown(app)


async def test_invite_new_user_forbidden(aiohttp_client):
    client = await aiohttp_client(create_app())
    response = await client.post(
        INVITE_NEW_USER_URL,
        data=ujson.dumps(
            {
                'edrpou': TEST_NEW_COMPANY_EDRPOU,
                'email': TEST_NEW_EMAIL,
            }
        ),
        headers=prepare_referer_headers(client),
    )
    assert response.status == 403


async def test_invite_new_users(mailbox, aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
        super_admin_permissions={'can_edit_client_data': True},
    )
    await set_company_config(app, company_id=user.company_id, master=True, admin_is_superadmin=True)
    await prepare_user_data(app, email=TEST_PARTNER_EMAIL, company_edrpou=TEST_PARTNER_EDRPOU)

    csv_file = open(DATA_PATH / 'invite_new_users.csv', 'rb')

    try:
        assert len(mailbox) == 0

        response = await client.post(
            '/internal-api/invite/new-users',
            data=[('file', csv_file)],
            headers=prepare_auth_headers(user),
        )
        assert response.status == 200

        assert await response.json() == {
            'rows_total': 9,
            'rows_invalid': 5,
            'invalid_row_numbers': [5, 6, 7, 8, 9],
            'duplicate_rows': 1,
            'send_invites': 2,
            'user_exists': 1,
            'error_send_invites': 0,
        }

        assert len(mailbox) == 2
        assert mailbox[0]['To'] == TEST_EMAIL
        assert mailbox[1]['To'] == TEST_NEW_EMAIL
    finally:
        if not csv_file.closed:
            csv_file.close()
        await cleanup_on_teardown(app)


async def test_invite_contacts(aiohttp_client, mailbox):
    app, client, user = await prepare_client(
        aiohttp_client, is_admin=True, company_edrpou=SUPER_ADMIN_EDRPOU
    )

    try:
        async with app['db'].acquire() as conn:
            await prepare_contacts(app, conn, user)
        response = await client.post(INVITE_CONTACTS_URL, headers=prepare_auth_headers(user))
        assert len(mailbox) == 5
        assert response.status == 200
        assert await response.json() is not None

        response = await client.post(INVITE_CONTACTS_URL, headers=prepare_auth_headers(user))
        assert response.status == 418
        assert len(mailbox) == 5
        assert await response.json() is not None
    finally:
        await cleanup_on_teardown(app)


async def test_registration(mailbox, aiohttp_client):
    app = create_app()
    client = await aiohttp_client(app)

    email = TEST_EMAIL

    assert len(mailbox) == 0

    response = await client.post(
        REGISTRATION_URL,
        data=ujson.dumps({'email': email, 'password': TEST_PASSWORD}),
        headers=prepare_referer_headers(client),
    )
    assert response.status == 200
    assert response.cookies.get('vchasno_test_session') is not None
    assert len(mailbox) == 1

    assert mailbox[0]['To'] == email

    async with services.db.acquire() as conn:
        user = await select_user_by_email(conn, email)

        assert user.password != TEST_PASSWORD
        assert check_password_hash(user.password, TEST_PASSWORD) is True
        assert user.email_confirmed is False
        assert user.registration_completed is False
        assert user.registration_method == RegistrationMethod.main
        assert user.is_logged_once is True

        vchasno_user = await select_vchasno_user(conn, email=email)
        assert vchasno_user is not None
        assert vchasno_user.email == email
        assert vchasno_user.id == user.id


async def test_registration_with_vchasno_user(mailbox, aiohttp_client):
    """
    Test that we can properly register user when vchasno user already exists
    """
    app = create_app()
    client = await aiohttp_client(app)

    await prepare_vchasno_user(id=TEST_UUID_2, email=TEST_NEW_EMAIL)

    response = await client.post(
        path=REGISTRATION_URL,
        json={'email': TEST_NEW_EMAIL, 'password': TEST_PASSWORD},
        headers=prepare_referer_headers(client),
    )
    assert response.status == 200
    assert len(mailbox) == 1
    assert mailbox[0]['To'] == TEST_NEW_EMAIL

    async with services.db.acquire() as conn:
        user = await select_user_by_email(conn, email=TEST_NEW_EMAIL)

        assert user.password != TEST_PASSWORD
        assert check_password_hash(user.password, TEST_PASSWORD) is True
        assert user.email_confirmed is False
        assert user.registration_completed is False
        assert user.registration_method == RegistrationMethod.main

        vchasno_user = await select_vchasno_user(conn, email=TEST_NEW_EMAIL)
        assert vchasno_user is not None
        assert vchasno_user.email == TEST_NEW_EMAIL

        # When we create a new user, we should look for the vchasno user with the same email
        # and use its id as the user id
        assert vchasno_user.id == user.id == TEST_UUID_2


@pytest.mark.parametrize('is_dealer', [True, False])
async def test_registration_with_referrer(mailbox, aiohttp_client, is_dealer):
    app = create_app()
    client = await aiohttp_client(app)

    try:
        referrer = await prepare_user_data(app, is_dealer=is_dealer)
        response = await client.post(
            REGISTRATION_URL,
            data=ujson.dumps(
                {
                    'email': TEST_EMAIL,
                    'password': TEST_PASSWORD,
                    'referrer': referrer.role_id,
                }
            ),
            headers=prepare_referer_headers(client),
        )
        assert response.status == 200
        async with app['db'].acquire() as conn:
            user = await select_user_by_email(conn, TEST_EMAIL)
            if is_dealer:
                assert user.pending_referrer_role_id == referrer.role_id
            else:
                assert user.pending_referrer_role_id is None
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'url, use_token',
    [
        (REGISTRATION_URL, False),
        (REGISTRATION_WITH_TOKEN_URL, True),
    ],
)
async def test_registration_logged_in(aiohttp_client, url, use_token):
    app, client, user = await prepare_client(
        aiohttp_client, False, email=TEST_EMAIL, password=TEST_PASSWORD
    )
    headers = prepare_referer_headers(client)

    token = None
    if use_token:
        token = generate_jwt_token(
            {'email': TEST_NEW_EMAIL, 'edrpou': TEST_NEW_COMPANY_EDRPOU},
            services.config.tokens.private_key,
        )

    try:
        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
            headers=headers,
        )
        assert response.status == 200

        response = await client.post(
            url,
            data=ujson.dumps(
                {
                    'email': TEST_NEW_EMAIL,
                    'password': TEST_PASSWORD,
                    'token': token,
                }
            ),
            headers=headers,
        )
        assert response.status == 200
        assert str(response.url.relative()) == '/app'

        async with app['db'].acquire() as conn:
            user = await select_user_by_email(conn, TEST_NEW_EMAIL)
            assert user is None

            vchasno_user = await select_vchasno_user(conn, email=TEST_NEW_EMAIL)
            assert vchasno_user is None

    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'token_email, user_email, verified_token',
    [
        (TEST_EMAIL, TEST_EMAIL, None),
        (TEST_EMAIL, TEST_NEW_EMAIL, None),
        (TEST_EMAIL, TEST_EMAIL, False),
        (TEST_EMAIL, TEST_NEW_EMAIL, False),
        (TEST_EMAIL, TEST_EMAIL, True),
        (TEST_EMAIL, TEST_NEW_EMAIL, True),
    ],
)
async def test_registration_with_token_recipient(
    mailbox,
    esputnik_box,
    aiohttp_client,
    token_email,
    user_email,
    verified_token,
):
    app, client = await prepare_app_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        short_token = await insert_short_registration_token(
            conn,
            {
                'edrpou': TEST_COMPANY_EDRPOU,
                'email': token_email,
                'is_verified_inviter': verified_token,
            },
        )
        extra_token = await insert_short_registration_token(
            conn,
            {
                'edrpou': TEST_COMPANY_EDRPOU,
                'email': token_email,
            },
        )

    assert len(esputnik_box) == 0
    response = await client.post(
        REGISTRATION_WITH_TOKEN_URL,
        data=ujson.dumps(
            {
                'email': user_email,
                'password': TEST_PASSWORD,
                'token': short_token,
            }
        ),
        headers=prepare_referer_headers(client),
    )
    assert response.status == 201

    async with app['db'].acquire() as conn:
        user = await select_user_by_email(conn, user_email)
        assert user.password != TEST_PASSWORD
        assert check_password_hash(user.password, TEST_PASSWORD)

        vchasno_user = await select_vchasno_user(conn, email=user_email)
        assert vchasno_user is not None
        assert vchasno_user.id == user.id
        assert vchasno_user.email == user_email

        # TODO: remove mailbox after full integration with ESputnik
        if token_email == user_email:
            assert user.email_confirmed is True
            assert len(mailbox) == 0
            assert len(esputnik_box) == 2
        else:
            assert user.email_confirmed is False
            assert len(mailbox) == 1
            assert len(esputnik_box) == 1

        user_roles = await select_all(
            conn,
            (
                sa.select([role_table])
                .select_from(
                    user_table.join(
                        role_table,
                        role_table.c.user_id == user_table.c.id,
                    ).join(
                        company_table,
                        company_table.c.id == role_table.c.company_id,
                    )
                )
                .where(user_table.c.id == user.id)
            ),
        )

        assert len(user_roles) == 0

        db_token = await select_short_registration_token(conn, short_token)
        assert db_token.registered_user_id == user.id
        assert db_token.date_registered is not None

        db_extra_token = await select_short_registration_token(conn, extra_token)
        assert db_extra_token.registered_user_id == user.id
        assert db_extra_token.date_registered is not None


async def test_registration_with_token_coworker(
    mailbox,
    esputnik_box,
    aiohttp_client,
):
    app, client, inviter = await prepare_client(
        aiohttp_client=aiohttp_client,
        create_billing_account=True,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    async with app['db'].acquire() as conn:
        short_token = await create_short_registration_token(
            conn=conn,
            edrpou=TEST_COMPANY_EDRPOU,
            email=TEST_NEW_EMAIL,
            invited_by_company_edrpou=TEST_COMPANY_EDRPOU,
            invited_by_user_id=inviter.id,
            invited_with_document=None,
            email_domains=None,
        )

    assert len(esputnik_box) == 0
    response = await client.post(
        REGISTRATION_WITH_TOKEN_URL,
        data=ujson.dumps(
            {
                'email': TEST_NEW_EMAIL,
                'password': TEST_PASSWORD,
                'token': short_token,
            }
        ),
        headers=prepare_referer_headers(client),
    )
    assert response.status == 201

    async with app['db'].acquire() as conn:
        user = await select_user_by_email(conn, TEST_NEW_EMAIL)
        assert user.password != TEST_PASSWORD
        assert check_password_hash(user.password, TEST_PASSWORD)

        vchasno_user = await select_vchasno_user(conn, email=TEST_NEW_EMAIL)
        assert vchasno_user is not None
        assert vchasno_user.id == user.id
        assert vchasno_user.email == TEST_NEW_EMAIL

        assert user.email_confirmed is True
        assert len(mailbox) == 0
        assert len(esputnik_box) == 2

        roles = await select_roles(conn=conn, user_id=user.id)
        assert len(roles) == 1
        role = roles[0]
        assert role.status == RoleStatus.active
        assert role.activation_source == RoleActivationSource.invite
        assert role.activated_by == inviter.role_id
        assert role.date_activated is not None
        assert role.date_invited is not None
        assert role.invited_by == inviter.role_id

        db_token = await select_short_registration_token(conn, short_token)
        assert db_token.registered_user_id == user.id
        assert db_token.date_registered is not None


@pytest.mark.parametrize('is_dealer', [False, True])
async def test_register_by_document_with_dealer_token(aiohttp_client, is_dealer):
    app, client, inviter = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
        is_dealer=is_dealer,
    )
    await set_company_config(
        app,
        company_id=inviter.company_id,
        allow_unregistered_document_view=False,
    )
    inviter_edrpou = inviter.company_edrpou

    invitee_edrpou = TEST_DOCUMENT_EDRPOU_RECIPIENT
    invitee_email = TEST_DOCUMENT_EMAIL_RECIPIENT

    try:
        # Document sent by inviter, creating short token
        document = await prepare_document_data(
            app,
            inviter,
            document_recipients=[
                {
                    'edrpou': invitee_edrpou,
                    'emails': [invitee_email],
                }
            ],
            status_id=DocumentStatus.ready_to_be_signed.value,
        )

        await sign_and_send_document(
            client,
            document.id,
            inviter,
            sign_data=prepare_signature_form_data(
                inviter,
                owner=inviter,
                recipient_edrpou=invitee_edrpou,
                recipient_email=invitee_email,
            ),
        )

        # Ensure invitation token by document created
        async with app['db'].acquire() as conn:
            short_token = await select_one(
                conn,
                (
                    short_registration_token_table.select().where(
                        sa.and_(
                            short_registration_token_table.c.invited_by_company == inviter_edrpou,
                            short_registration_token_table.c.invited_with_document == document.id,
                        )
                    )
                ),
            )
        assert short_token.invited_by_company == inviter_edrpou
        assert short_token.invited_with_document == document.id

        # Registration with token
        response = await client.post(
            REGISTRATION_WITH_TOKEN_URL,
            data=ujson.dumps(
                {
                    'email': invitee_email,
                    'password': TEST_PASSWORD,
                    'token': short_token.id,
                }
            ),
            headers=prepare_referer_headers(client),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            company = await select_company_by_edrpou(conn, edrpou=invitee_edrpou)
            # we don't create company automatically with short token. previously we do,
            # but we don't do it now
            assert company is None
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('is_dealer', [False, True])
async def test_register_with_dealer_token(aiohttp_client, is_dealer):
    app, client = await prepare_app_client(aiohttp_client)
    async with app['db'].acquire() as conn:
        inviter_edrpou = '000000'
        invitee_edrpou = '111111'
        inviter = await prepare_user_data(app, company_edrpou=inviter_edrpou, is_dealer=is_dealer)
        short_token = await insert_short_registration_token(
            conn,
            {
                'edrpou': invitee_edrpou,
                'email': TEST_EMAIL,
                'invited_by_user': inviter.id,
                'invited_by_company': inviter_edrpou,
            },
        )

    try:
        response = await client.post(
            REGISTRATION_WITH_TOKEN_URL,
            data=ujson.dumps(
                {
                    'email': TEST_EMAIL,
                    'password': TEST_PASSWORD,
                    'token': short_token,
                }
            ),
            headers=prepare_referer_headers(client),
        )
        assert response.status == 201

        async with app['db'].acquire() as conn:
            company = await select_company_by_edrpou(conn, edrpou=invitee_edrpou)
            # we don't create company automatically with short token. previously we do,
            # but we don't do it now
            assert company is None
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize('email_confirmed', [True, False])
async def test_resend_confirmation_email(mailbox, aiohttp_client, email_confirmed):
    app = create_app()
    client = await aiohttp_client(app)
    headers = prepare_referer_headers(client)

    async with app['db'].acquire() as conn:
        await insert_test_user(
            conn,
            {
                'email': TEST_EMAIL,
                'password': TEST_PASSWORD,
                'email_confirmed': email_confirmed,
            },
        )

    try:
        assert len(mailbox) == 0

        response = await client.post(
            LOGIN_URL,
            data=ujson.dumps({'email': TEST_EMAIL, 'password': TEST_PASSWORD}),
            headers=headers,
        )
        assert response.status == 200

        response = await client.post(RESEND_CONFIRMATION_EMAIL_URL, headers=headers)
        assert response.status == 200

        assert len(mailbox) == int(not email_confirmed)
    finally:
        await cleanup_on_teardown(app)


async def test_resend_confirmation_email_forbidden(aiohttp_client):
    client = await aiohttp_client(create_app())
    response = await client.post(
        RESEND_CONFIRMATION_EMAIL_URL, headers=prepare_referer_headers(client)
    )
    assert response.status == 403


@pytest.mark.parametrize(
    'data, expected',
    [
        (
            # First dict will be ignored, such doesn't has current_datetime
            [
                {'source': 'main'},
                {'source': 'main', 'current_datetime': 1548404093407},
                {'SOME': 'OTHER', 'current_datetime': 1548404093407},
            ],
            [
                {'source': 'main'},
                {'SOME': 'OTHER'},
            ],
        ),
        (
            # All item ignored, but handler works correct.
            [
                {'source': 'main'},
            ],
            [],
        ),
    ],
)
async def test_save_registration_info(aiohttp_client, data, expected):
    app = create_app()
    client = await aiohttp_client(app)

    user = await prepare_user_data(app)

    # User is already registered and doesn't have info
    await request_save_registration_info(
        client=client,
        user=user,
        attempts=data,
        status=HTTPStatus.CREATED,
    )

    async with app['db'].acquire() as conn:
        sources = await select_all(conn, registration_sources_table.select())
        assert len(expected) == len(sources)
        for source in sources:
            assert source.user_id == user.id

        case = unittest.TestCase()
        case.assertCountEqual([source.meta for source in sources], expected)

    # if user has registration info response with BAD_REQUEST else with OK
    await request_save_registration_info(
        client=client,
        user=user,
        attempts=data,
        status=HTTPStatus.OK if expected else HTTPStatus.CREATED,
    )


@pytest.mark.parametrize(
    'permission, expected_can_view_document, expected_user_role',
    [
        (
            DefaultRolePermissionsKey(user_role=UserRole.admin, can_view_document=False),
            False,
            UserRole.admin.value,
        ),
        (
            DefaultRolePermissionsKey(user_role=UserRole.admin, can_view_document=True),
            True,
            UserRole.admin.value,
        ),
        (
            DefaultRolePermissionsKey(user_role=UserRole.user, can_view_document=True),
            True,
            UserRole.user.value,
        ),
        (
            DefaultRolePermissionsKey(user_role=UserRole.user, can_view_document=False),
            False,
            UserRole.user.value,
        ),
        (
            DefaultRolePermissionsKey(),
            True,
            UserRole.admin.value,
        ),
    ],
)
async def test_registration_complete_second_role_permissions(
    aiohttp_client,
    permission: DefaultRolePermissionsKey,
    expected_can_view_document,
    expected_user_role,
):
    # Test company already has one registered admin
    app, client, user1 = await prepare_client(
        aiohttp_client,
        is_admin=True,
        company_edrpou=TEST_COMPANY_EDRPOU,
        feature_flags={'pass_sign_info_to_backend': True},
    )
    headers = prepare_referer_headers(client)

    async with app['db'].acquire() as conn:
        await prepare_company_config(
            company_edrpou=TEST_COMPANY_EDRPOU,
            config=CompanyConfig(default_role_permissions_key=permission),
        )

        user2 = await insert_test_user(
            conn,
            {
                'email': TEST_NEW_EMAIL,
                'password': TEST_PASSWORD,
            },
        )

        response = await client.post(
            LOGIN_URL,
            json={'email': TEST_NEW_EMAIL, 'password': TEST_PASSWORD},
            headers=headers,
        )
        assert response.status == HTTPStatus.OK

        response = await client.post(
            COMPLETE_REGISTRATION_URL,
            json={
                'company_edrpou': TEST_COMPANY_EDRPOU,
                'company_name': TEST_COMPANY_NAME,
                'name': TEST_NAME,
            },
            headers=headers,
        )
        assert response.status == HTTPStatus.OK, await response.json()

        user_two = await select_user_by_email(conn, user2.email)
        assert user_two.registration_completed is True
        assert user_two.can_edit_company is False
        assert user_two.can_edit_roles is False
        assert user_two.can_view_document == expected_can_view_document
        assert user_two.user_role == expected_user_role


async def test_get_registration_token(aiohttp_client):
    """
    Company registration token can be generated only when user is defined via sign
    session or cookie session
    """
    app, client, user = await prepare_client(aiohttp_client)
    redis: Redis = app['redis']

    # User is not defined, not allow such request
    headers = prepare_referer_headers(client)
    response = await client.post(COMPANY_REGISTRATION_TOKEN_URL, headers=headers)
    assert response.status == HTTPStatus.FORBIDDEN

    # User is defined, new token can be created
    headers = prepare_auth_headers(user)
    response = await client.post(COMPANY_REGISTRATION_TOKEN_URL, headers=headers)
    assert response.status == HTTPStatus.OK
    response_data = await response.json()
    assert response_data is not None
    stored_token = await redis.get(f'company-token-{user.email}')
    expected_token = response_data['token']
    assert stored_token == expected_token

    # not registered user defined via sign session
    document = await prepare_document_data(app, user, status_id=SIGNED_AND_SENT)
    sign_session = await prepare_sign_session_data(
        app=app,
        document=document,
        email='<EMAIL>',
        edrpou='77777777',
        created_by=user.role_id,
    )
    headers = prepare_sign_session_headers(sign_session, client)
    response = await client.post(COMPANY_REGISTRATION_TOKEN_URL, headers=headers)
    assert response.status == HTTPStatus.OK
    response_data = await response.json()
    assert response_data is not None
    stored_token = await redis.get('<EMAIL>')
    expected_token = response_data['token']
    assert stored_token == expected_token


@pytest.mark.parametrize(
    argnames=('email', 'edrpou', 'user_data', 'company_data', 'role_data', 'expected'),
    argvalues=[
        pytest.param(
            TEST_PARTNER_EMAIL,
            TEST_PARTNER_EDRPOU,
            {},
            {},
            {},
            {
                'role_activation_source': RoleActivationSource.signature,
                'role_activated_by': None,
                'role_has_date_activated': True,
            },
            id='company_not_registered_user_not_registered',
        ),
        pytest.param(
            TEST_PARTNER_EMAIL,
            TEST_PARTNER_EDRPOU,
            {
                'id': TEST_UUID_1,
                'email': TEST_PARTNER_EMAIL,
                'registration_completed': True,
                'password': '1234',
            },
            {},
            {},
            {
                'role_activation_source': RoleActivationSource.signature,
                'role_activated_by': None,
                'role_has_date_activated': True,
            },
            id='company_not_registered_user_registered',
        ),
        pytest.param(
            TEST_PARTNER_EMAIL,
            TEST_PARTNER_EDRPOU,
            {},
            {'id': TEST_UUID_2, 'edrpou': TEST_PARTNER_EDRPOU},
            {},
            {
                'role_activation_source': RoleActivationSource.signature,
                'role_activated_by': None,
                'role_has_date_activated': True,
            },
            id='company_registered_user_not_registered',
        ),
        pytest.param(
            TEST_PARTNER_EMAIL,
            TEST_PARTNER_EDRPOU,
            {
                'id': TEST_UUID_1,
                'email': TEST_PARTNER_EMAIL,
                'registration_completed': True,
                'password': '1234',
            },
            {'id': TEST_UUID_2, 'edrpou': TEST_PARTNER_EDRPOU},
            {
                'id': TEST_UUID_3,
                'user_id': TEST_UUID_1,
                'company_id': TEST_UUID_2,
                'status': RoleStatus.pending,
            },
            {
                'role_activation_source': RoleActivationSource.signature,
                'role_activated_by': None,
                'role_has_date_activated': True,
            },
            id='company_registered_user_registered_pending_role',
        ),
        pytest.param(
            TEST_PARTNER_EMAIL,
            TEST_PARTNER_EDRPOU,
            {
                'id': TEST_UUID_1,
                'email': TEST_PARTNER_EMAIL,
                'registration_completed': True,
                'password': '1234',
            },
            {'id': TEST_UUID_2, 'edrpou': TEST_PARTNER_EDRPOU},
            {},
            {
                'role_activation_source': RoleActivationSource.signature,
                'role_activated_by': None,
                'role_has_date_activated': True,
            },
            id='company_registered_user_registered_no_role',
        ),
        pytest.param(
            TEST_PARTNER_EMAIL,
            TEST_PARTNER_EDRPOU,
            {
                'id': TEST_UUID_1,
                'email': TEST_PARTNER_EMAIL,
                'registration_completed': True,
                'password': '1234',
            },
            {'id': TEST_UUID_2, 'edrpou': TEST_PARTNER_EDRPOU},
            {
                'id': TEST_UUID_3,
                'user_id': TEST_UUID_1,
                'company_id': TEST_UUID_2,
                'status': RoleStatus.pending,
            },
            {
                'role_activation_source': RoleActivationSource.signature,
                'role_activated_by': None,
                'role_has_date_activated': True,
            },
            id='company_registered_user_registered_pending_role_duplicate',
        ),
        pytest.param(
            TEST_PARTNER_EMAIL,
            TEST_PARTNER_EDRPOU,
            {
                'id': TEST_UUID_1,
                'email': TEST_PARTNER_EMAIL,
                'registration_completed': True,
                'password': '1234',
            },
            {'id': TEST_UUID_2, 'edrpou': TEST_PARTNER_EDRPOU},
            {
                'id': TEST_UUID_3,
                'user_id': TEST_UUID_1,
                'company_id': TEST_UUID_2,
                'status': RoleStatus.active,
                'activation_source': RoleActivationSource.invite,
                # let's pretend that the role was previously activated itself
                'activated_by': TEST_UUID_3,
                'date_activated': datetime.now(),
            },
            {
                # We don't override an activation source because the user is already active
                'role_activation_source': RoleActivationSource.invite,
                'role_activated_by': TEST_UUID_3,
                'role_has_date_activated': True,
            },
            id='company_registered_user_registered_active_role',
        ),
    ],
)
async def test_create_signer_role(
    monkeypatch,
    aiohttp_client,
    email,
    edrpou,
    user_data,
    company_data,
    role_data,
    expected: dict,
):
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user, status_id=SIGNED_AND_SENT)
    sign_session = await prepare_sign_session_data(
        app=app,
        document=document,
        email=email,
        edrpou=edrpou,
        created_by=user.role_id,
    )

    if company_data:
        await prepare_company_data(app, **company_data)
    if user_data:
        async with app['db'].acquire() as conn:
            await insert_test_user(conn, user_data)
    if role_data:
        async with app['db'].acquire() as conn:
            await prepare_coworker_role(conn, role_data)

    # get registration token
    token, hash_ = await request_registration_token(client, sign_session)

    # monkeypatch verify function
    sign = prepare_signature_info(SignatureType.signature, edrpou)
    monkeypatch.setattr(eusign_utils, 'verify_sync', lambda *_, **__: sign)

    # register signer
    response = await client.post(
        path='/internal-api/registration/signers',
        headers={
            **prepare_referer_headers(client),
            **prepare_sign_session_headers(sign_session, client),
        },
        json={
            'signature': 'SGVsbG8gd29ybGQ=',  # signature validation is mocked
            'token': token,
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()
    async with app['db'].acquire() as conn:
        signer = await select_user_by_email(conn, email, company_edrpou=edrpou)
        signer_role = await select_role(conn, user_id=signer.id, company_edrpou=edrpou)

    assert signer is not None
    assert signer.registration_completed is True
    assert signer.role_id is not None
    assert signer.role_status == RoleStatus.active
    assert signer.company_id is not None

    assert signer_role.activation_source == expected['role_activation_source']
    assert signer_role.activated_by == expected['role_activated_by']

    if expected['role_has_date_activated']:
        assert signer_role.date_activated is not None
    else:
        assert signer_role.date_activated is None


async def test_set_source(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    # set initial source
    response = await client.post(
        '/internal-api/registration/source',
        headers=prepare_auth_headers(user),
        json={'source': 'edi'},
    )
    assert response.status == 200

    async with app['db'].acquire() as conn:
        updated_user = await select_user_by_email(conn, user.email)
        # source has been updated
        assert updated_user.source == RegistrationSource.edi.value

        # now change it to another one
        response = await client.post(
            '/internal-api/registration/source',
            headers=prepare_auth_headers(user),
            json={'source': 'kasa'},
        )
        updated_user = await select_user_by_email(conn, user.email)
        assert response.status == 200
        assert updated_user.source == RegistrationSource.kasa.value

    response = await client.post(
        '/internal-api/registration/source',
        headers=prepare_auth_headers(user),
        json={'source': 'invalidsource'},
    )
    assert response.status == 400


@pytest.mark.parametrize(
    'user_before, body, user_after, expected',
    [
        pytest.param(
            {'phone': '', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'phone': '+380673456789', 'send_otp': True},
            {'phone': '+380673456789', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'http_status': HTTPStatus.OK, 'otp_sent': True},
            id='set_empty_phone_to_valid_with_otp',
        ),
        pytest.param(
            {'phone': '', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'phone': '+380673456789', 'send_otp': False},
            {'phone': '+380673456789', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'http_status': HTTPStatus.OK, 'otp_sent': False},
            id='set_empty_phone_to_valid_without_otp',
        ),
        pytest.param(
            {'phone': '', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'phone': '+380673456789'},  # Default send_otp=True
            {'phone': '+380673456789', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'http_status': HTTPStatus.OK, 'otp_sent': True},
            id='set_empty_phone_with_default_send_otp',
        ),
        pytest.param(
            {'phone': '+380999999999', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'phone': '+380673456789', 'send_otp': True},
            {'phone': '+380673456789', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'http_status': HTTPStatus.OK, 'otp_sent': True},
            id='change_unverified_phone_with_otp',
        ),
        pytest.param(
            {'phone': '+380999999999', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'phone': '+380673456789', 'send_otp': False},
            {'phone': '+380673456789', 'is_phone_verified': False, 'is_2fa_enabled': False},
            {'http_status': HTTPStatus.OK, 'otp_sent': False},
            id='change_unverified_phone_without_otp',
        ),
        pytest.param(
            {'phone': '+380999999999', 'is_phone_verified': True, 'is_2fa_enabled': False},
            {'phone': '+380673456789', 'send_otp': True},
            {'phone': '+380999999999', 'is_phone_verified': True, 'is_2fa_enabled': False},
            {
                'http_status': HTTPStatus.BAD_REQUEST,
                'reason': 'Для повторного встановлення телефону потрібен пароль',
            },
            id='cannot_change_verified_phone',
        ),
        pytest.param(
            {'phone': None, 'is_phone_verified': False, 'is_2fa_enabled': True},
            {'phone': '+380999999998', 'send_otp': True},
            {'phone': '+380999999998', 'is_phone_verified': False, 'is_2fa_enabled': True},
            {'http_status': HTTPStatus.OK, 'otp_sent': True},
            id='none_phone_2fa_to_valid_with_otp',
        ),
        pytest.param(
            {'phone': None, 'is_phone_verified': False, 'is_2fa_enabled': True},
            {'phone': '+380999999998', 'send_otp': False},
            {'phone': '+380999999998', 'is_phone_verified': False, 'is_2fa_enabled': True},
            {'http_status': HTTPStatus.OK, 'otp_sent': False},
            id='none_phone_2fa_to_valid_without_otp',
        ),
    ],
)
async def test_set_registration_phone(
    aiohttp_client,
    user_before: dict,
    body: dict,
    user_after: dict,
    expected: dict,
    evo_sender_mock,
    monkeypatch,
) -> None:
    otp_code = '123456'
    monkeypatch.setattr(secrets, 'randbelow', lambda _: int(otp_code))

    app, client, user = await prepare_client(aiohttp_client, **user_before)

    response = await client.post(
        path='/internal-api/registration/phone',
        headers=prepare_auth_headers(user),
        json=body,
    )
    assert response.status == expected['http_status'], await response.json()

    user = await get_base_user(user_id=user.id)
    assert user.phone == user_after['phone']
    assert user.is_phone_verified == user_after['is_phone_verified']
    assert user.is_2fa_enabled == user_after['is_2fa_enabled']

    if response.status == HTTPStatus.OK:
        if expected['otp_sent']:
            assert evo_sender_mock.phone == user_after['phone']
            assert otp_code in evo_sender_mock.message
        else:
            assert evo_sender_mock.phone == ''
            assert evo_sender_mock.message == ''

    if response.status != HTTPStatus.OK:
        assert evo_sender_mock.phone == ''
        assert evo_sender_mock.message == ''

        response_body = await response.json()
        assert response_body['reason'] == expected['reason']


@pytest.mark.parametrize('limit', [1, None])
async def test_invite_coworker_with_overdraft(aiohttp_client, test_flags, limit):
    app, client, user = await prepare_client(aiohttp_client)

    cid = user.company_id
    async with services.db.acquire() as conn:
        await update_billing_company_config(
            conn=conn,
            company_id=cid,
            config={CompanyLimit.employees.value: limit},
        )
        await update_company_entity_count(
            conn, cid, entity=StatEntity.role, count=limit if limit is not None else 1
        )

    resp = await client.post(
        INVITE_NEW_USER_URL,
        json={'email': TEST_EMAIL, 'edrpou': user.company_edrpou},
        headers=prepare_auth_headers(user),
    )
    if limit is not None:
        assert resp.status == 400
        json = await resp.json()
        assert json['code'] == Code.overdraft.name
    else:
        assert resp.status == 201


@pytest.mark.parametrize('limit', [1, None])
async def test_registration_with_token_with_overdraft(aiohttp_client, test_flags, limit):
    app, client, user = await prepare_client(aiohttp_client)

    cid = user.company_id
    async with services.db.acquire() as conn:
        await update_billing_company_config(
            conn=conn,
            company_id=cid,
            config={CompanyLimit.employees.value: limit},
        )
        await update_company_entity_count(
            conn, cid, entity=StatEntity.role, count=limit if limit is not None else 1
        )
        short_token = await insert_short_registration_token(
            conn,
            {
                'edrpou': user.company_edrpou,
                'email': '<EMAIL>',
                'is_verified_inviter': True,
                'invited_by_company': user.company_edrpou,
            },
        )

    data = {
        'email': '<EMAIL>',
        'password': TEST_PASSWORD,
        'token': short_token,
    }
    resp = await client.post(
        REGISTRATION_WITH_TOKEN_URL,
        json=data,
        headers=prepare_referer_headers(client),
    )
    if limit is not None:
        assert resp.status == 400
        json = await resp.json()
        assert json['code'] == Code.overdraft.name
    else:
        assert resp.status == 201


async def test_check_email_exists(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        phone='+380509999999',
        is_phone_verified=True,
    )

    response = await client.get(CHECK_EMAIL_EXISTS_URL)
    assert response.status == HTTPStatus.BAD_REQUEST

    response = await client.get(CHECK_EMAIL_EXISTS_URL, params={'email': user.email})
    assert response.status == HTTPStatus.OK

    response = await client.get(CHECK_EMAIL_EXISTS_URL, params={'email': '<EMAIL>'})
    assert response.status == HTTPStatus.NOT_FOUND
    result = await response.json()
    assert result['code'] == Code.object_does_not_exist.name

    # test rate limit
    for _ in range(27):
        response = await client.get(CHECK_EMAIL_EXISTS_URL, params={'email': user.email})
        assert response.status == HTTPStatus.OK

    response = await client.get(CHECK_EMAIL_EXISTS_URL, params={'email': user.email})
    assert response.status == HTTPStatus.TOO_MANY_REQUESTS


async def test_short_registration(aiohttp_client, esputnik_box, crm_box, concierge_emulation):
    app, client = await prepare_app_client(aiohttp_client)

    await prepare_user_data(
        app=app,
        email=TEST_EMAIL,
        password='1234',
        email_confirmed=False,
        registration_completed=False,
        phone='+380999999999',
        first_name='Test',
    )

    concierge_token = concierge_emulation.generate_new_session_token()

    token = generate_password_recover_token(TEST_EMAIL)
    response = await client.post(
        path='/auth-api/registration/short',
        json={
            'token': token,
            'password': TEST_PASSWORD,
        },
        headers={
            **prepare_referer_headers(client),
            concierge_headers.ACCESS_TOKEN: concierge_token,
        },
    )
    assert response.status == HTTPStatus.OK, await response.json()

    async with services.db.acquire() as conn:
        user = await select_base_user(conn, email=TEST_EMAIL)
        assert user is not None
        assert user.registration_completed is False  # will be true after adding first company
        assert user.phone == '+380999999999'
        assert user.email_confirmed is True
        assert user.is_phone_verified is False

    assert len(esputnik_box) == 2
    assert {item['__key'] for item in esputnik_box} == {
        ('https://esputnik.com/api/v1/contacts/', None),
        ('https://esputnik.com/api/v1/event/', 'Registration'),
    }
    assert len(crm_box) == 1
    assert crm_box == [
        {
            'email': TEST_EMAIL,
            'mobile_phone': '+380999999999',
            'given_name': 'Test',
            'vchasno_id': user.id,
        }
    ]

    assert concierge_emulation.count_user_session(user_id=user.id) == 1
    assert concierge_emulation.get_user_profile(user_id=user.id) == {
        'email': '<EMAIL>',
        'first_name': 'Test',
        'is_email_confirmed': True,
        'is_super_admin': False,
        'last_name': None,
        'phone': '+380999999999',
        'auth_phone': None,
    }
