import copy
import logging
import uuid
from collections.abc import Mapping
from datetime import datetime
from secrets import token_urlsafe

import sqlalchemy as sa
from aiohttp import web
from aiohttp.web_request import FileField

from api.errors import DoesNotExist, InvalidRequest, Object
from app.auth import concierge
from app.auth import utils as auth
from app.auth.db import (
    exist_admin_in_company,
    select_base_user,
    select_company_by_edrpou,
    select_is_company_registered,
    select_role_by_id,
    select_user,
    update_invited_data,
    update_role,
    update_user,
    update_vchasno_profile_user,
    upsert_company,
    upsert_role,
)
from app.auth.enums import (
    AuthFlow,
    RoleActivationSource,
    RoleStatus,
)
from app.auth.helpers import autogenerate_password
from app.auth.providers.google import GoogleTokenPayload
from app.auth.providers.microsoft import MicrosoftTokenPayload
from app.auth.types import (
    BaseUser,
    GoogleOAuthResponse,
    InsertRoleDict,
    MicrosoftOAuthResponse,
    Role,
    RoleDB,
    UpdateBaseUserDict,
    UpdateRoleDict,
    User,
)
from app.auth.utils import (
    create_coworker_role,
    create_user,
    get_company_config,
    get_short_company_name,
    get_user,
    insert_autogenerated_user,
    login_user_base,
    send_jobs_about_new_role,
    set_default_role_settings,
)
from app.billing.api import add_system_company_rate
from app.billing.types import AccountRate, CompanyRate, CompanyRateStatus
from app.contacts import utils as contacts
from app.crm.utils import send_company_to_crm, send_user_to_crm
from app.events.user_actions import Action, get_event_source, insert_user_action
from app.events.user_actions import types as user_actions_types
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.landing.constants import COMPANY_CHECK_USER_ID_COOKIE
from app.lib import eusign_utils
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import DAY, end_of_day, local_now, to_local_datetime
from app.lib.enums import UserRole
from app.lib.redirects import redirect
from app.lib.types import DataDict
from app.lib.urls import build_url
from app.profile.utils import (
    add_esputnik_contact,
    generate_esputnik_first_employee_event,
    send_create_initial_free_rate_job,
    update_esputnik_company_check,
    update_esputnik_company_type,
    update_esputnik_email_confirmation,
    update_esputnik_registration_completed,
)
from app.registration import emailing
from app.registration.enums import RegistrationMethod, RegistrationSource
from app.registration.schemas import InviteCoworkerPermissionSchema
from app.registration.types import (
    AddActiveRoleCtx,
    AddActiveRoleOutput,
    CompanyRegistrationToken,
    ConfirmEmailCtx,
    CreateActiveSignerOutput,
    CreateSignerRoleCtx,
    InviteCoworkerOptions,
    KasaRegistrationCtx,
    RegisteredWithTokenOutput,
    RegistrationCtx,
    RegistrationWithTokenCtx,
)
from app.services import services
from app.signatures.types import SignatureInfoData
from app.tokens.db import invalidate_short_registration_token
from app.trigger_notifications.utils import (
    TriggerNotificationCheckRecipients,
    TriggerNotificationCompanyRegistration,
)
from app.vchasno_profile.types import UpdateVchasnoUserDict
from app.youcontrol.utils import send_sync_company_info_from_youcontrol
from worker import topics

logger = logging.getLogger(__name__)

# 1 hour in seconds
COMPANY_REGISTRATION_TOKEN_TTL = 60 * 60


# Basic service level alias to functions from underlying modules
send_auto_welcome_email = emailing.send_auto_welcome_email


def redirect_after_confirmation(
    *,
    is_logged: bool = False,
    to_url: str | None = None,
) -> web.HTTPException:
    """Redirect to app if user is logged in, or to login if does not."""

    if to_url:
        return web.HTTPFound(to_url)

    if is_logged:
        return redirect('app', tail='')

    return redirect('auth', tail='/login')


async def activate_role(
    conn: DBConnection,
    *,
    user_id: str,
    company: DBRow,
    activation_source: RoleActivationSource,
    activated_by: str | None,
    with_signature_key: bool,
    position: str | None,
) -> DBRow:
    """Activate role in company. Make admin if no admins in company"""
    company_id = company.id

    # TAG: role_activation
    insert_data: InsertRoleDict = {
        'company_id': company_id,
        'user_id': user_id,
        'status': RoleStatus.active,
        'date_agreed': sa.text('now()'),
        'with_signature_key': with_signature_key,
        'date_activated': sa.text('now()'),
        'activated_by': activated_by,
        'activation_source': activation_source,
    }

    if position:
        insert_data['position'] = position

    if not await exist_admin_in_company(conn, company_id):
        # Make the first user's role in the company admin and set `can_edit_*` permissions to
        # True to allow the user to manage the company
        insert_data['user_role'] = UserRole.admin.value
        insert_data['can_edit_company'] = True
        insert_data['can_edit_roles'] = True
        insert_data['can_view_document'] = True
        insert_data['can_view_private_document'] = True

    # WARNING: If a user already exists in the database, we should not modify their permissions.
    # This is because as soon as the role is created, the permissions are set to the settings actual
    # at that time. After that, any changes to permissions or notification settings should be made
    # by the company admin, the user themselves, or the superadmin.
    #
    # One exception to this rule is when there is no admin in the company. In that case, we make
    # a role that we are trying to activate with elevated permissions to have at least one admin
    # in the company to manage it.
    update_data: UpdateRoleDict = copy.deepcopy(insert_data)

    # Set proper defaults based on config. We have different defaults for roles created by
    # signature and different for roles created by coworker invite
    config = await get_company_config(conn, company_id=company_id)
    if activation_source == RoleActivationSource.signature:
        set_default_role_settings(
            data=insert_data,
            default=config.default_role_permissions_key,
            invited_by=None,
        )
    else:
        set_default_role_settings(
            data=insert_data,
            default=config.default_role_permissions,
            invited_by=None,
        )

    logger.info(
        msg='Role activation',
        extra={'insert_data': str(insert_data), 'update_data': str(update_data)},
    )
    return await upsert_role(
        conn=conn,
        insert_data=insert_data,
        update_data=update_data,
    )


async def create_invited_coworker_role(
    conn: DBConnection,
    company_id: str,
    user_id: str,
    position: str | None,
    permissions: InviteCoworkerPermissionSchema | None,
    existed_role: Role | None,
    invited_by: User | None,
    has_hrs_role: bool = False,
    is_counted_in_billing_limit: bool = True,
) -> RoleDB:
    """
    Create a new role with given permissions for given user and company
    """

    # There is no "is_admin" column in "roles" table, so we need to pop it
    # and convert to "user_role" column
    is_admin: bool = (permissions.is_admin or False) if permissions else False

    role_data: UpdateRoleDict = permissions.to_update_dict() if permissions else {}
    role_data['user_role'] = UserRole.admin.value if is_admin else UserRole.user.value
    # TAG: role_activation
    role_data['status'] = RoleStatus.active
    role_data['position'] = position
    role_data['date_agreed'] = sa.text('now()')
    role_data['date_invited'] = sa.text('now()')
    if invited_by:
        role_data['invited_by'] = invited_by.role_id
        role_data['activated_by'] = invited_by.role_id
    role_data['date_activated'] = sa.text('now()')
    role_data['activation_source'] = RoleActivationSource.invite
    role_data['has_hrs_role'] = has_hrs_role
    role_data['is_counted_in_billing_limit'] = is_counted_in_billing_limit
    role_data.setdefault('can_edit_company', False)
    role_data.setdefault('can_edit_roles', False)

    if existed_role:
        role = await update_role(
            conn=conn,
            role_id=existed_role.id_,
            data=role_data,
        )
        assert role, 'Expected role to be updated'

    else:
        # Create a new role with given permissions
        role = await create_coworker_role(
            conn=conn,
            data={
                'company_id': company_id,
                'user_id': user_id,
                **role_data,
            },
            invited_by=invited_by,
        )

    return role


async def _prepare_add_role_company_data(conn: DBConnection, ctx: AddActiveRoleCtx) -> DataDict:
    name = ctx.company_name
    user = ctx.user
    company_data = {'edrpou': ctx.edrpou, 'is_legal': True}

    company = await select_company_by_edrpou(conn, ctx.edrpou)
    if not company and user.pending_referrer_role_id:
        referrer = await select_role_by_id(conn, user.pending_referrer_role_id)
        if referrer:
            company_data.update(
                {
                    'dealt_by_company': referrer.company_edrpou,
                    'dealt_by_user': referrer.user_id,
                }
            )

    if not company or (not company.name and not company.full_name):
        short_name = name and get_short_company_name(name)
        company_data.update({'name': short_name, 'full_name': name})

    return company_data


async def add_active_role(conn: DBConnection, ctx: AddActiveRoleCtx) -> AddActiveRoleOutput:
    """
    Add an active role to company for given user. Company will be created if not exist
    """
    user = ctx.user

    company_data = await _prepare_add_role_company_data(conn, ctx)

    update_user_data: UpdateBaseUserDict = {
        'registration_completed': True,
        'pending_referrer_role_id': None,
    }

    if not user.phone and ctx.phone:
        # Phone is provided only in cases when we sync user from crm-proxy
        # TODO: [EC-15] save to vchasno profile
        update_user_data['phone'] = ctx.phone

    update_vchasno_user_data: UpdateVchasnoUserDict = {
        'first_name': user.first_name or ctx.first_name or ctx.user_name,
        'second_name': user.second_name or ctx.second_name,
        'last_name': user.last_name or ctx.last_name,
    }

    logger.info(
        msg='Adding new active role to company',
        extra={'company_data': company_data, 'user_data': update_user_data},
    )

    async with conn.begin():
        is_company_registered_before = await select_is_company_registered(
            conn=conn,
            edrpou=ctx.edrpou,
        )

        company = await upsert_company(conn, company_data)

        role = await activate_role(
            conn=conn,
            user_id=user.id,
            company=company._row,
            activation_source=ctx.activation_source,
            activated_by=ctx.activated_by,
            with_signature_key=ctx.with_signature_key,
            position=ctx.position,
        )

        await contacts.add_role_to_contact_recipients_indexation(conn, role_id=role.id)
        await contacts.add_company_to_contact_recipients_indexation(conn, company_id=company.id)

        await update_vchasno_profile_user(
            conn=conn,
            user_id=user.id,
            data=update_vchasno_user_data,
        )
        update_user_data['last_role_id'] = role.id
        await update_user(
            conn=conn,
            user_id=ctx.user.id,
            data=update_user_data,
        )

        # todo: move this logic to "send_jobs_about_about_company_registration" or make it part
        #  of the database transaction
        if not is_company_registered_before:
            await send_create_initial_free_rate_job(company_id=company.id)

        if not is_company_registered_before and ctx.invited_type and ctx.invited_edrpou:
            await update_invited_data(
                conn=conn,
                company_id=company.id,
                invited_type=ctx.invited_type,
                invited_edrpou=ctx.invited_edrpou,
            )

    # WARNING: Do not schedule async jobs related to a new role within this function, as it
    # may be invoked within a database transaction context. Schedule async jobs in the
    # complementary function "send_jobs_about_new_active_role" and ensure it is called
    # outside the transaction. Also, remember to schedule async jobs using that function;
    # otherwise some part of the business logic will get lost.

    return AddActiveRoleOutput(
        role=RoleDB.from_row(role),
        company=company,
        is_company_registered=not is_company_registered_before,
    )


async def send_jobs_about_new_active_role(
    *,
    user_id: str,
    user_email: str,
    role_id: str,
    company_id: str,
    company_edrpou: str,
    company_name: str | None,
    is_company_registered: bool,
) -> None:
    # CRM jobs
    await send_user_to_crm(user_id)

    # Other jobs about a new role
    await send_jobs_about_new_role(role_id, company_id)

    # Esputnik jobs
    await update_esputnik_company_type(email=user_email, edrpou=company_edrpou)
    await update_esputnik_registration_completed(email=user_email)

    # Trigger notification
    trigger_notification = TriggerNotificationCheckRecipients()
    await trigger_notification.send(roles_ids=[role_id])

    if is_company_registered:
        await send_jobs_about_about_company_registration(
            email=user_email,
            company_edrpou=company_edrpou,
            company_name=company_name,
        )


async def save_event_about_new_active_role(
    *,
    request_source: user_actions_types.Source,
    user_email: str,
    company_id: str,
    signature_info: SignatureInfoData | None,
) -> None:
    if get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        return

    extra = {}
    if signature_info:
        extra.update(
            {
                'acsk': signature_info.acsk,
                'timemark': signature_info.timemark and signature_info.timemark.isoformat(),
                'serial_number': signature_info.serial_number,
            }
        )

    await insert_user_action(
        action=Action.role_create,
        source=request_source,
        email=user_email,
        company_id=company_id,
        extra=extra,
    )


async def activate_tov_trials_on_registration(conn: DBConnection, edrpou: str) -> None:
    company = await select_company_by_edrpou(conn, edrpou)
    if not company:
        logger.warning('Company not found')
        return

    start_date = local_now()
    end_date = to_local_datetime(end_of_day(start_date + 14 * DAY).replace(tzinfo=None))

    rate = CompanyRate(
        id_=str(uuid.uuid4()),
        company_id=company.id,
        company_edrpou=company.edrpou,
        rate=AccountRate.pro_plus_trial_2022_12,
        status=CompanyRateStatus.active,
        amount=0,
        start_date=start_date,
        end_date=end_date,
    )
    await add_system_company_rate(conn, rate=rate)

    logger.info(
        'Trial activated for TOV company on registration',
        extra={'edrpou': edrpou},
    )


async def schedule_sync_recipients_date_received_job(
    company_edrpou: str,
    date_registered: datetime | None = None,
) -> None:
    """
    Schedule an async job that sets the date_received in the recipients' table to the date when
    the company was registered
    """
    if date_registered is None:
        date_registered = local_now()

    await services.kafka.send_record(
        topic=topics.SYNC_RECIPIENTS_DATE_RECEIVED,
        value={
            'company_edrpou': company_edrpou,
            'date_registered': date_registered.isoformat(),
        },
    )


async def send_jobs_about_about_company_registration(
    *,
    email: str,
    company_edrpou: str,
    company_name: str | None,
) -> None:
    """
    Send async jobs after company registration (first active role in a company)
    """

    await schedule_sync_recipients_date_received_job(company_edrpou=company_edrpou)

    trigger_notification = TriggerNotificationCompanyRegistration()
    await trigger_notification.send_prepare_job(
        company_edrpou=company_edrpou,
        company_name=company_name,
    )

    await generate_esputnik_first_employee_event(email)

    await services.kafka.send_record(
        topic=topics.ESPUTNIK_COMPANY_REGISTRATION_SEND_EVENT,
        value={'email': email, 'edrpou': company_edrpou},
    )

    async with services.db.acquire() as conn:
        await send_sync_company_info_from_youcontrol(conn=conn, edrpou=company_edrpou)


def is_valid_email_domains(email: str, domains: list[str] | None) -> bool:
    if not domains:
        return True
    return any(email.endswith(f'@{domain}') for domain in domains)


def get_signature_bytes(p7s: str | bytes | bytearray | FileField) -> bytes:
    if isinstance(p7s, bytes):
        return p7s
    if isinstance(p7s, FileField):
        return p7s.file.read()
    if isinstance(p7s, bytearray):
        return bytes(p7s)

    logger.exception(
        'Bad signature type is used',
        extra={
            'p7s_type': str(type(p7s)),
            'p7s': p7s,
        },
    )
    raise InvalidRequest(reason=_('Файл підпису містить неправильний тип'))


def _get_company_registration_token() -> str:
    return token_urlsafe(16)


def get_company_registration_key(user_email: str) -> str:
    return f'company-token-{user_email}'


async def create_company_registration_token(user_email: str) -> CompanyRegistrationToken:
    """
    Create token that user can sign with a key to add a company to his profile.
    """
    token = _get_company_registration_token()

    token_bytes = token.encode()
    token_hash = await eusign_utils.generate_hash_base64(token_bytes)

    # We store the token in redis to check later that it's not generated by somewhere else
    # and it's not expired
    redis_key = get_company_registration_key(user_email=user_email)
    await services.redis.setex(redis_key, value=token, time=COMPANY_REGISTRATION_TOKEN_TTL)

    return CompanyRegistrationToken(original=token, base64_hash=token_hash)


async def get_company_registration_token(user_email: str) -> str | None:
    key = get_company_registration_key(user_email=user_email)
    value = await services.redis.get(key)
    return value


async def check_company_registration_token(token: str, user_email: str) -> bool:
    key = get_company_registration_key(user_email=user_email)
    value = await services.redis.get(key)
    return value == token


async def delete_company_registration_token(user_email: str) -> None:
    key = get_company_registration_key(user_email=user_email)
    await services.redis.delete(key)


async def schedule_create_signer_async_jobs(
    request: web.Request,
    output: CreateActiveSignerOutput | None,
) -> None:
    """
    The second part of the function "create_signer_base" that should be called
    outside the transaction.
    """

    if not output:
        return

    user = output.user

    if output.is_user_created:
        await send_user_to_crm(user_id=user.id)
        await add_esputnik_contact(email=user.email)

    if output.is_role_activated:
        await send_jobs_about_new_active_role(
            user_id=user.id,
            user_email=user.email,
            role_id=user.role_id,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
            company_name=user.company_name,
            is_company_registered=output.is_company_registered,
        )

        if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
            await save_event_about_new_active_role(
                request_source=get_event_source(request),
                user_email=user.email,
                company_id=user.company_id,
                signature_info=None,
            )


async def create_signer_base(
    conn: DBConnection,
    ctx: CreateSignerRoleCtx,
) -> CreateActiveSignerOutput:
    """
    Create an active signer role for the given user and company in signing context.

    For example, in sign sessions, a user does not always exist in Vchasno, so we need to create
    a new user and add an active role for them and only then add a signature from their behalf.
    """

    logger.info(
        msg='Creating active signer',
        extra={'ctx': ctx.to_dict()},
    )
    user = await select_user(conn, email=ctx.email, company_edrpou=ctx.edrpou)

    # User already exists in Vchasno and have active role, just do nothing
    if user and user.role_status == RoleStatus.active:
        return CreateActiveSignerOutput(
            user=user,
            is_role_activated=False,
            is_user_created=False,
            is_company_registered=False,
            signature_info=None,
        )

    base_user = await select_base_user(conn, email=ctx.email)

    async with conn.begin():
        # User doesn't exist in Vchasno at all, create a new autogenerated user
        # and then add an active role for him
        is_user_created: bool = False
        if not base_user:
            password = autogenerate_password()
            base_user = await insert_autogenerated_user(
                conn=conn,
                email=ctx.email,
                password=password,
                source=RegistrationSource.after_sign,
                is_email_confirmed=True,
                # We consider the registration completed (at least one active role)
                # because the user is created with the active role
                is_registration_completed=True,
            )
            is_user_created = True

        output = await add_active_role(
            conn=conn,
            ctx=AddActiveRoleCtx(
                user=base_user,
                edrpou=ctx.edrpou,
                activation_source=RoleActivationSource.signature,
                activated_by=None,
                company_name=ctx.company_name,
                user_name=ctx.user_name,
                first_name=ctx.user_first_name,
                last_name=ctx.user_last_name,
                signature_info=ctx.signature_info,
            ),
        )
        role = output.role

    user = await get_user(conn, role_id=role.id)
    if not user:
        raise DoesNotExist(Object.role, id=role.id)

    # WARN: remember to call "schedule_create_signer_async_jobs" outside of current transaction
    return CreateActiveSignerOutput(
        user=user,
        is_role_activated=True,
        is_user_created=is_user_created,
        is_company_registered=output.is_company_registered,
        signature_info=ctx.signature_info,
    )


async def send_jobs_about_autogenerated_user(user: DBRow) -> None:
    """
    Schedule async jobs after autogenerated user creation
    """
    await add_esputnik_contact(email=user.email)
    await send_user_to_crm(user_id=user.id)

    await send_jobs_about_company_update(company_id=user.company_id)
    await send_jobs_about_new_role(role_id=user.role_id, company_id=user.company_id)


async def send_jobs_about_company_update(company_id: str) -> None:
    """
    Schedule async jobs about new company creation/update

    NOTE: This function should be called outside the transaction to prevent data loss.

    NOTE: In some places, we don't know if a company was created or not because insert is hidden
    deep in call stack. See reference [1], [2]. So we call that function in cases where a company
    can be potentially created/updated. Underlying async jobs should work correctly if the
    company already existed before the call.

    [1] app.auth.db.insert_company (ignore_existing=True)
    [2] app.auth.db.upsert_company
    """

    await send_company_to_crm(company_id=company_id)


async def save_esputnik_company_check(cookies: Mapping[str, str], email: str) -> None:
    """
    Send information to ESputnik that the user has used counterparty
    verification on the landing page.
    """
    if company_check_user_id := cookies.get(COMPANY_CHECK_USER_ID_COOKIE):
        await update_esputnik_company_check(email=email, user_id=company_check_user_id)


async def confirm_email(
    conn: DBConnection,
    request: web.Request,
    ctx: ConfirmEmailCtx,
) -> None:
    """Confirm the email address for the given user and sign in the user."""

    user = await auth.update_user_email_confirmed(
        conn=conn,
        user_id=ctx.user.id,
        is_email_confirmed=True,
    )

    await concierge.sign_in_with_update(conn=conn, user_id=user.id, user=user)
    await send_confirm_email_jobs(user=user)
    await emailing.send_user_welcome_email(user=user)


async def send_confirm_email_jobs(user: BaseUser) -> None:
    """
    Send async jobs after user confirm email

    This function currently sends only one job to update the email confirmation
    status in ESputnik. However, in the future, it can be extended to include
    additional async jobs after the user confirms their email.
    """

    # ESputnik
    await update_esputnik_email_confirmation(email=user.email)


async def get_dealer_referrer_role_id(
    conn: DBConnection,
    referrer_role_id: str | None,
) -> str | None:
    """
    Get the dealer referrer role ID if the given referrer role ID corresponds
    to a dealer role.
    """
    if not referrer_role_id:
        return None

    referrer = await select_role_by_id(conn, role_id=referrer_role_id)
    if referrer and referrer.is_dealer:
        return referrer.id_

    return None


async def send_registration_jobs(
    user: BaseUser,
    cookies: Mapping[str, str],
) -> None:
    """Send async jobs after user user registration"""

    # CRM
    await send_user_to_crm(user.id)

    # ESputnik
    await add_esputnik_contact(kafka=services.kafka, email=user.email)
    await save_esputnik_company_check(cookies=cookies, email=user.email)


async def create_user_on_registration(
    conn: DBConnection,
    *,
    email: str,
    password: str | None,
    phone: str | None,
    first_name: str | None,
    second_name: str | None,
    last_name: str | None,
    promo_code: str | None,
    trial_auto_enable: bool,
    pending_referrer_role_id: str | None,
    source: RegistrationSource | None,
    is_email_confirmed: bool,
    registration_method: RegistrationMethod | None,
    google_id: str | None = None,
    microsoft_id: str | None = None,
    apple_id: str | None = None,
    is_registration_completed: bool,
) -> BaseUser:
    return await create_user(
        conn=conn,
        email=email,
        password=password,
        is_autogenerated_password=False,
        phone=phone,
        first_name=first_name,
        second_name=second_name,
        last_name=last_name,
        promo_code=promo_code,
        trial_auto_enabled=trial_auto_enable,
        pending_referrer_role_id=pending_referrer_role_id,
        source=source,
        is_email_confirmed=is_email_confirmed,
        is_registration_completed=is_registration_completed,
        google_id=google_id,
        microsoft_id=microsoft_id,
        apple_id=apple_id,
        registration_method=registration_method,
        created_by=None,
        is_phone_verified=False,
    )


async def web_registration(
    conn: DBConnection,
    request: web.Request,
    ctx: RegistrationCtx,
) -> BaseUser:
    """
    Perform the registration process from web journey for a new user by email and password.
    """

    # Get the ID of the dealer who referred the user
    referrer_id = await get_dealer_referrer_role_id(
        conn=conn,
        referrer_role_id=ctx.referrer,
    )
    user = await create_user_on_registration(
        conn=conn,
        email=ctx.email,
        password=ctx.password,
        phone=None,
        first_name=None,
        second_name=None,
        last_name=None,
        promo_code=ctx.promo_code,
        trial_auto_enable=ctx.trial_auto_enable,
        pending_referrer_role_id=referrer_id,
        source=ctx.source,
        is_email_confirmed=False,
        is_registration_completed=False,
        registration_method=ctx.registration_method,
    )

    # TODO: The current functions are also called in the context of server-to-server integration
    #  between EDO and Kasa, as well as in the mobile API. Neither of these use cookies or
    #  concierge headers, so there is no need to create a session. Investigate whether we can
    #  disable it in these cases and call it only for web registration.
    await login_user_base(
        request=request,
        conn=conn,
        user=user,
        second_factor=False,
    )

    # Send any necessary jobs related to the registration process
    await send_registration_jobs(user=user, cookies=request.cookies)

    await emailing.send_confirmation_email(
        email=user.email,
        redirect_url=ctx.redirect_url,
    )

    return user


async def registration_kasa(
    conn: DBConnection,
    request: web.Request,
    ctx: KasaRegistrationCtx,
) -> BaseUser:
    """
    This function is used to register a new user by other Vchasno Group services using private
    server-to-server integration. It is quite similar to the web registration, but without
    creating a user session because it mobile session is managed by the services themselves.

    For example, Kasa uses this function to register a new user in mobile app:
     -> Kasa mobile app -> Kasa backend -> EDO backend (this function)
    """

    user = await create_user_on_registration(
        conn=conn,
        email=ctx.email,
        password=ctx.password,
        # TODO: [EC-15] save to vchasno profile
        phone=ctx.phone,
        first_name=ctx.name,
        second_name=None,
        last_name=None,
        promo_code=None,
        trial_auto_enable=False,
        pending_referrer_role_id=None,
        source=ctx.source,
        is_email_confirmed=False,
        is_registration_completed=False,
        registration_method=ctx.registration_method,
    )

    await auth.db.upsert_invalid_password_count(conn, user_id=user.id, value=0)

    # Send any necessary jobs related to the registration process
    await send_registration_jobs(user=user, cookies=request.cookies)

    await emailing.send_confirmation_email(email=user.email, redirect_url=None)

    return user


async def google_registration(
    conn: DBConnection,
    token: GoogleTokenPayload,
    request: web.Request,
) -> GoogleOAuthResponse:
    """
    Perform the registration process for a new user by "Sign with Google".

    It's a combination of functions "app.registration.utils.registration"
    and "app.registration.utils.confirm_email".

    This function has WEB specific logic, like "login_user_base", so be careful to use it
     in the context of mobile API or server-to-server integrations.
    """

    from app.registration.validators import validate_google_registration

    ctx = await validate_google_registration(
        conn=conn,
        request=request,
        token=token,
    )

    # Get the ID of the dealer who referred the user
    referrer_id = await get_dealer_referrer_role_id(
        conn=conn,
        referrer_role_id=ctx.referrer,
    )

    async with conn.begin():
        user = await create_user_on_registration(
            conn=conn,
            email=token.email,
            password=None,
            phone=None,
            first_name=token.given_name,
            second_name=None,
            last_name=token.family_name,
            promo_code=ctx.promo_code,
            trial_auto_enable=ctx.trial_auto_enable,
            pending_referrer_role_id=referrer_id,
            source=ctx.source,
            # we trust google with email verification
            is_email_confirmed=True,
            is_registration_completed=False,
            google_id=token.sub,
            registration_method=RegistrationMethod.google,
        )

        await login_user_base(
            request=request,
            conn=conn,
            user=user,
            second_factor=False,
        )

    #  Send any necessary jobs related to the registration and email
    #  confirmation process
    await send_registration_jobs(user=user, cookies=request.cookies)
    await send_confirm_email_jobs(user=user)

    await emailing.send_user_welcome_email(user=user)

    # Redirect to URL from parameters or to "/app" by default
    next_url: str | None = ctx.redirect_url
    if not next_url:
        next_url = build_url('app', absolute=False, tail='')

    return GoogleOAuthResponse(
        email=user.email,
        flow=AuthFlow.registration,
        next_url=next_url,
        is_2fa_enabled=False,
    )


async def microsoft_registration(
    conn: DBConnection,
    token: MicrosoftTokenPayload,
    request: web.Request,
) -> MicrosoftOAuthResponse:
    """
    Perform the registration process for a new user by "Sign with Microsoft".

    It's a combination of functions "app.registration.utils.registration"
    and "app.registration.utils.confirm_email".
    """

    from app.registration.validators import validate_microsoft_registration

    ctx = await validate_microsoft_registration(
        conn=conn,
        request=request,
        token=token,
    )

    # Get the ID of the dealer who referred the user
    referrer_id = await get_dealer_referrer_role_id(
        conn=conn,
        referrer_role_id=ctx.referrer,
    )

    async with conn.begin():
        user = await create_user_on_registration(
            conn=conn,
            email=ctx.email,
            password=None,
            # TODO: [EC-15] save to vchasno profile
            phone=token.mobile_phone,
            first_name=token.given_name,
            second_name=None,
            last_name=token.surname,
            promo_code=ctx.promo_code,
            trial_auto_enable=ctx.trial_auto_enable,
            pending_referrer_role_id=referrer_id,
            source=ctx.source,
            # we trust microsoft with email verification
            is_email_confirmed=True,
            is_registration_completed=False,
            microsoft_id=token.id,
            registration_method=RegistrationMethod.microsoft,
        )

        await login_user_base(
            request=request,
            conn=conn,
            user=user,
            second_factor=False,
        )

    #  Send any necessary jobs related to the registration and email
    #  confirmation process
    await send_registration_jobs(user=user, cookies=request.cookies)
    await send_confirm_email_jobs(user=user)

    await emailing.send_user_welcome_email(user=user)

    # Redirect to URL from parameters or to "/app" by default
    next_url: str | None = ctx.redirect_url
    if not next_url:
        next_url = build_url('app', absolute=False, tail='')

    return MicrosoftOAuthResponse(
        email=user.email,
        flow=AuthFlow.registration,
        next_url=next_url,
        is_2fa_enabled=False,
    )


async def process_invite_coworker(
    conn: DBConnection,
    options: InviteCoworkerOptions,
    invited_user: BaseUser,
    invited_by: User | None = None,
) -> RoleDB:
    logger.info(
        'Invite coworker to Vshacno',
        extra={
            'company_id': options.company_id,
            'user_email': options.email,
            'recipient_email': options.email,
        },
    )

    role = await create_invited_coworker_role(
        conn=conn,
        company_id=options.company_id,
        user_id=invited_user.id,
        position=options.position,
        existed_role=options.existed_role,
        permissions=options.permissions,
        has_hrs_role=options.has_hrs_role,
        is_counted_in_billing_limit=options.is_counted_in_billing_limit,
        invited_by=invited_by,
    )

    # Complete registration
    if not invited_user.registration_completed:
        await update_user(
            conn=conn,
            user_id=invited_user.id,
            data={'registration_completed': True},
        )

    await contacts.add_role_to_contact_recipients_indexation(conn, role_id=role.id)

    return role


async def process_registration_with_token(
    conn: DBConnection,
    ctx: RegistrationWithTokenCtx,
) -> RegisteredWithTokenOutput:
    edrpou = ctx.token.edrpou
    email = ctx.email
    token = ctx.token

    # If user is invited by coworker we need create user with
    # registration_completed == True status and active role of regular user
    invited_by_coworker = token.invited_by_company == token.edrpou
    invited_by_company_edrpou = token.invited_by_company
    invited_by_user_id = token.invited_by_user
    invited_by = None
    if invited_by_user_id and invited_by_company_edrpou:
        invited_by = await select_user(
            conn=conn,
            user_id=invited_by_user_id,
            company_edrpou=invited_by_company_edrpou,
        )

    company_id: str | None = None
    role = None
    async with conn.begin():
        # Insert and return new User instance
        user = await create_user_on_registration(
            conn=conn,
            email=email,
            password=ctx.password,
            phone=None,
            first_name=None,
            second_name=None,
            last_name=None,
            promo_code=None,
            trial_auto_enable=False,
            pending_referrer_role_id=None,
            registration_method=None,
            is_email_confirmed=ctx.is_email_confirmed,
            is_registration_completed=invited_by_coworker,
            source=ctx.source,
        )
        user_id = user.id

        dealt_by_company = None
        dealt_by_user = None

        if invited_by_company := token.invited_by_company:
            inviter = await select_company_by_edrpou(
                conn=conn,
                edrpou=invited_by_company,
            )
            if inviter and inviter.is_dealer:
                dealt_by_company = token.invited_by_company
                dealt_by_user = token.invited_by_user

        # Insert role only if user is invited by coworker
        if invited_by_coworker:
            company_id = await auth.create_company(
                conn=conn,
                data={
                    'edrpou': edrpou,
                    'is_legal': True,
                    'dealt_by_company': dealt_by_company,
                    'dealt_by_user': dealt_by_user,
                },
                ignore_existing=True,
            )
            exist_admin = await exist_admin_in_company(conn, company_id)
            user_role = (
                UserRole.admin.value
                if invited_by_coworker and not exist_admin
                else UserRole.user.value
            )
            # Insert role for current user for given company
            # TAG: role_activation
            role = await create_coworker_role(
                conn=conn,
                data={
                    'company_id': company_id,
                    'user_id': user_id,
                    'date_agreed': sa.text('now()'),
                    'status': RoleStatus.active,
                    'show_invite_tooltip': True,
                    'user_role': user_role,
                    'can_edit_company': False,
                    'can_edit_roles': False,
                    'invited_by': invited_by.role_id if invited_by else None,
                    'date_invited': token.date_created,
                    'activated_by': invited_by.role_id if invited_by else None,
                    'date_activated': sa.func.now(),
                    'activation_source': RoleActivationSource.invite,
                },
                invited_by=invited_by,
            )
            await contacts.add_role_to_contact_recipients_indexation(conn, role_id=role.id)

            # And update user instance with last role ID to use
            await update_user(
                conn=conn,
                user_id=user_id,
                data={'last_role_id': role.id},
            )

        # After registration is done - invalidate registration token, so
        # user cannot use it another time
        await invalidate_short_registration_token(
            conn=conn,
            token=token,
            user_id=user_id,
        )

    return RegisteredWithTokenOutput(
        user=user,
        role=role,
        company_id=company_id,
    )
