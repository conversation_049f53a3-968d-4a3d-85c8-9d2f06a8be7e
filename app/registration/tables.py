import sqlalchemy as sa

from app.models import columns, metadata

# Table for storing information about sources of users registration and
# their attempts to finish the registration process.
registration_sources_table = sa.Table(
    'registration_sources',
    metadata,
    columns.UUID(),
    columns.ForeignKey(
        'user_id',
        'users.id',
        nullable=False,
        ondelete='CASCADE',
        index=True,
    ),
    # the order in which the the user was attempted to register.
    sa.Column('order', sa.SmallInteger(), nullable=False),
    # Store any JSON like data related to user registration process
    # such as UTM, vendor_id, promo code
    columns.JSONB('meta'),
    columns.DateTime('date_recorded'),
    columns.DateCreated(),
)
