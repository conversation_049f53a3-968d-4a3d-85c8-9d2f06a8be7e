import pytest

from api.uploads.types import MetaData
from app.signatures.types import P7SContent
from app.uploads.utils import (
    _merge_metadata_sources,
    _soft_correct_metadata,
    guess_filename_from_signature_container,
)

ROOT_P7S_CONTENT = P7SContent(content=b'', original=b'', signatures=[])


@pytest.mark.parametrize(
    'content, name, expected',
    [
        (
            P7SContent(content=ROOT_P7S_CONTENT, original=b'', signatures=[]),
            'file.p7s',
            'file.xml',
        ),
        (
            ROOT_P7S_CONTENT,
            'file.p7s',
            'file.xml',
        ),
        (
            P7SContent(content=ROOT_P7S_CONTENT, original=b'', signatures=[]),
            'file.pdf.p7s.p7s',
            'file.pdf',
        ),
        (
            P7SContent(content=ROOT_P7S_CONTENT, original=b'', signatures=[]),
            'file.pdf.p7s',
            'file.pdf',
        ),
        (
            ROOT_P7S_CONTENT,
            'file.pdf.p7s.p7s',
            'file.pdf.p7s',
        ),
    ],
)
async def test_guess_filename_from_signature_container(content, name, expected):
    result = guess_filename_from_signature_container(content, name)
    assert result == expected


@pytest.mark.parametrize(
    'meta_title, meta_xml, expected',
    [
        # both empty
        (
            None,
            None,
            MetaData(),
        ),
        # only title
        (
            MetaData(number='123'),
            None,
            MetaData(number='123'),
        ),
        # only xml
        (
            None,
            MetaData(external_id='321'),
            MetaData(external_id='321'),
        ),
        # both (combination)
        (
            MetaData(number='123'),
            MetaData(external_id='321'),
            MetaData(number='123', external_id='321'),
        ),
        # both (priority)
        (
            MetaData(number='321'),
            MetaData(number='123'),
            MetaData(number='321'),
        ),
        # both (amount priority)
        (
            MetaData(amount=0),
            MetaData(amount=321),
            MetaData(amount=0),
        ),
    ],
)
async def test_merge_metadata_sources(meta_title, meta_xml, expected):
    actual = _merge_metadata_sources(
        meta_xml=meta_xml,
        meta_title=meta_title,
    )
    assert actual == expected


@pytest.mark.parametrize(
    'meta, expected',
    [
        pytest.param(
            MetaData(),
            MetaData(),
            id='empty',
        ),
        pytest.param(
            MetaData(owner_edrpou='123'),
            MetaData(),
            id='owner_edrpou_invalid_1',
        ),
        pytest.param(
            MetaData(owner_edrpou='430349004656'),
            MetaData(),
            id='owner_edrpou_invalid_2',
        ),
        pytest.param(
            MetaData(owner_edrpou='00000000'),
            MetaData(),
            id='owner_edrpou_invalid_zero',
        ),
        pytest.param(
            MetaData(owner_edrpou='55555555'),
            MetaData(owner_edrpou='55555555'),
            id='owner_edrpou_valid',
        ),
        pytest.param(
            MetaData(owner_edrpou='  55555555  '),
            MetaData(owner_edrpou='55555555'),
            id='owner_edrpou_valid_with_spaces',
        ),
        pytest.param(
            MetaData(owner_edrpou=''),
            MetaData(owner_edrpou=None),
            id='owner_edrpou_ignore_empty',
        ),
        pytest.param(
            MetaData(recipient_edrpou=''),
            MetaData(recipient_edrpou=None),
            id='recipient_edrpou_ignore_empty',
        ),
        pytest.param(
            MetaData(recipient_edrpou='430349004656'),
            MetaData(recipient_edrpou=None),
            id='recipient_edrpou_invalid',
        ),
        pytest.param(
            MetaData(recipient_edrpou='77777777'),
            MetaData(recipient_edrpou='77777777'),
            id='recipient_edrpou_valid',
        ),
        pytest.param(
            MetaData(recipient_edrpou='77777777'),
            MetaData(recipient_edrpou='77777777'),
            id='recipient_edrpou_valid',
        ),
        pytest.param(
            MetaData(recipient_emails=None),
            MetaData(recipient_emails=None),
            id='recipient_emails_none',
        ),
        pytest.param(
            MetaData(recipient_emails=[]),
            MetaData(recipient_emails=None),
            id='recipient_emails_empty',
        ),
        pytest.param(
            MetaData(recipient_emails=['']),
            MetaData(recipient_emails=None),
            id='recipient_emails_empty_str',
        ),
        pytest.param(
            MetaData(recipient_emails=['<EMAIL>', '<EMAIL>']),
            MetaData(recipient_emails=['<EMAIL>', '<EMAIL>']),
            id='recipient_emails_valid',
        ),
        pytest.param(
            MetaData(recipient_emails=['<EMAIL>', 'not_email']),
            MetaData(recipient_emails=['<EMAIL>']),
            id='recipient_emails_mixed',
        ),
        pytest.param(
            MetaData(vendor_id=''),
            MetaData(vendor_id=None),
            id='vendor_id_ignore_empty',
        ),
        pytest.param(
            MetaData(amount=0),
            MetaData(amount=0),
            id='amount_preserve_zero',
        ),
    ],
)
async def test_soft_correct_metadata(meta: MetaData, expected: MetaData):
    actual = _soft_correct_metadata(meta)
    assert actual == expected
