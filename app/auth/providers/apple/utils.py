from dataclasses import dataclass

from aiohttp import web

from api.errors import AppleAuthorisationError, Code
from app.auth.db import select_base_user, update_user
from app.auth.providers.apple.client import apple_auth
from app.auth.providers.apple.validators import (
    AppleRegistrationValidator,
    validate_apple_auth,
)
from app.auth.types import BaseUser, UpdateBaseUserDict
from app.i18n import _
from app.lib.validators import validate, validate_json_request
from app.registration.enums import RegistrationMethod, RegistrationSource
from app.registration.types import AppleRegistrationCtx
from app.registration.utils import create_user_on_registration, get_dealer_referrer_role_id
from app.services import services


@dataclass
class AppleAuthCtx:
    user: BaseUser
    challenge_2fa: bool
    registration_ctx: AppleRegistrationCtx | None


async def get_user_with_apple(request: web.Request) -> AppleAuthCtx:
    """
    Handle login/registration by Apple.

    More information:
    https://developer.apple.com/documentation/sign_in_with_apple/sign_in_with_apple_js/configuring_your_webpage_for_sign_in_with_apple

    - id_token - jwt token with user info (https://developer.apple.com/documentation/sign_in_with_apple/sign_in_with_apple_rest_api/authenticating_users_with_sign_in_with_apple#3383773)
      Is not used. We get user info when getting access token using code.
      Because using code is more secure than using id_token.
    - code - code for getting access token
    - user - user info (optional, apple returns it only once for new user)
      {"name": { "firstName": string, "lastName": string }, "email": string}

    Apple doesn't return regular access token, but instead returns code for getting access token.

    Get user for given Apple auth data.
    If a user doesn't exist, create a new one.
    Expect: AppleAuthValidator and optional AppleRegistrationValidator
    """
    raw_data = await validate_json_request(request)
    auth = validate_apple_auth(data=raw_data)

    id_token = await apple_auth.get_access_token(code=auth.code)

    apple_user_id = id_token.sub
    email_verified = id_token.email_verified

    email = id_token.email
    if not email:
        raise AppleAuthorisationError(Code.oauth_without_email)

    ask_second_factor = True
    if auth.source == RegistrationSource.after_invite:
        if not auth.invite_email or auth.invite_email.lower() != email.lower():
            raise AppleAuthorisationError(code=Code.invalid_email_provided)
        ask_second_factor = False

    async with services.db.acquire() as conn:
        # CASE 1: User with given email doesn't exist,
        # but there is already user with same apple_id
        # https://vchasno-group.atlassian.net/browse/DOC-7381
        # Usually it happens when user attemts to hide his email
        # by using the same device and Apple Hide my Email feature.
        # https://support.apple.com/en-gb/105078
        user_with_matching_apple_id = await select_base_user(conn=conn, apple_id=apple_user_id)
        if user_with_matching_apple_id:
            return AppleAuthCtx(
                user=user_with_matching_apple_id,
                challenge_2fa=ask_second_factor,
                registration_ctx=None,
            )

        user = await select_base_user(conn, email=email)

        # CASE 2: User exists and has apple_id
        # Do nothing
        if user and user.apple_id:
            return AppleAuthCtx(
                user=user,
                challenge_2fa=ask_second_factor,
                registration_ctx=None,
            )

        # CASE 3: User with email exists and doesn't have apple_id
        # Try to connect existing user with apple_id
        if user and not user.apple_id:
            # can't connect user with apple_id if email is not verified
            if not email_verified:
                raise AppleAuthorisationError(
                    Code.invalid_request,
                    reason=_('Email from Apple ID is not verified'),
                )

            # Add Apple ID to user profile
            update_data: UpdateBaseUserDict = {'apple_id': apple_user_id}
            if auth.source == RegistrationSource.after_invite.value:
                update_data['registration_method'] = RegistrationMethod.apple

            await update_user(
                conn=conn,
                user_id=user.id,
                data=update_data,
            )
            return AppleAuthCtx(
                user=user,
                challenge_2fa=ask_second_factor,
                registration_ctx=None,
            )

        # CASE 4: User doesn't exist
        # Create a new user
        valid_data = validate(AppleRegistrationValidator, raw_data)

        ctx = AppleRegistrationCtx(
            trial_auto_enable=valid_data['trial_auto_enable'],
            promo_code=valid_data['promo_code'],
            referrer=valid_data['referrer'],
            redirect_url=valid_data['redirect'],
            source=RegistrationSource(valid_data['source']) if valid_data['source'] else None,
            registration_method=valid_data['source'],
            email=email,
            email_verified=email_verified,
            first_name=auth.user.name.first_name,
            last_name=auth.user.name.last_name,
        )

        # Get the ID of the dealer who referred the user
        referrer_id = await get_dealer_referrer_role_id(
            conn=conn,
            referrer_role_id=ctx.referrer,
        )

        user = await create_user_on_registration(
            conn=conn,
            email=email,
            password=None,
            phone=None,
            promo_code=ctx.promo_code,
            first_name=ctx.first_name,
            second_name=None,
            last_name=ctx.last_name,
            trial_auto_enable=ctx.trial_auto_enable,
            pending_referrer_role_id=referrer_id,
            source=ctx.source,
            # we trust apple with email verification process
            is_email_confirmed=ctx.email_verified,
            is_registration_completed=False,
            apple_id=apple_user_id,
            registration_method=RegistrationMethod.apple,
        )
        return AppleAuthCtx(
            user=user,
            challenge_2fa=False,
            registration_ctx=ctx,
        )
