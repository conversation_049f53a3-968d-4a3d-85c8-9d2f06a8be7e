from api.errors import (
    AccessDenied,
)
from app.auth.db import select_company_config
from app.auth.schemas import AntivirusSettings
from app.document_antivirus.db import (
    select_document_antivirus_check,
    select_document_antivirus_checks,
    select_draft_antivirus_check,
)
from app.document_antivirus.enums import (
    AntivirusCheckStatus,
    AntivirusProvider,
)
from app.i18n import _
from app.lib.database import DBConnection
from app.lib.types import (
    StrList,
)

CLOUD_STORAGE_SEC_CLEAN_STATUS = {'Clean', 'InfectedAllowed'}
CLOUD_STORAGE_SEC_INFECTED_STATUS = {'Infected', 'Error', 'Unscannable'}

ACCESS_DENIED_MESSAGE = _(
    'Доступ до документа забороненно адміністратором. Документ не перевірено або містить вірус.'
)

ALLOW_DOWNLOAD_STATUSES = {AntivirusCheckStatus.encrypted, AntivirusCheckStatus.clean}


async def validate_antivirus_check_download_document(
    conn: DBConnection,
    document_id: str,
    company_id: str,
    document_version_id: str | None = None,
) -> None:
    if not company_id:
        return

    row = await select_document_antivirus_check(
        conn=conn, document_id=document_id, document_version_id=document_version_id
    )

    if not row or row.status in ALLOW_DOWNLOAD_STATUSES:
        # document is uploaded before antivirus has been enabled
        # or
        # there is no settings for such files
        return

    config = await select_company_config(conn, company_id=company_id)
    if not _is_document_download_allowed(row.status, config.antivirus_settings):
        raise AccessDenied(reason=ACCESS_DENIED_MESSAGE)


async def validate_antivirus_check_download_documents(
    conn: DBConnection, documents_ids: StrList, company_id: str
) -> None:
    rows = await select_document_antivirus_checks(
        conn=conn,
        documents_ids=documents_ids,
        # TODO: when new provider will be added need to think
        #  what provider has higher priority
        provider=AntivirusProvider.eset,
    )
    if not rows or all(row.status in ALLOW_DOWNLOAD_STATUSES for row in rows):
        # there is no settings for such files
        return

    config = await select_company_config(conn, company_id=company_id)
    for row in rows:
        if not _is_document_download_allowed(row.status, config.antivirus_settings):
            raise AccessDenied(reason=ACCESS_DENIED_MESSAGE)


async def validate_antivirus_check_download_draft(
    *,
    conn: DBConnection,
    company_id: str | None,
    draft_id: str,
) -> None:
    if not company_id:
        return

    row = await select_draft_antivirus_check(
        conn=conn,
        draft_id=draft_id,
    )

    if not row or row.status in ALLOW_DOWNLOAD_STATUSES:
        # document is uploaded before antivirus has been enabled
        # or
        # there is no settings for such files
        return

    config = await select_company_config(conn, company_id=company_id)
    if not _is_document_download_allowed(row.status, config.antivirus_settings):
        raise AccessDenied(reason=ACCESS_DENIED_MESSAGE)


def _is_document_download_allowed(
    check_status: AntivirusCheckStatus, settings: AntivirusSettings
) -> bool:
    is_clean = check_status == AntivirusCheckStatus.clean
    is_infected_allowed = (
        check_status == AntivirusCheckStatus.infected and settings.is_download_infected_enabled
    )
    is_pending_allowed = (
        check_status == AntivirusCheckStatus.checking and settings.is_download_pending_enabled
    )

    return is_clean or is_infected_allowed or is_pending_allowed
