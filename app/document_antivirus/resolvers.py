from collections import defaultdict

from hiku.engine import (
    Context,
    pass_context,
)

from api.graph.constants import DB_ENGINE_KEY
from app.document_antivirus.db import select_document_antivirus_checks
from app.lib.types import StrList


@pass_context
async def resolve_antivirus_checks_for_document(ctx: Context, ids: StrList) -> list[list[str]]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        data = await select_document_antivirus_checks(conn, documents_ids=ids)

    check_mapping: defaultdict[str, StrList] = defaultdict(list)
    for antivirus_check in data:
        if antivirus_check.id_:
            check_mapping[antivirus_check.document_id].append(antivirus_check.id_)

    return [check_mapping.get(doc_id, []) for doc_id in ids]


@pass_context
async def resolve_antivirus_checks_for_document_version(
    ctx: Context, ids: StrList
) -> list[list[str]]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        data = await select_document_antivirus_checks(conn, versions_ids=ids)

    check_mapping: defaultdict[str, StrList] = defaultdict(list)
    for antivirus_check in data:
        if antivirus_check.document_version_id and antivirus_check.id_:
            check_mapping[antivirus_check.document_version_id].append(antivirus_check.id_)

    return [check_mapping.get(version_id, []) for version_id in ids]
