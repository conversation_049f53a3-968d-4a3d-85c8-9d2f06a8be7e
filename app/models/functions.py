"""
Track custom user defined functions for the database (PostgreSQL).

Be aware that you need manually to create these functions in the database using alembic migrations.

Pay attention to use functions via "sa.func.<function_name>" you need to import at least once
GenericFunction class to register it in the global SQLAlchemy registry. Check documentation:

https://docs.sqlalchemy.org/en/13/core/functions.html#sqlalchemy.sql.functions.GenericFunction
"""

from __future__ import annotations

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.sql.functions import GenericFunction


class SplitCommaSeparatedEmailFunc(GenericFunction):
    """
    Convert a comma-separated string of emails to an array of lowercase emails for
    case-insensitive search.

    Created for "documents.email_recipient" column
    """

    type = postgresql.ARRAY(sa.TEXT)
    indetifier = 'app_split_comma_separated_emails'

    sql = """
        CREATE OR REPLACE FUNCTION app_split_comma_separated_emails(input_string TEXT)
        RETURNS TEXT[] LANGUAGE SQL IMMUTABLE AS $$
          SELECT ARRAY(
            SELECT LOWER(TRIM(element))
            FROM unnest(string_to_array(input_string, ',')) AS element
          );
        $$;
    """


class LowercaseEmailsArrayFunc(GenericFunction):
    """
    Convert an array of emails to an array of lowercase emails for case-insensitive search.

    Created for "document_recipients.emails" column
    """

    type = postgresql.ARRAY(sa.TEXT)
    indetifier = 'app_lowercase_emails_array'

    sql = """
        CREATE OR REPLACE FUNCTION app_lowercase_emails_array(input_array TEXT[])
        RETURNS TEXT[] LANGUAGE SQL IMMUTABLE AS $$
          SELECT ARRAY(
            SELECT LOWER(element)
            FROM unnest(input_array) AS element
          );
        $$;
    """


# Shortcuts for better type hinting, you can use "sa.func.app_split_comma_separated_emails" or
# "split_comma_separated_emails" interchangeably
split_comma_separated_emails = sa.func.app_split_comma_separated_emails
lowercase_emails_array = sa.func.app_lowercase_emails_array
