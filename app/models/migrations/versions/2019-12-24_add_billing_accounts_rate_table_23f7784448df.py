"""Add billing_accounts.rate table

Revision ID: 23f7784448df
Revises: 2cd97976c6e7
Create Date: 2019-12-24 16:20:13.026554


Author: <PERSON><PERSON><PERSON><PERSON>
"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
from app.billing.enums import AccountRate
from app.models.types import SoftEnum

revision = '23f7784448df'
down_revision = '2cd97976c6e7'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'billing_accounts',
        sa.Column('rate', SoftEnum(AccountRate), server_default='old', nullable=False),
    )


def downgrade():
    op.drop_column('billing_accounts', 'rate')
