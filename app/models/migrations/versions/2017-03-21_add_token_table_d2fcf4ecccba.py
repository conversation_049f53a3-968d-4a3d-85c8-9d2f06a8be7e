"""Add token table.

Revision ID: d2fcf4ecccba
Revises: bbbbcba984db
Create Date: 2017-03-21 11:33:38.419983


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd2fcf4ecccba'
down_revision = 'bbbbcba984db'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tokens',
    sa.Column('token', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('role_id', postgresql.UUID(), nullable=False),
        sa.Column('vendor', sa.String(length=32), nullable=True),
        sa.Column('date_created', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('token')
    )
    op.create_index(op.f('ix_tokens_vendor'), 'tokens', ['vendor'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tokens_vendor'), table_name='tokens')
    op.drop_table('tokens')
    # ### end Alembic commands ###
