"""drop-onbording-archive

Revision ID: 09a8666f8e87
Revises: fb69ad96096d
Create Date: 2025-05-06 13:28:23.760196

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '09a8666f8e87'
down_revision = 'fb69ad96096d'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column('user_onboarding', 'has_seen_folders_creation_in_sidebar')
    op.drop_column('user_onboarding', 'has_seen_folders_creation_in_archive')


def downgrade():
    op.add_column(
        'user_onboarding',
        sa.Column(
            'has_seen_folders_creation_in_archive',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            nullable=False,
        ),
    )
    op.add_column(
        'user_onboarding',
        sa.Column(
            'has_seen_folders_creation_in_sidebar',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            nullable=False,
        ),
    )
