"""Create pending notifications table.

Revision ID: 5663f83edb23
Revises: e75a3ff36ce5
Create Date: 2017-09-27 08:53:37.642928


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5663f83edb23'
down_revision = 'e75a3ff36ce5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pending_notifications',
    sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('created_by', postgresql.UUID(), nullable=False),
        sa.Column('document_id', sa.String(length=64), nullable=False),
        sa.Column('group', postgresql.ENUM('inbox', 'comment', 'reject', name='pendingnotificationgroup'), nullable=False),
        sa.Column('date_created', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('date_sent', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['roles.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('pending_notifications')
    # ### end Alembic commands ###
