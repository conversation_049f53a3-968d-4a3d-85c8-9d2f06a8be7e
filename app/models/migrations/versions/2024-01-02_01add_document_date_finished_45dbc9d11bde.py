"""add_document_date_finished

Revision ID: 45dbc9d11bde
Revises: fbf2ef34994a
Create Date: 2024-01-02 14:00:58.769047

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '45dbc9d11bde'
down_revision = 'fbf2ef34994a'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'documents', sa.Column('date_finished', sa.DateTime(timezone=True), nullable=True)
    )


def downgrade():
    op.drop_column('documents', 'date_finished')
