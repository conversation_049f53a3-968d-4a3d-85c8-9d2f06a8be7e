"""Add user_onboarding_table

Revision ID: c06b5a375f95
Revises: 984dc235aa94
Create Date: 2023-02-15 14:34:30.012624

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c06b5a375f95'
down_revision = '984dc235aa94'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'user_onboarding',
        sa.<PERSON>umn('user_id', sa.String(length=64), nullable=True),
        sa.<PERSON>umn(
            'has_checked_companies',
            sa.<PERSON>(),
            server_default='0',
            nullable=False,
        ),
        sa.Column(
            'has_invited_recipient',
            sa.<PERSON>(),
            server_default='0',
            nullable=False,
        ),
        sa.Column(
            'has_invited_coworker',
            sa.<PERSON>(),
            server_default='0',
            nullable=False,
        ),
        sa.Column(
            'has_uploaded_document',
            sa.<PERSON>(),
            server_default='0',
            nullable=False,
        ),
        sa.<PERSON>umn('is_skipped', sa.<PERSON>(), server_default='0', nullable=False),
        sa.<PERSON>eyConstraint(('user_id',), ['users.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('user_id'),
    )
    # Migrate data from user_meta to user_onboarding
    op.execute(
        """
        INSERT INTO user_onboarding (
            user_id,
            has_checked_companies,
            has_invited_recipient,
            has_invited_coworker,
            has_uploaded_document,
            is_skipped
        )
        SELECT um.user_id,
               um.has_checked_companies,
               um.has_invited_recipient,
               um.has_invited_coworker,
               um.has_uploaded_document,
               um.is_onboarding_skipped
        FROM user_meta um
        WHERE um.has_checked_companies = true
           OR um.has_invited_recipient = true
           OR um.has_invited_coworker = true
           OR um.has_uploaded_document = true
           OR um.is_onboarding_skipped = true;
        """
    )
    op.drop_column('user_meta', 'has_uploaded_document')
    op.drop_column('user_meta', 'has_checked_companies')
    op.drop_column('user_meta', 'has_invited_recipient')
    op.drop_column('user_meta', 'is_onboarding_skipped')
    op.drop_column('user_meta', 'has_invited_coworker')

    # Cleanup user_meta table
    op.execute(
        """
        DELETE
        FROM user_meta
        WHERE invalid_password_count != 0;
        """
    )


def downgrade():
    op.add_column(
        'user_meta',
        sa.Column(
            'has_invited_coworker',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        'user_meta',
        sa.Column(
            'is_onboarding_skipped',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        'user_meta',
        sa.Column(
            'has_invited_recipient',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        'user_meta',
        sa.Column(
            'has_checked_companies',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        'user_meta',
        sa.Column(
            'has_uploaded_document',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.drop_table('user_onboarding')
