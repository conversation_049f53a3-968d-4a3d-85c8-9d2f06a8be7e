"""add_mobile_notifications_table

Revision ID: 3dd05b4b6e26
Revises: da42e3070ef3
Create Date: 2024-10-03 23:16:42.390417

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from app.models.types import SoftEnum
from app.mobile.notifications.types import MobileNotificationStatus

# revision identifiers, used by Alembic.
revision = '3dd05b4b6e26'
down_revision = '74f58d92d63b'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'mobile_notifications',
        sa.Column(
            'id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False
        ),
        sa.Column('role_id', postgresql.UUID(), nullable=True),
        sa.Column('title', sa.Text(), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('status', SoftEnum(MobileNotificationStatus), nullable=False),
        sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
    )


def downgrade():
    op.drop_table('mobile_notifications')
