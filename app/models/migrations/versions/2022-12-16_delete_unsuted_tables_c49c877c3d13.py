"""Delete unsuted tables

Revision ID: c49c877c3d13
Revises: 663b3388d8a3
Create Date: 2022-12-16 18:21:18.630574

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c49c877c3d13'
down_revision = '663b3388d8a3'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_table('call_responses')
    op.drop_table('callback_phones')
    op.drop_table('call_requests')
    op.drop_table('conversions')


def downgrade():
    op.create_table(
        'conversions',
        sa.Column(
            'id',
            sa.VARCHAR(length=64),
            server_default=sa.text('uuid_generate_v4()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column('flow', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
        sa.Column('step', sa.SMALLINT(), autoincrement=False, nullable=False),
        sa.Column(
            'data',
            postgresql.JSON(astext_type=sa.Text()),
            server_default=sa.text("'[]'::json"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'date_created',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'date_updated',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column('key', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
        sa.PrimaryKeyConstraint('id', name='conversions_pkey'),
        sa.UniqueConstraint('flow', 'step', 'key', name='uix_flow_step_key'),
    )
    op.create_table(
        'call_requests',
        sa.Column(
            'id',
            sa.VARCHAR(length=64),
            server_default=sa.text('uuid_generate_v4()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'category', sa.VARCHAR(length=64), autoincrement=False, nullable=True
        ),
        sa.Column('edrpou', sa.VARCHAR(length=64), autoincrement=False, nullable=True),
        sa.Column('phone', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
        sa.Column(
            'date_created',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.PrimaryKeyConstraint('id', name='call_requests_pkey'),
    )
    op.create_table(
        'callback_phones',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column('phone', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
        sa.Column(
            'is_processed',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'date_created',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.PrimaryKeyConstraint('id', name='callback_phones_pkey'),
    )
    op.create_table(
        'call_responses',
        sa.Column(
            'id',
            sa.VARCHAR(length=64),
            server_default=sa.text('uuid_generate_v4()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'call_id', sa.VARCHAR(length=64), autoincrement=False, nullable=False
        ),
        sa.Column(
            'call_status', sa.VARCHAR(length=32), autoincrement=False, nullable=True
        ),
        sa.Column('phone', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
        sa.Column(
            'results',
            postgresql.JSON(astext_type=sa.Text()),
            server_default=sa.text("'[]'::json"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            'date_created',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.PrimaryKeyConstraint('id', name='call_responses_pkey'),
    )
