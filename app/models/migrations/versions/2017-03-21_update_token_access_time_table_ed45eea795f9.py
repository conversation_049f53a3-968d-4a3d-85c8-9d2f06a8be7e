"""Update token access time table.

Revision ID: ed45eea795f9
Revises: 80b8ebd22246
Create Date: 2017-03-21 17:19:14.228601


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ed45eea795f9'
down_revision = '80b8ebd22246'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'token_access_times', ['token'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'token_access_times', type_='unique')
    # ### end Alembic commands ###
