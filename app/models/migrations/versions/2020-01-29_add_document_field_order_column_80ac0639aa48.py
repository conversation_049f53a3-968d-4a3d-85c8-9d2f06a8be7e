"""add document field order column

Revision ID: 80ac0639aa48
Revises: 207827b5e2f1
Create Date: 2020-01-29 15:55:10.095240


Author: <PERSON><PERSON>kiya<PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '80ac0639aa48'
down_revision = '207827b5e2f1'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('documents_fields', sa.Column('order', sa.Integer(), nullable=True))


def downgrade():
    op.drop_column('documents_fields', 'order')
