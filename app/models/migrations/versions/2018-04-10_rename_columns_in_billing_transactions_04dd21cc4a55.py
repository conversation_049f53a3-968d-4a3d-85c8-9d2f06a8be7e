"""Rename columns in billing_transactions, add new account type.

Revision ID: 04dd21cc4a55
Revises: 173114a8122d
Create Date: 2018-04-10 10:20:11.112878


Author: lequan
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.migrations.utils import modify_db_enum


# revision identifiers, used by Alembic.
revision = '04dd21cc4a55'
down_revision = '173114a8122d'
branch_labels = None
depends_on = None


def upgrade():
    modify_db_enum(
        op,
        'billing_accounts',
        'type',
        'accounttype',
        "('service_credit_external', 'service_credit_bonus', "
        "'service_debit_external', 'service_debit_bonus', 'service_debt', "
        "'client_debit', 'client_bonus', 'client_bonus_custom', "
        "'client_credit')"
    )

    op.add_column('billing_transactions', sa.Column('from_', postgresql.UUID(), nullable=False))
    op.add_column('billing_transactions', sa.Column('to_', postgresql.UUID(), nullable=False))
    op.drop_constraint('billing_transactions_from_fkey', 'billing_transactions', type_='foreignkey')
    op.drop_constraint('billing_transactions_to_fkey', 'billing_transactions', type_='foreignkey')
    op.create_foreign_key('billing_transactions_account_to_id_fkey', 'billing_transactions', 'billing_accounts', ['to_'], ['id'], ondelete='NO ACTION')
    op.create_foreign_key('billing_transactions_account_from_id_fkey', 'billing_transactions', 'billing_accounts', ['from_'], ['id'], ondelete='NO ACTION')
    op.drop_column('billing_transactions', 'from')
    op.drop_column('billing_transactions', 'to')


def downgrade():
    modify_db_enum(
        op,
        'billing_accounts',
        'type',
        'accounttype',
        "('service_credit_external', 'service_credit_bonus', "
        "'service_debit_external', 'service_debit_bonus', 'service_debt', "
        "'client_debit', 'client_bonus', 'client_credit')"
    )

    op.add_column('billing_transactions', sa.Column('to', postgresql.UUID(), autoincrement=False, nullable=False))
    op.add_column('billing_transactions', sa.Column('from', postgresql.UUID(), autoincrement=False, nullable=False))
    op.drop_constraint('billing_transactions_account_to_id_fkey', 'billing_transactions', type_='foreignkey')
    op.drop_constraint('billing_transactions_account_from_id_fkey', 'billing_transactions', type_='foreignkey')
    op.create_foreign_key('billing_transactions_to_fkey', 'billing_transactions', 'billing_accounts', ['to'], ['id'])
    op.create_foreign_key('billing_transactions_from_fkey', 'billing_transactions', 'billing_accounts', ['from'], ['id'])
    op.drop_column('billing_transactions', 'to_')
    op.drop_column('billing_transactions', 'from_')
