"""add groups

Revision ID: 5b8cd726ccb7
Revises: 3cf7dae31d51
Create Date: 2023-05-23 08:47:24.539394

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5b8cd726ccb7'
down_revision = 'b42e07c4ec86'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'groups',
        sa.Column(
            'id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False
        ),
        sa.Column('name', sa.Text()),
        sa.Column('is_deleted', sa.<PERSON>(), server_default='0', nullable=False),
        sa.Column('created_by', postgresql.UUID(), nullable=True),
        sa.Column('company_id', postgresql.UUID(), nullable=False),
        sa.Column('deleted_by', postgresql.UUID(), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'date_updated',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('date_deleted', sa.DateTime(timezone=True), nullable=True),
        sa.CheckConstraint(
            '(deleted_by IS NULL AND date_deleted IS NULL) OR (deleted_by IS NOT NULL AND date_deleted IS NOT NULL)',
            name='check_groups_deleted_by_date_deleted',
        ),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(['created_by'], ['roles.id'], ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(['deleted_by'], ['roles.id'], ondelete='NO ACTION'),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(op.f('ix_groups_company_id'), 'groups', ['company_id'], unique=False)
    op.create_index(
        'uix_groups_company_id_name',
        'groups',
        ['company_id', 'name'],
        unique=True,
        postgresql_where=sa.text('deleted_by IS NULL'),
    )
    op.create_table(
        'group_members',
        sa.Column(
            'id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False
        ),
        sa.Column('group_id', postgresql.UUID(), nullable=False),
        sa.Column('role_id', postgresql.UUID(), nullable=False),
        sa.Column('created_by', postgresql.UUID(), nullable=True),
        sa.Column('deleted_by', postgresql.UUID(), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'date_updated',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('date_deleted', sa.DateTime(timezone=True), nullable=True),
        sa.CheckConstraint(
            '(deleted_by IS NULL AND date_deleted IS NULL) OR (deleted_by IS NOT NULL AND date_deleted IS NOT NULL)',
            name='check_group_members_deleted_by_date_deleted',
        ),
        sa.ForeignKeyConstraint(['created_by'], ['roles.id'], ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(['deleted_by'], ['roles.id'], ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(['group_id'], ['groups.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(op.f('ix_group_members_group_id'), 'group_members', ['group_id'], unique=False)
    op.create_index(op.f('ix_group_members_role_id'), 'group_members', ['role_id'], unique=False)
    op.create_index(
        'uix_group_members_group_id_role_id',
        'group_members',
        ['group_id', 'role_id'],
        unique=True,
        postgresql_where=sa.text('deleted_by IS NULL'),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        'uix_group_members_group_id_role_id',
        table_name='group_members',
        postgresql_where=sa.text('deleted_by IS NULL'),
    )
    op.drop_index(op.f('ix_group_members_role_id'), table_name='group_members')
    op.drop_index(op.f('ix_group_members_group_id'), table_name='group_members')
    op.drop_table('group_members')
    op.drop_index(
        'uix_groups_company_id_name',
        table_name='groups',
        postgresql_where=sa.text('deleted_by IS NULL'),
    )
    op.drop_index(op.f('ix_groups_company_id'), table_name='groups')
    op.drop_table('groups')
    # ### end Alembic commands ###
