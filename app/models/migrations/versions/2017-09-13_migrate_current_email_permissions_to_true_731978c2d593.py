"""Migrate current email permissions to true.

Revision ID: 731978c2d593
Revises: 8a455649c1a4
Create Date: 2017-09-13 14:48:03.868779


Author: <PERSON>
"""
from alembic import op


# revision identifiers, used by Alembic.
revision = '731978c2d593'
down_revision = '8a455649c1a4'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    UPDATE roles
    SET
      can_receive_inbox = TRUE,
      can_receive_comments = TRUE,
      can_receive_rejects = TRUE,
      can_receive_reminders = TRUE;
    """)


def downgrade():
    op.execute("""
    UPDATE roles
    SET
      can_receive_inbox = FALSE,
      can_receive_comments = FALSE,
      can_receive_rejects = FALSE,
      can_receive_reminders = FALSE;
    """)
