"""Add role invited columns

Revision ID: 6f8bc6aaa0f4
Revises: b5693726246d
Create Date: 2024-12-03 16:19:43.298342

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6f8bc6aaa0f4'
down_revision = 'b5693726246d'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('roles', sa.Column('invited_by', postgresql.UUID(), nullable=True))
    op.add_column('roles', sa.Column('date_invited', sa.DateTime(timezone=True), nullable=True))
    op.create_foreign_key(
        'roles_invited_by_fkey',
        'roles',
        'roles',
        ['invited_by'],
        ['id'],
        ondelete='NO ACTION',
    )


def downgrade():
    op.drop_constraint(
        'roles_invited_by_fkey',
        'roles',
        type_='foreignkey',
    )
    op.drop_column('roles', 'date_invited')
    op.drop_column('roles', 'invited_by')
