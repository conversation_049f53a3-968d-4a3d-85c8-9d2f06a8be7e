"""Migrate stamp_is_legal flag.

Revision ID: 856f3559077d
Revises: c994fbd0b8a8
Create Date: 2017-06-22 17:55:13.681416


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '856f3559077d'
down_revision = 'c994fbd0b8a8'
branch_labels = None
depends_on = None

def upgrade():
    op.execute("""
UPDATE signatures
SET stamp_is_legal = TRUE
WHERE stamp IS NOT NULL;
    """)


def downgrade():
    pass
