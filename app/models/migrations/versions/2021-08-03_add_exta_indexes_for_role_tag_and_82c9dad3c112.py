"""add exta indexes for role_tag and document_tags tables

Revision ID: 82c9dad3c112
Revises: 2d6e135f78d3
Create Date: 2021-08-03 14:34:40.412625


Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '82c9dad3c112'
down_revision = '2d6e135f78d3'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT')  # hack for skipping transaction
    op.create_index(
        'idx_document_tags_tag_id',
        'document_tags',
        ['tag_id'],
        unique=False,
        postgresql_concurrently=True,
    )
    op.create_index(
        'idx_role_tag_role_id',
        'role_tag',
        ['role_id'],
        unique=False,
        postgresql_concurrently=True,
    )
    op.create_index(
        'idx_role_tag_tag_id', 'role_tag', ['tag_id'], unique=False, postgresql_concurrently=True
    )


def downgrade():
    op.drop_index('idx_document_tags_tag_id', table_name='role_tag')
    op.drop_index('idx_role_tag_role_id', table_name='role_tag')
    op.drop_index('idx_role_tag_tag_id', table_name='document_tags')
