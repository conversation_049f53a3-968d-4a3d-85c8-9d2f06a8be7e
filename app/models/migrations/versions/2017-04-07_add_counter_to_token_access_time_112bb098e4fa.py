"""Add counter to token access time

Revision ID: 112bb098e4fa
Revises: 8ea2341c0691
Create Date: 2017-04-07 16:32:01.855742


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '112bb098e4fa'
down_revision = '8ea2341c0691'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'token_access_times',
        sa.Column(
            'counter',
            sa.Integer(),
            server_default=sa.text('0'),
            nullable=False,
        ),
    )


def downgrade():
    op.drop_column('token_access_times', 'counter')
