"""del_listings_index

Revision ID: e60c80c5e82a
Revises: cd43dd9b43ad
Create Date: 2024-01-12 16:50:32.820941

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e60c80c5e82a'
down_revision = 'cd43dd9b43ad'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT;')
    op.drop_index('ix_access_edrpou_document_id_date_created', table_name='access_to_doc')


def downgrade():
    op.create_index(
        'ix_access_edrpou_document_id_date_created',
        'access_to_doc',
        ['access_edrpou', 'document_id', 'date_created'],
        unique=False,
    )
