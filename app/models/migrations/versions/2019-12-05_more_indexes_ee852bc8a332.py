""" Add more indexes

Revision ID: ee852bc8a332
Revises: 1bd84bc1526a
Create Date: 2019-12-05 17:04:13.126101

Author: <PERSON>
"""

from alembic import op
import sqlalchemy as sa


revision = 'ee852bc8a332'
down_revision = '1bd84bc1526a'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT')  # hack for skipping transaction
    op.create_index(
        'ix_billing_accounts_company_id',
        'billing_accounts',
        ['company_id'],
        unique=False,
        postgresql_where=sa.text('date_deleted IS NULL'),
        postgresql_concurrently=True,
    )
    op.create_index(
        'ix_comments_document_id',
        'comments',
        ['document_id'],
        unique=False,
        postgresql_concurrently=True,
    )
    op.create_index(
        'ix_contact_persons_email',
        'contact_persons',
        ['email'],
        unique=False,
        postgresql_concurrently=True,
    )


def downgrade():
    op.drop_index('ix_contact_persons_email', table_name='contact_persons')
    op.drop_index('ix_comments_document_id', table_name='comments')
    op.drop_index('ix_billing_accounts_company_id', table_name='billing_accounts')
