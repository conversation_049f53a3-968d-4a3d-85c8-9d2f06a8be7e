"""Add trigger notificationon type

Revision ID: c433d21ed41f
Revises: df6458138792
Create Date: 2019-04-26 15:06:48.474491


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa

from app.models.types import SoftEnum
from app.trigger_notifications.enums import TriggerNotificationType

# revision identifiers, used by Alembic.
revision = 'c433d21ed41f'
down_revision = 'df6458138792'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'trigger_notifications',
        sa.Column(
            'type',
            SoftEnum(TriggerNotificationType),
            nullable=False,
            server_default=TriggerNotificationType.how_to.value,
        )
    )
    op.execute("""
        UPDATE trigger_notifications
        SET url = 'http://help.vchasno.com.ua/howtosend'
    """)


def downgrade():
    op.drop_column('trigger_notifications', 'type')
