"""rate_extension status is not null

Revision ID: 367ba2140d2a
Revises: e0e68877d13e
Create Date: 2024-09-10 10:51:30.136776

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '367ba2140d2a'
down_revision = 'e0e68877d13e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('rate_extensions', 'bill_id', existing_type=postgresql.UUID(), nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('rate_extensions', 'bill_id', existing_type=postgresql.UUID(), nullable=True)
    # ### end Alembic commands ###
