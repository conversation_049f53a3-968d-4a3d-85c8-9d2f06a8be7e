"""Add onboarding.has_seen_new_uploading

Revision ID: e9d74a2734d3
Revises: 0bba46f5cd83
Create Date: 2024-04-15 17:54:27.804501

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e9d74a2734d3'
down_revision = '0bba46f5cd83'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'user_onboarding',
        sa.Column('has_seen_new_uploading', sa.<PERSON>(), server_default='0', nullable=False),
    )


def downgrade():
    op.drop_column('user_onboarding', 'has_seen_new_uploading')
