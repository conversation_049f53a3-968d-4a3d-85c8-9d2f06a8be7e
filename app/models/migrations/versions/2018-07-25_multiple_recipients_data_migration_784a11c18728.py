""" Multiple recipients, Data migration

Revision ID: 784a11c18728
Revises: 3b7f9f78b41d
Create Date: 2018-07-25 16:11:27.529321


Author: <PERSON>
"""
from alembic import op


# revision identifiers, used by Alembic.
revision = '784a11c18728'
down_revision = '3b7f9f78b41d'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    INSERT INTO document_recipients(emails, edrpou, document_id)
    SELECT DISTINCT
        regexp_split_to_array(email_recipient, '[;, ]+') AS emails,
        edrpou_recipient as edrpou,
        id AS document_id
    FROM documents
    WHERE documents.edrpou_recipient IS NOT NULL
    ON CONFLICT DO NOTHING;
    """)


def downgrade():
    pass
