"""add_super_admin_action

Revision ID: c2cf218abf5b
Revises: 358852b4a52a
Create Date: 2020-08-28 14:06:25.366514


Author: d.havdun
"""
import citext
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


revision = 'c2cf218abf5b'
down_revision = '358852b4a52a'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'super_admin_actions',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False
        ),
        sa.Column(
            'type',
            postgresql.ENUM(
                'document_request_state', name='superadminactiontype'
            ),
            nullable=False
        ),
        sa.Column(
            'role_id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False
        ),
        sa.Column(
            'email', citext.CIText(), nullable=False
        ),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False
        ),
        sa.Column(
            'extra_details',
            postgresql.JSON(astext_type=sa.Text()),
            server_default=sa.text("'[]'"),
            nullable=False
        ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    op.drop_table('super_admin_actions')
