"""Additional signature fields.

Revision ID: 1648f6b5a221
Revises: 31d685faf699
Create Date: 2017-06-29 14:48:46.111882


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1648f6b5a221'
down_revision = '31d685faf699'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('signatures', sa.Column('key_company_fullname', sa.String(length=128), nullable=True))
    op.add_column('signatures', sa.Column('key_owner_position', sa.String(length=128), nullable=True))
    op.add_column('signatures', sa.Column('stamp_company_fullname', sa.String(length=128), nullable=True))
    op.add_column('signatures', sa.Column('stamp_owner_position', sa.String(length=128), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('signatures', 'stamp_owner_position')
    op.drop_column('signatures', 'stamp_company_fullname')
    op.drop_column('signatures', 'key_owner_position')
    op.drop_column('signatures', 'key_company_fullname')
    # ### end Alembic commands ###
