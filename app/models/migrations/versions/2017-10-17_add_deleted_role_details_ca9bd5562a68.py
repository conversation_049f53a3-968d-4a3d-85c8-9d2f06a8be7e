"""Add deleted role details.

Revision ID: ca9bd5562a68
Revises: 06ff17fdd736
Create Date: 2017-10-17 12:55:01.283918


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ca9bd5562a68'
down_revision = '06ff17fdd736'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('roles', sa.Column('date_deleted', sa.DateTime(timezone=True), nullable=True))
    op.add_column('roles', sa.Column('deleted_by', postgresql.UUID(), nullable=True))
    op.create_foreign_key('roles_deleted_by_fkey', 'roles', 'roles', ['deleted_by'], ['id'], ondelete='NO ACTION')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('roles_deleted_by_fkey', 'roles', type_='foreignkey')
    op.drop_column('roles', 'deleted_by')
    op.drop_column('roles', 'date_deleted')
    # ### end Alembic commands ###
