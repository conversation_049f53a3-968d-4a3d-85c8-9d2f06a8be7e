"""add index_role_id_in_signatures

Revision ID: 12e9584d240b
Revises: e14fa58b21f0
Create Date: 2024-04-09 15:14:19.890077

"""
from alembic import op


# revision identifiers, used by Alembic.
revision = '12e9584d240b'
down_revision = '582b08e2adcc'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT;')
    op.execute(
        """
        CREATE INDEX
        CONCURRENTLY
        IF NOT EXISTS ix_signatures_role_id
        ON signatures (role_id);
        """
    )


def downgrade():
    op.drop_index(op.f('ix_signatures_role_id'), table_name='signatures')
