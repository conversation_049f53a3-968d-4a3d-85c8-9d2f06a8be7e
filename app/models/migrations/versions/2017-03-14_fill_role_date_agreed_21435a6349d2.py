"""Fill role date agreed.

Revision ID: 21435a6349d2
Revises: f33a4d7d6740
Create Date: 2017-03-14 06:15:50.792255


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '21435a6349d2'
down_revision = 'f33a4d7d6740'
branch_labels = None
depends_on = None

def upgrade():
    op.execute("""
UPDATE roles
SET date_agreed = date_created;
    """)


def downgrade():
    pass
