"""
Add reviews settings

Revision ID: d2960b1adc63
Revises: b09f4e4621a2
Create Date: 2018-05-31 17:49:10.303134

Author: <PERSON>
"""

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

revision = 'd2960b1adc63'
down_revision = 'b09f4e4621a2'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'reviews_settings',
        sa.Column('id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('document_id', sa.String(length=64), nullable=False),
        sa.Column('company_id', postgresql.UUID(), nullable=False),
        sa.Column('is_required', sa.Boolean(), server_default='0', nullable=False),
        sa.Column('date_created', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('date_updated', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='NO ACTION'),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ondelete='NO ACTION'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('document_id', 'company_id', name='uix_reviews_settings_doc_id_comp_id')
    )


def downgrade():
    op.drop_table('reviews_settings')
