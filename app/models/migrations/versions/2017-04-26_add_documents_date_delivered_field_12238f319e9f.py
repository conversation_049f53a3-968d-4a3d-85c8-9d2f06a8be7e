"""Add documents.date_delivered field.

Revision ID: 12238f319e9f
Revises: e35cbdbb23a7
Create Date: 2017-04-26 16:43:36.723017


Author: <PERSON>
"""
import sqlalchemy as sa

from alembic import op


# revision identifiers, used by Alembic.
revision = '12238f319e9f'
down_revision = 'e35cbdbb23a7'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'documents',
        sa.Column(
            'date_delivered',
            sa.DateTime(timezone=True),
            nullable=True),
    )


def downgrade():
    op.drop_column('documents', 'date_delivered')
