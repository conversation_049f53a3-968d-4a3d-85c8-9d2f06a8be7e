"""Remove call_response.call_id FK

Revision ID: 64fc0a928dc5
Revises: 8308c65181d1
Create Date: 2017-03-28 16:49:34.856397


Author: <PERSON>
"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '64fc0a928dc5'
down_revision = '8308c65181d1'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint('fk_call_id_call_request_id', 'call_responses')


def downgrade():
    op.create_foreign_key(
        'fk_call_id_call_request_id',
        'call_responses',
        'call_requests',
        ['call_id'],
        ['id'],
        ondelete='NO ACTION')
