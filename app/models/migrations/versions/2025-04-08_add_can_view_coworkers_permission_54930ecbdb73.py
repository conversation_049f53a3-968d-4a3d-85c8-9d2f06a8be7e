"""add_can_view_coworkers_permission

Revision ID: 54930ecbdb73
Revises: 73b8b72e00b9
Create Date: 2025-04-03 11:10:55.310051

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '54930ecbdb73'
down_revision = '73b8b72e00b9'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'roles', sa.Column('can_view_coworkers', sa.<PERSON>(), server_default='1', nullable=False)
    )


def downgrade():
    op.drop_column('roles', 'can_view_coworkers')
