"""Add survey_ai_documents.role_id

Revision ID: fb69ad96096d
Revises: 1de482a92702
Create Date: 2025-05-02 11:59:23.842294

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fb69ad96096d'
down_revision = '1de482a92702'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('survey_ai_documents', sa.Column('role_id', postgresql.UUID(), nullable=True))
    op.create_foreign_key(
        'survey_ai_documents_role_id_fkey',
        'survey_ai_documents',
        'roles',
        ['role_id'],
        ['id'],
        ondelete='CASCADE',
    )


def downgrade():
    op.drop_constraint(
        'survey_ai_documents_role_id_fkey',
        'survey_ai_documents',
        type_='foreignkey',
    )
    op.drop_column('survey_ai_documents', 'role_id')
