"""Migrate invalid Delivery recipients.

Revision ID: f4082b22dab0
Revises: fd06687db969
Create Date: 2017-07-20 13:30:49.678488


Author: <PERSON>
"""
from alembic import op


# revision identifiers, used by Alembic.
revision = 'f4082b22dab0'
down_revision = 'fd06687db969'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
    UPDATE documents AS t
    SET email_recipient = c.valid_email
    FROM (VALUES
        (' <EMAIL>', 'jbel<PERSON>@ukr.net'),
        ('maryna.oleksiuk@otpbank.cоm.ua', '<EMAIL>'),
        ('sdmkran@gmaіl.com', '<EMAIL>'),
        ('yaris_eleс**************', '<EMAIL>')
    ) AS c(previous_email, valid_email)
    WHERE
      t.edrpou_owner = '********' AND
      t.email_recipient = c.previous_email;
    """)

    op.execute("""
    UPDATE contragent_users AS t
    SET email = c.valid_email
    FROM (VALUES
        (' <EMAIL>', '<EMAIL>'),
        ('maryna.oleksiuk@otpbank.cоm.ua', '<EMAIL>'),
        ('sdmkran@gmaіl.com', '<EMAIL>'),
        ('yaris_eleс**************', '<EMAIL>')
    ) AS c(previous_email, valid_email)
    WHERE t.email = c.previous_email;
    """)

    op.execute("""
    UPDATE contragent_companies AS t
    SET accountant_email = c.valid_email
    FROM (VALUES
        (' <EMAIL>', '<EMAIL>'),
        ('maryna.oleksiuk@otpbank.cоm.ua', '<EMAIL>'),
        ('sdmkran@gmaіl.com', '<EMAIL>'),
        ('yaris_eleс**************', '<EMAIL>')
    ) AS c(email, valid_email)
    WHERE
      t.company_edrpou = '********' AND
      t.accountant_email = c.email;
    """)


def downgrade():
    pass
