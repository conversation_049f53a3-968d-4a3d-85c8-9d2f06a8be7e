"""Rename pdf_name columns.

Revision ID: e8c12fbd12d0
Revises: 5bffd325d59f
Create Date: 2017-10-13 12:36:41.627493


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e8c12fbd12d0'
down_revision = '5bffd325d59f'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('s3_archive_key', sa.Text(), nullable=True))
    op.add_column('documents', sa.Column('s3_xml_to_pdf_key', sa.Text(), nullable=True))
    op.drop_column('documents', 'pdf_name')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('pdf_name', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_column('documents', 's3_xml_to_pdf_key')
    op.drop_column('documents', 's3_archive_key')
    # ### end Alembic commands ###
