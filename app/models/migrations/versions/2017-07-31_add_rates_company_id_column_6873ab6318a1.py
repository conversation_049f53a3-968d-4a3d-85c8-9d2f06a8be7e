"""Add rates.company_id column.

Revision ID: 6873ab6318a1
Revises: 75c3764f6832
Create Date: 2017-07-31 03:50:32.214226


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6873ab6318a1'
down_revision = '75c3764f6832'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rates', sa.Column('company_id', postgresql.UUID(), nullable=True))
    op.create_foreign_key(None, 'rates', 'companies', ['company_id'], ['id'], ondelete='NO ACTION')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'rates', type_='foreignkey')
    op.drop_column('rates', 'company_id')
    # ### end Alembic commands ###
