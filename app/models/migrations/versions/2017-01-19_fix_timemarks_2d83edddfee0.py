"""Fix timemarks.

Revision ID: 2d83edddfee0
Revises: 5715e5cf0b32
Create Date: 2017-01-19 16:13:51.736769


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2d83edddfee0'
down_revision = '5715e5cf0b32'
branch_labels = None
depends_on = None


def upgrade():
    # Upgrade timestamp values by create new timestamp
    op.execute("""
CREATE OR REPLACE FUNCTION fix_timestamp(value timestamptz)
RETURNS timestamptz
AS $$
    select make_timestamptz(
        EXTRACT(YEAR FROM $1)::int,
        EXTRACT(MONTH FROM $1)::int,
        EXTRACT(DAY FROM $1)::int,
        EXTRACT(HOUR FROM $1)::int,
        EXTRACT(MINUTE FROM $1)::int,
        EXTRACT(SECOND FROM $1),
        'UTC'
    );
$$
LANGUAGE SQL;

UPDATE signatures
SET key_timemark = fix_timestamp(key_timemark)
WHERE key_timemark IS NOT null;

UPDATE signatures
SET stamp_timemark = fix_timestamp(stamp_timemark)
WHERE stamp_timemark IS NOT null;
    """)


def downgrade():
    # Downgrade timestamp values by storing them as UTC
    op.execute("""
UPDATE signatures
SET key_timemark = key_timemark AT TIME ZONE 'UTC'
WHERE key_timemark IS NOT null;

UPDATE signatures
SET stamp_timemark = stamp_timemark AT TIME ZONE 'UTC'
WHERE stamp_timemark IS NOT null;
    """)
