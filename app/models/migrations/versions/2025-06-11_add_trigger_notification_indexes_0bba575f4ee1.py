"""add_trigger_notification_indexes

Revision ID: 0bba575f4ee1
Revises: 525092936f48
Create Date: 2025-06-11 09:26:08.477348

"""

from alembic import op

from app.config import is_prod_environ

# revision identifiers, used by Alembic.
revision = '0bba575f4ee1'
down_revision = '525092936f48'
branch_labels = None
depends_on = None


def upgrade():
    # on production, execute this migration manually
    if is_prod_environ():
        return

    op.execute('COMMIT;')
    op.execute(
        """
        CREATE INDEX CONCURRENTLY
        IF NOT EXISTS idx_trigger_notifications_seen_display_date
        ON trigger_notifications (display_date DESC)
        WHERE status = 'seen';
        """
    )
    op.execute(
        """
        CREATE INDEX CONCURRENTLY
        IF NOT EXISTS idx_trigger_notifications_display_date
        ON trigger_notifications (display_date DESC);
        """
    )
    # ### end Alembic commands ###


def downgrade():
    op.drop_index(
        'idx_trigger_notifications_display_date',
        table_name='trigger_notifications',
    )
    op.drop_index(
        'idx_trigger_notifications_seen_display_date',
        table_name='trigger_notifications',
    )
    # ### end Alembic commands ###
