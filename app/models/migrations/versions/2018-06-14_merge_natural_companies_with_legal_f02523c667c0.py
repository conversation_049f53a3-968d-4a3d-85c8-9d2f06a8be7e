"""Merge natural companies with legal.

Revision ID: f02523c667c0
Revises: 03eaed54e245
Create Date: 2018-06-14 14:14:51.286010

Natural and legal companies linked by edrpou, possible cases:
    1. <PERSON><PERSON><PERSON> with only legal company -> do nothing.
    2. <PERSON><PERSON><PERSON> with only natural company -> change company to legal.
    3. <PERSON><PERSON><PERSON> with both legal and natural companies -> merge companies.

    To merge companies need to resolve all linked entities:
    - roles
      Used to connect users with companies, so:
      1. User have roles in both legal and natural companies -> merge roles
      then set natural role status to deleted.
      To merge roles need to change natural role to legal one in next entities:
      access_to_doc, comments, documents, document_signers, signatures.
      2. User have role only in legal company -> do nothing.
      3. User have role only in natural company -> relink this role to legal
      company by changing ``company_id``.

    - billing_accounts
      Need to move active accounts from natural company to legal company by
      changing ``company_id`` in this accounts.

    - reviews_settings
      Need to change natural ``company_id`` to legal one.

    - contacts
      Move contacts from natural company to legal company:
      1. Edrpou exist in contacts of both legal and natural companies -> do
      nothing.
      2. Edrpou exist only in contacts of legal company -> do nothing.
      3. Edrpou exist only in contacts of natural company -> relink this
      contact to legal company by changing ``company_id``.

    - document_links
      Need to change natural ``company_id`` to legal one.

    - document_signers
      Need to change natural ``company_id`` to legal one.

    - notifications
      Skip it.

Author: lequan
"""
import os

from alembic import op
from collections import namedtuple
import sqlalchemy as sa

from app.config import is_prod_environ
from app.config import get_level
from app.lib.enums import AppLevel

# revision identifiers, used by Alembic.
revision = 'f02523c667c0'
down_revision = '03eaed54e245'
branch_labels = None
depends_on = None


Company = namedtuple('Company', 'id edrpou')


def merge_billing_accounts(conn, company, legal_company_id):
    result = conn.execute(
        sa.text(
            """
            SELECT id
            FROM billing_accounts
            WHERE company_id = :company_id AND date_deleted IS NULL
        """
        ),
        company_id=company.id,
    ).fetchall()

    if len(result) == 0:
        return

    billing_account_ids = [row.id for row in result]
    for account_id in billing_account_ids:
        conn.execute(
            sa.text(
                """
                UPDATE billing_accounts
                SET company_id = :legal_company_id
                WHERE id = :account_id
            """
            ),
            account_id=account_id,
            legal_company_id=legal_company_id,
        )


def merge_contacts(conn, company, legal_company_id):
    # Get contacts from natural company
    result = conn.execute(
        sa.text(
            """
            SELECT id, edrpou FROM contacts WHERE company_id = :company_id
        """
        ),
        company_id=company.id,
    ).fetchall()

    if len(result) == 0:
        return

    natural_contacts_map = {}
    for row in result:
        natural_contacts_map[row.edrpou] = row.id

    # Get contacts from legal company
    result = conn.execute(
        sa.text(
            """
            SELECT id, edrpou FROM contacts WHERE company_id = :company_id
        """
        ),
        company_id=legal_company_id,
    ).fetchall()
    legal_contacts_map = {}
    for row in result:
        legal_contacts_map[row.edrpou] = row.id

    for edrpou, contact_id in natural_contacts_map.items():
        if not legal_contacts_map.get(edrpou):
            # Contact only in natural companies
            conn.execute(
                sa.text(
                    """
                    UPDATE contacts
                    SET company_id = :legal_company_id
                    WHERE id = :contact_id
                """
                ),
                contact_id=contact_id,
                legal_company_id=legal_company_id,
            )


def merge_roles(conn, company, admin_role_id, legal_company_id):
    # Get roles from natural company
    result = conn.execute(
        sa.text(
            """
            SELECT r.id AS role_id, u.id AS user_id
            FROM roles r JOIN users u ON r.user_id = u.id
            WHERE r.company_id = :company_id AND r.status = 'active'
        """
        ),
        company_id=company.id,
    ).fetchall()

    if len(result) == 0:
        return

    natural_user_role_map = {}
    for row in result:
        natural_user_role_map[row.user_id] = row.role_id

    # Get roles from legal company
    result = conn.execute(
        sa.text(
            """
            SELECT r.id AS role_id, u.id AS user_id, r.status
            FROM roles r JOIN users u ON r.user_id = u.id
            WHERE r.company_id = :company_id;
        """
        ),
        company_id=legal_company_id,
    ).fetchall()
    legal_user_role_map = {}
    for row in result:
        legal_user_role_map[row.user_id] = {'role_id': row.role_id, 'status': row.status}

    for user_id, natural_role_id in natural_user_role_map.items():
        legal_role = legal_user_role_map.get(user_id)
        if legal_role:
            # User in both natural and legal companies, merge it
            legal_role_id = legal_role['role_id']
            conn.execute(
                sa.text(
                    """
                    UPDATE access_to_doc
                    SET role_id = :legal_role_id
                    WHERE role_id = :natural_role_id
                """
                ),
                natural_role_id=natural_role_id,
                legal_role_id=legal_role_id,
            )
            conn.execute(
                sa.text(
                    """
                    UPDATE comments
                    SET role_id = :legal_role_id
                    WHERE role_id = :natural_role_id
                """
                ),
                natural_role_id=natural_role_id,
                legal_role_id=legal_role_id,
            )
            conn.execute(
                sa.text(
                    """
                    UPDATE documents
                    SET role_id = :legal_role_id, uploaded_by = :legal_role_id
                    WHERE role_id = :natural_role_id
                """
                ),
                natural_role_id=natural_role_id,
                legal_role_id=legal_role_id,
            )
            conn.execute(
                sa.text(
                    """
                    UPDATE document_signers
                    SET role_id = :legal_role_id
                    WHERE role_id = :natural_role_id
                """
                ),
                natural_role_id=natural_role_id,
                legal_role_id=legal_role_id,
            )
            conn.execute(
                sa.text(
                    """
                    UPDATE signatures
                    SET role_id = :legal_role_id
                    WHERE role_id = :natural_role_id
                """
                ),
                natural_role_id=natural_role_id,
                legal_role_id=legal_role_id,
            )
            # Set natural role as deleted
            conn.execute(
                sa.text(
                    """
                    UPDATE roles
                    SET status = 'deleted', deleted_by = :deleted_by
                    WHERE id = :role_id
                """
                ),
                role_id=natural_role_id,
                deleted_by=admin_role_id,
            )
            # Need to activate legal role if it is inactive
            if legal_role['status'] != 'active':
                conn.execute(
                    sa.text(
                        """
                        UPDATE roles
                        SET status = 'active', deleted_by = NULL
                        WHERE id = :role_id
                    """
                    ),
                    role_id=legal_role_id,
                )
        else:
            # User only in natural company, relink it to legal company
            conn.execute(
                sa.text(
                    """
                    UPDATE roles
                    SET company_id = :company_id
                    WHERE id = :role_id
                """
                ),
                role_id=natural_role_id,
                company_id=legal_company_id,
            )


def merge_natural_companies(conn, companies, admin_role_id):
    for company in companies:
        # Get legal company id
        result = conn.execute(
            sa.text(
                """
                SELECT id
                FROM companies
                WHERE id != :company_id AND edrpou = :company_edrpou;
            """
            ),
            company_id=company.id,
            company_edrpou=company.edrpou,
        ).fetchone()
        if not result:
            print(f'WARNING! No legal company for: {company.edrpou}/{company.id}')
            continue
        legal_company_id = result.id

        # Merge roles
        merge_roles(conn, company, admin_role_id, legal_company_id)

        # Merge billing accounts
        merge_billing_accounts(conn, company, legal_company_id)

        # Merge contacts
        merge_contacts(conn, company, legal_company_id)

        # Merge review settings
        conn.execute(
            sa.text(
                """
                UPDATE reviews_settings
                SET company_id = :legal_company_id
                WHERE company_id = :natural_company_id
            """
            ),
            natural_company_id=company.id,
            legal_company_id=legal_company_id,
        )

        # Merge document links
        conn.execute(
            sa.text(
                """
                UPDATE document_links
                SET company_id = :legal_company_id
                WHERE company_id = :natural_company_id
            """
            ),
            natural_company_id=company.id,
            legal_company_id=legal_company_id,
        )

        # Merge document signers
        conn.execute(
            sa.text(
                """
                UPDATE document_signers
                SET company_id = :legal_company_id
                WHERE company_id = :natural_company_id
            """
            ),
            natural_company_id=company.id,
            legal_company_id=legal_company_id,
        )


def update_pure_natural_companies(conn, companies):
    for company in companies:
        conn.execute(
            sa.text(
                """
                UPDATE companies SET is_legal = True WHERE id = :company_id
            """
            ),
            company_id=company.id,
        )


def upgrade():
    # Process only on dev and test environments
    if is_prod_environ():
        return

    conn = op.get_bind()

    # Get admin role id
    result = conn.execute(
        """
       SELECT r.id AS id
       FROM roles r
         JOIN users u ON r.user_id = u.id
         JOIN companies c on r.company_id = c.id
       WHERE u.email = '<EMAIL>'
         AND c.edrpou = '41231992';
   """
    ).fetchone()

    if not result:
        print('WARNING! Migration was not processed, no admin role id!')
        return

    admin_role_id = result.id

    # Get all natural companies
    result = conn.execute(
        """
        SELECT id, edrpou FROM companies WHERE is_legal = FALSE
    """
    ).fetchall()
    natural_companies = set([Company(id=row.id, edrpou=row.edrpou) for row in result])

    # Get natural companies which have legal one
    result = conn.execute(
        """
        SELECT id, edrpou
        FROM companies
        WHERE is_legal = FALSE
          AND edrpou IN (SELECT edrpou FROM companies WHERE is_legal = TRUE)
    """
    ).fetchall()
    companies_with_legal = set([Company(id=row.id, edrpou=row.edrpou) for row in result])

    # Change pure natural companies to legal
    companies_without_legal = natural_companies - companies_with_legal
    update_pure_natural_companies(conn, companies_without_legal)

    # Merge natural companies to legal
    merge_natural_companies(conn, companies_with_legal, admin_role_id)


def downgrade():
    pass
