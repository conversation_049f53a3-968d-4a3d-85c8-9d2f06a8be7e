""" Assign tag for document tag creator

Revision ID: cb6ba7ee20c9
Revises: 602392a11c2c
Create Date: 2019-02-15 11:09:19.010823


Author: <PERSON>
"""
from alembic import op

# revision identifiers, used by Alembic.
revision = 'cb6ba7ee20c9'
down_revision = '602392a11c2c'
branch_labels = None
depends_on = None


def upgrade():
    # create tag in user profile for users who create tags
    op.execute(
        """
            INSERT INTO role_tag (role_id, tag_id) 
            SELECT 
                document_tags.assigner_role_id as role_id, 
                document_tags.tag_id as tag_id 
            FROM document_tags
            WHERE assigner_role_id IS NOT NULL
            ON CONFLICT DO NOTHING;
        """
    )


def downgrade():
    pass
