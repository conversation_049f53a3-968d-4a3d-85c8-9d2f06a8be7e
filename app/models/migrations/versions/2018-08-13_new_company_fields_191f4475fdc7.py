"""
Add new company fields

Revision ID: 191f4475fdc7
Revises: 6ef2f07b2ddf
Create Date: 2018-08-13 15:32:06.901192

Author: <PERSON>
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '191f4475fdc7'
down_revision = '6ef2f07b2ddf'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('companies', sa.Column('activity_field', sa.String(length=64), nullable=True))
    op.add_column('companies', sa.Column('employees_number', sa.String(length=64), nullable=True))
    op.add_column('companies', sa.Column('phone', sa.String(length=32), nullable=True))


def downgrade():
    op.drop_column('companies', 'phone')
    op.drop_column('companies', 'employees_number')
    op.drop_column('companies', 'activity_field')
