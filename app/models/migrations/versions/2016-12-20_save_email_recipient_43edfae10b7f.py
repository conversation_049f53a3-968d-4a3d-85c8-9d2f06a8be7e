"""save email recipient

Revision ID: 43edfae10b7f
Revises: 054038b2d73f
Create Date: 2016-12-20 11:45:34.106743


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '43edfae10b7f'
down_revision = '054038b2d73f'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_column('access_to_doc', 'is_inbox')
    op.drop_column('access_to_doc', 'contragent_edrpou')
    op.drop_column('access_to_doc', 'contragent_email')
    op.add_column('documents', sa.Column('email_recipient', sa.String(length=256), nullable=True))


def downgrade():
    op.drop_column('documents', 'email_recipient')
    op.add_column('access_to_doc', sa.Column('contragent_email', sa.VARCHAR(length=256), autoincrement=False, nullable=True))
    op.add_column('access_to_doc', sa.Column('contragent_edrpou', sa.VARCHAR(length=64), autoincrement=False, nullable=True))
    op.add_column('access_to_doc', sa.Column('is_inbox', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True))
