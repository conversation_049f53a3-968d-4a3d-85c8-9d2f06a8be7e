"""add date updated for document

Revision ID: 93670a197677
Revises: bbc31de6eada
Create Date: 2016-12-26 18:04:44.328982


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '93670a197677'
down_revision = 'bbc31de6eada'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('documents', sa.Column('date_updated', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False))


def downgrade():
    op.drop_column('documents', 'date_updated')
