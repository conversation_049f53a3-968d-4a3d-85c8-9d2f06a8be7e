"""Add promo code field.

Revision ID: f3532e41b0b9
Revises: ed45eea795f9
Create Date: 2017-03-21 15:46:23.180759


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f3532e41b0b9'
down_revision = 'ed45eea795f9'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('promo_code', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'promo_code')
    # ### end Alembic commands ###
