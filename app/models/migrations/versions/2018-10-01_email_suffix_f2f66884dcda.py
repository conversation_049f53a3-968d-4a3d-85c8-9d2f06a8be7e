""" Added email domain

Revision ID: f2f66884dcda
Revises: 24edd0d71048
Create Date: 2018-10-01 13:51:11.508750


Author: <PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f2f66884dcda'
down_revision = '24edd0d71048'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'companies',
        sa.Column(
            'default_email_domain',
            sa.String(length=64),
            nullable=True,
        ),
    )
    op.add_column(
        'short_registration_tokens',
        sa.Column(
            'email_domain',
            sa.String(length=64),
            nullable=True,
        ),
    )


def downgrade():
    op.drop_column('short_registration_tokens', 'default_email_domain')
    op.drop_column('companies', 'email_domain')
