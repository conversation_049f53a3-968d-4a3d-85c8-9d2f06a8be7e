"""certificate_table_locality_fields

Revision ID: 2666a3fad742
Revises: 6071be9c0576
Create Date: 2022-02-17 12:24:18.305433


Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2666a3fad742'
down_revision = '6071be9c0576'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('certificates', sa.Column('locality', sa.String(length=128), nullable=True))
    op.add_column('certificates', sa.Column('state', sa.String(length=128), nullable=True))


def downgrade():
    op.drop_column('certificates', 'state')
    op.drop_column('certificates', 'locality')
