"""Add index for signatures.user_id

Revision ID: e6546c1d94c1
Revises: 57d0c323bada
Create Date: 2019-10-23 17:46:17.978738


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e6546c1d94c1'
down_revision = '57d0c323bada'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT')  # hack for skipping transaction
    op.create_index(
        'ix_signatures_user_id',
        'signatures',
        ['user_id'],
        unique=False,
        postgresql_concurrently=True,
    )


def downgrade():
    op.drop_index('ix_signatures_user_id', table_name='signatures')
