"""add_bill_id_column_to_billing_accounts

Revision ID: e14fa58b21f0
Revises: d571448dee87
Create Date: 2024-04-02 23:02:35.318871

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e14fa58b21f0'
down_revision = 'd571448dee87'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('billing_accounts', sa.Column('bill_id', sa.Text(), nullable=True))
    op.create_index(
        op.f('ix_billing_accounts_bill_id'), 'billing_accounts', ['bill_id'], unique=False
    )


def downgrade():
    op.drop_index(op.f('ix_billing_accounts_bill_id'), table_name='billing_accounts')
    op.drop_column('billing_accounts', 'bill_id')
