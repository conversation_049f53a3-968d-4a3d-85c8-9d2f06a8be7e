"""add_days_before_signature_expire_column

Revision ID: f9b84d586d3f
Revises: da42e3070ef3
Create Date: 2024-10-03 14:25:05.253051

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f9b84d586d3f'
down_revision = 'da42e3070ef3'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('banners', sa.Column('days_before_signature_expires', sa.Integer(), nullable=True))


def downgrade():
    op.drop_column('banners', 'days_before_signature_expires')
