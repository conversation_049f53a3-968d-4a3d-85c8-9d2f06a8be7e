"""Add roles.is_default_recipient column

Revision ID: 5d262420299d
Revises: eb374a11da09
Create Date: 2021-01-28 11:13:35.314555


Author: a<PERSON>k<PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '5d262420299d'
down_revision = 'eb374a11da09'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('roles', sa.Column('is_default_recipient', sa.<PERSON>(), server_default='0', nullable=False))


def downgrade():
    op.drop_column('roles', 'is_default_recipient')
