"""Alter company name column type.

Revision ID: a4c1cc70ba77
Revises: d462a580cbad
Create Date: 2017-09-08 08:32:40.395939


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a4c1cc70ba77'
down_revision = 'd462a580cbad'
branch_labels = None
depends_on = None


def upgrade():
    # Contacts
    op.alter_column(
        'contacts',
        'name',
        existing_type=sa.VARCHAR(length=256),
        type_=sa.Text())

    # Companies
    op.alter_column(
        'companies',
        'name',
        existing_type=sa.VARCHAR(length=255),
        type_=sa.Text())
    op.alter_column(
        'companies',
        'full_name',
        existing_type=sa.VARCHAR(length=255),
        type_=sa.Text())


def downgrade():
    # Contacts
    op.alter_column(
        'contacts',
        'name',
        existing_type=sa.Text(),
        type_=sa.VARCHAR(length=256))

    # Companies
    op.alter_column(
        'companies',
        'name',
        existing_type=sa.Text(),
        type_=sa.VARCHAR(length=255))
    op.alter_column(
        'companies',
        'full_name',
        existing_type=sa.Text(),
        type_=sa.VARCHAR(length=255))
