"""Add user_actions model.

Revision ID: dd3d4f4772ce
Revises: 66ce89563d30
Create Date: 2017-05-04 15:09:36.150398


Author: <PERSON>
"""
import citext
import sqlalchemy as sa

from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dd3d4f4772ce'
down_revision = '66ce89563d30'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'user_actions',
        sa.Column(
            'id',
            postgresql.UUID(),
            server_default=sa.text('uuid_generate_v4()'),
            nullable=False),
        sa.Column('role_id', postgresql.UUID(), nullable=True),
        sa.Column('action', sa.Text(), nullable=False),
        sa.Column('email', citext.CIText(), nullable=False),
        sa.Column('edrpou', sa.String(length=64), nullable=True),
        sa.Column(
            'extra',
            postgresql.JSON(astext_type=sa.Text()),
            server_default=sa.text("'{}'::json"),
            nullable=False),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False),
        sa.PrimaryKeyConstraint('id'),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_actions')
    # ### end Alembic commands ###
