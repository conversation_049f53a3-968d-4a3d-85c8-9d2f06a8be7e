"""
BigInteger column type for billing.

Revision ID: abd2c7854326
Revises: 925d71f59689
Create Date: 2018-05-27 03:24:26.478399


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'abd2c7854326'
down_revision = '925d71f59689'
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        'billing_accounts',
        'amount',
        existing_type=sa.INTEGER(),
        type_=sa.BigInteger(),
        existing_nullable=False,
        existing_server_default=sa.text('0'),
    )
    op.alter_column(
        'billing_accounts',
        'amount_left',
        existing_type=sa.INTEGER(),
        type_=sa.BigInteger(),
        existing_nullable=False,
        existing_server_default=sa.text('0'),
    )
    op.alter_column(
        'billing_accounts',
        'units',
        existing_type=sa.INTEGER(),
        type_=sa.BigInteger(),
        existing_nullable=False,
        existing_server_default=sa.text('0'),
    )
    op.alter_column(
        'billing_accounts',
        'units_left',
        existing_type=sa.INTEGER(),
        type_=sa.BigInteger(),
        existing_nullable=False,
        existing_server_default=sa.text('0'),
    )


def downgrade():
    op.alter_column(
        'billing_accounts',
        'units_left',
        existing_type=sa.BigInteger(),
        type_=sa.INTEGER(),
        existing_nullable=False,
        existing_server_default=sa.text('0'),
    )
    op.alter_column(
        'billing_accounts',
        'units',
        existing_type=sa.BigInteger(),
        type_=sa.INTEGER(),
        existing_nullable=False,
        existing_server_default=sa.text('0'),
    )
    op.alter_column(
        'billing_accounts',
        'amount_left',
        existing_type=sa.BigInteger(),
        type_=sa.INTEGER(),
        existing_nullable=False,
        existing_server_default=sa.text('0'),
    )
    op.alter_column(
        'billing_accounts',
        'amount',
        existing_type=sa.BigInteger(),
        type_=sa.INTEGER(),
        existing_nullable=False,
        existing_server_default=sa.text('0'),
    )
