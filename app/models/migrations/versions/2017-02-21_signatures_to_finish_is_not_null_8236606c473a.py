"""Signatures to finish is not null.

Revision ID: 8236606c473a
Revises: d2f17f258f2c
Create Date: 2017-02-21 15:57:55.702769


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8236606c473a'
down_revision = 'd2f17f258f2c'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('documents', 'signatures_to_finish',
               existing_type=sa.SMALLINT(),
               nullable=False,
               existing_server_default=sa.text('2'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('documents', 'signatures_to_finish',
               existing_type=sa.SMALLINT(),
               nullable=True,
               existing_server_default=sa.text('2'))
    # ### end Alembic commands ###
