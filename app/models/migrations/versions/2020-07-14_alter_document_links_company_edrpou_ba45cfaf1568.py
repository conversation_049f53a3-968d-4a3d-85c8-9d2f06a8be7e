"""Alter document_links.company_edrpou column

Revision ID: ba45cfaf1568
Revises: ad11758b9edc
Create Date: 2020-07-14 12:42:35.038438


Author: <PERSON><PERSON><PERSON><PERSON>zy<PERSON>
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ba45cfaf1568'
down_revision = 'ad11758b9edc'
branch_labels = None
depends_on = None


def upgrade():
    # Fill company edrpou
    op.execute('''
        UPDATE document_links links
        SET company_edrpou = c.edrpou
        FROM companies c WHERE c.id = links.company_id
    ''')
    op.execute('ALTER TABLE document_links ALTER COLUMN company_edrpou SET NOT NULL;')
    op.execute('ALTER TABLE document_links ALTER COLUMN company_id DROP NOT NULL;')


def downgrade():
    op.execute('ALTER TABLE document_links ALTER COLUMN company_edrpou DROP NOT NULL;')
    op.execute('ALTER TABLE document_links ALTER COLUMN company_id SET NOT NULL;')
