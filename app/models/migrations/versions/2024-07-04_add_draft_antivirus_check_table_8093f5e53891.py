"""add draft_antivirus_check table

Revision ID: 8093f5e53891
Revises: 68194f55e6cf
Create Date: 2024-07-04 17:48:42.114055

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.migrations.utils import DummyEnum
from app.models.types import SoftEnum


# revision identifiers, used by Alembic.
revision = '8093f5e53891'
down_revision = '68194f55e6cf'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'draft_antivirus_check',
        sa.Column('draft_id', postgresql.UUID(), nullable=False),
        sa.Column('status', SoftEnum(DummyEnum), nullable=False),
        sa.Column('provider', SoftEnum(DummyEnum), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'date_updated',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(['draft_id'], ['drafts.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('draft_id'),
    )


def downgrade():
    op.drop_table('draft_antivirus_check')
