"""Remove signature migration columns

Revision ID: ca0284237099
Revises: 7e2daa561080
Create Date: 2024-04-22 17:19:34.208179

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ca0284237099'
down_revision = '7e2daa561080'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_table('signature_migration_errors')
    op.drop_column('signatures', 'is_stamp_uploaded')
    op.drop_column('signatures', 'has_key')
    op.drop_column('signatures', 'is_key_uploaded')
    op.drop_column('signatures', 'has_stamp')


def downgrade():
    op.add_column(
        'signatures',
        sa.Column(
            'has_stamp',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        'signatures',
        sa.Column(
            'is_key_uploaded',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.add_column(
        'signatures',
        sa.Column(
            'has_key',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        'signatures',
        sa.Column(
            'is_stamp_uploaded',
            sa.BOOLEAN(),
            server_default=sa.text('false'),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.create_table(
        'signature_migration_errors',
        sa.Column(
            'id',
            sa.VARCHAR(length=64),
            server_default=sa.text('uuid_generate_v4()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column('signature_id', postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column('error', sa.TEXT(), autoincrement=False, nullable=False),
        sa.Column(
            'context',
            postgresql.JSONB(astext_type=sa.Text()),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column(
            'date_created',
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text('now()'),
            autoincrement=False,
            nullable=False,
        ),
        sa.PrimaryKeyConstraint('id', name='signature_migration_errors_pkey'),
    )
