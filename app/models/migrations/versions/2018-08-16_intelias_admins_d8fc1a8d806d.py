""" Intelias admins

Revision ID: d8fc1a8d806d
Revises: 7a028e917c1b
Create Date: 2018-08-16 17:57:26.559271


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
from sqlalchemy.dialects.postgresql import insert

from app.auth.enums import RoleStatus
from app.auth.tables import user_table, company_table, role_table
from app.config import get_level
from app.lib.enums import UserRole

revision = 'd8fc1a8d806d'
down_revision = '7a028e917c1b'
branch_labels = None
depends_on = None

FIRST_ADMIN = '<EMAIL>'
ANOTHER_ADMINS = {
    '<EMAIL>': [
        '3266106218',
        '3484507400',
        '3444216082',
        '3584505628',
        '3086505141',
        '3452312067',
        '2684211497',
        '3465306783',
        '3081612949',
        '2803605036',
        '2998512830',
        '3368605048',
        '3168701192',
        '3432506056',
        '3017224130',
        '3396803653',
        '3248406721',
        '3322602503',
        '3162405574',
        '3390215597',
        '3033621897',
        '3325807072',
        '3144405836',
        '3119122543',
        '3373710755',
        '3555604416',
        '3494807635',
        '3241414486',
        '3434705730',
        '3169105956',
        '3082005444',
        '3216420037',
        '3394002747',
        '3301210002',
        '3198102486',
        '3249909023',
        '3434700757',
        '3289011286',
        '2980809439',
        '3420808719',
        '2949716056',
        '3324302819',
        '3344300356',
        '3374609077',
        '3184920156',
        '3344003568',
        '3359310839',
        '2940012038',
        '3430906282',
        '3372215894',
        '3184604284',
        '3310003794',
        '2792113578',
        '3363605726',
        '3295615546',
        '3463806896',
        '3446702699',
        '3075808092',
        '3431806522',
        '3343806396',
    ],
    '<EMAIL>': [
        '3301603635',
        '3357105133',
        '3396106152',
        '3031805998',
        '3410401234',
        '3285903038',
        '3050506417',
        '3331109846',
        '3372201289',
        '3413200138',
        '3151307153',
        '2774607418',
        '3314516110',
        '3281018115',
        '3450312469',
        '3348101765',
        '3282213454',
        '3261204803',
        '3085300052',
        '2999913509',
        '3266904320',
        '3060009294',
        '2814804812',
        '3339411822',
        '3347602115',
        '3297503914',
        '3221219417',
        '3123206328',
        '3104505939',
        '2948907995',
        '3282219436',
        '3307003661',
        '2910610476',
        '3389115695',
        '3161306232',
        '3158806078',
        '3234805757',
        '2984508372',
        '3208504176',
        '3112321854',
        '3221805668',
        '3141818956',
        '3196107198',
        '3304017541',
        '2984213394',
        '3078908143',
        '3151024554',
        '3220800295',
        '3233605996',
        '3226905405',
        '3132619398',
        '3120016876',
        '3152913626',
        '3197307615',
        '3364510802',
        '2955700429',
        '3384209830',
        '2885517875',
        '2795504202',
        '3576000402',
        '3074608408',
        '3345818240',
        '2995710635',
        '2646803539',
        '3049606675',
        '3344405812',
        '3309003276',
        '3152116758',
        '3145119519',
        '3124806540',
        '3213511982',
        '3263309785',
        '3502705050',
        '3176405655',
        '2959412535',
        '3296012810',
        '3031504760',
        '3252203554',
        '3363713880',
        '3382308417',
        '3129602693',
        '3308801818',
        '3239816717',
        '3190110951',
        '3314501014',
        '3154201646',
        '3282503760',
        '3257419259',
        '3032905638',
        '3035805867',
        '3358307979',
        '3359611476',
        '3277105670',
        '3342102648',
        '3258205398',
        '2716814679',
        '3273410152',
        '2914403157',
        '3278206266',
        '3310702621',
        '3127007795',
        '3285003632',
    ],
    '<EMAIL>': [
        '3070702913',
        '3024908834',
        '3304614376',
        '3318112379',
        '3365901779',
        '2814911053',
        '3124114698',
        '3305418315',
        '3359710265',
        '2793809336',
        '2842203615',
        '3279315059',
        '3050606678',
        '2965503571',
        '3138217195',
        '3264716593',
        '3321905650',
        '2950905799',
        '3081521410',
        '3266521756',
        '3196705577',
        '3232024332',
        '3021712938',
        '3363513619',
        '3273303898',
        '3271305257',
        '3443403148',
        '3191621179',
        '3343418350',
        '2991015234',
        '3352810395',
        '3153510903',
        '2981211098',
        '3065500759',
        '2997503998',
        '2841006078',
        '3357903650',
        '3079320530',
        '3252911358',
        '2993115994',
        '3343911635',
        '3242603493',
        '3258011471',
        '3123406599',
        '3249402073',
        '3217217810',
        '3273609127',
        '3044718694',
        '2851505951',
        '3233113330',
        '3177810214',
        '3053921071',
        '3408803575',
        '3265219111',
        '3164816218',
        '3263920851',
        '2908706256',
        '3018220382',
        '2944608557',
        '3373302531',
        '3211615232',
        '3270011515',
        '3268110946',
        '3314809217',
        '3147001395',
        '3294711691',
        '2999603519',
        '3129707530',
        '2931718510',
        '3300802237',
        '3358308136',
        '3389702215',
        '3311009611',
        '3380104570',
        '3207917936',
        '3165005330',
        '3052323677',
        '3249016538',
        '3156121738',
        '3336711799',
        '3176010591',
        '3245714438',
        '3186217635',
        '3191906736',
        '3375909533',
        '2835717858',
        '3229710699',
        '2921421197',
        '3328700315',
        '3037022313',
        '3426412292',
        '3256414992',
        '3320314574',
        '3182413715',
        '2914007171',
        '2975820853',
        '3264618879',
        '3243017058',
        '3271119836',
        '2885912010',
        '3152805713',
        '3126119344',
    ],
    '<EMAIL>': [
        '3133314331',
        '3190419533',
        '3165204713',
        '3445315138',
        '3268302554',
        '3260613097',
        '3454314394',
        '3059310332',
        '3224919276',
        '2707713737',
        '3242519056',
        '3573809702',
        '3401713932',
        '3299317833',
        '3107101077',
        '3187510911',
        '3253505036',
        '3276719476',
        '3265910729',
        '3104733337',
        '2607222665',
        '3193116552',
        '3297308034',
        '3424911459',
        '3454603946',
        '3425302015',
        '2785710512',
        '3283807824',
        '3057217927',
        '3537205688',
        '3235306815',
        '3200810076',
        '3356105370',
        '3452402914',
        '3063404977',
        '3306317859',
        '2600108031',
        '3374816119',
        '2958508458',
        '3372806316',
        '3389900974',
        '3206613075',
        '3034217655',
        '3385508056',
        '3275613036',
        '3260908901',
        '3284807506',
        '3370414534',
        '3196319375',
        '3265816449',
        '3123720345',
        '3270917630',
        '3158104133',
        '3267317955',
        '3289212253',
        '3192100450',
        '3113122035',
        '3176607504',
        '3359613983',
        '3324617909',
        '3227517741',
        '3336106523',
        '3112814123',
        '3277217197',
        '3071119311',
        '3078419332',
        '2857118211',
        '3309214513',
        '3308315599',
        '3117822793',
        '3338012812',
        '3229713954',
        '3200402332',
        '3085415478',
        '3298417476',
        '3117618750',
        '3550900321',
        '3144418418',
        '3094919213',
        '3211104540',
        '3072014186',
        '3419816572',
        '3130220799',
        '3013615106',
        '3169716799',
        '3012806911',
        '2985102669',
        '3310006090',
        '3326218348',
    ],
    '<EMAIL>': [
        '3308302965',
        '3091208698',
        '2967711338',
        '3319603170',
        '3071408095',
        '3193615568',
        '2956408797',
        '3152205757',
        '3374513472',
        '3233222995',
        '3576404011',
        '3329002198',
        '3306003464',
        '3229510036',
        '3323817568',
        '2978413059',
        '3148605894',
        '3286005756',
        '2979811298',
        '3283218131',
        '3009902697',
        '3275205654',
        '3083113577',
        '3082609997',
        '3525202238',
        '3291805059',
        '3089121655',
        '3203018590',
        '3028906195',
        '3314603162',
        '2837910531',
        '3344913057',
        '2822507538',
        '2924017419',
        '3183010172',
        '3288404571',
        '3212021835',
        '2620913735',
        '3225006455',
        '3094407397',
        '3379209073',
        '2996811097',
        '2264106457',
        '3355101434',
        '3281101886',
        '3275004319',
    ],
    '<EMAIL>': [
        '3188508724',
        '2940904676',
        '3199504517',
        '3525907098',
        '3338701719',
        '3071011736',
        '3163107276',
        '3054806211',
        '3163222585',
        '3357206329',
        '3415300402',
        '3376418634',
        '3029310134',
        '2978813099',
        '3224903557',
        '3129001552',
        '3304206684',
        '3303420474',
        '3178304502',
        '2783712537',
        '3025404218',
        '3018912152',
        '3438603310',
        '3251805732',
        '3387302997',
        '3080706547',
        '3465700098',
        '3013011061',
        '2849907297',
        '3187201036',
        '3373810338',
        '3426808039',
        '3208707889',
        '3351112730',
        '3189306159',
        '3489709517',
        '3329503956',
        '3293602635',
        '3249613578',
        '3307702312',
        '2992211197',
        '3378710090',
        '3241306093',
        '2831012175',
        '3320602875',
        '3314209209',
        '2908510769',
        '3116008813',
        '3176209771',
        '3432607685',
        '3371900810',
        '3250400630',
        '3085008955',
        '2979909534',
        '3112509432',
        '2846503677',
        '3302903288',
        '3508402834',
        '3016914915',
        '2718101935',
        '2848514573',
        '3413610115',
        '3061019850',
        '3210104991',
        '3370110279',
        '3170605278',
        '3394306094',
        '3268316814',
        '3455007932',
        '3124005770',
        '3245404079',
        '2913403077',
        '3373810315',
        '3019004389',
        '3370106040',
        '3040908354',
        '3374200479',
        '2854120314',
        '3048722032',
        '3247116137',
        '3032303275',
        '3369901057',
        '3220224601',
        '3175205894',
        '3224804154',
        '3219805872',
        '3277202033',
        '3492203542',
        '3382010957',
        '3401809373',
        '2942103031',
        '3065421527',
        '3184022373',
        '3100205788',
        '3165811139',
        '2972012004',
        '3054005190',
        '3278503697',
    ],
    '<EMAIL>': [
        '3377116832',
        '3463213693',
        '3165021591',
        '3269014836',
        '3009407310',
        '3296008397',
        '2882002672',
        '3095216816',
        '3392801652',
        '3349110607',
        '3400100694',
        '3284306012',
        '2921313898',
        '3091822018',
        '3184024617',
        '3359405293',
        '3167115498',
        '3418012106',
        '2964003751',
        '2802810152',
        '3246517974',
        '3238417853',
        '3258712728',
        '2986712494',
        '3179624718',
        '3182902438',
        '3428706477',
        '3338816631',
        '3318612658',
        '3035224674',
        '3221208037',
        '3304220838',
        '3346413460',
        '3372307522',
        '3433309364',
        '3218213073',
        '3234912788',
        '3274516171',
        '3242216819',
        '3041315851',
        '3078804010',
        '3471815015',
        '3389712174',
        '3365516073',
        '3041820902',
        '2834613275',
        '3022707942',
        '3101316112',
        '3174118334',
        '3279816970',
        '3226307792',
        '3340718953',
        '3257700024',
        '3033914610',
        '3095012133',
        '2835005496',
        '3020113780',
        '3177001711',
        '3207510751',
        '3316104457',
        '2884903693',
    ],
    '<EMAIL>': [
        '3157608714',
        '3376309435',
        '2632807350',
        '3277615889',
        '2968110534',
        '3360914712',
        '3265104491',
        '3249100437',
        '3101705281',
        '2746613893',
        '3221112906',
        '3345606676',
        '3297206336',
        '3234600134',
        '3131220996',
        '3285505358',
        '2624018952',
        '3272516737',
        '3344202876',
        '2894603372',
        '3188005992',
        '3221504570',
        '3034105250',
        '2835913924',
        '2962301138',
        '3232205794',
        '3023808627',
        '3390309112',
        '3294904580',
        '3168204858',
        '2965410080',
        '3157408110',
        '3210015490',
        '2948417232',
        '3222013870',
        '2906510335',
        '2842308578',
        '3133804954',
        '3264005078',
        '3386604799',
        '3270019348',
        '3036406876',
        '3356507255',
        '3248311195',
        '3117809153',
        '3076809498',
        '2924712115',
        '3315502935',
        '3416802599',
        '3180105592',
        '3228701694',
        '3173019974',
        '3375013437',
        '3076907271',
        '3336903097',
        '3228001203',
        '3194706393',
        '3192803452',
        '3063305258',
        '3274604159',
        '3380214931',
        '2889007166',
        '3159305601',
        '3104103538',
        '3303610757',
        '3299703783',
        '3331502882',
        '3042000133',
        '3021606654',
        '3293503817',
        '3374014274',
        '3186922431',
        '3119005259',
        '2993806690',
        '3181405638',
        '3379507018',
        '3103107490',
        '3088809256',
        '2720115037',
        '3521600195',
        '3271014311',
        '3225006057',
        '3281603566',
        '2790115171',
        '3024704759',
        '3305703683',
        '3222504216',
        '3473714218',
        '3232009823',
        '3049206857',
        '3216205794',
        '3252618034',
        '3284503440',
        '3237416125',
        '3073605970',
        '3040011692',
        '3436009971',
        '2951613735',
        '3295212907',
        '3375703531',
        '3259105135',
        '3044006030',
        '3099215640',
        '3119016393',
        '3324212037',
        '3240402619',
        '3251818758',
        '3385710079',
        '3537803874',
        '3338808891',
        '3035412479',
        '3260604231',
        '3281609694',
        '3402706534',
        '3206806074',
        '3249519311',
        '3018916136',
        '3161405781',
    ],
    '<EMAIL>': [
        '3142109917',
        '3248703999',
        '3275303030',
        '3197801853',
        '2963609300',
        '3289305154',
        '3255204499',
        '3190805196',
        '3221219299',
        '3006408203',
        '3300704394',
        '3174407530',
        '3197405036',
        '3249914391',
        '3211505543',
        '3062221074',
        '2925116235',
        '3261319217',
        '3350412422',
        '3508300574',
        '3170200454',
        '3224300800',
        '3415415390',
        '3322307690',
        '3345902289',
        '2971720038',
        '3152107095',
        '3055305773',
        '3282006413',
        '2877103739',
        '3270610892',
        '2805612755',
        '3069710098',
        '3073606336',
        '3207404899',
        '3268705732',
        '3195706297',
        '3387505757',
        '3107506622',
        '3318509476',
        '3121905673',
        '2902103330',
        '3405202674',
        '3236305311',
        '2971811936',
        '3286206781',
        '3342713334',
        '3210517438',
        '3157225899',
        '3121506234',
        '2657502230',
        '3272121451',
        '3094905117',
        '3158127598',
        '3072808999',
        '2933011751',
        '3274916182',
        '3196705917',
        '3517614833',
        '2745009717',
        '3343004517',
        '3090008412',
        '3389702760',
        '3094308439',
        '3140305588',
        '2335910777',
        '3240614632',
        '2839707551',
        '3131605118',
        '3310302721',
        '3435905004',
        '3195505431',
        '3299302679',
        '3214104087',
        '3376315330',
        '3198418376',
        '2801011412',
        '3317706794',
        '3316517758',
        '2808011338',
        '3203605886',
        '2978910657',
        '3398308406',
        '3329504498',
        '3395808954',
        '3379412014',
        '3155607356',
        '3113605960',
        '3041311317',
        '3152311913',
        '3184605617',
    ],
    '<EMAIL>': [
        '3130203211',
        '3055623251',
        '3146721019',
        '3408801607',
        '3230913732',
        '3143510351',
        '3249114931',
        '3142207100',
        '3074116752',
        '3168217335',
        '3106321136',
        '3209502810',
        '3050901164',
        '3324900022',
        '3271820548',
        '2997306952',
        '3156026892',
        '2873206198',
        '3186602031',
        '3036001338',
        '3104014826',
        '3354207966',
        '3114322710',
        '3278818115',
        '2910205237',
        '2764607470',
        '3468011261',
        '3231014257',
    ],
}

LEVEL = get_level()


def print_func(msg):
    if LEVEL == 'prod':
        print(msg)


def upgrade():
    connection = op.get_bind()

    # Select first admin
    admin = connection.execute(
        sa.select([user_table.c.email]).where(user_table.c.email == FIRST_ADMIN)
    ).fetchone()

    if not admin:
        print_func(f'WARNING! No user with email {FIRST_ADMIN}!')

    role_data = []

    for email, edrpous in ANOTHER_ADMINS.items():
        companies = connection.execute(
            sa.select([company_table.c.edrpou, company_table.c.id]).where(
                company_table.c.edrpou.in_(edrpous)
            )
        )
        existed_company_edrpous = set()
        existed_company_ids = set()
        for company in companies:
            existed_company_edrpous.add(company.edrpou)
            existed_company_ids.add(company.id)

        another_admin = connection.execute(
            sa.select([user_table.c.id]).where(user_table.c.email == email)
        ).fetchone()

        missing_edrpous = set(edrpous) - existed_company_edrpous
        if missing_edrpous:
            print_func(f'WARNING! Missing edrpous: {", ".join(missing_edrpous)}')

        for company_id in existed_company_ids:
            if not another_admin:
                print_func(f'WARNING! No user with email {email}!')
            else:
                role_data.append(
                    {
                        'company_id': company_id,
                        'user_id': another_admin.id,
                        'status': RoleStatus.active,
                        'user_role': UserRole.admin.value,
                    }
                )

            if admin:
                role_data.append(
                    {
                        'company_id': company_id,
                        'user_id': admin.id,
                        'status': RoleStatus.active,
                        'user_role': UserRole.admin.value,
                    }
                )

    if role_data:
        op.execute(
            insert(role_table)
            .values(role_data)
            .on_conflict_do_update(
                index_elements=[role_table.c.user_id, role_table.c.company_id],
                set_={
                    'status': RoleStatus.active,
                    'user_role': UserRole.admin.value,
                },
            )
        )


def downgrade():
    connection = op.get_bind()

    conditions = [
        sa.and_(
            sa.or_(
                user_table.c.email == email,
                user_table.c.email == FIRST_ADMIN,
            ),
            company_table.c.edrpou.in_(edrpous),
        )
        for email, edrpous in ANOTHER_ADMINS.items()
    ]

    roles = connection.execute(
        sa.select([sa.distinct(role_table.c.id)])
        .select_from(
            role_table.join(company_table, company_table.c.id == role_table.c.company_id).join(
                user_table, user_table.c.id == role_table.c.user_id
            )
        )
        .where(sa.or_(*conditions))
    )
    role_ids = [role.id for role in roles]

    op.execute(
        role_table.update()
        .values(
            {
                'status': RoleStatus.pending,
                'user_role': UserRole.user.value,
            }
        )
        .where(role_table.c.id.in_(role_ids))
    )
