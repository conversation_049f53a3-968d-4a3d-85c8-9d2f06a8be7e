"""Add new bill columns

Revision ID: 394b854c37a5
Revises: 5a45f83e8850
Create Date: 2024-08-16 19:48:33.286835

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.migrations.utils import DummyEnum
from app.models.types import SoftEnum

# revision identifiers, used by Alembic.
revision = '394b854c37a5'
down_revision = '5a45f83e8850'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'bills',
        sa.Column(
            'services_type',
            SoftEnum(DummyEnum),
            nullable=True,
        ),
    )
    op.add_column(
        'bills',
        sa.Column(
            'services',
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
    )


def downgrade():
    op.drop_column('bills', 'services')
    op.drop_column('bills', 'services_type')
