"""Migrate signatures.role_id column.

Revision ID: 00ac8315956a
Revises: 0db9bc685195
Create Date: 2017-10-10 08:15:21.680601


Author: <PERSON>
"""
from alembic import op


# revision identifiers, used by Alembic.
revision = '00ac8315956a'
down_revision = '0db9bc685195'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        UPDATE signatures
        SET role_id = t.role_id_
        FROM (
               SELECT signatures.id,
                      roles.id
               FROM signatures
                 LEFT JOIN companies ON companies.edrpou = signatures.key_owner_edrpou AND
                                        companies.is_legal IS TRUE
                 LEFT JOIN users ON users.id = signatures.user_id
                 LEFT JOIN roles ON roles.user_id = users.id AND
                                    roles.company_id = companies.id
               WHERE roles.id IS NOT NULL AND
                     signatures.role_id IS NULL
               GROUP BY signatures.id,
                        roles.id
             ) t(id_, role_id_)
        WHERE role_id IS NULL AND
              key_owner_edrpou IS NOT NULL AND
              id = t.id_;
    """)


def downgrade():
    pass
