"""Update SignSessionDocumentStatus enum.

Revision ID: d76e05354682
Revises: ba1dd03fc229
Create Date: 2017-08-02 14:44:36.334862


Author: <PERSON>
"""
from alembic import op
import sqlalchemy as sa

from app.models.migrations.utils import modify_db_enum


# revision identifiers, used by Alembic.
revision = 'd76e05354682'
down_revision = 'ba1dd03fc229'
branch_labels = None
depends_on = None

def upgrade():
    modify_db_enum(
        op,
        'sign_sessions',
        'document_status',
        'signsessiondocumentstatus',
        "('viewed', 'commented', 'rejected', 'signed')")


def downgrade():
    modify_db_enum(
        op,
        'sign_sessions',
        'document_status',
        'signsessiondocumentstatus',
        "('viewed', 'rejected', 'signed')")
