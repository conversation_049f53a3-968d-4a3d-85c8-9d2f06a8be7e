"""Add vchasno_profile_sync table

Revision ID: 4b0390b5586a
Revises: 00a1ad547bb2
Create Date: 2024-12-10 11:50:00.800026

"""

from alembic import op
import sqlalchemy as sa

from app.models.migrations.utils import DummyEnum
from app.models.types import SoftEnum

# revision identifiers, used by Alembic.
revision = '4b0390b5586a'
down_revision = '00a1ad547bb2'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'vchasno_profile_sync',
        sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
        sa.Column('entity_type', SoftEnum(DummyEnum), nullable=False),
        sa.Column('entity_id', sa.Text(), nullable=False),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint('id'),
    )
    op.add_column(
        'vchasno_users',
        sa.Column(
            'date_updated',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
    )


def downgrade():
    op.drop_column('vchasno_users', 'date_updated')
    op.drop_table('vchasno_profile_sync')
