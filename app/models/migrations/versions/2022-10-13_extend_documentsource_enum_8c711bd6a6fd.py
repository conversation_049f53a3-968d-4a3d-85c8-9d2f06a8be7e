"""extend_documentsource_enum

Revision ID: 8c711bd6a6fd
Revises: e98ccfe3e901
Create Date: 2022-10-13 13:20:52.539815

"""
from alembic import op

from app.models.migrations.utils import modify_db_enum


revision = "8c711bd6a6fd"
down_revision = "07b94391ec77"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("COMMIT;")
    op.execute("ALTER TYPE documentsource ADD VALUE 'zakupki';")


def downgrade():
    modify_db_enum(
        op,
        'documents',
        'source',
        'documentsource',
        "('vchasno', 'vchasno_container', 'edi', 'edi_random_role', 'art_office', 'edoc', 'ifin', 'ifin_doki', 'ifin_edi', 'medoc', 'papka24', 'zvit')",
        default="'vchasno'",
    )
