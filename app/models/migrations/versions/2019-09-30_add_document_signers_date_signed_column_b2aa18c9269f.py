"""Add document_signers.date_signed column

Revision ID: b2aa18c9269f
Revises: c41f7b02c55d
Create Date: 2019-09-30 16:07:53.231027


Author: <PERSON><PERSON><PERSON><PERSON>
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'b2aa18c9269f'
down_revision = 'c41f7b02c55d'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('COMMIT')
    op.add_column(
        'document_signers',
        sa.Column('date_signed', sa.DateTime(timezone=True), nullable=True),
    )
    op.create_index(
        op.f('ix_document_signers_date_signed'),
        'document_signers',
        ['date_signed'],
        unique=False,
        postgresql_concurrently=True,
    )


def downgrade():
    op.drop_index(
        op.f('ix_document_signers_date_signed'),
        table_name='document_signers',
    )
    op.drop_column('document_signers', 'date_signed')
