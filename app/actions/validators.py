from aiohttp import web
from trafaret_validator import TrafaretValidator

from app.actions.enums import ActionPage
from app.lib import validators
from app.lib.database import DBRow
from app.lib.types import DataDict

ACTIONS_COUNT_LIMIT = 100_000


class SaveVisitValidator(TrafaretValidator):
    page = validators.Enum(ActionPage)


async def validate_save_visit(request: web.Request, user: DBRow) -> DataDict:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate(SaveVisitValidator, raw_data)
    page = ActionPage(data['page'])
    return {'page': page, 'email': user.email, 'edrpou': user.company_edrpou}
