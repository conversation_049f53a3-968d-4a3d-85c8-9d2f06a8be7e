from datetime import timedelta

from app.auth.types import User
from app.drafts.db import select_draft
from app.editor.session_providers import EditSessionDraft, EditSessionTemplate
from app.editor.tests.utils import initiate_editor_draft_versioned, initiate_editor_template
from app.editor.utils import get_context
from app.events.user_actions.db import select_user_actions_for
from app.lib.datetime_utils import utc_now
from app.services import services
from app.templates.db import select_template
from app.templates.tests.utils import prepare_template
from app.tests.common import TimeManager, prepare_client

TEST_UUID_1 = '14b7066b-cd16-4af4-88c5-58285c6c8644'
TEST_UUID_2 = 'b7b18b79-fe7f-47d9-9122-c16a0421b3b2'
TEST_UUID_3 = '57fabbd0-f085-4c11-890b-74a5892272a6'
TEST_UUID_4 = '2c705883-70ad-4ff2-833f-d72bd06d4ee7'


async def get_edit_session_draft(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        first_name='Test',
        last_name='User',
        user_id=TEST_UUID_1,
    )
    edit_ctx, _, draft = await initiate_editor_draft_versioned(
        app=app,
        user=User.from_row(user),
    )
    context = await get_context(edit_ctx.to_dict())
    return EditSessionDraft(
        context=context,
    )


async def get_edit_session_template(aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client,
        first_name='Test',
        last_name='User',
        user_id=TEST_UUID_1,
    )
    template = await prepare_template(User.from_row(user))
    edit_ctx = await initiate_editor_template(
        user=User.from_row(user),
        template=template,
    )
    context = await get_context(edit_ctx.to_dict())
    return EditSessionTemplate(
        context=context,
    )


async def test_edit_session_draft_file_metadata(aiohttp_client):
    edit_session_draft = await get_edit_session_draft(aiohttp_client)
    async with services.db.acquire() as conn:
        assert await edit_session_draft.file_metadata(conn) == 29


async def test_edit_session_draft_file_content(aiohttp_client):
    edit_session_draft = await get_edit_session_draft(aiohttp_client)
    async with services.db.acquire() as conn:
        assert await edit_session_draft.file_content(conn) == b'test-document-version-content'


async def test_edit_session_draft_file_save(aiohttp_client, s3_emulation):
    edit_session_draft = await get_edit_session_draft(aiohttp_client)
    new_content = b'new-content'
    async with services.db.acquire() as conn:
        draft_before = await select_draft(conn=conn)
        # Act
        async with TimeManager(conn) as time:
            date = utc_now() + timedelta(hours=1)
            await time.set_now(date)
            await edit_session_draft.file_save(conn, new_content)

        # Assert
        draft_after = await select_draft(conn=conn)

    assert draft_before.date_updated != draft_after.date_updated
    assert s3_emulation.get_draft(draft_id=draft_before.id).body == new_content


async def test_edit_session_template_file_metadata(aiohttp_client):
    edit_session_template = await get_edit_session_template(aiohttp_client)
    async with services.db.acquire() as conn:
        assert await edit_session_template.file_metadata(conn) == 1595


async def test_edit_session_template_file_content(aiohttp_client, s3_emulation):
    edit_session_template = await get_edit_session_template(aiohttp_client)
    async with services.db.acquire() as conn:
        assert (
            await edit_session_template.file_content(conn)
            == s3_emulation.get_template(template_id=edit_session_template.context.entity_id).body
        )


async def test_edit_session_template_file_save(aiohttp_client, s3_emulation):
    edit_session_template = await get_edit_session_template(aiohttp_client)
    new_content = b'new-content'
    async with services.db.acquire() as conn:
        template_before = await select_template(
            conn=conn, template_id=edit_session_template.context.entity_id
        )
        # Act
        async with TimeManager(conn) as time:
            date = utc_now() + timedelta(hours=1)
            await time.set_now(date)
            await edit_session_template.file_save(conn, new_content)

        # Assert
        template_after = await select_template(
            conn=conn, template_id=edit_session_template.context.entity_id
        )

    assert template_before.date_updated != template_after.date_updated
    assert (
        s3_emulation.get_template(template_id=edit_session_template.context.entity_id).body
        == new_content
    )

    user_actions = await select_user_actions_for(email=edit_session_template.context.user_email)
    assert len(user_actions) == 1
