import datetime
from typing import NamedTuple

from app.esputnik.enums import RegistrationGroup
from app.lib.database import DBRow


class AddContactsResponse(NamedTuple):
    asyncSessionId: str  # noqa: N815
    id: str


class SubscribeContactResponse(NamedTuple):
    id: str


class Contact(NamedTuple):
    """
    ESputnik contact.

    Do not confuse with app.contacts.types.Contact,
    which represents recipients contacts in our system
    """

    email: str
    phone: str | None

    first_name: str | None
    second_name: str | None
    last_name: str | None

    # There is no bool type in ESputnik, used int 1/0 instead
    is_registered: int

    date_registered: datetime.datetime | None

    is_legal: int | None
    is_natural: int | None

    is_admin: int | None

    email_confirmed: int
    registration_completed: int

    registration_group: RegistrationGroup | None

    @staticmethod
    def from_db(
        row: DBRow,
        *,
        is_legal: int | None = None,
        is_natural: int | None = None,
    ) -> 'Contact':
        group = row['registration_group']
        enum_value = group and RegistrationGroup[group]
        return Contact(
            email=row['email'],
            phone=row['phone'],
            first_name=row['first_name'],
            last_name=row['last_name'],
            second_name=row['second_name'],
            is_registered=row['is_registered'],
            date_registered=row['date_created'],
            is_legal=is_legal,
            is_natural=is_natural,
            is_admin=row['is_admin'],
            email_confirmed=row['email_confirmed'],
            registration_completed=row['registration_completed'],
            registration_group=enum_value,
        )

    @staticmethod
    def get_guest_contact(
        email: str, phone: str | None = None, name: str | None = None
    ) -> 'Contact':
        return Contact(
            email=email,
            first_name=name,
            phone=phone,
            is_registered=0,
            email_confirmed=0,
            registration_completed=0,
            last_name=None,
            date_registered=None,
            is_legal=None,
            is_natural=None,
            is_admin=None,
            registration_group=None,
            second_name=None,
        )
