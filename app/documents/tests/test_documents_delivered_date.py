import pytest
import ujson

from app.auth.db import update_role
from app.auth.enums import RoleStatus
from app.documents.db import select_document_by_id
from app.documents.enums import FirstSignBy
from app.lib.enums import DocumentStatus
from app.tests.common import (
    GRAPHQL_URL,
    LOGIN_URL,
    LOGOUT_URL,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    TEST_USER_PASSWORD,
    cleanup_on_teardown,
    fetch_graphql,
    fetch_graphql_raw,
    login,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_referer_headers,
    prepare_signature_form_data,
    prepare_user_data,
    send_document,
    sign_and_send_document,
    with_elastic,
)


async def date_delivered(client, headers):
    data = await fetch_graphql(
        client, '{ allDocuments { documents { dateDelivered } } }', headers=headers
    )
    return data['allDocuments']['documents'][0]['dateDelivered']


async def retrieve_document(client, document_id, headers):
    response = await fetch_graphql_raw(
        client=client,
        query="""
            query GetDocument($id: ID!) {
                document(id: $id) {
                    id
                }
            }
        """,
        variables={'id': document_id},
        headers=headers,
    )
    assert response.status == 200
    response_json = await response.json()
    return response_json


async def test_date_delivered_after_incomplete_registration(aiohttp_client):
    app, client, owner = await prepare_client(aiohttp_client)

    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        password=TEST_USER_PASSWORD,
    )
    recipient_headers = prepare_auth_headers(recipient)

    document = await prepare_document_data(
        app,
        owner,
        create_document_access_for_recipients=True,
        status_id=DocumentStatus.signed_and_sent.value,
        another_recipients=[recipient],
    )
    document_id = document.id

    try:
        assert document.date_delivered is None

        async with app['db'].acquire() as conn:
            # Imitate case, when incomplete registrations have active roles
            await update_role(
                conn=conn,
                role_id=recipient.role_id,
                data={'status': RoleStatus.pending},
            )

            # Not delivered after login
            await login(client, recipient.email, TEST_USER_PASSWORD)
            updated_document = await select_document_by_id(conn, document_id)
            assert updated_document.date_delivered is None

            # Not delivered after resolving documents
            await client.post(
                GRAPHQL_URL,
                data=ujson.dumps({'query': '{ allDocuments { documents { title } } }'}),
                headers=recipient_headers,
            )
            updated_document = await select_document_by_id(conn, document_id)
            assert updated_document.date_delivered is None
    finally:
        await cleanup_on_teardown(app)


@pytest.mark.parametrize(
    'use_case, url, expected',
    [
        ('activate_role', '/internal-api/roles/{0}/activate', False),
        ('login', LOGIN_URL, False),
        ('resolve_documents_count', GRAPHQL_URL, False),
        ('logout', LOGOUT_URL, False),
    ],
)
async def test_documents_delivered_data(aiohttp_client, use_case, url, expected):
    """
    Previously those cases triggered date_delivered update, but now they don't, so let's
    leave them here for some time to check that we stopped updating date_delivered in those
    cases
    """
    # Owner
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    owner_headers = prepare_auth_headers(owner)
    document = await prepare_document_data(app, owner)

    # Recipient
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    recipient_headers = prepare_auth_headers(recipient)

    await sign_and_send_document(
        client,
        document.id,
        owner,
        recipient_edrpou=recipient.company_edrpou,
        recipient_email=recipient.email,
    )

    async with with_elastic(app, [document.id]):
        # Waiting for recipient action
        assert not await date_delivered(client, owner_headers)

        # Delivered after role switch
        if use_case == 'activate_role':
            await client.post(url.format(recipient.role_id), headers=recipient_headers)
        # Delivered after login
        elif use_case == 'login':
            await client.post(
                url,
                data=ujson.dumps(
                    {
                        'email': recipient.email,
                        'password': TEST_USER_PASSWORD,
                    }
                ),
                headers=prepare_referer_headers(client),
            )
        # Delivered after resolving documents
        elif use_case == 'resolve_documents_count':
            await client.post(
                url,
                data=ujson.dumps({'query': '{ countDocuments }'}),
                headers=recipient_headers,
            )
        # Not delivered on other use cases, such as logout
        elif use_case == 'logout':
            await client.post(url, headers=recipient_headers)

        assert bool(await date_delivered(client, owner_headers)) is expected


async def test_3p_documents_delivered_date(aiohttp_client):
    # Owner
    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    owner_headers = prepare_auth_headers(owner)

    # Recipient
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    recipient_headers = prepare_auth_headers(recipient)

    # Document sent
    document = await prepare_document_data(
        app,
        owner,
        status_id=DocumentStatus.uploaded.value,
        first_sign_by=FirstSignBy.recipient,
    )
    document_id = document.id

    async def is_document_delivered():
        async with with_elastic(app, [document_id]):
            is_owner = bool(await date_delivered(client, owner_headers))
            is_recipient = bool(await date_delivered(client, recipient_headers))

        return {
            'owner': is_owner,
            'recipient': is_recipient,
        }

    await send_document(
        client=client,
        document_id=document_id,
        recipient_edrpou=recipient.company_edrpou,
        recipient_email=recipient.email,
        headers=owner_headers,
    )

    # Not delivered after sending
    assert await is_document_delivered() == {'owner': False, 'recipient': False}

    # Delivered after recipient retrieve the document
    await retrieve_document(client, document_id, headers=recipient_headers)
    assert await is_document_delivered() == {'owner': True, 'recipient': True}

    # No delivered date after recipient sign
    await sign_and_send_document(
        client,
        document_id,
        recipient,
        sign_data=prepare_signature_form_data(recipient, owner=owner),
    )
    assert await is_document_delivered() == {'owner': False, 'recipient': False}

    # Not delivered after recipient retrieve the document
    await retrieve_document(client, document_id, headers=recipient_headers)
    assert await is_document_delivered() == {'owner': False, 'recipient': False}

    # Delivered after an owner retrieves the document
    await retrieve_document(client, document_id, headers=owner_headers)
    assert await is_document_delivered() == {'owner': True, 'recipient': True}
