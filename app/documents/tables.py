import citext
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app import models
from app.auth.tables import company_table, role_table
from app.documents.constants import (
    DEFAULT_EXPECTED_OWNER_SIGNATURES,
    DEFAULT_EXPECTED_RECIPIENT_SIGNATURES,
    DEFAULT_SIGNATURES_TO_FINISH,
)
from app.documents.enums import (
    AccessSource,
    DeleteRequestStatus,
    DocumentSource,
    FirstSignBy,
)
from app.lib.enums import SignatureFormat
from app.models import columns, metadata
from app.models import types as custom_types

document_table = sa.Table(
    'documents',
    metadata,
    # Primary and foreign keys
    columns.ID(),
    # Role of the document owner. Most of the time it's the same as the `uploaded_by` and company
    # of that role is equal to `edrpou_owner`. But in some cases (Sovtes and Agroyard case) we
    # allow to upload document by vendor account and then assign `edrpou_owner` to some other
    # company. In that case there will be no actual document owner role of the document and this
    # field will point to the role from vendor account.
    columns.ForeignKey('role_id', 'roles.id', nullable=True, ondelete='NO ACTION'),
    # Document may be uploaded by master account but owned by some other user
    # See Agroyard, Sovtes, Portmone cases for more details
    # https://tabula-rasa.atlassian.net/browse/DOC-6775
    # When performing joins for this attribute, consider using the following pattern:
    #   sa.and_(document_table.c.uploaded_by == ..., document_table.c.edrpou_owner == ...)
    # to prevent operations over uploader, who isn't owner of the document.
    columns.ForeignKey('uploaded_by', 'roles.id', index=True, ondelete='NO ACTION'),
    # TODO: remove it
    columns.SimpleUUID(
        'hermes_document_id',
        nullable=True,
        # TODO[DOC-7426]: Remove this index
        index=True,
    ),
    # Owner/recipient
    columns.EDRPOU('edrpou_owner', nullable=False, index=True),
    columns.EDRPOU('edrpou_recipient', index=True),
    # Note: this field might contain multiple emails separated by commas
    columns.Email('email_recipient', nullable=True),
    # Title/status
    sa.Column('title', sa.String(512), nullable=False),
    # TODO: make that field not nullable because we don't have any documents in our system
    #   without extension (2024-04-04)
    sa.Column('extension', sa.String(64)),
    sa.Column('archive_name', sa.String(512)),
    # Enum app.lib.enums.DocumentStatus
    sa.Column('status_id', sa.SmallInteger(), index=True),
    # Metadata
    columns.DateTime('date_document', index=True),
    sa.Column('type', sa.String(512)),
    sa.Column('number', sa.String(512)),
    # monetary value is stored in "копійках" (1 UAH * 100)
    # In our team this field is also known as "Сума документа"
    sa.Column('amount', sa.BigInteger(), nullable=True),
    # Source/vendor data
    # Source field stores original document source, if not starts with
    # `vchasno`, means that file imported from external system
    columns.Enum('source', DocumentSource, DocumentSource.vchasno),
    # Vendor field stores the way how document uploaded to the system.
    # Nullable - uploaded via internal API, API - via public API, 1C - via 1C
    # plugin
    sa.Column('vendor', sa.String(64)),
    # External Id from vendor system
    sa.Column('vendor_id', sa.String(255), index=True),
    # First sign by owner (default behaviour) or recipient (3P document)?
    columns.Enum('first_sign_by', FirstSignBy, FirstSignBy.owner),
    # Internal document is used only by owner company and can't be send
    # TODO: Merge `first_sign_by` and `is_internal` to one for convenience
    columns.Boolean('is_internal', default_value=False, index=True),
    # Multilateral document is used for frontent to mark documents with flow
    columns.Boolean('is_multilateral', default_value=False),
    # Document marked with that flag indicates
    # that document signed by signatures that no longer valid
    columns.Boolean('is_invalid_signed', nullable=True),
    # Number of signatures before document moved to finished state
    # TODO: Remove as obsolete after implementing code to work with
    # expected_owner_signatures + expected_recipient_signatures
    # Warning: this field is DEPRECATED
    sa.Column(
        'signatures_to_finish',
        sa.SmallInteger(),
        default=DEFAULT_SIGNATURES_TO_FINISH,
        nullable=False,
        server_default=sa.text(str(DEFAULT_SIGNATURES_TO_FINISH)),
    ),
    # Number of owner signatures before document be able to shared with
    # recipient or finished (for invoices).
    # For multilateral documents, we use "documents_flows.signatures_count" column instead of
    # this and next field.
    sa.Column(
        'expected_owner_signatures',
        sa.SmallInteger(),
        default=DEFAULT_EXPECTED_OWNER_SIGNATURES,
        nullable=False,
        server_default=sa.text(str(DEFAULT_EXPECTED_OWNER_SIGNATURES)),
    ),
    # Number of recipient signatures before document will became finished or
    # be able to be shared with owner (for 3P documents)
    sa.Column(
        'expected_recipient_signatures',
        sa.SmallInteger(),
        default=DEFAULT_EXPECTED_RECIPIENT_SIGNATURES,
        nullable=False,
        server_default=sa.text(str(DEFAULT_EXPECTED_RECIPIENT_SIGNATURES)),
    ),
    # Expected type of signature for given documents
    columns.SoftEnum('signature_format', SignatureFormat, nullable=True),
    # Document category: act, bill, waybill, etc
    columns.ForeignKey(
        'category',
        'document_categories.id',
        nullable=True,
        ondelete='NO ACTION',
    ),
    # URIs for cached archive / PDF from XML on S3
    sa.Column('s3_archive_key', sa.Text()),
    sa.Column('s3_xml_to_pdf_key', sa.Text()),
    # Additional fields
    columns.Boolean(
        'has_changed_for_public_api',
        default_value=True,
        # TODO[DOC-7426]: Remove this index
        index=True,
    ),
    # TODO: Remove this unused field
    columns.Boolean('is_protected'),
    # Used for full elasticsearch indexing
    columns.Seqnum('documents_seqnum'),
    # Dates
    columns.DateCreated(index=True),
    columns.DateUpdated(),
    columns.DateTime('date_delivered', index=True),
    columns.DateTime('date_finished'),
    # TODO[ID]: To delete after `v9.0.0` release
    # Use `role_id` column instead
    columns.ForeignKey(
        'user_id',
        'users.id',
        nullable=True,
        ondelete='NO ACTION',
    ),
    # TODO[DOC-7426]: Remove this index
    sa.Index(
        'idx_documents_edrpou_owner_for_multilateral',
        'edrpou_owner',
        postgresql_where=sa.text('is_multilateral = true'),
        postgresql_concurrently=True,
    ),
    sa.Index(
        'idx_document_id_is_invalid_signed',
        'id',
        'is_invalid_signed',
        postgresql_where=sa.text('is_invalid_signed is true'),
    ),
    sa.Index(
        'ix_documents_email_recipient',
        models.functions.split_comma_separated_emails('email_recipient'),
        postgresql_using='gin',
        postgresql_concurrently=True,
    ),
)
# TODO[DOC-7426]: Remove this index
sa.Index(
    'idx_documents_number_gin_trgm_ops',
    document_table.c.number,
    postgresql_using='gin',
    postgresql_ops={'number': 'gin_trgm_ops'},
    postgresql_concurrently=True,
)

# Table for implementation filter 'processed' in api list_incoming_documents
documents_processed_table = sa.Table(
    'documents_processed',
    metadata,
    columns.UUID(),
    columns.ForeignKey('document_id', 'documents.id', ondelete='CASCADE', nullable=False),
    columns.ForeignKey('company_id', 'companies.id', nullable=False, ondelete='NO ACTION'),
    columns.Boolean('processed', nullable=False, default=False),
    columns.DateCreated(),
    columns.DateUpdated(),
    sa.UniqueConstraint(
        'document_id',
        'company_id',
        name='uix_unique_together_document_id_and_company_id',
    ),
)


document_meta_table = sa.Table(
    'documents_meta',
    metadata,
    columns.ForeignKey(
        'document_id',
        'documents.id',
        primary_key=True,
        ondelete='CASCADE',
        nullable=False,
    ),
    # eusign hash GOST 34.311
    sa.Column('content_hash', sa.String(128), nullable=False),
    sa.Column('content_length', sa.Integer(), nullable=False),
)

# Table for storing document recipients, including document owner, if document owner
# participate in document flow
document_recipients_table = sa.Table(
    'document_recipients',
    metadata,
    columns.UUID(),
    sa.Column(
        'emails',
        postgresql.ARRAY(sa.String),
        nullable=True,
        default=None,
    ),
    # docs/choose-email.md
    columns.Boolean(
        'is_emails_hidden',
        nullable=False,
        default=False,
        server_default=sa.false(),
    ),
    columns.EDRPOU(nullable=False, index=True),
    columns.DateCreated(),
    columns.DateUpdated(),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        nullable=False,
        index=True,
        ondelete='NO ACTION',
    ),
    # TODO: migrate to "nullable=False"
    # Metadata from external systems. Currently we are storing metadata only from "Zakupivli.pro"
    # (former "Zakupki.prom.ua") system. See DOC-5276 and "prepare_zakupki_external_meta" function
    # for more details.
    columns.JSONB('external_meta', nullable=True),
    # This is a date when the document was sent to the current recipient
    columns.DateTime('date_sent', nullable=True),
    # The date when the recipient received the document.
    # If the recipient’s company is registered and a document is sent to the recipient, only then
    # is the document considered "received" by the recipient. Usually, this date is the same as
    # the "date_sent" date. However, if the company is not registered, this date is set only when
    # the recipient's company will be actually registered, and then it is set to the date of
    # registration. The main purpose of this and the next date is to help clients win disputes in
    # court
    columns.DateTime('date_received', nullable=True),
    # The date when the document was opened by the recipient
    columns.DateTime('date_delivered', nullable=True),
    columns.ForeignKey(
        'assigner_role_id',
        'roles.id',
        ondelete='SET NULL',
    ),
    columns.Boolean('from_flow', default=False),
)


sa.Index(
    'uidx_document_recipients_document_id_edrpou',
    document_recipients_table.c.document_id,
    document_recipients_table.c.edrpou,
    unique=True,
    # EDRPOU is not unique for "multilateral" documents because single company can be recipient
    # multiple times. Example: company1 -> company2 -> company1
    postgresql_where=document_recipients_table.c.from_flow.is_(False),
)


# TODO[DOC-7426]: Remove this index
sa.Index(
    'ix_document_recipients_emails',
    document_recipients_table.c.emails,
    postgresql_using='gin',
    postgresql_concurrently=True,
)


sa.Index(
    'ix_document_recipients_emails_normalized',
    models.functions.lowercase_emails_array(document_recipients_table.c.emails),
    postgresql_using='gin',
    postgresql_concurrently=True,
)

# TODO[DOC-7426]: Remove this index
sa.Index(
    'ix_document_recipients_document_id_edrpou_not_delivered',
    document_recipients_table.c.document_id,
    document_recipients_table.c.edrpou,
    postgresql_where=sa.text('date_sent is not null and date_delivered is null'),
    postgresql_concurrently=True,
)

# TODO[DOC-7426]: Remove this index
sa.Index(
    'ix_document_recipients_edrpou_document_id_not_delivered',
    document_recipients_table.c.edrpou,
    document_recipients_table.c.document_id,
    postgresql_where=sa.text('date_sent is not null and date_delivered is null'),
    postgresql_concurrently=True,
)

# For "update_document_date_received_on_company_registration" task
sa.Index(
    'ix_document_recipients_edrpou_id_not_received',
    document_recipients_table.c.edrpou,
    document_recipients_table.c.id,
    postgresql_where=sa.text('date_sent is not null and date_received is null'),
    postgresql_concurrently=True,
)

# Table used only to autofill recipient emails on document uploading
latest_document_recipients_table = sa.Table(
    'latest_document_recipients',
    metadata,
    columns.EDRPOU('owner_edrpou', nullable=False),
    columns.EDRPOU('recipient_edrpou', nullable=False),
    # WARNING: Before selecting the recipients_emails column, typecast it to text[] because
    # psycopg2 and SQLAlchemy 3.13 do not properly support citext[], which can lead to incorrect
    # results. See: https://github.com/mahmoudimus/sqlalchemy-citext/issues/9
    columns.Array('recipient_emails', citext.CIText(), nullable=False),
    columns.DateUpdated(),
    # Such owner_edrpou and recipient_edrpou pair should be unique, not null, and we don't need
    # to reference this table elsewhere, we can make a composite primary key from these two
    # fields. Having a separate primary key is not needed, but might slow down the inserts.
    sa.PrimaryKeyConstraint(
        'owner_edrpou',
        'recipient_edrpou',
        name='pk_latest_document_recipients',
        # The "fillfactor" parameter is set to increase the chance of HOT updates, reduce bloat
        # and to speed up UPDATE operations.
        # TODO: uncomment after upgrading to SQLAlchemy 1.4 because 1.3 doesn't support
        #   setting it in table definition. For now it is set manually in migration.
        # postgresql_with={'fillfactor': 70},
    ),
)

# Table for storing information about access that role have to the document
# See "app/documents/README.md" for more details about this table.
listing_table = sa.Table(
    'access_to_doc',
    metadata,
    columns.ID(),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        nullable=False,
        ondelete='NO ACTION',
        index=True,
    ),
    columns.EDRPOU(
        'access_edrpou',
        nullable=False,
        # TODO[DOC-7426]: Remove this index
        index=True,
    ),
    columns.ForeignKey(
        'role_id',
        'roles.id',
        nullable=True,
        index=True,
        ondelete='NO ACTION',
    ),
    sa.Column(
        'sources',
        custom_types.BitMask(AccessSource),
        default=AccessSource.default,
        server_default=str(AccessSource.default.value),
        nullable=False,
    ),
    columns.DateCreated(),
    sa.UniqueConstraint(
        'access_edrpou',
        'role_id',
        'document_id',
        name='uix_access_to_doc_access_edrpou_role_id_document_id',
    ),
)

sa.Index(
    'ix_access_to_doc_date_created',
    listing_table.c.date_created,
    postgresql_concurrently=True,
)


# Table for storing information about access that company have to the document
# See "app/documents/README.md" for more details about this table.
company_listing_table = sa.Table(
    'company_first_access_to_doc',
    metadata,
    columns.ID(),
    columns.EDRPOU(nullable=False),
    columns.ForeignKey(
        'document_id',
        'documents.id',
        nullable=False,
        ondelete='CASCADE',
        index=True,
    ),
    # Copy of the "documents.seqnum"
    sa.Column(
        'document_num',
        sa.BigInteger(),
        nullable=True,
    ),
    columns.DateCreated(),
    sa.UniqueConstraint(
        'edrpou',
        'document_id',
        name='uix_company_edrpou_document_id',
    ),
)

# Table to store information whenever a document has "private" access level for this company.
# If a document is missing here, it means that document has "extended" access level.
document_access_settings_private_table = sa.Table(
    'document_access_settings_private',
    metadata,
    columns.UUID(),
    columns.EDRPOU(nullable=False),
    columns.ForeignKey(
        name='document_id',
        mixed='documents.id',
        nullable=False,
        ondelete='CASCADE',
        index=True,
    ),
    columns.DateCreated(),
    sa.UniqueConstraint('edrpou', 'document_id', name='uix_document_access_settings_private'),
)

sa.Index(
    'ix_edrpou_date_created_document_num',
    company_listing_table.c.edrpou,
    company_listing_table.c.date_created.desc(),
    company_listing_table.c.document_num.desc(),
)


document_link_table = sa.Table(
    'document_links',
    metadata,
    columns.UUID(),
    # TODO: migrate to "nullable=False"
    columns.ForeignKey(
        'company_id',  # link creator
        'companies.id',
        nullable=True,
        ondelete='NO ACTION',
    ),
    columns.EDRPOU('company_edrpou', nullable=False),
    columns.ForeignKey(
        'parent_id',
        'documents.id',
        nullable=False,
        ondelete='NO ACTION',
        index=True,
    ),
    columns.ForeignKey(
        'child_id',
        'documents.id',
        nullable=False,
        ondelete='NO ACTION',
        index=True,
    ),
    columns.DateCreated(),
)


delete_request_table = sa.Table(
    'delete_document_requests',
    metadata,
    columns.UUID(),
    columns.Text('document_id', index=True),
    columns.Seqnum('delete_documents_seqnum'),
    columns.ForeignKey(
        'initiator_role_id',
        'roles.id',
        index=True,
        nullable=False,
        ondelete='CASCADE',
    ),
    columns.EDRPOU(name='receiver_edrpou'),
    columns.EDRPOU(name='initiator_edrpou', nullable=False),
    # redundant field, not used anymore
    sa.Column('recipients_emails', postgresql.ARRAY(sa.String), nullable=False, default=[]),
    columns.SoftEnum(
        'status',
        DeleteRequestStatus,
        default_value=DeleteRequestStatus.new,
        index=True,
    ),
    columns.Text('message'),
    columns.Text('reject_message', nullable=True),
    columns.DateCreated(),
    columns.DateTime('date_accepted', False, False),
    columns.DateTime('date_rejected', False, False),
)

# Per document settings for delete request
delete_document_settings_table = sa.Table(
    'delete_document_settings',
    metadata,
    columns.ForeignKey(
        'document_id',
        'documents.id',
        ondelete='CASCADE',
        primary_key=True,
        nullable=False,
    ),
    # Document can be deleted only with delete request
    columns.Boolean('is_delete_request_required'),
    columns.DateCreated(),
    columns.DateUpdated(),
)


document_listing_join = document_table.join(
    listing_table,
    listing_table.c.document_id == document_table.c.id,
)


document_recipients_join = document_table.outerjoin(
    document_recipients_table,
    document_recipients_table.c.document_id == document_table.c.id,
)


# This alias used for support complicated operation over emails like `LIKE`
# NOTE: be aware to use this alias, because it can cause slow down of
# the database queries.
unnested_recipient_emails = sa.alias(
    sa.select(
        [
            document_recipients_table.c.document_id,
            sa.func.unnest(document_recipients_table.c.emails).label('email'),
            document_recipients_table.c.edrpou,
            document_recipients_table.c.id,
            document_recipients_table.c.assigner_role_id,
        ]
    )
)

document_with_uploader_fields = [
    document_table,
    company_table.c.id.label('uploaded_by_company_id'),
    company_table.c.edrpou.label('uploaded_by_edrpou'),
    company_table.c.name.label('uploaded_by_name'),
    company_table.c.is_legal.label('uploaded_by_is_legal'),
]
document_with_uploader_join = document_table.outerjoin(
    role_table,
    role_table.c.id == document_table.c.uploaded_by,
).outerjoin(
    company_table,
    company_table.c.id == role_table.c.company_id,
)
