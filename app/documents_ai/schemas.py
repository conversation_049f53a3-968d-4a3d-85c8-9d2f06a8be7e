import datetime
import logging
import typing as t

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    ValidationError,
    ValidationInfo,
    WrapValidator,
    field_validator,
)

from app.documents_ai.enums import ModelName
from app.lib import validators_pydantic as pv
from app.lib.database import DBRow
from app.lib.money import to_subunits_decimal_from_units, to_units_decimal_from_subunits
from app.lib.types import DataDict

logger = logging.getLogger(__name__)


def _invalid_to_none(v: t.Any, handler: t.Callable[[t.Any], t.Any]) -> t.Any:
    """
    Specific helper for DocumentMetaSuggest model.
    Helps to put None values for fields that don't pass validation by model"""
    try:
        return handler(v)
    except ValidationError as err:
        logger.info(
            'Unexpected field value from AI model response',
            extra={
                'error_message': str(err),
                'value': str(v),
            },
        )
        return None


class DocumentMetaSuggest(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    document_id: pv.UUID

    # data, suggested by model from bedrock
    category: int | None
    edrpou_recipient: t.Annotated[pv.EDRPOU | None, WrapValidator(_invalid_to_none)] = Field(
        None, alias='edrpou'
    )
    date_document: t.Annotated[datetime.date | None, WrapValidator(_invalid_to_none)] = Field(
        None, alias='date'
    )
    number: str | None = Field(None, max_length=512)
    amount: t.Annotated[pv.Price | None, WrapValidator(_invalid_to_none)]

    # time in seconds for invoking model from bedrock
    request_duration: float

    # length of text chunk extracted from begin/end of document
    chunk_length: int

    # number of tokens used during request a model from bedrock
    # it is used for calculating cost of service
    tokens_used: int

    # model name, which used for generating AI suggestions
    model: ModelName

    @field_validator('category', mode='after')
    @classmethod
    def validate_category_exists(cls, v: int | None, info: ValidationInfo) -> int | None:
        if isinstance(info.context, dict):
            available_categories: set[int] = info.context.get('available_categories', set())
            if v is not None and v not in available_categories:
                return None
        return v

    def to_db(self) -> DataDict:
        data = self.model_dump(mode='json')
        amount = data['amount']
        data['amount'] = to_subunits_decimal_from_units(amount) if amount else None
        data['model'] = self.model
        return data

    @classmethod
    def from_db(cls, row: DBRow) -> t.Self:
        result = cls.model_validate(row, from_attributes=True)
        if amount := result.amount:
            result.amount = to_units_decimal_from_subunits(amount)
        return result


class DocumentMetaSuggestResponse(BaseModel):
    title: str | None

    # data, suggested by model from bedrock
    category: int | None
    edrpou_recipient: pv.EDRPOU | None = Field(..., serialization_alias='companyEdrpou')
    date_document: datetime.date | None = Field(..., serialization_alias='date')
    number: str | None = Field(..., max_length=512)
    amount: pv.Price | None

    # recipient additional data
    recipient_name: str | None = Field(..., serialization_alias='companyName')
    recipient_email: str | None = Field(..., serialization_alias='companyEmail')

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', by_alias=True)

    @classmethod
    def empty(cls) -> t.Self:
        return cls(
            title=None,
            category=None,
            edrpou_recipient=None,
            date_document=None,
            number=None,
            amount=None,
            recipient_name=None,
            recipient_email=None,
        )


class DocumentSummaryResponse(BaseModel):
    model: str
    summary: str
    duration_seconds: float
    input_tokens: int
    output_tokens: int
    total_price_usd: float

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json', by_alias=True)


class ModelResponse(BaseModel):
    input_tokens: int
    output_tokens: int
    raw_data: t.Any
