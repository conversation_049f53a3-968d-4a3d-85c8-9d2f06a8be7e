import typing as t

from aiohttp import web
from aiohttp.web_request import FileField

from api.errors import AccessDenied, InvalidRequest
from app.auth.utils import get_company_config
from app.documents_ai.constants import ALLOWED_AI_SUGGEST_EXTENSIONS
from app.i18n import _
from app.lib.helpers import run_sync
from app.lib.validators import validate_post_request
from app.services import services
from app.uploads.utils import prepare_file_details_from_filename


class UploadedFile(t.NamedTuple):
    name: str
    title: str
    extension: str
    content: bytes


async def validate_document_meta_suggest_enabled(company_id: str) -> None:
    async with services.db.acquire() as conn:
        company_config = await get_company_config(conn, company_id=company_id)
    if not company_config.allow_suggesting_document_meta_with_ai:
        raise AccessDenied(reason=_('Доступ обмежено адміністратором компанії'))


async def validate_upload_file(request: web.Request) -> UploadedFile:
    post_data = await validate_post_request(request)
    file_item = None
    for item in post_data.values():
        if not isinstance(item, FileField):
            continue
        file_item = item

    if file_item is None:
        raise InvalidRequest(details=_('Не знайдено файл у запиті'))

    file_details = prepare_file_details_from_filename(file_item.filename)
    if file_details.extension not in ALLOWED_AI_SUGGEST_EXTENSIONS:
        raise InvalidRequest(
            reason=_('Недопустимий формат файлу'),
            details={'allowed': ALLOWED_AI_SUGGEST_EXTENSIONS},
        )

    file_content = await run_sync(file_item.file.read)
    file_item.file.close()

    return UploadedFile(
        title=file_details.title,
        name=file_details.name,
        extension=file_details.extension,
        content=file_content,
    )
