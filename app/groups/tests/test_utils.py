from datetime import datetime

import pytest

from api.errors import AlreadyExists
from app.groups.db import (
    insert_group_member,
    select_group,
    select_group_member,
    select_group_members,
    select_groups,
    update_group,
)
from app.groups.utils import (
    add_group,
    add_group_member,
    remove_group,
    remove_group_members,
)
from app.tests.common import (
    prepare_client,
    prepare_user_data,
)


async def test_group_add(aiohttp_client):
    # arrange
    app, _, user = await prepare_client(aiohttp_client)

    # act
    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )

    # assert
    assert group.id is not None
    assert group.name == 'test'
    assert group.created_by == user.role_id
    assert group.company_id == user.company_id
    assert group.deleted_by is None
    assert group.date_created is not None
    assert group.date_updated is not None
    assert group.date_deleted is None


async def test_group_add_same_name(aiohttp_client):
    """
    Test that adding a group with the same name raises an error.
    """

    # arrange
    app, _, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        await add_group(
            conn=conn,
            name='test',
            user=user,
        )

        # act & assert
        with pytest.raises(AlreadyExists):
            await add_group(
                conn=conn,
                name='test',
                user=user,
            )


async def test_group_get(aiohttp_client):
    # arrange
    app, _, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )
        await add_group(
            conn=conn,
            name='test2',
            user=user,
        )

        # act
        group_by_id = await select_group(conn=conn, id=group.id)
        group_by_name = await select_group(conn=conn, name=group.name)
        group_by_company = await select_group(conn=conn, company_id=group.company_id)
        group_by_name_and_company = await select_group(
            conn=conn, name=group.name, company_id=group.company_id
        )

        # assert
        assert group_by_id == group
        assert group_by_name == group
        assert group_by_company == group
        assert group_by_name_and_company == group


async def test_group_select(aiohttp_client):
    # arrange
    app, _, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )
        await add_group(
            conn=conn,
            name='test2',
            user=user,
        )

        # act
        groups_by_company = await select_groups(
            conn=conn,
            company_id=user.company_id,
        )
        groups_by_company_and_role = await select_groups(
            conn=conn,
            company_id=user.company_id,
            created_by=user.role_id,
        )
        groups_by_name = await select_groups(conn=conn, name=group.name)

        # assert
        assert len(groups_by_company) == 2
        assert len(groups_by_company_and_role) == 2
        assert len(groups_by_name) == 1


async def test_group_update(aiohttp_client):
    # arrange
    app, _, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )

        # act
        updated_group = await update_group(
            conn=conn,
            id=group.id,
            name='test2',
        )

    # assert
    assert updated_group.name == 'test2'


async def test_group_delete(aiohttp_client):
    """
    Test that deleting a group sets the deleted_by and date_deleted fields.
    """

    # arrange
    app, _, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )

        # act
        await remove_group(
            conn,
            id=group.id,
            deleted_by=user.role_id,
        )

        # assert
        deleted_group = await select_group(conn=conn, id=group.id)

    assert deleted_group.deleted_by == user.role_id
    assert deleted_group.date_deleted is not None


async def test_group_member_add(aiohttp_client):
    # arrange
    app, _, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )

        # act
        group_member = await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user.role_id,
        )

        # assert
        assert group_member.id is not None
        assert group_member.group_id == group.id
        assert group_member.role_id == user.role_id
        assert group_member.date_created is not None
        assert group_member.date_updated is not None


async def test_group_member_same_role(aiohttp_client):
    # arrange
    app, _, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user.role_id,
        )
        # act & assert
        with pytest.raises(AlreadyExists):
            await add_group_member(
                conn=conn,
                group_id=group.id,
                role_id=user.role_id,
                initiator=user,
            )


async def test_group_member_get(aiohttp_client):
    # arrange
    app, _, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )

        group_member = await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user.role_id,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=coworker.role_id,
        )
        await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=coworker.role_id,
            deleted_by=user.role_id,
            date_deleted=datetime.now(),
        )

        # assert
        member_by_id = await select_group_member(conn=conn, id=group_member.id)
        members_by_group = await select_group_members(
            conn,
            is_deleted=False,
            group_ids=[group.id],
        )
        members_by_group_is_deleted = await select_group_members(
            conn,
            is_deleted=True,
            group_ids=[group.id],
        )

        # assert
        assert member_by_id == group_member
        assert len(members_by_group) == 2
        assert len(members_by_group_is_deleted) == 1


async def test_group_member_delete(aiohttp_client):
    """
    Test that deleting a group member sets the deleted_by and date_deleted fields.
    """
    # arrange
    app, _, user = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        group = await add_group(
            conn=conn,
            name='test',
            user=user,
        )
        group_member = await insert_group_member(
            conn=conn,
            group_id=group.id,
            role_id=user.role_id,
        )

        # act
        await remove_group_members(
            conn=conn,
            deleted_by=user.role_id,
            ids=[group_member.id],
        )

        # assert
        group_member_deleted = await select_group_member(
            conn=conn,
            id=group_member.id,
        )
        assert group_member_deleted.deleted_by == user.role_id
        assert group_member_deleted.date_deleted is not None
