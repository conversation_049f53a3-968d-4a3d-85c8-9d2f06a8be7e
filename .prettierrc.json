{"tabWidth": 4, "singleQuote": true, "trailingComma": "all", "semi": true, "importOrder": ["^react(.*)$", "^@evo(.*)$", "^(@|uuid|cookie)(.*)$", "^[a-zA-Z0-9]+[-/]?(.*)$", "^(.*)(/i18n|/lib/tz)(.*)$", "^(.*)(/types|/enums)(.*)$", "(selectors|actions|Actions|ActionCreators)$", "(utils|helpers)$", "^(.*)(/ui/)(.*)$", "<THIRD_PARTY_MODULES>", "svg$", "(png|jpg|jpeg|gif)$", "(css|styl)$"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "overrides": [{"files": "*.md", "options": {"proseWrap": "always", "printWidth": 100, "tabWidth": 2, "useTabs": false}}]}